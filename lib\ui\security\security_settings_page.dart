import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/auth_bloc/auth_bloc.dart';
import '../../bloc/auth_bloc/auth_event.dart';
import '../../bloc/auth_bloc/auth_state.dart';
import '../auth/components/auth_text_field.dart';

class SecuritySettingsPage extends StatefulWidget {
  const SecuritySettingsPage({super.key});

  @override
  State<SecuritySettingsPage> createState() => _SecuritySettingsPageState();
}

class _SecuritySettingsPageState extends State<SecuritySettingsPage> {
  final _newEmailController = TextEditingController();
  final _otpController = TextEditingController();

  bool _showOtpField = false;
  String? _emailError;
  String? _otpError;

  @override
  void dispose() {
    _newEmailController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  void _onChangeEmail() {
    final newEmail = _newEmailController.text.trim();

    if (newEmail.isEmpty) {
      setState(() {
        _emailError = 'Please enter your new email address';
      });
      return;
    }

    if (!newEmail.contains('@') || !newEmail.contains('.')) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      return;
    }

    context.read<AuthBloc>().add(EmailChangeRequested(newEmail: newEmail));
  }

  void _onVerifyOtp() {
    final code = _otpController.text.trim();

    if (code.length != 6) {
      setState(() {
        _otpError = 'Please enter the 6-digit code';
      });
      return;
    }

    context.read<AuthBloc>().add(EmailChangeOTPSubmitted(code: code));
  }

  void _onRequestNewDeviceVerification() {
    // Example: Simulate new device verification
    const email = '<EMAIL>'; // In real app, get from current user
    context.read<AuthBloc>().add(NewDeviceVerificationRequested(email: email));
  }

  void _onRequestSecurityOperation() {
    // Example: Simulate security operation
    const email = '<EMAIL>'; // In real app, get from current user
    context.read<AuthBloc>().add(
      SecurityOperationRequested(
        operation: 'Change Security Settings',
        email: email,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Security Settings'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state.successMessage != null) {
            if (state.successMessage!.contains('Verification code sent')) {
              setState(() {
                _showOtpField = true;
                _emailError = null;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.successMessage!),
                  backgroundColor: Colors.blue,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            } else if (state.successMessage!.contains('updated successfully') ||
                state.successMessage!.contains('verified successfully') ||
                state.successMessage!.contains('completed')) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.successMessage!),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
              setState(() {
                _showOtpField = false;
                _newEmailController.clear();
                _otpController.clear();
              });
            }
          }

          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'Security & Verification',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Manage your account security settings and verification methods.',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 32),

              // Email Change Section
              _buildSectionCard(
                title: 'Change Email Address',
                description: 'Update your email address with OTP verification',
                child: Column(
                  children: [
                    AuthTextField(
                      controller: _newEmailController,
                      hintText: 'Enter new email address',
                      keyboardType: TextInputType.emailAddress,
                      errorText: _emailError,
                      enabled: !_showOtpField,
                      onChanged: (value) {
                        if (_emailError != null) {
                          setState(() {
                            _emailError = null;
                          });
                        }
                      },
                    ),

                    if (_showOtpField) ...[
                      const SizedBox(height: 16),
                      AuthTextField(
                        controller: _otpController,
                        hintText: 'Enter 6-digit verification code',
                        keyboardType: TextInputType.number,
                        errorText: _otpError,
                        maxLength: 6,
                        onChanged: (value) {
                          if (_otpError != null) {
                            setState(() {
                              _otpError = null;
                            });
                          }
                        },
                      ),
                    ],

                    const SizedBox(height: 16),
                    BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        return AuthPrimaryButton(
                          text: _showOtpField ? 'Verify Code' : 'Change Email',
                          isLoading: state.isLoading,
                          onPressed:
                              _showOtpField ? _onVerifyOtp : _onChangeEmail,
                        );
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Demo Buttons Section
              _buildSectionCard(
                title: 'Security Demonstrations',
                description: 'Test different OTP security scenarios',
                child: Column(
                  children: [
                    _buildDemoButton(
                      title: 'New Device Verification',
                      description: 'Simulate login from new device',
                      onPressed: _onRequestNewDeviceVerification,
                    ),
                    const SizedBox(height: 12),
                    _buildDemoButton(
                      title: 'Security Operation',
                      description: 'Simulate sensitive operation requiring OTP',
                      onPressed: _onRequestSecurityOperation,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String description,
    required Widget child,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            theme.brightness == Brightness.dark
                ? Colors.grey[900]
                : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              theme.brightness == Brightness.dark
                  ? Colors.grey[700]!
                  : Colors.grey[300]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildDemoButton({
    required String title,
    required String description,
    required VoidCallback onPressed,
  }) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
