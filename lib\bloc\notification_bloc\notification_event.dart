import 'package:equatable/equatable.dart';

abstract class NotificationEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class LoadNotificationsEvent extends NotificationEvent {}

class RefreshNotificationsEvent extends NotificationEvent {}

class LoadMoreNotificationsEvent extends NotificationEvent {}

class MarkNotificationAsReadEvent extends NotificationEvent {
  final String notificationId;

  MarkNotificationAsReadEvent(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

class MarkNotificationAsUnreadEvent extends NotificationEvent {
  final String notificationId;

  MarkNotificationAsUnreadEvent(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

class MarkAllAsReadEvent extends NotificationEvent {}

class DeleteNotificationEvent extends NotificationEvent {
  final String notificationId;

  DeleteNotificationEvent(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

class FilterNotificationsByTypeEvent extends NotificationEvent {
  final String? filterType;

  FilterNotificationsByTypeEvent(this.filterType);

  @override
  List<Object?> get props => [filterType];
}

class ToggleNotificationSettingsEvent extends NotificationEvent {
  final String settingType;
  final bool enabled;

  ToggleNotificationSettingsEvent(this.settingType, this.enabled);

  @override
  List<Object> get props => [settingType, enabled];
}

class SearchNotificationsEvent extends NotificationEvent {
  final String query;

  SearchNotificationsEvent(this.query);

  @override
  List<Object> get props => [query];
}

class ClearSearchEvent extends NotificationEvent {}

class AddNewNotificationEvent extends NotificationEvent {
  final NotificationModel notification;

  AddNewNotificationEvent(this.notification);

  @override
  List<Object> get props => [notification];
}

// Notification Model
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime timestamp;
  final bool isRead;
  final String? imageUrl;
  final String? actionUrl;
  final Map<String, dynamic>? metadata;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.imageUrl,
    this.actionUrl,
    this.metadata,
  });

  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    DateTime? timestamp,
    bool? isRead,
    String? imageUrl,
    String? actionUrl,
    Map<String, dynamic>? metadata,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      metadata: metadata ?? this.metadata,
    );
  }
}

// Notification Types
class NotificationTypes {
  static const String like = 'like';
  static const String comment = 'comment';
  static const String follow = 'follow';
  static const String mention = 'mention';
  static const String message = 'message';
  static const String post = 'post';
  static const String business = 'business';
  static const String system = 'system';
  static const String promotion = 'promotion';
  static const String reminder = 'reminder';
}
