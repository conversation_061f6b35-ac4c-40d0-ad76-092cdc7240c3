# Professional Connectivity System

This connectivity system implements professional offline handling patterns used by big apps like Facebook, Instagram, and WhatsApp. It provides real-time connectivity monitoring, smart UI responses, and seamless user experience during network changes.

## Features

### 🚀 **Dual-Layer Detection**
- **Network Detection**: Uses `connectivity_plus` to detect WiFi, mobile data, ethernet connections
- **Internet Verification**: Uses `internet_connection_checker_plus` to verify actual internet access
- **Smart Differentiation**: Distinguishes between "connected to WiFi but no internet" vs "fully online"

### 📱 **Big App UX Patterns**
- **Facebook-style SnackBars**: Persistent offline notifications with action buttons
- **Instagram-style Banners**: Top banners for offline states with retry options
- **WhatsApp-style Settings**: Direct navigation to device WiFi/mobile data settings

### ⚡ **Performance Optimized**
- **Debounced Updates**: Prevents rapid state changes from overwhelming the UI
- **Efficient Monitoring**: Background connectivity monitoring with minimal battery impact
- **Smart Caching**: Optimized internet checks with fallback servers

## Quick Start

### 1. Wrap Your App
```dart
// In app.dart
home: const ConnectivityWrapper(
  child: SplashScreen(),
),
```

### 2. Add to BLoC Providers
```dart
// In main.dart
BlocProvider<ConnectivityBloc>(create: (_) => ConnectivityBloc()),
```

### 3. Use Connectivity Indicators
```dart
// In AppBar
actions: [
  CompactConnectivityIndicator(),
],

// In UI
DetailedConnectivityIndicator(),
ConnectivityDotIndicator(),
```

## Components

### ConnectivityBloc
Professional state management for connectivity:
```dart
// Check connectivity
context.read<ConnectivityBloc>().add(ConnectivityCheckRequested());

// Retry connection
context.read<ConnectivityBloc>().add(ConnectivityRetryRequested());

// Open device settings
context.read<ConnectivityBloc>().add(
  ConnectivityOpenSettingsRequested(settingsType: ConnectivitySettingsType.wifi),
);
```

### ConnectivityWrapper
Smart UI wrapper with offline handling:
```dart
ConnectivityWrapper(
  showBanner: true,        // Show offline banner
  showSnackBar: true,      // Show offline SnackBar
  onConnectivityRestored: () => print('Back online!'),
  onConnectivityLost: () => print('Gone offline!'),
  child: YourPage(),
)
```

### Connectivity Indicators
Visual status indicators:
```dart
// Compact (for AppBars)
CompactConnectivityIndicator()

// Detailed (with text)
DetailedConnectivityIndicator()

// Dot indicator
ConnectivityDotIndicator(size: 12)

// Status bar
StatusBarConnectivityIndicator()
```

### Connectivity Utils
Helper functions for easy connectivity checks:
```dart
// Check if online
if (context.isOnline) {
  // Perform network operation
}

// Execute only if online
await context.executeIfOnline(() async {
  return await apiCall();
});

// Show offline message if needed
context.showOfflineMessageIfNeeded();
```

### ConnectivityAware Mixin
For widgets that need connectivity awareness:
```dart
class MyWidget extends StatefulWidget {
  // ...
}

class _MyWidgetState extends State<MyWidget> with ConnectivityAware {
  @override
  void onConnectivityChanged(ConnectivityState state) {
    if (state.isConnected) {
      // Handle connection restored
    } else {
      // Handle connection lost
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (isOnline) OnlineWidget(),
        if (isOffline) OfflineWidget(),
      ],
    );
  }
}
```

## Advanced Usage

### Custom Connectivity Wrapper
```dart
CallbackConnectivityWrapper(
  onOnline: () {
    // Custom online logic
    syncData();
    refreshContent();
  },
  onOffline: () {
    // Custom offline logic
    showOfflineMode();
    pauseUploads();
  },
  child: YourWidget(),
)
```

### Network Operation with Retry
```dart
final result = await ConnectivityUtils.executeWithConnectivityCheck(
  context,
  () async {
    return await apiService.getData();
  },
  maxRetries: 3,
  retryDelay: Duration(seconds: 2),
  showMessages: true,
);
```

### Wait for Connection
```dart
try {
  await ConnectivityUtils.waitForConnection(
    context,
    timeout: Duration(minutes: 5),
  );
  // Connection is now available
  await performNetworkOperation();
} catch (e) {
  // Timeout or error
}
```

## Connectivity States

### ConnectivityStatus
- `checking`: Initial state, checking connectivity
- `connected`: Fully online (network + internet)
- `disconnected`: No network connection
- `limitedConnection`: Network available but no internet
- `retrying`: Attempting to reconnect
- `error`: Error occurred during check

### Connection Types
- `wifi`: Connected via WiFi
- `mobile`: Connected via mobile data
- `ethernet`: Connected via ethernet
- `vpn`: Connected via VPN
- `bluetooth`: Connected via Bluetooth
- `other`: Other connection type
- `none`: No connection

## Best Practices

### 1. **Use Appropriate Wrappers**
```dart
// For full app
ConnectivityWrapper(child: app)

// For simple pages
SimpleConnectivityWrapper(child: page)

// For custom callbacks
CallbackConnectivityWrapper(onOnline: callback, child: widget)
```

### 2. **Handle Network Operations Gracefully**
```dart
// ✅ Good
await context.executeIfOnline(() async {
  return await apiCall();
}, onOffline: () {
  showCachedData();
});

// ❌ Avoid
await apiCall(); // No offline handling
```

### 3. **Provide User Feedback**
```dart
// ✅ Good - Show connectivity status
AppBar(
  title: Text('My App'),
  actions: [CompactConnectivityIndicator()],
)

// ✅ Good - Handle offline gracefully
if (context.isOffline) {
  return OfflineWidget();
}
```

### 4. **Use Connectivity Indicators**
```dart
// In AppBars
CompactConnectivityIndicator()

// In status areas
DetailedConnectivityIndicator()

// For minimal UI
ConnectivityDotIndicator()
```

## Demo Page

See `connectivity_demo_page.dart` for a complete demonstration of all features including:
- Real-time status monitoring
- All indicator types
- Action buttons for testing
- Settings toggles
- Network operation examples

## Integration with Existing Code

The connectivity system is designed to integrate seamlessly with your existing BLoC architecture:

1. **Already added to MultiBlocProvider** in `main.dart`
2. **Wrapped your app** in `app.dart`
3. **Added indicator to home page** AppBar
4. **Ready to use** throughout your app

## Performance Notes

- **Debounced Updates**: 500ms debounce prevents rapid state changes
- **Optimized Checks**: Uses multiple fallback servers for reliability
- **Efficient Monitoring**: Background monitoring with minimal resource usage
- **Smart Caching**: Reduces unnecessary network checks

This system provides the same professional offline handling as major apps while being lightweight and easy to integrate into your existing Flutter business app.
