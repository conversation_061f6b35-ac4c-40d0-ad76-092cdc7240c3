import 'package:equatable/equatable.dart';

abstract class ChatEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class LoadConversationsEvent extends ChatEvent {}

class LoadMessagesEvent extends ChatEvent {
  final String conversationId;

  LoadMessagesEvent(this.conversationId);

  @override
  List<Object> get props => [conversationId];
}

class SendMessageEvent extends ChatEvent {
  final String conversationId;
  final String message;
  final String? attachmentPath;
  final String? attachmentType;

  SendMessageEvent(
    this.conversationId,
    this.message, {
    this.attachmentPath,
    this.attachmentType,
  });

  @override
  List<Object> get props => [
        conversationId,
        message,
        attachmentPath ?? '',
        attachmentType ?? '',
      ];
}

class StartNewConversationEvent extends ChatEvent {
  final String userId;
  final String userName;

  StartNewConversationEvent(this.userId, this.userName);

  @override
  List<Object> get props => [userId, userName];
}

class MarkMessageAsReadEvent extends ChatEvent {
  final String messageId;

  MarkMessageAsReadEvent(this.messageId);

  @override
  List<Object> get props => [messageId];
}

class DeleteMessageEvent extends ChatEvent {
  final String messageId;

  DeleteMessageEvent(this.messageId);

  @override
  List<Object> get props => [messageId];
}

class DeleteConversationEvent extends ChatEvent {
  final String conversationId;

  DeleteConversationEvent(this.conversationId);

  @override
  List<Object> get props => [conversationId];
}

class ToggleTypingEvent extends ChatEvent {
  final String conversationId;
  final bool isTyping;

  ToggleTypingEvent(this.conversationId, this.isTyping);

  @override
  List<Object> get props => [conversationId, isTyping];
}

class ShowAttachmentOptionsEvent extends ChatEvent {}

class HideAttachmentOptionsEvent extends ChatEvent {}

class SelectAttachmentEvent extends ChatEvent {
  final String attachmentPath;
  final String attachmentType;

  SelectAttachmentEvent(this.attachmentPath, this.attachmentType);

  @override
  List<Object> get props => [attachmentPath, attachmentType];
}
