-- =====================================================
-- REVIEWS AND NOTIFICATIONS SYSTEM
-- Trust and engagement features
-- =====================================================

-- =====================================================
-- 13. REVIEWS TABLE (Trust System)
-- =====================================================

CREATE TABLE public.reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    reviewer_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    shop_id UUID REFERENCES public.shops(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE, -- Optional product review
    order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE, -- Optional order reference
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(200),
    review_text TEXT CHECK (length(review_text) <= 2000),
    
    -- Review metadata
    is_verified_purchase BOOLEAN DEFAULT FALSE, -- Verified through order
    is_anonymous BOOLEAN DEFAULT FALSE,
    
    -- Media attachments
    images TEXT[] DEFAULT '{}',
    
    -- Moderation
    is_approved BOOLEAN DEFAULT TRUE,
    is_flagged BOOLEAN DEFAULT FALSE,
    moderation_notes TEXT,
    moderated_by UUID REFERENCES public.profiles(id),
    moderated_at TIMESTAMPTZ,
    
    -- Helpfulness tracking
    helpful_count INTEGER DEFAULT 0 CHECK (helpful_count >= 0),
    not_helpful_count INTEGER DEFAULT 0 CHECK (not_helpful_count >= 0),
    
    -- Response from shop owner
    shop_response TEXT,
    shop_response_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT review_content_required CHECK (
        review_text IS NOT NULL OR title IS NOT NULL
    )
    -- Note: One review per order constraint handled by unique index below
);

-- Indexes for performance
CREATE INDEX idx_reviews_reviewer_id ON public.reviews(reviewer_id);
CREATE INDEX idx_reviews_shop_id ON public.reviews(shop_id);
CREATE INDEX idx_reviews_product_id ON public.reviews(product_id);
CREATE INDEX idx_reviews_order_id ON public.reviews(order_id);
CREATE INDEX idx_reviews_rating ON public.reviews(rating);
CREATE INDEX idx_reviews_created_at ON public.reviews(created_at DESC);
CREATE INDEX idx_reviews_is_approved ON public.reviews(is_approved);

-- Composite indexes for common queries
CREATE INDEX idx_reviews_shop_approved ON public.reviews(shop_id, is_approved, created_at DESC);
CREATE INDEX idx_reviews_product_approved ON public.reviews(product_id, is_approved, created_at DESC);

-- Unique constraint to ensure one review per order per reviewer (replaces CHECK constraint)
CREATE UNIQUE INDEX idx_reviews_unique_order_reviewer ON public.reviews(order_id, reviewer_id) WHERE order_id IS NOT NULL;

-- =====================================================
-- REVIEW HELPFULNESS TABLE
-- =====================================================

CREATE TABLE public.review_helpfulness (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    review_id UUID REFERENCES public.reviews(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    is_helpful BOOLEAN NOT NULL, -- true = helpful, false = not helpful
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(review_id, user_id) -- One vote per user per review
);

-- Indexes
CREATE INDEX idx_review_helpfulness_review_id ON public.review_helpfulness(review_id);
CREATE INDEX idx_review_helpfulness_user_id ON public.review_helpfulness(user_id);

-- =====================================================
-- 14. NOTIFICATIONS TABLE (User Engagement)
-- =====================================================

CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    
    -- Notification content
    type VARCHAR(50) NOT NULL, -- follow, like, comment, order, review, etc.
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    
    -- Notification data and context
    data JSONB DEFAULT '{}', -- Additional structured data
    action_url TEXT, -- Deep link or URL to relevant content
    
    -- Notification status
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    
    -- Priority and grouping
    priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    category VARCHAR(50) DEFAULT 'general', -- social, commerce, system, etc.
    group_key VARCHAR(100), -- For grouping similar notifications
    
    -- Delivery tracking
    is_push_sent BOOLEAN DEFAULT FALSE,
    is_email_sent BOOLEAN DEFAULT FALSE,
    push_sent_at TIMESTAMPTZ,
    email_sent_at TIMESTAMPTZ,
    
    -- Expiration
    expires_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    read_at TIMESTAMPTZ,
    dismissed_at TIMESTAMPTZ
);

-- Indexes for performance (critical for notifications)
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at DESC);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_category ON public.notifications(category);
CREATE INDEX idx_notifications_group_key ON public.notifications(group_key);

-- Composite indexes for user notification queries
CREATE INDEX idx_notifications_user_unread ON public.notifications(user_id, is_read, created_at DESC) WHERE is_read = false;
CREATE INDEX idx_notifications_user_category ON public.notifications(user_id, category, created_at DESC);

-- Partial index for active notifications (removed NOW() function to make it IMMUTABLE)
CREATE INDEX idx_notifications_active ON public.notifications(user_id, created_at DESC)
WHERE is_dismissed = false;

-- =====================================================
-- RLS POLICIES FOR REVIEWS AND NOTIFICATIONS
-- =====================================================

-- REVIEWS TABLE SECURITY
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

-- Anyone can view approved reviews
CREATE POLICY "reviews_select_policy" ON public.reviews
FOR SELECT USING (
    is_approved = true OR
    reviewer_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.shops s 
        WHERE s.id = shop_id AND s.owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only verified purchasers can create reviews
CREATE POLICY "reviews_insert_policy" ON public.reviews
FOR INSERT WITH CHECK (
    auth.uid() = reviewer_id AND
    auth.uid() IS NOT NULL AND
    -- Must have purchased from the shop (if order_id provided)
    (order_id IS NULL OR EXISTS (
        SELECT 1 FROM public.orders o 
        WHERE o.id = order_id AND o.buyer_id = auth.uid() AND o.status = 'delivered'
    ))
);

-- Reviewers can update their own reviews, shop owners can respond
CREATE POLICY "reviews_update_policy" ON public.reviews
FOR UPDATE USING (
    reviewer_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.shops s 
        WHERE s.id = shop_id AND s.owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only moderators can delete reviews
CREATE POLICY "reviews_delete_policy" ON public.reviews
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- REVIEW HELPFULNESS SECURITY
ALTER TABLE public.review_helpfulness ENABLE ROW LEVEL SECURITY;

-- Users can view helpfulness votes on reviews they can see
CREATE POLICY "review_helpfulness_select_policy" ON public.review_helpfulness
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.reviews r 
        WHERE r.id = review_id AND r.is_approved = true
    )
);

-- Authenticated users can vote on review helpfulness
CREATE POLICY "review_helpfulness_insert_policy" ON public.review_helpfulness
FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
);

-- Users can update their own votes
CREATE POLICY "review_helpfulness_update_policy" ON public.review_helpfulness
FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own votes
CREATE POLICY "review_helpfulness_delete_policy" ON public.review_helpfulness
FOR DELETE USING (user_id = auth.uid());

-- NOTIFICATIONS TABLE SECURITY
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Users can only see their own notifications
CREATE POLICY "notifications_select_policy" ON public.notifications
FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- System can create notifications
CREATE POLICY "notifications_insert_policy" ON public.notifications
FOR INSERT WITH CHECK (true); -- Handled by functions with SECURITY DEFINER

-- Users can update their own notifications (mark as read, etc.)
CREATE POLICY "notifications_update_policy" ON public.notifications
FOR UPDATE USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- Users can delete their own notifications
CREATE POLICY "notifications_delete_policy" ON public.notifications
FOR DELETE USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- =====================================================
-- REVIEW SYSTEM FUNCTIONS
-- =====================================================

-- Function to create a review
CREATE OR REPLACE FUNCTION public.create_review(
    p_reviewer_id UUID,
    p_shop_id UUID,
    p_rating INTEGER,
    p_product_id UUID DEFAULT NULL,
    p_order_id UUID DEFAULT NULL,
    p_title VARCHAR(200) DEFAULT NULL,
    p_review_text TEXT DEFAULT NULL,
    p_images TEXT[] DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    review_id UUID;
    is_verified BOOLEAN := FALSE;
BEGIN
    -- Check if this is a verified purchase
    IF p_order_id IS NOT NULL THEN
        SELECT EXISTS (
            SELECT 1 FROM public.orders o 
            WHERE o.id = p_order_id 
            AND o.buyer_id = p_reviewer_id 
            AND o.status = 'delivered'
        ) INTO is_verified;
        
        IF NOT is_verified THEN
            RAISE EXCEPTION 'Can only review after order is delivered';
        END IF;
    END IF;
    
    -- Create the review
    INSERT INTO public.reviews (
        reviewer_id,
        shop_id,
        product_id,
        order_id,
        rating,
        title,
        review_text,
        images,
        is_verified_purchase
    ) VALUES (
        p_reviewer_id,
        p_shop_id,
        p_product_id,
        p_order_id,
        p_rating,
        p_title,
        p_review_text,
        p_images,
        is_verified
    ) RETURNING id INTO review_id;
    
    -- Update shop rating
    PERFORM public.calculate_shop_rating(p_shop_id);
    
    -- Create notification for shop owner
    PERFORM public.create_notification(
        (SELECT owner_id FROM public.shops WHERE id = p_shop_id),
        'review',
        'New Review',
        'You received a new ' || p_rating || '-star review',
        jsonb_build_object(
            'review_id', review_id,
            'rating', p_rating,
            'reviewer_id', p_reviewer_id
        )
    );
    
    RETURN review_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to vote on review helpfulness
CREATE OR REPLACE FUNCTION public.vote_review_helpfulness(
    p_review_id UUID,
    p_user_id UUID,
    p_is_helpful BOOLEAN
)
RETURNS BOOLEAN AS $$
DECLARE
    old_vote BOOLEAN;
BEGIN
    -- Get existing vote
    SELECT is_helpful INTO old_vote
    FROM public.review_helpfulness
    WHERE review_id = p_review_id AND user_id = p_user_id;

    -- Insert or update vote
    INSERT INTO public.review_helpfulness (review_id, user_id, is_helpful)
    VALUES (p_review_id, p_user_id, p_is_helpful)
    ON CONFLICT (review_id, user_id)
    DO UPDATE SET is_helpful = p_is_helpful, created_at = NOW();

    -- Update review counters
    IF old_vote IS NULL THEN
        -- New vote
        IF p_is_helpful THEN
            UPDATE public.reviews SET helpful_count = helpful_count + 1 WHERE id = p_review_id;
        ELSE
            UPDATE public.reviews SET not_helpful_count = not_helpful_count + 1 WHERE id = p_review_id;
        END IF;
    ELSIF old_vote != p_is_helpful THEN
        -- Changed vote
        IF p_is_helpful THEN
            UPDATE public.reviews
            SET helpful_count = helpful_count + 1, not_helpful_count = GREATEST(not_helpful_count - 1, 0)
            WHERE id = p_review_id;
        ELSE
            UPDATE public.reviews
            SET helpful_count = GREATEST(helpful_count - 1, 0), not_helpful_count = not_helpful_count + 1
            WHERE id = p_review_id;
        END IF;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get shop reviews with pagination
CREATE OR REPLACE FUNCTION public.get_shop_reviews(
    p_shop_id UUID,
    p_rating_filter INTEGER DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    review_id UUID,
    reviewer_name VARCHAR(100),
    reviewer_avatar TEXT,
    rating INTEGER,
    title VARCHAR(200),
    review_text TEXT,
    images TEXT[],
    is_verified_purchase BOOLEAN,
    helpful_count INTEGER,
    not_helpful_count INTEGER,
    shop_response TEXT,
    shop_response_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    user_helpfulness_vote BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        r.id,
        CASE WHEN r.is_anonymous THEN 'Anonymous' ELSE p.full_name END,
        CASE WHEN r.is_anonymous THEN NULL ELSE p.avatar_url END,
        r.rating,
        r.title,
        r.review_text,
        r.images,
        r.is_verified_purchase,
        r.helpful_count,
        r.not_helpful_count,
        r.shop_response,
        r.shop_response_at,
        r.created_at,
        rh.is_helpful
    FROM public.reviews r
    JOIN public.profiles p ON r.reviewer_id = p.id
    LEFT JOIN public.review_helpfulness rh ON r.id = rh.review_id AND rh.user_id = auth.uid()
    WHERE
        r.shop_id = p_shop_id AND
        r.is_approved = true AND
        (p_rating_filter IS NULL OR r.rating = p_rating_filter)
    ORDER BY r.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- NOTIFICATION SYSTEM FUNCTIONS
-- =====================================================

-- Enhanced notification creation function (already exists, but updating)
CREATE OR REPLACE FUNCTION public.create_notification(
    recipient_id UUID,
    notification_type VARCHAR(50),
    title VARCHAR(200),
    message TEXT,
    data JSONB DEFAULT '{}',
    priority VARCHAR(10) DEFAULT 'normal',
    category VARCHAR(50) DEFAULT 'general',
    action_url TEXT DEFAULT NULL,
    group_key VARCHAR(100) DEFAULT NULL,
    expires_at TIMESTAMPTZ DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        data,
        priority,
        category,
        action_url,
        group_key,
        expires_at
    ) VALUES (
        recipient_id,
        notification_type,
        title,
        message,
        data,
        priority,
        category,
        action_url,
        group_key,
        expires_at
    ) RETURNING id INTO notification_id;

    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notifications as read
CREATE OR REPLACE FUNCTION public.mark_notifications_read(
    p_user_id UUID,
    p_notification_ids UUID[] DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    IF p_notification_ids IS NULL THEN
        -- Mark all unread notifications as read
        UPDATE public.notifications
        SET is_read = true, read_at = NOW()
        WHERE user_id = p_user_id AND is_read = false;
    ELSE
        -- Mark specific notifications as read
        UPDATE public.notifications
        SET is_read = true, read_at = NOW()
        WHERE user_id = p_user_id AND id = ANY(p_notification_ids) AND is_read = false;
    END IF;

    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user notifications
CREATE OR REPLACE FUNCTION public.get_user_notifications(
    p_user_id UUID,
    p_category VARCHAR(50) DEFAULT NULL,
    p_unread_only BOOLEAN DEFAULT FALSE,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    notification_id UUID,
    type VARCHAR(50),
    title VARCHAR(200),
    message TEXT,
    data JSONB,
    priority VARCHAR(10),
    category VARCHAR(50),
    action_url TEXT,
    is_read BOOLEAN,
    created_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        n.id,
        n.type,
        n.title,
        n.message,
        n.data,
        n.priority,
        n.category,
        n.action_url,
        n.is_read,
        n.created_at,
        n.read_at
    FROM public.notifications n
    WHERE
        n.user_id = p_user_id AND
        n.is_dismissed = false AND
        (n.expires_at IS NULL OR n.expires_at > NOW()) AND
        (p_category IS NULL OR n.category = p_category) AND
        (NOT p_unread_only OR n.is_read = false)
    ORDER BY
        CASE n.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'normal' THEN 3
            WHEN 'low' THEN 4
        END,
        n.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get notification counts
CREATE OR REPLACE FUNCTION public.get_notification_counts(p_user_id UUID)
RETURNS TABLE (
    total_unread INTEGER,
    social_unread INTEGER,
    commerce_unread INTEGER,
    system_unread INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_unread,
        COUNT(CASE WHEN category = 'social' THEN 1 END)::INTEGER as social_unread,
        COUNT(CASE WHEN category = 'commerce' THEN 1 END)::INTEGER as commerce_unread,
        COUNT(CASE WHEN category = 'system' THEN 1 END)::INTEGER as system_unread
    FROM public.notifications
    WHERE
        user_id = p_user_id AND
        is_read = false AND
        is_dismissed = false AND
        (expires_at IS NULL OR expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- TRIGGERS FOR REVIEWS AND NOTIFICATIONS
-- =====================================================

-- Trigger to update review helpfulness counters
CREATE OR REPLACE FUNCTION public.update_review_helpfulness_counters()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.is_helpful THEN
            UPDATE public.reviews SET helpful_count = helpful_count + 1 WHERE id = NEW.review_id;
        ELSE
            UPDATE public.reviews SET not_helpful_count = not_helpful_count + 1 WHERE id = NEW.review_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.is_helpful != NEW.is_helpful THEN
            IF NEW.is_helpful THEN
                UPDATE public.reviews
                SET helpful_count = helpful_count + 1, not_helpful_count = GREATEST(not_helpful_count - 1, 0)
                WHERE id = NEW.review_id;
            ELSE
                UPDATE public.reviews
                SET helpful_count = GREATEST(helpful_count - 1, 0), not_helpful_count = not_helpful_count + 1
                WHERE id = NEW.review_id;
            END IF;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.is_helpful THEN
            UPDATE public.reviews SET helpful_count = GREATEST(helpful_count - 1, 0) WHERE id = OLD.review_id;
        ELSE
            UPDATE public.reviews SET not_helpful_count = GREATEST(not_helpful_count - 1, 0) WHERE id = OLD.review_id;
        END IF;
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER review_helpfulness_counter_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.review_helpfulness
    FOR EACH ROW EXECUTE FUNCTION public.update_review_helpfulness_counters();

-- Trigger to update shop stats when reviews are added/updated
CREATE TRIGGER reviews_shop_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.reviews
    FOR EACH ROW EXECUTE FUNCTION public.update_shop_stats();

-- Trigger to update timestamps
CREATE TRIGGER update_reviews_updated_at
    BEFORE UPDATE ON public.reviews
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions on review and notification tables
GRANT SELECT, INSERT, UPDATE ON public.reviews TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.review_helpfulness TO authenticated;
GRANT SELECT, UPDATE, DELETE ON public.notifications TO authenticated;
GRANT INSERT ON public.notifications TO service_role; -- For system notifications
