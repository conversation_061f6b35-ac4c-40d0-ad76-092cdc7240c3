import 'package:flutter_test/flutter_test.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/models/auth/user_role.dart';

void main() {
  group('AuthState Google Sign-In Tests', () {
    test('googleUserNeedsOnboarding creates correct state', () {
      // Create initial state
      const initialState = AuthState(
        status: AuthStatus.initial,
        isLoading: false,
      );

      // Test googleUserNeedsOnboarding method
      final newUserState = initialState.googleUserNeedsOnboarding(
        userId: 'test-user-id',
        userName: 'Test User',
        userEmail: '<EMAIL>',
        userProfileImage: 'https://example.com/avatar.jpg',
        accessToken: 'test-access-token',
      );

      // Verify the state is correct
      expect(newUserState.status, AuthStatus.unauthenticated);
      expect(newUserState.signupStep, SignupStep.categorySelection);
      expect(newUserState.isGoogleSignup, true);
      expect(newUserState.userId, 'test-user-id');
      expect(newUserState.userName, 'Test User');
      expect(newUserState.userEmail, '<EMAIL>');
      expect(newUserState.userProfileImage, 'https://example.com/avatar.jpg');
      expect(newUserState.accessToken, 'test-access-token');
      expect(newUserState.isLoading, false);
      expect(newUserState.errorMessage, null);

      print('✅ Google user onboarding state test passed');
      print('📋 State details:');
      print('   - Status: ${newUserState.status}');
      print('   - SignupStep: ${newUserState.signupStep}');
      print('   - IsGoogleSignup: ${newUserState.isGoogleSignup}');
      print('   - UserId: ${newUserState.userId}');
    });

    test(
      'AuthWrapper condition should match googleUserNeedsOnboarding state',
      () {
        // Create the exact state that googleUserNeedsOnboarding produces
        const initialState = AuthState(
          status: AuthStatus.initial,
          isLoading: false,
        );

        final googleUserState = initialState.googleUserNeedsOnboarding(
          userId: 'test-user-id',
          userName: 'Test User',
          userEmail: '<EMAIL>',
        );

        // Test the AuthWrapper condition
        final shouldShowCategorySelection =
            googleUserState.status == AuthStatus.unauthenticated &&
            googleUserState.signupStep == SignupStep.categorySelection &&
            googleUserState.isGoogleSignup == true;

        expect(shouldShowCategorySelection, true);
        print('✅ AuthWrapper condition test passed');
        print('📋 Condition check: $shouldShowCategorySelection');
      },
    );

    test('authenticated state for existing Google user', () {
      const initialState = AuthState(
        status: AuthStatus.initial,
        isLoading: false,
      );

      final authenticatedState = initialState.authenticated(
        userId: 'existing-user-id',
        userName: 'Existing User',
        userEmail: '<EMAIL>',
        userPhone: '+1234567890',
        userProfileImage: 'https://example.com/existing-avatar.jpg',
        userBio: 'Existing user bio',
        accessToken: 'existing-access-token',
        userRole: UserRole.user,
      );

      expect(authenticatedState.status, AuthStatus.authenticated);
      expect(authenticatedState.signupStep, null);
      expect(authenticatedState.userId, 'existing-user-id');
      expect(authenticatedState.userName, 'Existing User');
      expect(authenticatedState.userEmail, '<EMAIL>');

      print('✅ Existing user authentication state test passed');
      print('📋 State details:');
      print('   - Status: ${authenticatedState.status}');
      print('   - SignupStep: ${authenticatedState.signupStep}');
      print('   - UserId: ${authenticatedState.userId}');
    });

    test('googleSignInCompleted creates correct state for popup', () {
      // Create initial state
      const initialState = AuthState(
        status: AuthStatus.initial,
        isLoading: false,
      );

      // Test googleSignInCompleted method
      final completedState = initialState.googleSignInCompleted(
        userId: 'test-user-id',
        userName: 'Test User',
        userEmail: '<EMAIL>',
        userProfileImage: 'https://example.com/avatar.jpg',
        accessToken: 'test-access-token',
      );

      // Verify the state is correct for showing popup
      expect(completedState.status, AuthStatus.unauthenticated);
      expect(completedState.signupStep, null); // No signup step yet
      expect(completedState.isGoogleSignup, true);
      expect(completedState.userId, 'test-user-id');
      expect(completedState.userName, 'Test User');

      // Test AuthWrapper condition for showing popup
      final shouldShowPopup =
          completedState.status == AuthStatus.unauthenticated &&
          completedState.isGoogleSignup == true &&
          completedState.signupStep == null &&
          completedState.userId != null;

      expect(shouldShowPopup, true);
      print('✅ Google sign-in completion popup test passed');
      print('📋 Should show popup: $shouldShowPopup');
    });
  });
}
