import 'package:equatable/equatable.dart';

enum SearchStatus { initial, loading, loaded, error }

class SearchResult extends Equatable {
  final String id;
  final String title;
  final String subtitle;
  final String imageUrl;
  final String type; // 'user', 'post', 'product', 'business'

  const SearchResult({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.type,
  });

  @override
  List<Object> get props => [id, title, subtitle, imageUrl, type];
}

class SearchState extends Equatable {
  final SearchStatus status;
  final String query;
  final List<SearchResult> results;
  final List<String> searchHistory;
  final List<String> trendingSearches;
  final List<String> suggestions;
  final List<String> activeFilters;
  final String? errorMessage;

  const SearchState({
    this.status = SearchStatus.initial,
    this.query = '',
    this.results = const [],
    this.searchHistory = const [],
    this.trendingSearches = const [],
    this.suggestions = const [],
    this.activeFilters = const [],
    this.errorMessage,
  });

  SearchState copyWith({
    SearchStatus? status,
    String? query,
    List<SearchResult>? results,
    List<String>? searchHistory,
    List<String>? trendingSearches,
    List<String>? suggestions,
    List<String>? activeFilters,
    String? errorMessage,
  }) {
    return SearchState(
      status: status ?? this.status,
      query: query ?? this.query,
      results: results ?? this.results,
      searchHistory: searchHistory ?? this.searchHistory,
      trendingSearches: trendingSearches ?? this.trendingSearches,
      suggestions: suggestions ?? this.suggestions,
      activeFilters: activeFilters ?? this.activeFilters,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  bool get hasQuery => query.isNotEmpty;
  bool get hasResults => results.isNotEmpty;
  bool get hasHistory => searchHistory.isNotEmpty;

  @override
  List<Object?> get props => [
        status,
        query,
        results,
        searchHistory,
        trendingSearches,
        suggestions,
        activeFilters,
        errorMessage,
      ];
}
