import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

/// <PERSON>ript to apply the infinite recursion fix migration
/// Run this once to fix the RLS policy issue
void main() async {
  // Initialize logger
  final logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://xvnzbkllxdssgdpwwnjl.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2bnpia2xseGRzc2dkcHd3bmpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3ODUzNDYsImV4cCI6MjA2ODM2MTM0Nn0.P4fp9oE5RBTAq-iUghLmznI6B8o41Qy8DOnjPGqY0LY',
  );

  final client = Supabase.instance.client;

  try {
    logger.i('🔧 Applying infinite recursion fix migration...');

    // Read the migration file
    final migrationFile = File(
      'supabase/migrations/012_fix_infinite_recursion.sql',
    );
    final migrationSql = await migrationFile.readAsString();

    // Split the migration into individual statements
    final statements =
        migrationSql
            .split(';')
            .where(
              (stmt) => stmt.trim().isNotEmpty && !stmt.trim().startsWith('--'),
            )
            .toList();

    // Execute each statement
    for (int i = 0; i < statements.length; i++) {
      final statement = statements[i].trim();
      if (statement.isEmpty) continue;

      logger.i('📝 Executing statement ${i + 1}/${statements.length}...');

      try {
        await client.rpc('exec_sql', params: {'sql': statement});
        logger.i('✅ Statement ${i + 1} executed successfully');
      } catch (e) {
        logger.e('❌ Error executing statement ${i + 1}: $e');
        logger.d('Statement: $statement');
        // Continue with other statements
      }
    }

    logger.i('🎉 Migration completed!');
    logger.i('🔍 Testing profile access...');

    // Test the fix by trying to access profiles
    final profiles = await client
        .from('profiles')
        .select('id, username, is_private')
        .limit(5);

    logger.i(
      '✅ Profile access test successful! Found ${profiles.length} profiles',
    );
  } catch (e) {
    logger.e('❌ Migration failed: $e');
  }

  exit(0);
}
