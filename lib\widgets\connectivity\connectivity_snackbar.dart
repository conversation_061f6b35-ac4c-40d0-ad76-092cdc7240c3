import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:business_app/bloc/connectivity_bloc/connectivity_bloc.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Professional connectivity SnackBar (Facebook/WhatsApp style)
class ConnectivitySnackBar {
  /// Create offline SnackBar
  static SnackBar offline({
    required ConnectivityState state,
    VoidCallback? onRetry,
    VoidCallback? onOpenSettings,
    VoidCallback? onDismiss,
  }) {
    return SnackBar(
      content: _OfflineSnackBarContent(
        state: state,
        onRetry: onRetry,
        onOpenSettings: onOpenSettings,
      ),
      backgroundColor: Colors.grey[900],
      behavior: SnackBarBehavior.floating,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      duration: const Duration(days: 1), // Persistent until dismissed
      action: SnackBarAction(
        label: 'Dismiss',
        textColor: Colors.blue[300],
        onPressed: () {
          onDismiss?.call();
        },
      ),
    );
  }

  /// Create connection restored SnackBar
  static SnackBar connected({VoidCallback? onDismiss}) {
    return SnackBar(
      content: const _ConnectedSnackBarContent(),
      backgroundColor: Colors.green[700],
      behavior: SnackBarBehavior.floating,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      duration: const Duration(milliseconds: 2000),
    );
  }

  /// Create retry SnackBar
  static SnackBar retrying() {
    return SnackBar(
      content: const _RetryingSnackBarContent(),
      backgroundColor: Colors.orange[700],
      behavior: SnackBarBehavior.floating,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      duration: const Duration(seconds: 3),
    );
  }
}

/// Offline SnackBar content widget
class _OfflineSnackBarContent extends StatelessWidget {
  final ConnectivityState state;
  final VoidCallback? onRetry;
  final VoidCallback? onOpenSettings;

  const _OfflineSnackBarContent({
    required this.state,
    this.onRetry,
    this.onOpenSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(LucideIcons.wifiOff, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _getOfflineMessage(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        if (state.hasLimitedConnection) ...[
          const SizedBox(height: 8),
          Text(
            'Connected to ${state.connectionTypeString} but no internet access',
            style: TextStyle(color: Colors.grey[300], fontSize: 12),
          ),
        ],
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            _buildActionButton('Retry', onRetry, Colors.blue[300]!),
            const SizedBox(width: 16),
            _buildActionButton(
              _getSettingsButtonText(),
              onOpenSettings,
              Colors.blue[300]!,
            ),
          ],
        ),
      ],
    );
  }

  String _getOfflineMessage() {
    if (state.hasLimitedConnection) {
      return 'No internet connection';
    }
    return 'You are currently offline';
  }

  String _getSettingsButtonText() {
    switch (state.connectivityResult) {
      case ConnectivityResult.wifi:
        return 'WiFi Settings';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      default:
        return 'Settings';
    }
  }

  Widget _buildActionButton(String text, VoidCallback? onPressed, Color color) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: color.withOpacity(0.5)),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}

/// Connected SnackBar content widget
class _ConnectedSnackBarContent extends StatelessWidget {
  const _ConnectedSnackBarContent();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(LucideIcons.wifi, color: Colors.white, size: 20),
        const SizedBox(width: 12),
        const Expanded(
          child: Text(
            'Connection restored',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ),
        Icon(LucideIcons.check, color: Colors.white, size: 20),
      ],
    );
  }
}

/// Retrying SnackBar content widget
class _RetryingSnackBarContent extends StatelessWidget {
  const _RetryingSnackBarContent();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: Text(
            'Checking connection...',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }
}
