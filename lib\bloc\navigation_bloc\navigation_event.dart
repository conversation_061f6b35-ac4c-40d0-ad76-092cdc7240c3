import 'package:equatable/equatable.dart';

abstract class NavigationEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class NavigateToPageEvent extends NavigationEvent {
  final int pageIndex;

  NavigateToPageEvent(this.pageIndex);

  @override
  List<Object> get props => [pageIndex];
}

class ToggleBottomBarVisibilityEvent extends NavigationEvent {
  final bool isVisible;

  ToggleBottomBarVisibilityEvent(this.isVisible);

  @override
  List<Object> get props => [isVisible];
}

class ToggleDrawerEvent extends NavigationEvent {}

class OpenDrawerEvent extends NavigationEvent {}

class CloseDrawerEvent extends NavigationEvent {}

class SetTabIndexEvent extends NavigationEvent {
  final int tabIndex;

  SetTabIndexEvent(this.tabIndex);

  @override
  List<Object> get props => [tabIndex];
}
