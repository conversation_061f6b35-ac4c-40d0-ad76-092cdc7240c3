import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';

class SignupPasswordPage extends StatefulWidget {
  const SignupPasswordPage({super.key});

  @override
  State<SignupPasswordPage> createState() => _SignupPasswordPageState();
}

class _SignupPasswordPageState extends State<SignupPasswordPage> {
  final _passwordController = TextEditingController();
  String? _passwordError;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  void _onNext() {
    final password = _passwordController.text;

    if (password.length < 8) {
      setState(() {
        _passwordError = 'Password must be at least 8 characters';
      });
      return;
    }

    if (!_hasLetter(password)) {
      setState(() {
        _passwordError = 'Password must contain at least one letter';
      });
      return;
    }

    if (!_hasNumber(password)) {
      setState(() {
        _passwordError = 'Password must contain at least one number';
      });
      return;
    }

    context.read<AuthBloc>().add(SignupPasswordCreated(password: password));
  }

  bool _hasLetter(String password) {
    return RegExp(r'[a-zA-Z]').hasMatch(password);
  }

  bool _hasNumber(String password) {
    return RegExp(r'[0-9]').hasMatch(password);
  }

  bool _hasSpecialChar(String password) {
    return RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password);
  }

  PasswordStrength _getPasswordStrength(String password) {
    if (password.length < 8) return PasswordStrength.weak;

    int score = 0;
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (_hasLetter(password)) score++;
    if (_hasNumber(password)) score++;
    if (_hasSpecialChar(password)) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[a-z]').hasMatch(password)) score++;

    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }

  Color _getStrengthColor(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.medium:
        return Colors.orange;
      case PasswordStrength.strong:
        return Colors.green;
    }
  }

  String _getStrengthText(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.medium:
        return 'Medium';
      case PasswordStrength.strong:
        return 'Strong';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final password = _passwordController.text;
    final strength =
        password.isNotEmpty ? _getPasswordStrength(password) : null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: IntrinsicHeight(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),

                    // Header with progress
                    const Column(
                      children: [
                        AuthProgressIndicator(currentStep: 2, totalSteps: 5),
                        SizedBox(height: 20),
                        AuthPasswordHeader(),
                      ],
                    ),

                    const SizedBox(height: 40),

                    // Password field
                    AuthPasswordField(
                      controller: _passwordController,
                      label: 'Password',
                      hintText: 'Enter your password',
                      errorText: _passwordError,
                      autofocus: true,
                      onChanged: (value) {
                        setState(() {
                          _passwordError = null;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Password strength indicator
                    if (password.isNotEmpty && strength != null) ...[
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 4,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(2),
                              ),
                              child: FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor:
                                    strength == PasswordStrength.weak
                                        ? 0.33
                                        : strength == PasswordStrength.medium
                                        ? 0.66
                                        : 1.0,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: _getStrengthColor(strength),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            _getStrengthText(strength),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: _getStrengthColor(strength),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Password requirements
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Password requirements:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          _buildRequirement(
                            'At least 8 characters',
                            password.length >= 8,
                          ),
                          _buildRequirement(
                            'Contains a letter',
                            _hasLetter(password),
                          ),
                          _buildRequirement(
                            'Contains a number',
                            _hasNumber(password),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(),

                    // Next button
                    BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        return AuthPrimaryButton(
                          text: 'Next',
                          isLoading: state.isLoading,
                          onPressed: _onNext,
                        );
                      },
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRequirement(String text, bool isMet) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isMet ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 16,
            color: isMet ? Colors.green : Colors.grey[400],
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 13,
              color: isMet ? Colors.green : Colors.grey[600],
              fontWeight: isMet ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}

enum PasswordStrength { weak, medium, strong }
