import 'package:flutter_bloc/flutter_bloc.dart';
import 'new_message_event.dart';
import 'new_message_state.dart';

class NewMessageBloc extends Bloc<NewMessageEvent, NewMessageState> {
  NewMessageBloc() : super(const NewMessageState()) {
    on<LoadContactsEvent>(_onLoadContacts);
    on<LoadSuggestedContactsEvent>(_onLoadSuggestedContacts);
    on<LoadRecentContactsEvent>(_onLoadRecentContacts);
    on<SearchContactsEvent>(_onSearchContacts);
    on<ClearSearchEvent>(_onClearSearch);
    on<ToggleContactSelectionEvent>(_onToggleContactSelection);
    on<ChangePageEvent>(_onChangePage);
    on<StartConversationWithContactEvent>(_onStartConversationWithContact);
    on<RefreshContactsEvent>(_onRefreshContacts);
    on<LoadMoreContactsEvent>(_onLoadMoreContacts);
  }

  Future<void> _onLoadContacts(
    LoadContactsEvent event,
    Emitter<NewMessageState> emit,
  ) async {
    emit(state.copyWith(status: NewMessageStatus.loading));
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final contacts = _generateMockContacts();
      
      emit(state.copyWith(
        status: NewMessageStatus.loaded,
        contacts: contacts,
        filteredContacts: contacts,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: NewMessageStatus.error,
        errorMessage: 'Failed to load contacts: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadSuggestedContacts(
    LoadSuggestedContactsEvent event,
    Emitter<NewMessageState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      final suggestedContacts = _generateMockSuggestedContacts();
      
      emit(state.copyWith(suggestedContacts: suggestedContacts));
    } catch (e) {
      emit(state.copyWith(
        status: NewMessageStatus.error,
        errorMessage: 'Failed to load suggested contacts: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadRecentContacts(
    LoadRecentContactsEvent event,
    Emitter<NewMessageState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      
      final recentContacts = _generateMockRecentContacts();
      
      emit(state.copyWith(recentContacts: recentContacts));
    } catch (e) {
      emit(state.copyWith(
        status: NewMessageStatus.error,
        errorMessage: 'Failed to load recent contacts: ${e.toString()}',
      ));
    }
  }

  void _onSearchContacts(
    SearchContactsEvent event,
    Emitter<NewMessageState> emit,
  ) {
    emit(state.copyWith(
      searchQuery: event.query,
      isSearching: event.query.isNotEmpty,
      status: NewMessageStatus.searching,
    ));

    if (event.query.isEmpty) {
      emit(state.copyWith(
        filteredContacts: state.contacts,
        status: NewMessageStatus.loaded,
        isSearching: false,
      ));
      return;
    }

    final filteredContacts = state.contacts.where((contact) {
      return contact.name.toLowerCase().contains(event.query.toLowerCase()) ||
          contact.phoneNumber.contains(event.query);
    }).toList();

    emit(state.copyWith(
      filteredContacts: filteredContacts,
      status: NewMessageStatus.loaded,
    ));
  }

  void _onClearSearch(
    ClearSearchEvent event,
    Emitter<NewMessageState> emit,
  ) {
    emit(state.copyWith(
      searchQuery: '',
      filteredContacts: state.contacts,
      isSearching: false,
      status: NewMessageStatus.loaded,
    ));
  }

  void _onToggleContactSelection(
    ToggleContactSelectionEvent event,
    Emitter<NewMessageState> emit,
  ) {
    final selectedIds = Set<String>.from(state.selectedContactIds);
    
    if (selectedIds.contains(event.contactId)) {
      selectedIds.remove(event.contactId);
    } else {
      selectedIds.add(event.contactId);
    }

    emit(state.copyWith(selectedContactIds: selectedIds));
  }

  void _onChangePage(
    ChangePageEvent event,
    Emitter<NewMessageState> emit,
  ) {
    emit(state.copyWith(currentPageIndex: event.pageIndex));
  }

  void _onStartConversationWithContact(
    StartConversationWithContactEvent event,
    Emitter<NewMessageState> emit,
  ) {
    // This event can be handled by the parent widget to navigate to chat
    // or trigger the ChatBloc to start a new conversation
  }

  Future<void> _onRefreshContacts(
    RefreshContactsEvent event,
    Emitter<NewMessageState> emit,
  ) async {
    emit(state.copyWith(status: NewMessageStatus.loading));
    
    try {
      await Future.delayed(const Duration(milliseconds: 800));
      
      final contacts = _generateMockContacts();
      final suggestedContacts = _generateMockSuggestedContacts();
      final recentContacts = _generateMockRecentContacts();
      
      emit(state.copyWith(
        status: NewMessageStatus.loaded,
        contacts: contacts,
        suggestedContacts: suggestedContacts,
        recentContacts: recentContacts,
        filteredContacts: state.searchQuery.isEmpty ? contacts : 
          contacts.where((contact) {
            return contact.name.toLowerCase().contains(state.searchQuery.toLowerCase()) ||
                contact.phoneNumber.contains(state.searchQuery);
          }).toList(),
      ));
    } catch (e) {
      emit(state.copyWith(
        status: NewMessageStatus.error,
        errorMessage: 'Failed to refresh contacts: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadMoreContacts(
    LoadMoreContactsEvent event,
    Emitter<NewMessageState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final moreContacts = _generateMockContacts(startIndex: state.contacts.length);
      final updatedContacts = [...state.contacts, ...moreContacts];
      
      emit(state.copyWith(
        contacts: updatedContacts,
        filteredContacts: state.searchQuery.isEmpty ? updatedContacts :
          updatedContacts.where((contact) {
            return contact.name.toLowerCase().contains(state.searchQuery.toLowerCase()) ||
                contact.phoneNumber.contains(state.searchQuery);
          }).toList(),
      ));
    } catch (e) {
      emit(state.copyWith(
        status: NewMessageStatus.error,
        errorMessage: 'Failed to load more contacts: ${e.toString()}',
      ));
    }
  }

  List<Contact> _generateMockContacts({int startIndex = 0}) {
    final names = [
      'Secunda', 'Soft', 'Greenland', 'Tell Me Why', 'AKAI', 'Solitude', 
      'What It Feels Like', 'Take Me To The Sun', 'Jeremy Soule', 'Nasty C'
    ];
    
    return List.generate(names.length, (index) {
      final actualIndex = startIndex + index;
      return Contact(
        id: 'contact_$actualIndex',
        name: names[index % names.length],
        phoneNumber: '098467167${actualIndex.toString().padLeft(2, '0')}',
        isOnline: actualIndex % 3 == 0,
        lastSeen: DateTime.now().subtract(Duration(minutes: actualIndex * 10)),
      );
    });
  }

  List<Contact> _generateMockSuggestedContacts() {
    return [
      const Contact(
        id: 'suggested_1',
        name: 'Person A',
        phoneNumber: '0984671670',
        isOnline: true,
      ),
      const Contact(
        id: 'suggested_2',
        name: 'Person B',
        phoneNumber: '0984671671',
        isOnline: false,
      ),
      const Contact(
        id: 'suggested_3',
        name: 'Person C',
        phoneNumber: '0984671672',
        isOnline: true,
      ),
      const Contact(
        id: 'suggested_4',
        name: 'Person D',
        phoneNumber: '0984671673',
        isOnline: false,
      ),
    ];
  }

  List<Contact> _generateMockRecentContacts() {
    return [
      Contact(
        id: 'recent_1',
        name: 'Recent 1',
        phoneNumber: '0984671680',
        isOnline: true,
        lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      Contact(
        id: 'recent_2',
        name: 'Recent 2',
        phoneNumber: '0984671681',
        isOnline: false,
        lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      Contact(
        id: 'recent_3',
        name: 'Recent 3',
        phoneNumber: '0984671682',
        isOnline: true,
        lastSeen: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
    ];
  }
}
