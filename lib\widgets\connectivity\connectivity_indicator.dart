import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:business_app/bloc/connectivity_bloc/connectivity_bloc.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Professional connectivity status indicator
/// Can be used in AppBars, status bars, or anywhere in the UI
class ConnectivityIndicator extends StatelessWidget {
  final bool showText;
  final bool showIcon;
  final double iconSize;
  final TextStyle? textStyle;
  final Color? activeColor;
  final Color? inactiveColor;

  const ConnectivityIndicator({
    super.key,
    this.showText = true,
    this.showIcon = true,
    this.iconSize = 16,
    this.textStyle,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultActiveColor = activeColor ?? Colors.green;
    final defaultInactiveColor = inactiveColor ?? Colors.red;

    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, state) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showIcon) ...[
                Icon(
                  _getIcon(state),
                  size: iconSize,
                  color: _getColor(
                    state,
                    defaultActiveColor,
                    defaultInactiveColor,
                  ),
                ),
                if (showText) const SizedBox(width: 4),
              ],
              if (showText)
                Text(
                  _getText(state),
                  style:
                      textStyle ??
                      theme.textTheme.bodySmall?.copyWith(
                        color: _getColor(
                          state,
                          defaultActiveColor,
                          defaultInactiveColor,
                        ),
                        fontWeight: FontWeight.w500,
                      ),
                ),
            ],
          ),
        );
      },
    );
  }

  IconData _getIcon(ConnectivityState state) {
    if (state.isRetrying) return LucideIcons.refreshCw;
    if (state.isConnected) {
      switch (state.connectivityResult) {
        case ConnectivityResult.wifi:
          return LucideIcons.wifi;
        case ConnectivityResult.mobile:
          return LucideIcons.smartphone;
        case ConnectivityResult.ethernet:
          return LucideIcons.globe;
        default:
          return LucideIcons.wifi;
      }
    }
    if (state.hasLimitedConnection) return LucideIcons.wifiOff;
    return LucideIcons.wifiOff;
  }

  Color _getColor(
    ConnectivityState state,
    Color activeColor,
    Color inactiveColor,
  ) {
    if (state.isConnected) return activeColor;
    if (state.isRetrying) return Colors.orange;
    if (state.hasLimitedConnection) return Colors.amber;
    return inactiveColor;
  }

  String _getText(ConnectivityState state) {
    if (state.isRetrying) return 'Checking...';
    if (state.isConnected) return state.connectionTypeString;
    if (state.hasLimitedConnection) return 'Limited';
    return 'Offline';
  }
}

/// Compact connectivity indicator for AppBars
class CompactConnectivityIndicator extends StatelessWidget {
  const CompactConnectivityIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return const ConnectivityIndicator(showText: false, iconSize: 18);
  }
}

/// Detailed connectivity indicator with status text
class DetailedConnectivityIndicator extends StatelessWidget {
  const DetailedConnectivityIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return const ConnectivityIndicator(
      showText: true,
      showIcon: true,
      iconSize: 16,
    );
  }
}

/// Connectivity indicator for status bars
class StatusBarConnectivityIndicator extends StatelessWidget {
  const StatusBarConnectivityIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, state) {
        if (state.isConnected) return const SizedBox.shrink();

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 4),
          color: _getStatusBarColor(state),
          child: Center(
            child: Text(
              _getStatusBarText(state),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getStatusBarColor(ConnectivityState state) {
    if (state.isRetrying) return Colors.orange;
    if (state.hasLimitedConnection) return Colors.amber[700]!;
    return Colors.red[700]!;
  }

  String _getStatusBarText(ConnectivityState state) {
    if (state.isRetrying) return 'Checking connection...';
    if (state.hasLimitedConnection) return 'No internet access';
    return 'No connection';
  }
}

/// Animated connectivity dot indicator
class ConnectivityDotIndicator extends StatelessWidget {
  final double size;

  const ConnectivityDotIndicator({super.key, this.size = 8});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, state) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _getDotColor(state),
          ),
          child:
              state.isRetrying
                  ? SizedBox(
                    width: size,
                    height: size,
                    child: CircularProgressIndicator(
                      strokeWidth: 1,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : null,
        );
      },
    );
  }

  Color _getDotColor(ConnectivityState state) {
    if (state.isConnected) return Colors.green;
    if (state.isRetrying) return Colors.orange;
    if (state.hasLimitedConnection) return Colors.amber;
    return Colors.red;
  }
}
