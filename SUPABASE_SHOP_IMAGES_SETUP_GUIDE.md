# Supabase Shop Images Storage Setup Guide

This guide will help you set up the storage buckets and policies for shop logo and banner images in your Supabase project.

## Prerequisites

1. You have a Supabase project set up
2. You have access to the Supabase dashboard
3. You have the SQL Editor access in your Supabase project

## Step 1: Create Storage Buckets

Go to your Supabase dashboard → Storage → Create new bucket

### Create Shop Logo Bucket
1. Bucket name: `shop-logo`
2. Public bucket: ✅ (checked)
3. File size limit: 3MB
4. Allowed MIME types: `image/jpeg,image/png,image/webp`

### Create Shop Banner Bucket
1. Bucket name: `shop-banner`
2. Public bucket: ✅ (checked)
3. File size limit: 8MB
4. Allowed MIME types: `image/jpeg,image/png,image/webp`

## Step 2: Create Shop Logo Storage Policies

Run these SQL commands in your Supabase SQL Editor:

```sql
-- Policy: Allow authenticated users to upload shop logos to their own folder
CREATE POLICY "Users can upload shop logos to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own shop logos
CREATE POLICY "Users can view own shop logos" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to shop logos (for viewing shops)
CREATE POLICY "Public can view shop logos" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'shop-logo');

-- Policy: Allow users to update their own shop logos
CREATE POLICY "Users can update own shop logos" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own shop logos
CREATE POLICY "Users can delete own shop logos" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
```

## Step 3: Create Shop Banner Storage Policies

```sql
-- Policy: Allow authenticated users to upload shop banners to their own folder
CREATE POLICY "Users can upload shop banners to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own shop banners
CREATE POLICY "Users can view own shop banners" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to shop banners (for viewing shops)
CREATE POLICY "Public can view shop banners" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'shop-banner');

-- Policy: Allow users to update their own shop banners
CREATE POLICY "Users can update own shop banners" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own shop banners
CREATE POLICY "Users can delete own shop banners" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
```

## Step 4: Verify Setup

After running the SQL commands, verify that:

1. Both buckets (`shop-logo` and `shop-banner`) are created and public
2. All policies are created and active
3. Test upload functionality from your app

## File Structure

The uploaded files will be organized as:
```
shop-logo/
  ├── user_id_1/
  │   ├── shop_logo_shop_id_1_timestamp.jpg
  │   └── shop_logo_shop_id_2_timestamp.png
  └── user_id_2/
      └── shop_logo_shop_id_3_timestamp.webp

shop-banner/
  ├── user_id_1/
  │   ├── shop_banner_shop_id_1_timestamp.jpg
  │   └── shop_banner_shop_id_2_timestamp.png
  └── user_id_2/
      └── shop_banner_shop_id_3_timestamp.webp
```

## Security Notes

- Users can only upload/modify images in their own user folder
- Public read access allows anyone to view shop images (necessary for marketplace)
- File size limits prevent abuse
- MIME type restrictions ensure only images are uploaded
- Timestamp-based naming prevents conflicts and enables versioning

## Usage in App

After setup, you can use these methods in your Flutter app:

```dart
// Upload shop logo
final result = await ShopService.uploadAndUpdateShopLogo(
  shopId: 'shop-id',
  imageFile: File('path/to/logo.jpg'),
);

// Upload shop banner
final result = await ShopService.uploadAndUpdateShopBanner(
  shopId: 'shop-id', 
  imageFile: File('path/to/banner.jpg'),
);
```
