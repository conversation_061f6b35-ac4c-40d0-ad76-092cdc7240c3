import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/theme_bloc/theme_bloc.dart';
import 'package:business_app/bloc/theme_bloc/theme_event.dart';
import 'package:business_app/bloc/theme_bloc/theme_state.dart';
import 'package:business_app/app.dart';

void main() {
  group('Theme Performance Tests', () {
    testWidgets('Theme switching should be instant', (WidgetTester tester) async {
      // Create the app with ThemeBloc
      await tester.pumpWidget(
        BlocProvider<ThemeBloc>(
          create: (_) => ThemeBloc(),
          child: const MyApp(),
        ),
      );

      // Get the ThemeBloc
      final themeBloc = BlocProvider.of<ThemeBloc>(tester.element(find.byType(MyApp)));

      // Record start time
      final startTime = DateTime.now();

      // Switch to dark theme
      themeBloc.add(SetThemeModeEvent(AppThemeMode.dark));

      // Pump and settle to complete all animations
      await tester.pumpAndSettle();

      // Record end time
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      // Theme switch should complete within 200ms for good UX
      expect(duration.inMilliseconds, lessThan(200));

      // Verify theme actually changed
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.themeMode, ThemeMode.dark);
    });

    testWidgets('Multiple rapid theme switches should not cause issues', (WidgetTester tester) async {
      await tester.pumpWidget(
        BlocProvider<ThemeBloc>(
          create: (_) => ThemeBloc(),
          child: const MyApp(),
        ),
      );

      final themeBloc = BlocProvider.of<ThemeBloc>(tester.element(find.byType(MyApp)));

      // Rapidly switch themes multiple times
      for (int i = 0; i < 5; i++) {
        themeBloc.add(SetThemeModeEvent(AppThemeMode.dark));
        await tester.pump(const Duration(milliseconds: 10));
        
        themeBloc.add(SetThemeModeEvent(AppThemeMode.light));
        await tester.pump(const Duration(milliseconds: 10));
        
        themeBloc.add(SetThemeModeEvent(AppThemeMode.system));
        await tester.pump(const Duration(milliseconds: 10));
      }

      // Should complete without errors
      await tester.pumpAndSettle();

      // App should still be responsive
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    test('ThemeBloc should emit states immediately', () async {
      final themeBloc = ThemeBloc();
      final states = <ThemeState>[];

      // Listen to state changes
      themeBloc.stream.listen(states.add);

      final startTime = DateTime.now();
      
      // Trigger theme change
      themeBloc.add(SetThemeModeEvent(AppThemeMode.dark));

      // Wait a short time for state emission
      await Future.delayed(const Duration(milliseconds: 50));

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      // State should be emitted within 50ms
      expect(duration.inMilliseconds, lessThan(50));
      expect(states.isNotEmpty, true);
      expect(states.last.mode, AppThemeMode.dark);

      themeBloc.close();
    });
  });
}
