# Admin User Setup Guide

This guide will help you add your email `<EMAIL>` as an admin user in your business app.

## Prerequisites

1. **Create an account first**: Make sure you have created an account with the email `<EMAIL>` in your app
2. **Database access**: You need access to your Supabase project

## Method 1: Using the Flutter Script (Recommended)

1. **Run the admin setup script**:
   ```bash
   dart run add_admin_user.dart
   ```

2. **Follow the script output** - it will guide you through the process and verify the setup.

## Method 2: Using Supabase Dashboard (Easiest)

1. **Go to your Supabase Dashboard**:
   - Visit [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Select your project

2. **Navigate to Table Editor**:
   - Click on "Table Editor" in the left sidebar
   - Select the `profiles` table

3. **Find your user**:
   - Look for the row where `email = '<EMAIL>'`
   - If you don't see it, make sure you've created an account first

4. **Update the role**:
   - Click on the `role` field for your user
   - Change it from `user` to `super_admin`
   - Save the changes

## Method 3: Using SQL Query

1. **Go to SQL Editor** in your Supabase dashboard

2. **Run this query**:
   ```sql
   UPDATE public.profiles 
   SET role = 'super_admin', updated_at = NOW() 
   WHERE email = '<EMAIL>';
   ```

3. **Verify the update**:
   ```sql
   SELECT id, username, full_name, email, role, created_at 
   FROM public.profiles 
   WHERE email = '<EMAIL>';
   ```

## Method 4: Using Database Migration

1. **Apply the migration**:
   ```bash
   supabase db push
   ```
   This will apply the migration file `011_add_admin_user.sql` that automatically assigns your email as super admin.

## Method 5: Using the Admin Functions (Programmatic)

If you want to use the admin functions from your Flutter app:

```dart
import 'package:business_app/services/supabase_service.dart';

// Assign admin role
final result = await AuthService.assignAdminRole(
  userEmail: '<EMAIL>',
  role: 'super_admin',
);

if (result.isSuccess) {
  print('Admin role assigned successfully!');
} else {
  print('Error: ${result.error}');
}
```

## Available Roles

Your system supports these roles (in order of privileges):

1. **`user`** - Regular user (default)
2. **`moderator`** - Content moderation privileges
3. **`admin`** - Administrative privileges
4. **`super_admin`** - Full system access (recommended for you)

## Verification

After assigning the admin role, you can verify it worked by:

1. **Check in the app**: Your user model should show `isAdmin = true` and `isSuperAdmin = true`

2. **Query the database**:
   ```sql
   SELECT email, role FROM public.profiles WHERE role IN ('admin', 'super_admin', 'moderator');
   ```

3. **Use the Flutter function**:
   ```dart
   final isAdmin = await AuthService.isCurrentUserAdmin();
   print('Current user is admin: $isAdmin');
   ```

## Troubleshooting

### "User not found" error
- Make sure you've created an account with `<EMAIL>` first
- Check that the email is exactly correct (case-sensitive)

### Permission denied
- Make sure you're using the correct Supabase credentials
- Check that the database functions have been deployed

### Functions not found
- Run `supabase db push` to deploy the latest migrations
- Make sure all migration files have been applied

## Admin Capabilities

Once you have admin access, you can:

- **User Management**: View, edit, and manage user accounts
- **Content Moderation**: Review and moderate posts and content
- **System Analytics**: Access business stats and analytics
- **Role Management**: Assign roles to other users
- **Financial Management**: View financial data and reports
- **System Configuration**: Manage app settings and configurations

## Security Notes

- **Super Admin** role has full system access - use responsibly
- Consider creating additional admin users for team members
- Regularly review admin user list for security
- Use the principle of least privilege for other team members

## Next Steps

After becoming an admin:

1. **Test admin features**: Try accessing admin-only sections of your app
2. **Create additional admins**: Add other team members as needed
3. **Set up monitoring**: Monitor admin activities for security
4. **Review permissions**: Ensure role-based access control is working properly

---

**Need Help?**
If you encounter any issues, check the console logs for detailed error messages or contact your development team.
