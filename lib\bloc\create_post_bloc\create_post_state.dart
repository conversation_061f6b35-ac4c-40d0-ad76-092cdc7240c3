import 'package:equatable/equatable.dart';

enum CreatePostStatus { initial, editing, validating, submitting, submitted, error, savingDraft, draftSaved }

class PostDraft extends Equatable {
  final String id;
  final String content;
  final List<String> images;
  final String? videoPath;
  final String? location;
  final List<String> tags;
  final String privacy;
  final DateTime createdAt;
  final DateTime? scheduledTime;

  const PostDraft({
    required this.id,
    required this.content,
    this.images = const [],
    this.videoPath,
    this.location,
    this.tags = const [],
    this.privacy = 'public',
    required this.createdAt,
    this.scheduledTime,
  });

  PostDraft copyWith({
    String? id,
    String? content,
    List<String>? images,
    String? videoPath,
    String? location,
    List<String>? tags,
    String? privacy,
    DateTime? createdAt,
    DateTime? scheduledTime,
  }) {
    return PostDraft(
      id: id ?? this.id,
      content: content ?? this.content,
      images: images ?? this.images,
      videoPath: videoPath ?? this.videoPath,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      privacy: privacy ?? this.privacy,
      createdAt: createdAt ?? this.createdAt,
      scheduledTime: scheduledTime ?? this.scheduledTime,
    );
  }

  @override
  List<Object?> get props => [
        id,
        content,
        images,
        videoPath,
        location,
        tags,
        privacy,
        createdAt,
        scheduledTime,
      ];
}

class CreatePostState extends Equatable {
  final CreatePostStatus status;
  final String content;
  final List<String> images;
  final String? videoPath;
  final bool includeLocation;
  final String? location;
  final List<String> tags;
  final String privacy;
  final DateTime? scheduledTime;
  final List<String> validationErrors;
  final String? errorMessage;
  final PostDraft? currentDraft;

  const CreatePostState({
    this.status = CreatePostStatus.initial,
    this.content = '',
    this.images = const [],
    this.videoPath,
    this.includeLocation = false,
    this.location,
    this.tags = const [],
    this.privacy = 'public',
    this.scheduledTime,
    this.validationErrors = const [],
    this.errorMessage,
    this.currentDraft,
  });

  CreatePostState copyWith({
    CreatePostStatus? status,
    String? content,
    List<String>? images,
    String? videoPath,
    bool? includeLocation,
    String? location,
    List<String>? tags,
    String? privacy,
    DateTime? scheduledTime,
    List<String>? validationErrors,
    String? errorMessage,
    PostDraft? currentDraft,
  }) {
    return CreatePostState(
      status: status ?? this.status,
      content: content ?? this.content,
      images: images ?? this.images,
      videoPath: videoPath ?? this.videoPath,
      includeLocation: includeLocation ?? this.includeLocation,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      privacy: privacy ?? this.privacy,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      validationErrors: validationErrors ?? this.validationErrors,
      errorMessage: errorMessage ?? this.errorMessage,
      currentDraft: currentDraft ?? this.currentDraft,
    );
  }

  bool get hasContent => content.trim().isNotEmpty;
  bool get hasImages => images.isNotEmpty;
  bool get hasVideo => videoPath != null;
  bool get hasLocation => includeLocation && location != null && location!.isNotEmpty;
  bool get hasTags => tags.isNotEmpty;
  bool get isScheduled => scheduledTime != null;
  bool get isValid => validationErrors.isEmpty && hasContent;
  bool get canSubmit => isValid && status != CreatePostStatus.submitting;

  int get characterCount => content.length;
  int get maxCharacters => 280; // Twitter-like limit
  bool get isOverLimit => characterCount > maxCharacters;

  @override
  List<Object?> get props => [
        status,
        content,
        images,
        videoPath,
        includeLocation,
        location,
        tags,
        privacy,
        scheduledTime,
        validationErrors,
        errorMessage,
        currentDraft,
      ];
}
