import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'business_stats.dart';

/// Demo page to showcase the new Business Stats UI
/// This can be used for testing and demonstration purposes
class BusinessStatsDemoPage extends StatelessWidget {
  const BusinessStatsDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Business Stats Demo'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showInfoDialog(context),
          ),
        ],
      ),
      body: const BusinessStats(),
    );
  }

  void _showInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Business Stats Features'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '🎯 Overview Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• Key performance metrics'),
              Text('• Performance overview cards'),
              Text('• Quick business insights'),
              SizedBox(height: 12),
              Text(
                '💰 Revenue Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• Revenue trend charts'),
              Text('• Profit/Loss analysis'),
              Text('• Financial forecasting'),
              SizedBox(height: 12),
              Text(
                '📊 Sales Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• Sales by category charts'),
              Text('• Customer engagement analytics'),
              Text('• Sales distribution pie charts'),
              SizedBox(height: 12),
              Text(
                '🤖 Insights Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• AI-powered recommendations'),
              Text('• Performance comparisons'),
              Text('• Growth metrics analysis'),
              SizedBox(height: 12),
              Text(
                '✨ Features:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• Responsive design'),
              Text('• Beautiful animations'),
              Text('• Professional charts'),
              Text('• Dark/Light theme support'),
              Text('• Interactive tooltips'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }
}

/// Widget to demonstrate individual chart components
class ChartDemoPage extends StatelessWidget {
  const ChartDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chart Components Demo'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chart Components',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'This demo showcases individual chart components that can be used throughout the app.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 24),
            
            // Add individual chart demos here if needed
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Available Chart Types:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 12),
                    Text('• Line Charts (Revenue trends)'),
                    Text('• Bar Charts (Sales by category)'),
                    Text('• Pie Charts (Sales distribution)'),
                    Text('• Area Charts (Engagement trends)'),
                    Text('• Metric Cards (KPI displays)'),
                    SizedBox(height: 12),
                    Text(
                      'All charts are:',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text('• Responsive and adaptive'),
                    Text('• Animated with smooth transitions'),
                    Text('• Interactive with tooltips'),
                    Text('• Styled for professional appearance'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
