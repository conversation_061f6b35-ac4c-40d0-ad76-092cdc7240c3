import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'components/voice_call_interface.dart';
import 'components/video_call_interface.dart';
import 'components/call_demo_helper.dart';

class ChatInterfaceUI extends StatefulWidget {
  const ChatInterfaceUI({super.key});

  @override
  State<ChatInterfaceUI> createState() => _ChatInterfaceUIState();
}

class _ChatInterfaceUIState extends State<ChatInterfaceUI> {
  final TextEditingController _messageController = TextEditingController();
  bool _isTyping = false;
  // bool _isTextEmpty = true;

  void _showActionsSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        //return was DraggableScrollableSheet, but keyboard was overlapping textfield hence the padding
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: DraggableScrollableSheet(
            initialChildSize: 0.5,
            minChildSize: 0.3,
            maxChildSize: 0.95,
            builder: (_, controller) {
              return ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    // color: Colors.black.withOpacity(0.3),
                    color: Theme.of(
                      context,
                    ).colorScheme.surface.withOpacity(0.3),
                    padding: const EdgeInsets.all(16),
                    child: ListView(
                      controller: controller,
                      children: [
                        const Center(
                          child: Icon(
                            Icons.horizontal_rule,
                            size: 35,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 10),
                        const Text(
                          'Quick Actions',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 15),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 26),
                            _buildVerticalActionItem(
                              Icons.camera_alt,
                              'Camera',
                            ),
                            _buildVerticalActionItem(Icons.photo, 'Gallery'),
                            _buildVerticalActionItem(Icons.music_note, 'Audio'),
                            _buildVerticalActionItem(
                              Icons.location_on,
                              'Location',
                            ),
                            _buildVerticalActionItem(
                              Icons.insert_drive_file,
                              'Files',
                            ),
                            _buildVerticalActionItem(Icons.mic, 'Voice'),
                            //_buildVerticalActionItem(Icons.gif_box, 'GIF'),
                            _buildVerticalActionItem(Icons.poll, 'Poll'),
                            _buildVerticalActionItem(
                              Icons.more_horiz_outlined,
                              'More',
                            ),
                          ],
                        ),

                        /*
                      original
                      Wrap(
                        spacing: 20,
                        runSpacing: 20,
                        children: [
                          _buildActionItem(Icons.photo, 'Gallery'),
                          _buildActionItem(Icons.music_note, 'Audio'),
                          _buildActionItem(Icons.location_on, 'Location'),
                          _buildActionItem(Icons.camera_alt, 'Camera'),
                          _buildActionItem(Icons.mic, 'Voice'),
                          _buildActionItem(Icons.gif_box, 'GIF'),
                          _buildActionItem(Icons.note, 'Notes'),
                          _buildActionItem(Icons.apps, 'Apps'),
                        ],
                      ),*/
                        const SizedBox(height: 25),
                        const Divider(),
                        const SizedBox(height: 10),
                        _buildTextField(),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  //for items in a column
  Widget _buildVerticalActionItem(IconData icon, String label) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 22,
            // backgroundColor: Colors.teal.shade100,
            backgroundColor: Theme.of(
              context,
            ).colorScheme.surface.withOpacity(0.3),
            child: Icon(icon, size: 22, color: Colors.blue),
          ),
          const SizedBox(width: 16),
          Text(
            label,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
  //for items in a column

  Widget _buildTextField() {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Color(0xFF6D4C41),
              //color: Theme.of(context).colorScheme.surface.withOpacity(0.999),
              borderRadius: BorderRadius.circular(25),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: TextField(
              controller: _messageController,
              minLines: 1,
              maxLines: 3,
              onChanged: (text) {
                setState(() {
                  _isTyping = text.isNotEmpty;
                });
              },
              decoration: InputDecoration(
                hintStyle: TextStyle(color: Colors.white70),

                prefixIcon:
                    _isTyping ? null : Icon(Icons.mic, color: Colors.white70),
                hintText: 'Write message',
                border: InputBorder.none,
              ),
            ),
          ),
        ),
        const SizedBox(width: 10),
        IconButton(
          onPressed: () {},
          icon: const Icon(Icons.send_rounded, color: Colors.teal),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return Scaffold(
      //backgroundColor: Colors.grey.shade100,
      appBar: AppBar(
        //backgroundColor: Colors.white,
        elevation: 0,
        //foregroundColor: Colors.black,
        // ...existing code...
        title: Row(
          children: [
            const CircleAvatar(
              backgroundImage: NetworkImage('https://i.pravatar.cc/150?img=8'),
              radius: 18,
            ),
            const SizedBox(width: 10),
            Expanded(
              // <-- Add this
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Aubrey Tanaka',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      overflow: TextOverflow.ellipsis,
                    ),
                    softWrap: true, // <-- Optional, true by default
                  ),
                  Text(
                    _isTyping ? 'typing...' : 'Online',
                    style: TextStyle(
                      fontSize: 12,
                      color: _isTyping ? Colors.teal : Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        // ...existing code...
        actions: [
          IconButton(
            icon: const Icon(Icons.call, color: Colors.teal, size: 20),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => VoiceCallInterface(
                        contactName: 'Aubrey Tanaka',
                        contactImage: 'https://i.pravatar.cc/150?img=8',
                        isIncoming: false,
                        onEndCall: () {
                          // Handle call end
                        },
                      ),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.videocam, color: Colors.teal, size: 20),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => VideoCallInterface(
                        contactName: 'Aubrey Tanaka',
                        contactImage: 'https://i.pravatar.cc/150?img=8',
                        isIncoming: false,
                        onEndCall: () {
                          // Handle call end
                        },
                      ),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(child: Center(child: NewDefaultPlaceholderUi())),
          /*
          //for default placeholder
          const Expanded(
            child: Center(
              child: Text(
                'Start chatting...',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ),
          ),*/
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              children: [
                IconButton(
                  onPressed: _showActionsSheet,
                  icon: const Icon(
                    Icons.add_circle,
                    color: Colors.teal,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(child: _buildTextField()),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showModalBottomSheet(
            context: context,
            backgroundColor: Colors.transparent,
            builder:
                (context) => Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
                  child: CallDemoHelper.buildCallTestWidget(context),
                ),
          );
        },
        backgroundColor: Colors.teal,
        child: const Icon(Icons.phone, color: Colors.white),
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }
}

//for new default placeholder
class NewDefaultPlaceholderUi extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //backgroundColor: Color(0xFF1A1A1A),
      backgroundColor: Colors.transparent,
      body: Center(
        child: Wrap(
          children: [
            Container(
              width: 300,
              height: 350,
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                //color: Color(0xFF2C2C2C),
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    //color: Colors.black26,
                    color: Colors.transparent,
                    blurRadius: 10,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: NetworkImage(
                      'https://i.pravatar.cc/150?img=8', // Replace with a real URL
                    ),
                    backgroundColor: Colors.grey[300],
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Aubrey Tanaka',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      //color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Handle view profile action
                    },
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: Color(0xFF3A3A3A),
                      padding: EdgeInsets.symmetric(
                        horizontal: 18,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                    child: Text('View Business'),
                  ),
                  SizedBox(height: 16),
                  Text(
                    "108,561 Customers • 6 Shops\nYou don't follow each other on this Business App",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16, //color: Colors.grey[400]
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
