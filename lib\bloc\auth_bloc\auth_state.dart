import 'package:equatable/equatable.dart';
import 'package:business_app/models/auth/user_role.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

enum SignupStep {
  basicInfo,
  password,
  birthday,
  username,
  profileSetup,
  categorySelection,
  completed,
}

enum LoginStep {
  identifier,
  password,
  otpVerification,
  emailVerificationNeeded,
}

class AuthState extends Equatable {
  final AuthStatus status;
  final SignupStep? signupStep;
  final LoginStep? loginStep;
  final String? errorMessage;
  final String? successMessage;
  final bool isLoading;

  // User data during signup process
  final String? tempName;
  final String? tempEmailOrPhone;
  final DateTime? tempDateOfBirth;
  final bool? tempIsEmail;
  final String? tempPassword;
  final String? tempUsername;
  final String? tempProfileImagePath;
  final String? tempBio;
  final String? tempVerificationCode;
  final List<String>? tempSelectedCategories;

  // Google Sign-In specific data
  final bool? isGoogleSignup;
  final String? googleProfileImageUrl;

  // Current user data (when authenticated)
  final String? userId;
  final String? userName;
  final String? userEmail;
  final String? userPhone;
  final String? userProfileImage;
  final String? userBio;
  final String? accessToken;
  final UserRole? userRole;

  // Password reset data
  final String? resetEmailOrPhone;
  final String? resetCode;
  final String? resolvedEmail; // The actual email resolved from identifier

  // Security verification data
  final String? securityOperation;
  final String? newEmailPending;
  final bool? isNewDevice;

  const AuthState({
    this.status = AuthStatus.initial,
    this.signupStep,
    this.loginStep,
    this.errorMessage,
    this.successMessage,
    this.isLoading = false,
    this.tempName,
    this.tempEmailOrPhone,
    this.tempDateOfBirth,
    this.tempIsEmail,
    this.tempPassword,
    this.tempUsername,
    this.tempProfileImagePath,
    this.tempBio,
    this.tempVerificationCode,
    this.tempSelectedCategories,
    this.isGoogleSignup,
    this.googleProfileImageUrl,
    this.userId,
    this.userName,
    this.userEmail,
    this.userPhone,
    this.userProfileImage,
    this.userBio,
    this.accessToken,
    this.userRole,
    this.resetEmailOrPhone,
    this.resetCode,
    this.resolvedEmail,
    this.securityOperation,
    this.newEmailPending,
    this.isNewDevice,
  });

  // Initial state
  factory AuthState.initial() {
    return const AuthState(status: AuthStatus.initial, isLoading: false);
  }

  // Loading state
  AuthState loading() {
    return copyWith(
      status: AuthStatus.loading,
      isLoading: true,
      errorMessage: null,
    );
  }

  // Authenticated state
  AuthState authenticated({
    required String userId,
    required String userName,
    String? userEmail,
    String? userPhone,
    String? userProfileImage,
    String? userBio,
    String? accessToken,
    UserRole? userRole,
  }) {
    return copyWith(
      status: AuthStatus.authenticated,
      isLoading: false,
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      userPhone: userPhone,
      userProfileImage: userProfileImage,
      userBio: userBio,
      accessToken: accessToken,
      userRole: userRole ?? UserRole.user,
      errorMessage: null,
      // Clear temp data
      tempName: null,
      tempEmailOrPhone: null,
      tempDateOfBirth: null,
      tempIsEmail: null,
      tempPassword: null,
      tempUsername: null,
      tempProfileImagePath: null,
      tempBio: null,
      tempVerificationCode: null,
      tempSelectedCategories: null,
      signupStep: null,
    );
  }

  // Unauthenticated state
  AuthState unauthenticated() {
    return copyWith(
      status: AuthStatus.unauthenticated,
      isLoading: false,
      userId: null,
      userName: null,
      userEmail: null,
      userPhone: null,
      userProfileImage: null,
      userBio: null,
      accessToken: null,
      errorMessage: null,
    );
  }

  // Error state
  AuthState error(String message) {
    return copyWith(
      status: AuthStatus.error,
      isLoading: false,
      errorMessage: message,
      // Clear resetCode on error to prevent UI from showing password field incorrectly
      resetCode: null,
    );
  }

  // Success state
  AuthState success(String message) {
    return copyWith(
      isLoading: false,
      successMessage: message,
      errorMessage: null,
    );
  }

  // Signup step states
  AuthState signupBasicInfo({
    required String name,
    required String emailOrPhone,
    required DateTime dateOfBirth,
    required bool isEmail,
  }) {
    return copyWith(
      status: AuthStatus.unauthenticated,
      signupStep: SignupStep.password, // Changed to go to password first
      isLoading: false,
      tempName: name,
      tempEmailOrPhone: emailOrPhone,
      tempDateOfBirth: dateOfBirth,
      tempIsEmail: isEmail,
      errorMessage: null,
    );
  }

  // Removed signupVerification - no longer needed in Spotify-style flow

  AuthState signupPassword({required String password}) {
    return copyWith(
      signupStep: SignupStep.username, // Go directly to username after password
      isLoading: false,
      tempPassword: password,
      errorMessage: null,
    );
  }

  AuthState signupBirthday({required DateTime dateOfBirth}) {
    return copyWith(
      signupStep: SignupStep.username,
      isLoading: false,
      tempDateOfBirth: dateOfBirth,
      errorMessage: null,
    );
  }

  AuthState signupUsername({required String username}) {
    return copyWith(
      signupStep: SignupStep.profileSetup,
      isLoading: false,
      tempUsername: username,
      errorMessage: null,
    );
  }

  AuthState signupProfileSetup({String? profileImagePath, String? bio}) {
    return copyWith(
      signupStep: SignupStep.categorySelection,
      isLoading: false,
      tempProfileImagePath: profileImagePath,
      tempBio: bio,
      errorMessage: null,
    );
  }

  AuthState signupCategorySelection({
    required List<String> selectedCategories,
  }) {
    return copyWith(
      signupStep: SignupStep.completed,
      isLoading: false,
      tempSelectedCategories: selectedCategories,
      errorMessage: null,
    );
  }

  // Google signup flow - redirect to basic info with pre-filled data
  AuthState googleSignupRedirect({
    required String name,
    required String email,
    String? profileImageUrl,
  }) {
    return copyWith(
      status: AuthStatus.unauthenticated,
      signupStep: SignupStep.basicInfo,
      isLoading: false,
      tempName: name,
      tempEmailOrPhone: email,
      tempIsEmail: true,
      isGoogleSignup: true,
      googleProfileImageUrl: profileImageUrl,
      errorMessage: null,
    );
  }

  // Google sign-in completed - show confirmation popup
  AuthState googleSignInCompleted({
    required String userId,
    required String userName,
    String? userEmail,
    String? userProfileImage,
    String? accessToken,
  }) {
    return copyWith(
      status: AuthStatus.unauthenticated,
      signupStep: null, // No signup step yet, waiting for user confirmation
      isLoading: false,
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      userProfileImage: userProfileImage,
      accessToken: accessToken,
      isGoogleSignup: true,
      errorMessage: null,
    );
  }

  // Google user needs onboarding - go directly to category selection
  AuthState googleUserNeedsOnboarding({
    required String userId,
    required String userName,
    String? userEmail,
    String? userProfileImage,
    String? accessToken,
  }) {
    return copyWith(
      status: AuthStatus.unauthenticated,
      signupStep: SignupStep.categorySelection,
      isLoading: false,
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      userProfileImage: userProfileImage,
      accessToken: accessToken,
      isGoogleSignup: true,
      errorMessage: null,
    );
  }

  // Password reset states
  AuthState forgotPasswordSent({required String emailOrPhone}) {
    return copyWith(
      isLoading: false,
      resetEmailOrPhone: emailOrPhone,
      tempEmailOrPhone: emailOrPhone, // Store for OTP verification
      successMessage: 'Reset code sent to your email',
      errorMessage: null,
    );
  }

  AuthState passwordResetVerified({required String code}) {
    return copyWith(
      status:
          AuthStatus.unauthenticated, // Explicitly set status to non-loading
      isLoading: false,
      resetCode: code,
      errorMessage: null,
      successMessage:
          null, // Don't set success message to prevent BlocListener conflicts
    );
  }

  AuthState passwordResetCompleted() {
    return copyWith(
      isLoading: false,
      resetEmailOrPhone: null,
      resetCode: null,
      successMessage: 'Password reset successfully',
      errorMessage: null,
    );
  }

  // Security operation states
  AuthState newDeviceVerificationSent({required String email}) {
    return copyWith(
      isLoading: false,
      tempEmailOrPhone: email,
      isNewDevice: true,
      successMessage: 'Verification code sent to your email',
      errorMessage: null,
    );
  }

  AuthState newDeviceVerified() {
    return copyWith(
      isLoading: false,
      isNewDevice: false,
      successMessage: 'Device verified successfully',
      errorMessage: null,
    );
  }

  AuthState emailChangeRequested({required String newEmail}) {
    return copyWith(
      isLoading: false,
      newEmailPending: newEmail,
      successMessage: 'Verification code sent to new email',
      errorMessage: null,
    );
  }

  AuthState emailChangeCompleted() {
    return copyWith(
      isLoading: false,
      newEmailPending: null,
      successMessage: 'Email updated successfully',
      errorMessage: null,
    );
  }

  AuthState securityOperationRequested({required String operation}) {
    return copyWith(
      isLoading: false,
      securityOperation: operation,
      successMessage: 'Security verification required',
      errorMessage: null,
    );
  }

  AuthState securityOperationCompleted() {
    return copyWith(
      isLoading: false,
      securityOperation: null,
      successMessage: 'Security verification completed',
      errorMessage: null,
    );
  }

  AuthState copyWith({
    AuthStatus? status,
    SignupStep? signupStep,
    LoginStep? loginStep,
    String? errorMessage,
    String? successMessage,
    bool? isLoading,
    String? tempName,
    String? tempEmailOrPhone,
    DateTime? tempDateOfBirth,
    bool? tempIsEmail,
    String? tempPassword,
    String? tempUsername,
    String? tempProfileImagePath,
    String? tempBio,
    String? tempVerificationCode,
    List<String>? tempSelectedCategories,
    bool? isGoogleSignup,
    String? googleProfileImageUrl,
    String? userId,
    String? userName,
    String? userEmail,
    String? userPhone,
    String? userProfileImage,
    String? userBio,
    String? accessToken,
    UserRole? userRole,
    String? resetEmailOrPhone,
    String? resetCode,
    String? resolvedEmail,
    String? securityOperation,
    String? newEmailPending,
    bool? isNewDevice,
  }) {
    return AuthState(
      status: status ?? this.status,
      signupStep: signupStep ?? this.signupStep,
      loginStep: loginStep ?? this.loginStep,
      errorMessage: errorMessage,
      successMessage: successMessage,
      isLoading: isLoading ?? this.isLoading,
      tempName: tempName ?? this.tempName,
      tempEmailOrPhone: tempEmailOrPhone ?? this.tempEmailOrPhone,
      tempDateOfBirth: tempDateOfBirth ?? this.tempDateOfBirth,
      tempIsEmail: tempIsEmail ?? this.tempIsEmail,
      tempPassword: tempPassword ?? this.tempPassword,
      tempUsername: tempUsername ?? this.tempUsername,
      tempProfileImagePath: tempProfileImagePath ?? this.tempProfileImagePath,
      tempBio: tempBio ?? this.tempBio,
      tempVerificationCode: tempVerificationCode ?? this.tempVerificationCode,
      tempSelectedCategories:
          tempSelectedCategories ?? this.tempSelectedCategories,
      isGoogleSignup: isGoogleSignup ?? this.isGoogleSignup,
      googleProfileImageUrl:
          googleProfileImageUrl ?? this.googleProfileImageUrl,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userPhone: userPhone ?? this.userPhone,
      userProfileImage: userProfileImage ?? this.userProfileImage,
      userBio: userBio ?? this.userBio,
      accessToken: accessToken ?? this.accessToken,
      userRole: userRole ?? this.userRole,
      resetEmailOrPhone: resetEmailOrPhone ?? this.resetEmailOrPhone,
      resetCode: resetCode ?? this.resetCode,
      resolvedEmail: resolvedEmail ?? this.resolvedEmail,
      securityOperation: securityOperation ?? this.securityOperation,
      newEmailPending: newEmailPending ?? this.newEmailPending,
      isNewDevice: isNewDevice ?? this.isNewDevice,
    );
  }

  @override
  List<Object?> get props => [
    status,
    signupStep,
    loginStep,
    errorMessage,
    successMessage,
    isLoading,
    tempName,
    tempEmailOrPhone,
    tempDateOfBirth,
    tempIsEmail,
    tempPassword,
    tempUsername,
    tempProfileImagePath,
    tempBio,
    tempVerificationCode,
    tempSelectedCategories,
    userId,
    userName,
    userEmail,
    userPhone,
    userProfileImage,
    userBio,
    accessToken,
    userRole,
    resetEmailOrPhone,
    resetCode,
    resolvedEmail,
    securityOperation,
    newEmailPending,
    isNewDevice,
  ];
}
