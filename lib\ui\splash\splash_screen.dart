import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:business_app/ui/auth/auth_wrapper.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/services.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _backgroundController;
  late AnimationController _particleController;

  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<Offset> _logoSlideAnimation;

  late Animation<double> _titleOpacityAnimation;
  late Animation<Offset> _titleSlideAnimation;
  late Animation<double> _titleScaleAnimation;

  late Animation<double> _descriptionOpacityAnimation;
  late Animation<Offset> _descriptionSlideAnimation;

  late Animation<double> _backgroundOpacityAnimation;
  late Animation<double> _particleAnimation;

  // Authentication state tracking
  bool _authCheckCompleted = false;
  bool _animationsCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Logo animations
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _logoScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _logoOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.4, curve: Curves.easeIn),
      ),
    );

    _logoRotationAnimation = Tween<double>(begin: -0.5, end: 0.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutBack),
      ),
    );

    _logoSlideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
      ),
    );

    // Text animations
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _titleOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _titleSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
      ),
    );

    _titleScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _descriptionOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.3, 0.8, curve: Curves.easeIn),
      ),
    );

    _descriptionSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.3, 0.9, curve: Curves.easeOutCubic),
      ),
    );

    // Background animations
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _backgroundOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _backgroundController,
        curve: const Interval(0.0, 0.3, curve: Curves.easeIn),
      ),
    );

    // Particle animation
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );
  }

  void _startAnimationSequence() async {
    // Start background animation immediately
    _backgroundController.forward();

    // Start particle animation
    _particleController.repeat();

    // Delay then start logo animation
    await Future.delayed(const Duration(milliseconds: 200));
    _logoController.forward();

    // Delay then start text animation
    await Future.delayed(const Duration(milliseconds: 600));
    _textController.forward();

    // Mark animations as completed after minimum display time
    await Future.delayed(
      const Duration(milliseconds: 1800),
    ); // Reduced from 2500ms
    _animationsCompleted = true;
    _checkAndNavigate();
  }

  void _checkAndNavigate() {
    // Only navigate when both animations and auth check are complete
    if (_animationsCompleted && _authCheckCompleted && mounted) {
      _navigateToDestination();
    }
  }

  void _navigateToDestination() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) => const AuthWrapper(),
          transitionDuration: const Duration(
            milliseconds: 300,
          ), // Faster transition
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _backgroundController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.height < 700;

    // Set status bar style for splash
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            theme.brightness == Brightness.light
                ? Brightness.dark
                : Brightness.light,
        statusBarBrightness: theme.brightness,
      ),
    );

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        // Mark auth check as completed when we get any definitive state
        if (state.status == AuthStatus.authenticated ||
            state.status == AuthStatus.unauthenticated ||
            state.status == AuthStatus.error) {
          _authCheckCompleted = true;
          _checkAndNavigate();
        }
      },
      child: Scaffold(
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors:
                  theme.brightness == Brightness.light
                      ? [
                        const Color(0xFF1DA1F2).withValues(alpha: 0.1),
                        Colors.white,
                        const Color(0xFF1DA1F2).withValues(alpha: 0.05),
                      ]
                      : [
                        const Color(0xFF1DA1F2).withValues(alpha: 0.2),
                        const Color(0xFF121212),
                        const Color(0xFF1DA1F2).withValues(alpha: 0.1),
                      ],
            ),
          ),
          child: Stack(
            children: [
              // Animated background particles
              _buildAnimatedParticles(size),

              // Main content
              SafeArea(
                child: Center(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 32.0,
                        vertical: isSmallScreen ? 16.0 : 32.0,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Logo section
                          _buildLogoSection(theme, isSmallScreen),

                          SizedBox(height: isSmallScreen ? 32 : 48),

                          // Title section
                          _buildTitleSection(theme, isSmallScreen),

                          SizedBox(height: isSmallScreen ? 16 : 24),

                          // Description section
                          _buildDescriptionSection(theme, isSmallScreen),

                          SizedBox(height: isSmallScreen ? 32 : 48),

                          // Loading indicator
                          _buildLoadingIndicator(theme),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedParticles(Size size) {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return CustomPaint(
          size: size,
          painter: ParticlesPainter(
            animation: _particleAnimation.value,
            theme: Theme.of(context),
          ),
        );
      },
    );
  }

  Widget _buildLogoSection(ThemeData theme, bool isSmallScreen) {
    return AnimatedBuilder(
      animation: _logoController,
      builder: (context, child) {
        return SlideTransition(
          position: _logoSlideAnimation,
          child: FadeTransition(
            opacity: _logoOpacityAnimation,
            child: Transform.scale(
              scale: _logoScaleAnimation.value,
              child: Transform.rotate(
                angle: _logoRotationAnimation.value,
                child: Container(
                  width: isSmallScreen ? 120 : 150,
                  height: isSmallScreen ? 120 : 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                        blurRadius: 30,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          theme.colorScheme.primary.withValues(alpha: 0.1),
                          Colors.white.withValues(alpha: 0.9),
                        ],
                      ),
                    ),
                    child: ClipOval(
                      child: Container(
                        width: isSmallScreen ? 120 : 150,
                        height: isSmallScreen ? 120 : 150,
                        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                        child: ClipOval(
                          child: Image.asset(
                            'assets/images/Trademate-logo.png',
                            width: isSmallScreen ? 88 : 110,
                            height: isSmallScreen ? 88 : 110,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: isSmallScreen ? 88 : 110,
                                height: isSmallScreen ? 88 : 110,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                ),
                                child: Icon(
                                  Icons.storefront_rounded,
                                  size: isSmallScreen ? 44 : 55,
                                  color: theme.colorScheme.primary,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTitleSection(ThemeData theme, bool isSmallScreen) {
    return AnimatedBuilder(
      animation: _textController,
      builder: (context, child) {
        return SlideTransition(
          position: _titleSlideAnimation,
          child: FadeTransition(
            opacity: _titleOpacityAnimation,
            child: Transform.scale(
              scale: _titleScaleAnimation.value,
              child: Column(
                children: [
                  Text(
                    'Trademate',
                    style: theme.textTheme.headlineLarge?.copyWith(
                      fontSize: isSmallScreen ? 32 : 42,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                      letterSpacing: 1.2,
                      shadows: [
                        Shadow(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.3,
                          ),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 60,
                    height: 3,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primary,
                          theme.colorScheme.secondary,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDescriptionSection(ThemeData theme, bool isSmallScreen) {
    return AnimatedBuilder(
      animation: _textController,
      builder: (context, child) {
        return SlideTransition(
          position: _descriptionSlideAnimation,
          child: FadeTransition(
            opacity: _descriptionOpacityAnimation,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Your ultimate trading & marketplace center.\nConnect, trade, and grow your business.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontSize: isSmallScreen ? 16 : 18,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  height: 1.5,
                  letterSpacing: 0.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator(ThemeData theme) {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _backgroundOpacityAnimation,
          child: Column(
            children: [
              // Spotify-style 3 dots loading animation
              SizedBox(
                width: 100,
                height: 50,
                child: _SpotifyDotsLoader(
                  color: theme.colorScheme.primary,
                  dotSize: 12.0,
                  spacing: 16.0,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Loading your experience...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Spotify-style 3 dots loading animation widget
class _SpotifyDotsLoader extends StatefulWidget {
  final Color color;
  final double dotSize;
  final double spacing;

  const _SpotifyDotsLoader({
    required this.color,
    this.dotSize = 8.0,
    this.spacing = 6.0,
  });

  @override
  State<_SpotifyDotsLoader> createState() => _SpotifyDotsLoaderState();
}

class _SpotifyDotsLoaderState extends State<_SpotifyDotsLoader>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );
    });

    _animations =
        _controllers.map((controller) {
          return Tween<double>(begin: 0.4, end: 1.0).animate(
            CurvedAnimation(parent: controller, curve: Curves.easeInOutCubic),
          );
        }).toList();

    _startAnimation();
  }

  void _startAnimation() async {
    while (mounted) {
      // Forward animation with smoother timing
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].forward();
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }

      // Pause at peak
      await Future.delayed(const Duration(milliseconds: 300));

      // Reverse animation
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].reverse();
        }
      }

      // Pause before next cycle
      await Future.delayed(const Duration(milliseconds: 600));
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: widget.spacing / 2),
              child: Transform.scale(
                scale: _animations[index].value,
                child: Container(
                  width: widget.dotSize,
                  height: widget.dotSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.color.withValues(
                      alpha: _animations[index].value,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: widget.color.withValues(alpha: 0.4),
                        blurRadius: 8,
                        spreadRadius: 2,
                        offset: const Offset(0, 2),
                      ),
                      BoxShadow(
                        color: widget.color.withValues(alpha: 0.2),
                        blurRadius: 16,
                        spreadRadius: 4,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

// Custom painter for animated background particles
class ParticlesPainter extends CustomPainter {
  final double animation;
  final ThemeData theme;

  ParticlesPainter({required this.animation, required this.theme});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = theme.colorScheme.primary.withValues(alpha: 0.1)
          ..style = PaintingStyle.fill;

    // Create floating particles
    for (int i = 0; i < 20; i++) {
      final x =
          (size.width * 0.1) +
          (size.width * 0.8 * ((i * 0.618) % 1.0)) +
          (30 * math.sin(animation * 2 * math.pi + i));

      final y =
          (size.height * 0.1) +
          (size.height * 0.8 * ((i * 0.382) % 1.0)) +
          (20 * math.cos(animation * 2 * math.pi + i * 0.5));

      final radius = 2 + (3 * math.sin(animation * math.pi + i));

      canvas.drawCircle(Offset(x, y), radius, paint);
    }

    // Create subtle gradient overlay
    final gradientPaint =
        Paint()
          ..shader = RadialGradient(
            center: Alignment.center,
            radius: 1.0,
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.05),
              Colors.transparent,
            ],
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      gradientPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
