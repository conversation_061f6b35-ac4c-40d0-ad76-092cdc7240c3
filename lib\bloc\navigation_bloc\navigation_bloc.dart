import 'package:flutter_bloc/flutter_bloc.dart';
import 'navigation_event.dart';
import 'navigation_state.dart';

class NavigationBloc extends Bloc<NavigationEvent, NavigationState> {
  NavigationBloc() : super(const NavigationState()) {
    on<NavigateToPageEvent>(_onNavigateToPage);
    on<ToggleBottomBarVisibilityEvent>(_onToggleBottomBarVisibility);
    on<ToggleDrawerEvent>(_onToggleDrawer);
    on<OpenDrawerEvent>(_onOpenDrawer);
    on<CloseDrawerEvent>(_onCloseDrawer);
    on<SetTabIndexEvent>(_onSetTabIndex);
  }

  void _onNavigateToPage(
    NavigateToPageEvent event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(selectedPageIndex: event.pageIndex));
  }

  void _onToggleBottomBarVisibility(
    ToggleBottomBarVisibilityEvent event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(isBottomBarVisible: event.isVisible));
  }

  void _onToggleDrawer(
    ToggleDrawerEvent event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(isDrawerOpen: !state.isDrawerOpen));
  }

  void _onOpenDrawer(
    OpenDrawerEvent event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(isDrawerOpen: true));
  }

  void _onCloseDrawer(
    CloseDrawerEvent event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(isDrawerOpen: false));
  }

  void _onSetTabIndex(
    SetTabIndexEvent event,
    Emitter<NavigationState> emit,
  ) {
    emit(state.copyWith(selectedTabIndex: event.tabIndex));
  }
}
