import 'package:business_app/ui/home_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/signup/signup_basic_info_page.dart';
import 'package:business_app/ui/auth/signup/signup_password_page.dart';
import 'package:business_app/ui/auth/signup/signup_birthday_page.dart';
import 'package:business_app/ui/auth/signup/signup_username_page.dart';
import 'package:business_app/ui/auth/signup/signup_profile_page.dart';
import 'package:business_app/ui/auth/signup/category_selection_page.dart';
import 'package:business_app/ui/auth/components/spotify_error_popup.dart';

class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  State<SignupPage> createState() => _SignupPageState();
}

class _SignupPageState extends State<SignupPage> {
  @override
  void initState() {
    super.initState();
    // Only initialize signup flow once when the widget is first created
    final currentState = context.read<AuthBloc>().state;
    if (currentState.signupStep == null) {
      context.read<AuthBloc>().add(SignupStarted());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state.status == AuthStatus.authenticated &&
            state.signupStep == null) {
          // Only navigate when signup is completely finished (signupStep is null)
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const MyHomePage()),
            (route) => false,
          );
        } else if (state.status == AuthStatus.error) {
          // Handle Google sign-in errors only - other errors are handled by individual step pages
          final errorMessage = (state.errorMessage ?? '').toLowerCase();
          if (errorMessage.contains('google') ||
              errorMessage.contains('sign-in') ||
              errorMessage.contains('authentication failed') ||
              errorMessage.contains('id token') ||
              errorMessage.contains('supabase')) {
            // Show Spotify-style error popup for Google sign-in errors
            GoogleSignInErrorPopup.show(context: context);
          }
          // Removed regular SnackBar to prevent duplicate error messages
          // Individual step pages (signup_basic_info_page.dart, etc.) handle their own errors
        }
      },
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          switch (state.signupStep) {
            case SignupStep.basicInfo:
              return const SignupBasicInfoPage();
            case SignupStep.password:
              return const SignupPasswordPage();
            case SignupStep.birthday:
              return const SignupBirthdayPage();
            case SignupStep.username:
              return const SignupUsernamePage();
            case SignupStep.profileSetup:
              return const SignupProfilePage();
            case SignupStep.categorySelection:
              return const CategorySelectionPage();
            case SignupStep.completed:
              // This should trigger authentication and navigation
              return const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              );
            default:
              return const SignupBasicInfoPage();
          }
        },
      ),
    );
  }
}
