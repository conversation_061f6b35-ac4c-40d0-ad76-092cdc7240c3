-- =====================================================
-- BUSINESS APP - CORE TABLES MIGRATION
-- Following Big App Best Practices with Supabase
-- =====================================================

-- Note: Using IF NOT EXISTS to handle existing tables safely
-- If you want to start fresh, manually drop tables first

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For fuzzy text search
CREATE EXTENSION IF NOT EXISTS "unaccent"; -- For accent-insensitive search

-- =====================================================
-- IMMUTABLE WRAPPER FUNCTIONS FOR FULL-TEXT SEARCH
-- =====================================================

-- Create IMMUTABLE wrapper function for to_tsvector to use in indexes
CREATE OR REPLACE FUNCTION public.immutable_to_tsvector(text)
RETURNS tsvector
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT to_tsvector($1);
$$;

-- Create IMMUTABLE wrapper function for array_to_string to use in indexes
CREATE OR REPLACE FUNCTION public.immutable_array_to_string(text[], text)
RETURNS text
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT array_to_string($1, $2);
$$;

-- =====================================================
-- 1. PROFILES TABLE (User Management)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE, -- Made nullable for phone-only users
    phone VARCHAR(20),
    avatar_url TEXT,
    bio TEXT,
    date_of_birth DATE,
    location VARCHAR(100),
    website_url TEXT,
    
    -- Role and verification
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'business', 'moderator', 'admin', 'super_admin')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_business BOOLEAN DEFAULT FALSE,
    
    -- Social counters (denormalized for performance)
    followers_count INTEGER DEFAULT 0 CHECK (followers_count >= 0),
    following_count INTEGER DEFAULT 0 CHECK (following_count >= 0),
    posts_count INTEGER DEFAULT 0 CHECK (posts_count >= 0),
    
    -- Privacy settings
    is_private BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    preferences JSONB DEFAULT '{}',
    social_links JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_seen_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_profiles_username ON public.profiles(username);
CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_profiles_role ON public.profiles(role);
CREATE INDEX idx_profiles_is_business ON public.profiles(is_business);
CREATE INDEX idx_profiles_created_at ON public.profiles(created_at DESC);
CREATE INDEX idx_profiles_last_seen ON public.profiles(last_seen_at DESC);

-- Full-text search index (using IMMUTABLE wrapper function)
CREATE INDEX idx_profiles_search ON public.profiles
USING gin(public.immutable_to_tsvector(username || ' ' || full_name || ' ' || COALESCE(bio, '')));

-- =====================================================
-- 2. SHOPS TABLE (Business Profiles)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.shops (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    
    -- Basic info
    shop_name VARCHAR(100) NOT NULL,
    shop_description TEXT,
    shop_logo_url TEXT,
    shop_banner_url TEXT,
    
    -- Business details
    business_type VARCHAR(50) DEFAULT 'individual' CHECK (business_type IN ('individual', 'company', 'corporation')),
    business_registration_number VARCHAR(100),
    tax_id VARCHAR(50),
    
    -- Contact info
    location VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    website_url TEXT,
    
    -- Social and metadata
    social_links JSONB DEFAULT '{}',
    business_hours JSONB DEFAULT '{}',
    
    -- Status and verification
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    verification_level VARCHAR(20) DEFAULT 'none' CHECK (verification_level IN ('none', 'basic', 'premium', 'enterprise')),
    
    -- Performance metrics (denormalized)
    rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0 AND rating <= 5),
    total_reviews INTEGER DEFAULT 0 CHECK (total_reviews >= 0),
    total_sales INTEGER DEFAULT 0 CHECK (total_sales >= 0),
    total_revenue DECIMAL(12,2) DEFAULT 0.00 CHECK (total_revenue >= 0),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_owner_shop UNIQUE(owner_id), -- One shop per user for now
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Indexes for performance
CREATE INDEX idx_shops_owner_id ON public.shops(owner_id);
CREATE INDEX idx_shops_is_active ON public.shops(is_active);
CREATE INDEX idx_shops_is_verified ON public.shops(is_verified);
CREATE INDEX idx_shops_rating ON public.shops(rating DESC);
CREATE INDEX idx_shops_created_at ON public.shops(created_at DESC);
CREATE INDEX idx_shops_location ON public.shops(location);

-- Full-text search index (using IMMUTABLE wrapper function)
CREATE INDEX idx_shops_search ON public.shops
USING gin(public.immutable_to_tsvector(shop_name || ' ' || COALESCE(shop_description, '') || ' ' || COALESCE(location, '')));

-- =====================================================
-- 3. CATEGORIES TABLE (Product Organization)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_url TEXT,
    image_url TEXT,
    
    -- Hierarchy support
    parent_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
    level INTEGER DEFAULT 0 CHECK (level >= 0 AND level <= 3), -- Max 3 levels deep
    path TEXT, -- Materialized path for efficient queries
    
    -- Display and ordering
    sort_order INTEGER DEFAULT 0,
    color_hex VARCHAR(7) DEFAULT '#6B7280',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_category_name_parent UNIQUE(name, parent_id),
    CONSTRAINT valid_color_hex CHECK (color_hex ~* '^#[0-9A-Fa-f]{6}$')
);

-- Indexes for performance
CREATE INDEX idx_categories_parent_id ON public.categories(parent_id);
CREATE INDEX idx_categories_is_active ON public.categories(is_active);
CREATE INDEX idx_categories_is_featured ON public.categories(is_featured);
CREATE INDEX idx_categories_sort_order ON public.categories(sort_order);
CREATE INDEX idx_categories_path ON public.categories(path);

-- Full-text search index (using IMMUTABLE wrapper function)
CREATE INDEX idx_categories_search ON public.categories
USING gin(public.immutable_to_tsvector(name || ' ' || COALESCE(description, '')));

-- =====================================================
-- 4. PRODUCTS TABLE (Core Marketplace)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    shop_id UUID REFERENCES public.shops(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
    
    -- Basic product info
    name VARCHAR(200) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    
    -- Pricing
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    currency VARCHAR(3) DEFAULT 'USD',
    discount_price DECIMAL(10,2) CHECK (discount_price >= 0 AND discount_price < price),
    cost_price DECIMAL(10,2) CHECK (cost_price >= 0), -- For profit calculations
    
    -- Media
    images TEXT[] DEFAULT '{}',
    video_url TEXT,
    
    -- Inventory and status
    stock_quantity INTEGER DEFAULT 0 CHECK (stock_quantity >= 0),
    sku VARCHAR(100),
    condition VARCHAR(20) DEFAULT 'new' CHECK (condition IN ('new', 'used', 'refurbished')),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'inactive', 'sold', 'archived')),
    
    -- Product attributes
    brand VARCHAR(100),
    weight DECIMAL(8,3), -- in kg
    dimensions JSONB DEFAULT '{}', -- {length, width, height}
    tags TEXT[] DEFAULT '{}',
    specifications JSONB DEFAULT '{}',
    
    -- Performance metrics (denormalized)
    view_count INTEGER DEFAULT 0 CHECK (view_count >= 0),
    like_count INTEGER DEFAULT 0 CHECK (like_count >= 0),
    rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0 AND rating <= 5),
    review_count INTEGER DEFAULT 0 CHECK (review_count >= 0),
    
    -- SEO and marketing
    is_featured BOOLEAN DEFAULT FALSE,
    is_promoted BOOLEAN DEFAULT FALSE,
    promoted_until TIMESTAMPTZ,
    
    -- Shipping
    shipping_required BOOLEAN DEFAULT TRUE,
    shipping_weight DECIMAL(8,3),
    shipping_dimensions JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT unique_shop_sku UNIQUE(shop_id, sku),
    CONSTRAINT valid_currency CHECK (currency IN ('USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'))
);

-- Indexes for performance (critical for marketplace)
CREATE INDEX idx_products_shop_id ON public.products(shop_id);
CREATE INDEX idx_products_category_id ON public.products(category_id);
CREATE INDEX idx_products_status ON public.products(status);
CREATE INDEX idx_products_price ON public.products(price);
CREATE INDEX idx_products_created_at ON public.products(created_at DESC);
CREATE INDEX idx_products_rating ON public.products(rating DESC);
CREATE INDEX idx_products_view_count ON public.products(view_count DESC);
CREATE INDEX idx_products_is_featured ON public.products(is_featured);

-- Composite indexes for common queries
CREATE INDEX idx_products_active_category ON public.products(category_id, status) WHERE status = 'active';
CREATE INDEX idx_products_shop_active ON public.products(shop_id, status) WHERE status = 'active';
CREATE INDEX idx_products_featured_active ON public.products(is_featured, status, created_at DESC) WHERE status = 'active';

-- Full-text search index (critical for product discovery, using IMMUTABLE wrapper function)
CREATE INDEX idx_products_search ON public.products
USING gin(public.immutable_to_tsvector(name || ' ' || COALESCE(description, '') || ' ' || COALESCE(brand, '') || ' ' || public.immutable_array_to_string(tags, ' ')));

-- GIN index for tags array
CREATE INDEX idx_products_tags ON public.products USING gin(tags);

-- =====================================================
-- 5. POSTS TABLE (Social Media)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES public.products(id) ON DELETE SET NULL, -- Optional product link
    
    -- Content
    content TEXT,
    media_urls TEXT[] DEFAULT '{}',
    post_type VARCHAR(20) DEFAULT 'text' CHECK (post_type IN ('text', 'image', 'video', 'product', 'story')),
    
    -- Visibility and status
    is_public BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived', 'deleted')),
    
    -- Location and metadata
    location VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    mentions TEXT[] DEFAULT '{}', -- @username mentions
    hashtags TEXT[] DEFAULT '{}', -- #hashtag extraction
    
    -- Engagement metrics (denormalized for performance)
    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),
    comments_count INTEGER DEFAULT 0 CHECK (comments_count >= 0),
    shares_count INTEGER DEFAULT 0 CHECK (shares_count >= 0),
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT content_or_media_required CHECK (
        content IS NOT NULL AND content != '' OR 
        array_length(media_urls, 1) > 0
    )
);

-- Indexes for social media performance
CREATE INDEX idx_posts_user_id ON public.posts(user_id);
CREATE INDEX idx_posts_product_id ON public.posts(product_id);
CREATE INDEX idx_posts_created_at ON public.posts(created_at DESC);
CREATE INDEX idx_posts_is_public ON public.posts(is_public);
CREATE INDEX idx_posts_status ON public.posts(status);
CREATE INDEX idx_posts_post_type ON public.posts(post_type);

-- Composite indexes for feed queries
CREATE INDEX idx_posts_public_feed ON public.posts(is_public, status, created_at DESC) WHERE is_public = true AND status = 'published';
CREATE INDEX idx_posts_user_timeline ON public.posts(user_id, status, created_at DESC) WHERE status = 'published';

-- Full-text search index (using IMMUTABLE wrapper function)
CREATE INDEX idx_posts_search ON public.posts
USING gin(public.immutable_to_tsvector(COALESCE(content, '') || ' ' || public.immutable_array_to_string(tags, ' ') || ' ' || public.immutable_array_to_string(hashtags, ' ')));

-- GIN indexes for arrays
CREATE INDEX idx_posts_tags ON public.posts USING gin(tags);
CREATE INDEX idx_posts_hashtags ON public.posts USING gin(hashtags);
CREATE INDEX idx_posts_mentions ON public.posts USING gin(mentions);

-- =====================================================
-- 6. FOLLOWS TABLE (Social Relationships)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.follows (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    follower_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    following_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),

    -- Constraints
    CONSTRAINT unique_follow_relationship UNIQUE(follower_id, following_id),
    CONSTRAINT no_self_follow CHECK (follower_id != following_id)
);

-- Indexes for performance
CREATE INDEX idx_follows_follower_id ON public.follows(follower_id);
CREATE INDEX idx_follows_following_id ON public.follows(following_id);
CREATE INDEX idx_follows_created_at ON public.follows(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX idx_follows_follower_created ON public.follows(follower_id, created_at DESC);
CREATE INDEX idx_follows_following_created ON public.follows(following_id, created_at DESC);
