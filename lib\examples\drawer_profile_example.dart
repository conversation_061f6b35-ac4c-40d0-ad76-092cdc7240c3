import 'package:business_app/components/custom_advanced_drawer.dart';
import 'package:business_app/services/supabase_service.dart';
import 'package:business_app/models/auth/user_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_drawer/flutter_advanced_drawer.dart';

/// Example page demonstrating the dynamic drawer profile functionality
class DrawerProfileExamplePage extends StatefulWidget {
  const DrawerProfileExamplePage({super.key});

  @override
  State<DrawerProfileExamplePage> createState() =>
      _DrawerProfileExamplePageState();
}

class _DrawerProfileExamplePageState extends State<DrawerProfileExamplePage> {
  final _advancedDrawerController = AdvancedDrawerController();
  UserModel? currentUser;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    try {
      final user = await AuthService.getCurrentUserProfile();
      if (mounted) {
        setState(() {
          currentUser = user;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshDrawerProfile() async {
    // Refresh local data - the drawer will automatically update
    await _loadUserProfile();
    // Show a snackbar to indicate refresh
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Profile data refreshed'),
          backgroundColor: Color(0xFF1DA1F2),
          duration: Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomAdvancedDrawer(
      controller: _advancedDrawerController,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Dynamic Drawer Profile Example'),
          backgroundColor: const Color(0xFF1DA1F2),
          foregroundColor: Colors.white,
          leading: IconButton(
            onPressed: _handleMenuButtonPressed,
            icon: ValueListenableBuilder<AdvancedDrawerValue>(
              valueListenable: _advancedDrawerController,
              builder: (_, value, __) {
                return AnimatedSwitcher(
                  duration: const Duration(milliseconds: 250),
                  child: Icon(
                    value.visible ? Icons.clear : Icons.menu,
                    key: ValueKey<bool>(value.visible),
                  ),
                );
              },
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Dynamic Drawer Profile Demo',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              const Text(
                'This example demonstrates how the drawer displays dynamic user profile information from the database:',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 20),
              const Text(
                '• Profile picture (from database or fallback to default)',
                style: TextStyle(fontSize: 14),
              ),
              const Text('• User\'s full name', style: TextStyle(fontSize: 14)),
              const Text(
                '• Username with @ symbol',
                style: TextStyle(fontSize: 14),
              ),
              const Text(
                '• Followers count (displayed as "Customers")',
                style: TextStyle(fontSize: 14),
              ),
              const Text('• Following count', style: TextStyle(fontSize: 14)),
              const SizedBox(height: 30),
              if (isLoading)
                const Center(child: CircularProgressIndicator())
              else if (currentUser != null)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Current User Profile:',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text('Name: ${currentUser!.name}'),
                        Text('Username: @${currentUser!.username}'),
                        Text('Followers: ${currentUser!.followersCount}'),
                        Text('Following: ${currentUser!.followingCount}'),
                        Text('Email: ${currentUser!.email ?? 'Not provided'}'),
                        Text('Phone: ${currentUser!.phone ?? 'Not provided'}'),
                      ],
                    ),
                  ),
                )
              else
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text('No user profile found. Please log in.'),
                  ),
                ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _refreshDrawerProfile,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1DA1F2),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Refresh Drawer Profile'),
              ),
              const SizedBox(height: 10),
              const Text(
                'Pull down on the drawer content to refresh the profile data.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleMenuButtonPressed() {
    _advancedDrawerController.showDrawer();
  }
}
