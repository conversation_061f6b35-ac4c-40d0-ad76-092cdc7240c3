import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:business_app/bloc/profile_bloc/profile_bloc.dart';
import 'package:business_app/bloc/profile_bloc/profile_event.dart';
import 'package:business_app/bloc/profile_bloc/profile_state.dart';
import 'package:business_app/avatar/avatar.dart';
import 'package:business_app/services/supabase_service.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  late TextEditingController _nameController;
  late TextEditingController _usernameController;
  late TextEditingController _bioController;
  late FocusNode _nameFocus;
  late FocusNode _usernameFocus;
  late FocusNode _bioFocus;

  // Character limits
  static const int nameLimit = 50;
  static const int usernameLimit = 30;
  static const int bioLimit = 160;

  bool _hasChanges = false;
  String _originalUsername = '';
  String _originalName = '';
  String _originalBio = '';

  // Avatar system integration
  String? _currentProfileImageUrl;
  String? _currentBackgroundImageUrl;
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _usernameController = TextEditingController();
    _bioController = TextEditingController();
    _nameFocus = FocusNode();
    _usernameFocus = FocusNode();
    _bioFocus = FocusNode();

    // Initialize with current profile data
    final profileState = context.read<ProfileBloc>().state;
    _originalName = profileState.name;
    _originalUsername = profileState.username;
    _originalBio = profileState.bio;

    _nameController.text = _originalName;
    _usernameController.text = _originalUsername;
    _bioController.text = _originalBio;

    // Initialize avatar system variables
    _currentProfileImageUrl =
        profileState.profileImageUrl.isNotEmpty
            ? profileState.profileImageUrl
            : null;
    _currentBackgroundImageUrl =
        profileState.backgroundImageUrl.isNotEmpty
            ? profileState.backgroundImageUrl
            : null;
    _currentUserId = Supabase.instance.client.auth.currentUser?.id;
    print('👤 Current user ID: $_currentUserId');

    // Add listeners to detect changes
    _nameController.addListener(_onTextChanged);
    _usernameController.addListener(_onUsernameChanged);
    _bioController.addListener(_onTextChanged);
  }

  // Avatar system image handlers
  void _handleProfileImageChanged(String? imageUrl) {
    print('🖼️ Profile image changed: $imageUrl');
    setState(() {
      _currentProfileImageUrl = imageUrl;
      _hasChanges = true;
    });

    // Update database immediately when avatar widget uploads image
    if (imageUrl != null) {
      _updateProfileImageInDatabase(imageUrl);
    }
  }

  void _handleBackgroundImageChanged(String? imageUrl) {
    print('🌄 Background image changed: $imageUrl');
    setState(() {
      _currentBackgroundImageUrl = imageUrl;
      _hasChanges = true;
    });

    // Update database immediately when avatar widget uploads image
    if (imageUrl != null) {
      _updateBackgroundImageInDatabase(imageUrl);
    }
  }

  Future<void> _updateProfileImageInDatabase(String imageUrl) async {
    try {
      final currentUser = await AuthService.getCurrentUserProfile();
      if (currentUser != null) {
        final updatedUser = currentUser.copyWith(
          profileImageUrl: imageUrl,
          updatedAt: DateTime.now(),
        );
        final result = await AuthService.updateProfile(updatedUser);
        print('✅ Profile image updated in database: $imageUrl');

        // Verify the update worked by checking the returned user
        if (result.profileImageUrl == imageUrl) {
          print('✅ Database update verified successfully');
        } else {
          print('⚠️ Database update may have failed - URL mismatch');
          print('Expected: $imageUrl');
          print('Got: ${result.profileImageUrl}');
        }
      }
    } catch (e) {
      print('❌ Failed to update profile image in database: $e');
      // Show error to user
      if (mounted) {
        _showSpotifyStyleError('Unable to upload file. Please try again');
      }
    }
  }

  Future<void> _updateBackgroundImageInDatabase(String imageUrl) async {
    try {
      final currentUser = await AuthService.getCurrentUserProfile();
      if (currentUser != null) {
        final updatedUser = currentUser.copyWith(
          backgroundImageUrl: imageUrl,
          updatedAt: DateTime.now(),
        );
        final result = await AuthService.updateProfile(updatedUser);
        print('✅ Background image updated in database: $imageUrl');

        // Verify the update worked by checking the returned user
        if (result.backgroundImageUrl == imageUrl) {
          print('✅ Database update verified successfully');
        } else {
          print('⚠️ Database update may have failed - URL mismatch');
          print('Expected: $imageUrl');
          print('Got: ${result.backgroundImageUrl}');
        }
      }
    } catch (e) {
      print('❌ Failed to update background image in database: $e');
      // Show error to user
      if (mounted) {
        _showSpotifyStyleError('Unable to upload file. Please try again');
      }
    }
  }

  void _handleImageError(String error) {
    print('❌ Image error: $error');
    _showSpotifyStyleError('Unable to upload file. Please try again');
  }

  void _onTextChanged() {
    setState(() {
      _hasChanges = true;
    });
  }

  void _onUsernameChanged() {
    setState(() {
      _hasChanges = true;
    });

    final username = _usernameController.text.trim();

    // Clear validation if username is empty or same as original
    if (username.isEmpty || username == _originalUsername) {
      context.read<ProfileBloc>().add(ClearUsernameValidationEvent());
      return;
    }

    // Only validate if username has minimum length (like Telegram)
    if (username.length >= 3) {
      // Trigger username validation with debounce handled in ProfileBloc
      context.read<ProfileBloc>().add(
        CheckUsernameAvailabilityEvent(username: username),
      );
    } else {
      // Clear validation for usernames shorter than 3 characters
      context.read<ProfileBloc>().add(ClearUsernameValidationEvent());
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    _nameFocus.dispose();
    _usernameFocus.dispose();
    _bioFocus.dispose();
    super.dispose();
  }

  void _saveProfile() {
    if (!_hasChanges) {
      Navigator.pop(context);
      return;
    }

    // Check if username validation is in progress or failed
    final profileState = context.read<ProfileBloc>().state;
    final username = _usernameController.text.trim();

    if (username != _originalUsername) {
      if (profileState.usernameValidationStatus ==
          UsernameValidationStatus.checking) {
        _showSpotifyStyleError(
          'Please wait for username validation to complete',
        );
        return;
      }

      if (profileState.usernameValidationStatus ==
          UsernameValidationStatus.unavailable) {
        _showSpotifyStyleError(
          profileState.usernameValidationError ?? 'Username not available',
        );
        return;
      }

      if (profileState.usernameValidationStatus ==
          UsernameValidationStatus.error) {
        _showSpotifyStyleError(
          'Failed to validate username. Please try again.',
        );
        return;
      }
    }

    // Trigger profile update
    // Note: Images are already uploaded and updated via avatar system
    // Only pass the current URLs, not the file objects
    context.read<ProfileBloc>().add(
      UpdateProfileEvent(
        name: _nameController.text.trim(),
        username: username,
        bio: _bioController.text.trim(),
        profileImageUrl: _currentProfileImageUrl,
        backgroundImageUrl: _currentBackgroundImageUrl,
        // Don't pass file objects since images are already uploaded
        profileImageFile: null,
        backgroundImageFile: null,
      ),
    );
  }

  void _showSpotifyStyleError(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).dialogBackgroundColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  message,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        elevation: 0,
        content: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade600,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        duration: const Duration(milliseconds: 800),
      ),
    );
  }

  void _discardChanges() {
    if (_hasChanges) {
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: const Text(
                'Discard changes?',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              content: const Text(
                'You have unsaved changes. Are you sure you want to discard them?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                    Navigator.pop(context); // Close edit page
                  },
                  child: const Text(
                    'Discard',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
      );
    } else {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<ProfileBloc, ProfileState>(
      listener: (context, state) {
        if (state.status == ProfileStatus.loaded) {
          _showSuccessMessage('Profile updated successfully');
          Navigator.pop(context);
        } else if (state.status == ProfileStatus.error) {
          _showSpotifyStyleError('Profile not updated. Please try again');
        }
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.scaffoldBackgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: _discardChanges,
          ),
          title: const Text(
            'Edit profile',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          actions: [
            BlocBuilder<ProfileBloc, ProfileState>(
              builder: (context, state) {
                if (state.status == ProfileStatus.updating) {
                  return const Padding(
                    padding: EdgeInsets.all(16),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFF1DA1F2),
                        ),
                      ),
                    ),
                  );
                }

                return TextButton(
                  onPressed: _hasChanges ? _saveProfile : null,
                  child: Text(
                    'Save',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color:
                          _hasChanges ? const Color(0xFF1DA1F2) : Colors.grey,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  // Background Photo Section - Enhanced with Avatar System
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: BackgroundImageWidget(
                      currentImageUrl: _currentBackgroundImageUrl,
                      onImageChanged: _handleBackgroundImageChanged,
                      onError: _handleImageError,
                      height: 200,
                      isEditable: true,
                      userId: _currentUserId,
                      overlayWidget: Container(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _nameController.text.isNotEmpty
                                  ? _nameController.text
                                  : 'Your Name',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    offset: Offset(0, 1),
                                    blurRadius: 3,
                                    color: Colors.black54,
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              '@${_usernameController.text.isNotEmpty ? _usernameController.text : 'username'}',
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                                shadows: [
                                  Shadow(
                                    offset: Offset(0, 1),
                                    blurRadius: 3,
                                    color: Colors.black54,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Spacing to accommodate floating profile photo
                  const SizedBox(height: 60),

                  // Profile photo hint text
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 0),
                        child: Text(
                          'Tap to change profile photo',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Form Fields
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      children: [
                        _buildTextField(
                          controller: _nameController,
                          focusNode: _nameFocus,
                          label: 'Name',
                          maxLength: nameLimit,
                          hintText: 'Enter your name',
                          prefixIcon: Icons.person_outline,
                        ),
                        const SizedBox(height: 24),
                        _buildTextField(
                          controller: _usernameController,
                          focusNode: _usernameFocus,
                          label: 'Username',
                          maxLength: usernameLimit,
                          hintText: 'Enter your username',
                          prefixIcon: Icons.alternate_email,
                        ),
                        const SizedBox(height: 24),
                        _buildTextField(
                          controller: _bioController,
                          focusNode: _bioFocus,
                          label: 'Bio',
                          maxLength: bioLimit,
                          hintText: 'Tell people about yourself...',
                          prefixIcon: Icons.description_outlined,
                          maxLines: 4,
                        ),
                        const SizedBox(height: 24),

                        // Image Configuration Info
                        _buildImageConfigInfo(),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Floating Profile Picture - Enhanced with Avatar System
            Positioned(
              top: 160, // Position it to overlap the background image
              left:
                  48, // Adjusted to account for background image padding (24 + 24)
              child: Material(
                color: Colors.transparent,
                elevation: 8,
                shape: const CircleBorder(),
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Colors.blue.withOpacity(0.1),
                        Colors.purple.withOpacity(0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(4),
                  child: ProfileImageWidget(
                    currentImageUrl: _currentProfileImageUrl,
                    onImageChanged: _handleProfileImageChanged,
                    onError: _handleImageError,
                    radius: 40,
                    isEditable: true,
                    userId: _currentUserId,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageConfigInfo() {
    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 20,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Image Requirements',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.titleMedium?.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildConfigRow('Profile Image', ImageConfig.profile),
            const SizedBox(height: 8),
            _buildConfigRow('Background Image', ImageConfig.background),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigRow(String title, ImageConfig config) {
    final maxSizeMB = (config.maxFileSizeBytes / (1024 * 1024)).toStringAsFixed(
      1,
    );
    final formats = config.allowedExtensions.join(', ');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
        ),
        const SizedBox(height: 2),
        Text(
          'Max size: ${maxSizeMB}MB • Formats: $formats',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required int maxLength,
    required String hintText,
    required IconData prefixIcon,
    int maxLines = 1,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isUsernameField = label == 'Username';

    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        // Determine border color based on validation status for username field
        Color borderColor;
        if (isUsernameField && controller.text.trim() != _originalUsername) {
          switch (state.usernameValidationStatus) {
            case UsernameValidationStatus.checking:
              borderColor = Colors.orange;
              break;
            case UsernameValidationStatus.available:
              borderColor = Colors.green;
              break;
            case UsernameValidationStatus.unavailable:
            case UsernameValidationStatus.error:
              borderColor = Colors.red;
              break;
            default:
              borderColor =
                  focusNode.hasFocus
                      ? const Color(0xFF1DA1F2)
                      : isDark
                      ? Colors.grey[700]!
                      : Colors.grey[300]!;
          }
        } else {
          borderColor =
              focusNode.hasFocus
                  ? const Color(0xFF1DA1F2)
                  : isDark
                  ? Colors.grey[700]!
                  : Colors.grey[300]!;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: borderColor,
                  width: focusNode.hasFocus ? 2 : 1,
                ),
                color: isDark ? Colors.grey[900] : Colors.grey[50],
                boxShadow:
                    focusNode.hasFocus
                        ? [
                          BoxShadow(
                            color: borderColor.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                        : null,
              ),
              child: TextField(
                controller: controller,
                focusNode: focusNode,
                maxLines: maxLines,
                style: TextStyle(
                  fontSize: 16,
                  color: theme.textTheme.bodyLarge?.color,
                  fontWeight: FontWeight.w500,
                ),
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle: TextStyle(fontSize: 16, color: Colors.grey[500]),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: maxLines > 1 ? 16 : 14,
                  ),
                  prefixIcon: Icon(
                    prefixIcon,
                    color: focusNode.hasFocus ? borderColor : Colors.grey[500],
                    size: 22,
                  ),
                  suffixIcon:
                      isUsernameField &&
                              controller.text.trim() != _originalUsername
                          ? _buildUsernameValidationIcon(state)
                          : null,
                  counterText: '${controller.text.length}/$maxLength',
                  counterStyle: TextStyle(
                    color:
                        controller.text.length >= maxLength * 0.9
                            ? Colors.orange
                            : Colors.grey[500],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                onChanged: (value) {
                  // For username field, changes are handled by controller listener
                  // For other fields, this sets _hasChanges = true
                  if (!isUsernameField) {
                    setState(() {
                      _hasChanges = true;
                    });
                  }
                },
                inputFormatters: [
                  LengthLimitingTextInputFormatter(maxLength),
                  if (isUsernameField)
                    FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_]')),
                ],
              ),
            ),
            // Username validation message
            if (isUsernameField && controller.text.trim() != _originalUsername)
              Padding(
                padding: const EdgeInsets.only(top: 8, left: 16),
                child: _buildUsernameValidationMessage(state),
              ),
          ],
        );
      },
    );
  }

  Widget _buildUsernameValidationIcon(ProfileState state) {
    switch (state.usernameValidationStatus) {
      case UsernameValidationStatus.checking:
        return Container(
          padding: const EdgeInsets.all(12),
          child: const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
          ),
        );
      case UsernameValidationStatus.available:
        return Container(
          padding: const EdgeInsets.all(12),
          child: const Icon(Icons.check_circle, color: Colors.green, size: 20),
        );
      case UsernameValidationStatus.unavailable:
        return Container(
          padding: const EdgeInsets.all(12),
          child: const Icon(Icons.cancel, color: Colors.red, size: 20),
        );
      case UsernameValidationStatus.error:
        return Container(
          padding: const EdgeInsets.all(12),
          child: const Icon(
            Icons.error_outline,
            color: Colors.orange,
            size: 20,
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildUsernameValidationMessage(ProfileState state) {
    switch (state.usernameValidationStatus) {
      case UsernameValidationStatus.checking:
        return Row(
          children: [
            const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Checking availability...',
              style: TextStyle(
                color: Colors.orange[600],
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      case UsernameValidationStatus.available:
        return Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 14),
            const SizedBox(width: 6),
            Text(
              'Username is available',
              style: TextStyle(
                color: Colors.green[600],
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      case UsernameValidationStatus.unavailable:
        return Row(
          children: [
            const Icon(Icons.cancel, color: Colors.red, size: 14),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                state.usernameValidationError ?? 'Username not available',
                style: TextStyle(
                  color: Colors.red[600],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      case UsernameValidationStatus.error:
        return Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.orange, size: 14),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                state.usernameValidationError ?? 'Failed to check availability',
                style: TextStyle(
                  color: Colors.orange[600],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }
}
