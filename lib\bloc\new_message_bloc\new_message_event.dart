import 'package:equatable/equatable.dart';

abstract class NewMessageEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class LoadContactsEvent extends NewMessageEvent {}

class LoadSuggestedContactsEvent extends NewMessageEvent {}

class LoadRecentContactsEvent extends NewMessageEvent {}

class SearchContactsEvent extends NewMessageEvent {
  final String query;

  SearchContactsEvent(this.query);

  @override
  List<Object> get props => [query];
}

class ClearSearchEvent extends NewMessageEvent {}

class ToggleContactSelectionEvent extends NewMessageEvent {
  final String contactId;

  ToggleContactSelectionEvent(this.contactId);

  @override
  List<Object> get props => [contactId];
}

class ChangePageEvent extends NewMessageEvent {
  final int pageIndex;

  ChangePageEvent(this.pageIndex);

  @override
  List<Object> get props => [pageIndex];
}

class StartConversationWithContactEvent extends NewMessageEvent {
  final String contactId;
  final String contactName;

  StartConversationWithContactEvent(this.contactId, this.contactName);

  @override
  List<Object> get props => [contactId, contactName];
}

class RefreshContactsEvent extends NewMessageEvent {}

class LoadMoreContactsEvent extends NewMessageEvent {}
