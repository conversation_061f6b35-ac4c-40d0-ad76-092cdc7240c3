import 'package:equatable/equatable.dart';

abstract class CategorySelectionEvent extends Equatable {
  const CategorySelectionEvent();

  @override
  List<Object?> get props => [];
}

class LoadCategories extends CategorySelectionEvent {
  const LoadCategories();
}

class ToggleCategorySelection extends CategorySelectionEvent {
  final String categoryId;

  const ToggleCategorySelection({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class SelectCategory extends CategorySelectionEvent {
  final String categoryId;

  const SelectCategory({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class DeselectCategory extends CategorySelectionEvent {
  final String categoryId;

  const DeselectCategory({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class ClearAllSelections extends CategorySelectionEvent {
  const ClearAllSelections();
}

class ValidateSelection extends CategorySelectionEvent {
  const ValidateSelection();
}

class ResetCategorySelection extends CategorySelectionEvent {
  const ResetCategorySelection();
}
