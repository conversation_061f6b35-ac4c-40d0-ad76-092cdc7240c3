import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/new_message_bloc/new_message_bloc.dart';
import 'package:business_app/bloc/new_message_bloc/new_message_event.dart';
import 'package:business_app/bloc/new_message_bloc/new_message_state.dart';
import 'package:business_app/bloc/chat_bloc/chat_bloc.dart';
import 'package:business_app/bloc/chat_bloc/chat_event.dart';

class NewMessage extends StatefulWidget {
  const NewMessage({super.key});

  @override
  State<NewMessage> createState() => _NewMessageState();
}

class _NewMessageState extends State<NewMessage> {
  final PageController _pageController = PageController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load initial data
    context.read<NewMessageBloc>().add(LoadContactsEvent());
    context.read<NewMessageBloc>().add(LoadSuggestedContactsEvent());
    context.read<NewMessageBloc>().add(LoadRecentContactsEvent());
  }

  @override
  void dispose() {
    _pageController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return BlocBuilder<NewMessageBloc, NewMessageState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            centerTitle: true,
            title: const Text(
              'New Message',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
            ),
            actions: [
              if (state.selectedContactIds.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.check),
                  onPressed:
                      () => _startConversationWithSelectedContacts(
                        context,
                        state,
                      ),
                ),
            ],
          ),
          body: Column(
            children: [
              // Search Bar
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: TextField(
                    controller: _searchController,
                    style: const TextStyle(color: Colors.white),
                    onChanged: (query) {
                      context.read<NewMessageBloc>().add(
                        SearchContactsEvent(query),
                      );
                    },
                    decoration: InputDecoration(
                      hintText: 'Search contacts...',
                      hintStyle: const TextStyle(color: Colors.white54),
                      prefixIcon: const Icon(
                        Icons.search,
                        color: Colors.white54,
                      ),
                      suffixIcon:
                          state.searchQuery.isNotEmpty
                              ? IconButton(
                                icon: const Icon(
                                  Icons.clear,
                                  color: Colors.white54,
                                ),
                                onPressed: () {
                                  _searchController.clear();
                                  context.read<NewMessageBloc>().add(
                                    ClearSearchEvent(),
                                  );
                                },
                              )
                              : null,
                      border: InputBorder.none,
                    ),
                  ),
                ),
              ),

              // Loading indicator
              if (state.status == NewMessageStatus.loading)
                const Expanded(
                  child: Center(child: CircularProgressIndicator()),
                ),

              // Error state
              if (state.status == NewMessageStatus.error)
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          state.errorMessage ?? 'An error occurred',
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 16.h),
                        ElevatedButton(
                          onPressed: () {
                            context.read<NewMessageBloc>().add(
                              RefreshContactsEvent(),
                            );
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),

              // Content
              if (state.status == NewMessageStatus.loaded ||
                  state.status == NewMessageStatus.searching)
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    onPageChanged: (index) {
                      context.read<NewMessageBloc>().add(
                        ChangePageEvent(index),
                      );
                    },
                    children: [
                      CategorySection(
                        title: "Contacts",
                        contacts:
                            state.isSearching
                                ? state.filteredContacts
                                : state.contacts,
                        selectedContactIds: state.selectedContactIds,
                        onContactTap:
                            (contact) => _handleContactTap(context, contact),
                        onContactLongPress:
                            (contact) =>
                                _handleContactSelection(context, contact),
                      ),
                      CategorySection(
                        title: "Suggested",
                        subtitle: "From your phone",
                        contacts: state.suggestedContacts,
                        selectedContactIds: state.selectedContactIds,
                        onContactTap:
                            (contact) => _handleContactTap(context, contact),
                        onContactLongPress:
                            (contact) =>
                                _handleContactSelection(context, contact),
                      ),
                      CategorySection(
                        title: "Recents",
                        contacts: state.recentContacts,
                        selectedContactIds: state.selectedContactIds,
                        onContactTap:
                            (contact) => _handleContactTap(context, contact),
                        onContactLongPress:
                            (contact) =>
                                _handleContactSelection(context, contact),
                      ),
                    ],
                  ),
                ),

              // Page indicator
              if (state.status == NewMessageStatus.loaded ||
                  state.status == NewMessageStatus.searching)
                Column(
                  children: [
                    SizedBox(height: 12.h),
                    SmoothPageIndicator(
                      controller: _pageController,
                      count: 3,
                      effect: WormEffect(
                        activeDotColor: Colors.blue,
                        dotColor: Colors.grey,
                        dotHeight: 8.h,
                        dotWidth: 8.w,
                      ),
                    ),
                    SizedBox(height: 16.h),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  void _handleContactTap(BuildContext context, Contact contact) {
    // Start conversation with single contact
    context.read<ChatBloc>().add(
      StartNewConversationEvent(contact.id, contact.name),
    );
    Navigator.of(context).pop(); // Return to messages screen
  }

  void _handleContactSelection(BuildContext context, Contact contact) {
    context.read<NewMessageBloc>().add(ToggleContactSelectionEvent(contact.id));
  }

  void _startConversationWithSelectedContacts(
    BuildContext context,
    NewMessageState state,
  ) {
    // For now, start conversation with the first selected contact
    // In a real app, you might want to create a group chat
    if (state.selectedContactIds.isNotEmpty) {
      final firstContactId = state.selectedContactIds.first;
      final contact = _findContactById(state, firstContactId);
      if (contact != null) {
        context.read<ChatBloc>().add(
          StartNewConversationEvent(contact.id, contact.name),
        );
        Navigator.of(context).pop();
      }
    }
  }

  Contact? _findContactById(NewMessageState state, String contactId) {
    // Search in all contact lists
    for (final contact in [
      ...state.contacts,
      ...state.suggestedContacts,
      ...state.recentContacts,
    ]) {
      if (contact.id == contactId) {
        return contact;
      }
    }
    return null;
  }
}

class CategorySection extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Contact> contacts;
  final Set<String> selectedContactIds;
  final Function(Contact) onContactTap;
  final Function(Contact) onContactLongPress;

  const CategorySection({
    super.key,
    required this.title,
    required this.contacts,
    required this.selectedContactIds,
    required this.onContactTap,
    required this.onContactLongPress,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
          shrinkWrap: true,
          children: [
            SizedBox(height: 10.h),
            Text(
              title,
              style: TextStyle(fontSize: 22.sp, fontWeight: FontWeight.bold),
            ),
            if (subtitle != null)
              Text(subtitle!, style: TextStyle(fontSize: 14.sp)),
            SizedBox(height: 10.h),
            if (contacts.isEmpty)
              Padding(
                padding: EdgeInsets.symmetric(vertical: 20.h),
                child: Center(
                  child: Text(
                    'No contacts found',
                    style: TextStyle(fontSize: 16.sp, color: Colors.grey),
                  ),
                ),
              )
            else
              ...contacts.map((contact) {
                final isSelected = selectedContactIds.contains(contact.id);
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: EdgeInsets.symmetric(vertical: 6.h),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Colors.blue.withValues(alpha: 0.1)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8.r),
                    border:
                        isSelected
                            ? Border.all(color: Colors.blue, width: 1)
                            : null,
                  ),
                  child: ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: Stack(
                      children: [
                        Container(
                          width: 48.w,
                          height: 48.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30.r),
                            color: Colors.grey[800],
                          ),
                          child:
                              contact.profileImage != null
                                  ? ClipRRect(
                                    borderRadius: BorderRadius.circular(30.r),
                                    child: Image.asset(
                                      contact.profileImage!,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              const Icon(
                                                Icons.person,
                                                color: Colors.white54,
                                              ),
                                    ),
                                  )
                                  : const Icon(
                                    Icons.person,
                                    color: Colors.white54,
                                  ),
                        ),
                        if (contact.isOnline)
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              width: 12.w,
                              height: 12.w,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    title: Text(
                      contact.name,
                      style: TextStyle(
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    subtitle: Text(
                      contact.phoneNumber,
                      style: const TextStyle(fontWeight: FontWeight.normal),
                    ),
                    trailing:
                        isSelected
                            ? Icon(
                              Icons.check_circle,
                              color: Colors.blue,
                              size: 24.sp,
                            )
                            : Icon(
                              Icons.message,
                              color: Colors.blue,
                              size: 20.sp,
                            ),
                    onTap: () => onContactTap(contact),
                    onLongPress: () => onContactLongPress(contact),
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }
}

// Required packages:
// flutter_screenutil: ^5.9.0
// smooth_page_indicator: ^1.1.0
