-- =====================================================
-- FIX PHONE SIGNUP ISSUES
-- Migration to fix database constraints for phone-only users
-- =====================================================

-- 1. Make email field nullable in profiles table
ALTER TABLE public.profiles ALTER COLUMN email DROP NOT NULL;

-- 2. Update the create_user_profile function to handle phone-only users
CREATE OR REPLACE FUNCTION public.create_user_profile()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_phone TEXT;
    generated_username TEXT;
    display_name TEXT;
BEGIN
    -- Extract email and phone from the user record
    user_email := NEW.email;
    user_phone := NEW.phone;
    
    -- Generate username based on available information
    IF user_email IS NOT NULL THEN
        generated_username := public.generate_username(user_email);
        display_name := COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(user_email, '@', 1));
    ELSIF user_phone IS NOT NULL THEN
        generated_username := 'user_' || substring(replace(user_phone, '+', ''), 1, 8) || '_' || extract(epoch from now())::text;
        display_name := COALESCE(NEW.raw_user_meta_data->>'full_name', 'User');
    ELSE
        -- Fallback for edge cases
        generated_username := 'user_' || NEW.id::text;
        display_name := COALESCE(NEW.raw_user_meta_data->>'full_name', 'User');
    END IF;

    INSERT INTO public.profiles (
        id,
        username,
        full_name,
        email,
        phone,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        generated_username,
        display_name,
        user_email,
        user_phone,
        NOW(),
        NOW()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Add constraint to ensure either email or phone exists
ALTER TABLE public.profiles ADD CONSTRAINT profiles_contact_check 
CHECK (email IS NOT NULL OR phone IS NOT NULL);

-- 4. Create index for phone field for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_phone ON public.profiles(phone);

-- 5. Update RLS policies to handle phone-only users
-- Update the profiles select policy to work with phone users
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
CREATE POLICY "profiles_select_policy" ON public.profiles
FOR SELECT USING (
    -- Own profile
    auth.uid() = id OR
    -- Public profiles (non-private)
    is_private = false OR
    -- Following relationship exists
    EXISTS (
        SELECT 1 FROM public.follows 
        WHERE follower_id = auth.uid() AND following_id = id
    )
);
