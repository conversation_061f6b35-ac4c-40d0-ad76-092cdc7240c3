import 'package:flutter/material.dart';
import 'package:business_app/services/supabase_service.dart';
import 'package:business_app/utils/auth_test_helper.dart';

class UserExistenceTestPage extends StatefulWidget {
  const UserExistenceTestPage({super.key});

  @override
  State<UserExistenceTestPage> createState() => _UserExistenceTestPageState();
}

class _UserExistenceTestPageState extends State<UserExistenceTestPage> {
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  String _testResults = '';
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _testUserExistence() async {
    setState(() {
      _isLoading = true;
      _testResults = 'Running tests...';
    });

    try {
      final email = _emailController.text.trim();
      final phone = _phoneController.text.trim();

      // Test individual methods
      final results = StringBuffer();
      results.writeln('=== User Existence Test Results ===\n');

      if (email.isNotEmpty) {
        results.writeln('Testing email: $email');
        final emailExists = await AuthService.checkUserExistsByEmail(email);
        results.writeln('Email exists: $emailExists\n');
      }

      if (phone.isNotEmpty) {
        results.writeln('Testing phone: $phone');
        final phoneExists = await AuthService.checkUserExistsByPhone(phone);
        results.writeln('Phone exists: $phoneExists\n');
      }

      // Run comprehensive test
      results.writeln('Running comprehensive test...');
      final testResult = await AuthTestHelper.testUserExistenceCheck(
        email: email,
        phone: phone,
      );

      results.writeln(
        'Test Result: ${testResult.status == TestStatus.success ? 'SUCCESS' : 'FAILED'}',
      );
      results.writeln('Message: ${testResult.message}');

      setState(() {
        _testResults = results.toString();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResults = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Existence Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test User Existence Check',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email to test',
                hintText: 'Enter email address',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone to test',
                hintText: 'Enter phone number (e.g., +265123456789)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 20),

            ElevatedButton(
              onPressed: _isLoading ? null : _testUserExistence,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child:
                  _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text('Run Test'),
            ),
            const SizedBox(height: 20),

            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults.isEmpty
                        ? 'Test results will appear here...'
                        : _testResults,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),
            const Text(
              'Instructions:\n'
              '1. Enter an email or phone number to test\n'
              '2. Click "Run Test" to check if user exists\n'
              '3. Try with existing and non-existing users\n'
              '4. Check the console for detailed logs',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
