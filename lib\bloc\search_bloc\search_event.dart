import 'package:equatable/equatable.dart';

abstract class SearchEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class SearchQueryChangedEvent extends SearchEvent {
  final String query;

  SearchQueryChangedEvent(this.query);

  @override
  List<Object> get props => [query];
}

class SearchSubmittedEvent extends SearchEvent {
  final String query;

  SearchSubmittedEvent(this.query);

  @override
  List<Object> get props => [query];
}

class ClearSearchEvent extends SearchEvent {}

class LoadSearchHistoryEvent extends SearchEvent {}

class AddToSearchHistoryEvent extends SearchEvent {
  final String query;

  AddToSearchHistoryEvent(this.query);

  @override
  List<Object> get props => [query];
}

class RemoveFromSearchHistoryEvent extends SearchEvent {
  final String query;

  RemoveFromSearchHistoryEvent(this.query);

  @override
  List<Object> get props => [query];
}

class ClearSearchHistoryEvent extends SearchEvent {}

class ToggleSearchFilterEvent extends SearchEvent {
  final String filter;

  ToggleSearchFilterEvent(this.filter);

  @override
  List<Object> get props => [filter];
}

class LoadTrendingSearchesEvent extends SearchEvent {}

class SelectSearchSuggestionEvent extends SearchEvent {
  final String suggestion;

  SelectSearchSuggestionEvent(this.suggestion);

  @override
  List<Object> get props => [suggestion];
}
