import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/posts_bloc/posts_bloc.dart';
import 'package:business_app/bloc/posts_bloc/posts_event.dart';
import 'package:business_app/bloc/posts_bloc/posts_state.dart';
import 'package:business_app/ui/user_profiles/components/pages/widgets/post_image_grid.dart';
import 'package:business_app/ui/user_profiles/components/pages/widgets/expandable_text.dart';

class UserPostCard extends StatelessWidget {
  final Post post;
  final VoidCallback onMoreActions;

  const UserPostCard({
    super.key,
    required this.post,
    required this.onMoreActions,
  });

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          bottom: BorderSide(
            color: isDark ? Colors.grey[800]! : Colors.grey[200]!,
            width: 0.5,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with profile info and more button
            Row(
              children: [
                // Profile Image
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.grey[300],
                  backgroundImage:
                      post.authorProfileImage?.isNotEmpty == true
                          ? AssetImage(post.authorProfileImage!)
                          : null,
                  child:
                      post.authorProfileImage?.isEmpty != false
                          ? Icon(
                            Icons.person,
                            color: Colors.grey[600],
                            size: 20,
                          )
                          : null,
                ),
                const SizedBox(width: 12),

                // Name and username
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              post.authorName,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: isDark ? Colors.white : Colors.black,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.verified,
                            color: const Color(0xFF1DA1F2),
                            size: 16,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              post.authorUsername,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            ' · ${_formatTimeAgo(post.createdAt)}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // More actions button
                IconButton(
                  onPressed: onMoreActions,
                  icon: Icon(Icons.more_horiz, color: Colors.grey[600]),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Post content
            if (post.content.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: ExpandablePostText(
                  text: post.content,
                  style: TextStyle(
                    fontSize: 16,
                    height: 1.4,
                    color: isDark ? Colors.white : Colors.black,
                  ),
                ),
              ),

            // Post images
            if (post.images.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: PostImageGrid(images: post.images),
              ),

            // Engagement stats
            Row(
              children: [
                // Likes
                BlocBuilder<PostsBloc, PostsState>(
                  builder: (context, state) {
                    final currentPost = state.posts.firstWhere(
                      (p) => p.id == post.id,
                      orElse: () => post,
                    );

                    return GestureDetector(
                      onTap: () {
                        context.read<PostsBloc>().add(ToggleLikeEvent(post.id));
                      },
                      child: Row(
                        children: [
                          Icon(
                            currentPost.isLiked
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color:
                                currentPost.isLiked
                                    ? Colors.red
                                    : Colors.grey[600],
                            size: 20,
                          ),
                          if (currentPost.likesCount > 0) ...[
                            const SizedBox(width: 4),
                            Text(
                              _formatCount(currentPost.likesCount),
                              style: TextStyle(
                                color:
                                    currentPost.isLiked
                                        ? Colors.red
                                        : Colors.grey[600],
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                ),

                const SizedBox(width: 24),

                // Comments
                Row(
                  children: [
                    Icon(
                      Icons.mode_comment_outlined,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    if (post.commentsCount > 0) ...[
                      const SizedBox(width: 4),
                      Text(
                        _formatCount(post.commentsCount),
                        style: TextStyle(color: Colors.grey[600], fontSize: 13),
                      ),
                    ],
                  ],
                ),

                const SizedBox(width: 24),

                // Shares
                Row(
                  children: [
                    Icon(Icons.repeat, color: Colors.grey[600], size: 20),
                    if (post.sharesCount > 0) ...[
                      const SizedBox(width: 4),
                      Text(
                        _formatCount(post.sharesCount),
                        style: TextStyle(color: Colors.grey[600], fontSize: 13),
                      ),
                    ],
                  ],
                ),

                const Spacer(),

                // Bookmark
                BlocBuilder<PostsBloc, PostsState>(
                  builder: (context, state) {
                    final currentPost = state.posts.firstWhere(
                      (p) => p.id == post.id,
                      orElse: () => post,
                    );

                    return GestureDetector(
                      onTap: () {
                        context.read<PostsBloc>().add(
                          ToggleBookmarkEvent(post.id),
                        );
                      },
                      child: Icon(
                        currentPost.isBookmarked
                            ? Icons.bookmark
                            : Icons.bookmark_border,
                        color:
                            currentPost.isBookmarked
                                ? const Color(0xFF1DA1F2)
                                : Colors.grey[600],
                        size: 20,
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
