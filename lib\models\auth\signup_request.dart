import 'package:equatable/equatable.dart';

class SignupRequest extends Equatable {
  final String name;
  final String username;
  final String password;
  final String? email;
  final String? phone;
  final DateTime dateOfBirth;
  final String? profileImagePath;
  final String? bio;
  final String verificationCode;

  const SignupRequest({
    required this.name,
    required this.username,
    required this.password,
    this.email,
    this.phone,
    required this.dateOfBirth,
    this.profileImagePath,
    this.bio,
    required this.verificationCode,
  });

  // Factory constructor for creating SignupRequest from JSON
  factory SignupRequest.fromJson(Map<String, dynamic> json) {
    return SignupRequest(
      name: json['name'] as String,
      username: json['username'] as String,
      password: json['password'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      dateOfBirth: DateTime.parse(json['date_of_birth'] as String),
      profileImagePath: json['profile_image_path'] as String?,
      bio: json['bio'] as String?,
      verificationCode: json['verification_code'] as String,
    );
  }

  // Convert SignupRequest to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'username': username,
      'password': password,
      'email': email,
      'phone': phone,
      'date_of_birth': dateOfBirth.toIso8601String(),
      'profile_image_path': profileImagePath,
      'bio': bio,
      'verification_code': verificationCode,
    };
  }

  // Create a copy with updated fields
  SignupRequest copyWith({
    String? name,
    String? username,
    String? password,
    String? email,
    String? phone,
    DateTime? dateOfBirth,
    String? profileImagePath,
    String? bio,
    String? verificationCode,
  }) {
    return SignupRequest(
      name: name ?? this.name,
      username: username ?? this.username,
      password: password ?? this.password,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      bio: bio ?? this.bio,
      verificationCode: verificationCode ?? this.verificationCode,
    );
  }

  // Validation methods
  bool get isValidName {
    return name.trim().isNotEmpty && name.trim().length >= 2;
  }

  bool get isValidUsername {
    return RegExp(r'^[a-zA-Z0-9_]{3,30}$').hasMatch(username);
  }

  bool get isValidPassword {
    // At least 8 characters, contains letter and number
    return password.length >= 8 && 
           RegExp(r'^(?=.*[a-zA-Z])(?=.*\d)').hasMatch(password);
  }

  bool get isValidEmail {
    if (email == null) return true; // Email is optional if phone is provided
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email!);
  }

  bool get isValidPhone {
    if (phone == null) return true; // Phone is optional if email is provided
    return RegExp(r'^\+?[1-9]\d{1,14}$')
        .hasMatch(phone!.replaceAll(RegExp(r'[\s\-\(\)]'), ''));
  }

  bool get hasContactInfo {
    return (email != null && email!.isNotEmpty) || 
           (phone != null && phone!.isNotEmpty);
  }

  bool get isValidDateOfBirth {
    final now = DateTime.now();
    final age = now.year - dateOfBirth.year;
    
    // Must be at least 13 years old
    if (age < 13) return false;
    
    // Check if birthday has occurred this year
    if (age == 13) {
      final birthdayThisYear = DateTime(now.year, dateOfBirth.month, dateOfBirth.day);
      return now.isAfter(birthdayThisYear) || now.isAtSameMomentAs(birthdayThisYear);
    }
    
    return true;
  }

  bool get isValidVerificationCode {
    return verificationCode.length == 6 && RegExp(r'^\d{6}$').hasMatch(verificationCode);
  }

  bool get isValidBio {
    if (bio == null || bio!.isEmpty) return true; // Bio is optional
    return bio!.length <= 160; // Twitter-like bio limit
  }

  bool get isValid {
    return isValidName &&
           isValidUsername &&
           isValidPassword &&
           isValidEmail &&
           isValidPhone &&
           hasContactInfo &&
           isValidDateOfBirth &&
           isValidVerificationCode &&
           isValidBio;
  }

  // Get contact type
  String get contactType {
    if (email != null && email!.isNotEmpty) return 'email';
    if (phone != null && phone!.isNotEmpty) return 'phone';
    return 'none';
  }

  // Get contact value
  String? get contactValue {
    if (email != null && email!.isNotEmpty) return email;
    if (phone != null && phone!.isNotEmpty) return phone;
    return null;
  }

  // Get age
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    
    return age;
  }

  @override
  List<Object?> get props => [
        name,
        username,
        password,
        email,
        phone,
        dateOfBirth,
        profileImagePath,
        bio,
        verificationCode,
      ];

  @override
  String toString() {
    return 'SignupRequest(name: $name, username: $username, contactType: $contactType)';
  }
}

class SignupResponse extends Equatable {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final Map<String, dynamic> user;
  final bool requiresVerification;

  const SignupResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.user,
    this.requiresVerification = false,
  });

  // Factory constructor for creating SignupResponse from JSON
  factory SignupResponse.fromJson(Map<String, dynamic> json) {
    return SignupResponse(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
      tokenType: json['token_type'] as String? ?? 'Bearer',
      expiresIn: json['expires_in'] as int,
      user: json['user'] as Map<String, dynamic>,
      requiresVerification: json['requires_verification'] as bool? ?? false,
    );
  }

  // Convert SignupResponse to JSON
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
      'user': user,
      'requires_verification': requiresVerification,
    };
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        tokenType,
        expiresIn,
        user,
        requiresVerification,
      ];

  @override
  String toString() {
    return 'SignupResponse(tokenType: $tokenType, requiresVerification: $requiresVerification)';
  }
}

class VerificationRequest extends Equatable {
  final String identifier; // email or phone
  final String code;

  const VerificationRequest({
    required this.identifier,
    required this.code,
  });

  // Factory constructor for creating VerificationRequest from JSON
  factory VerificationRequest.fromJson(Map<String, dynamic> json) {
    return VerificationRequest(
      identifier: json['identifier'] as String,
      code: json['code'] as String,
    );
  }

  // Convert VerificationRequest to JSON
  Map<String, dynamic> toJson() {
    return {
      'identifier': identifier,
      'code': code,
    };
  }

  @override
  List<Object?> get props => [identifier, code];

  @override
  String toString() {
    return 'VerificationRequest(identifier: $identifier)';
  }
}
