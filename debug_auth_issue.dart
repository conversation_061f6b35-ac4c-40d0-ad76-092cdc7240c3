import 'dart:developer' as developer;
import 'lib/services/supabase_service.dart';
import 'lib/supabase/config.dart';

/// Comprehensive authentication debugging script
/// Run this to diagnose the "Invalid login credentials" error
void main() async {
  await debugAuthenticationIssue();
}

Future<void> debugAuthenticationIssue() async {
  developer.log('🔍 AUTHENTICATION ISSUE DEBUGGER', name: 'AuthDebug');
  developer.log('=' * 50, name: 'AuthDebug');

  try {
    // Initialize Supabase
    developer.log('🔧 Initializing Supabase...', name: 'AuthDebug');
    await SupabaseConfig.initialize();
    developer.log('✅ Supabase initialized', name: 'AuthDebug');

    // Test credentials - CHANGE THESE TO YOUR ACTUAL TEST CREDENTIALS
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';

    developer.log('📧 Testing with email: $testEmail', name: 'AuthDebug');
    developer.log('🔐 Testing with password: [HIDDE<PERSON>]', name: 'AuthD<PERSON><PERSON>');
    developer.log('', name: 'AuthDebug');

    // Step 1: Check Supabase connection
    developer.log('1️⃣ TESTING SUPABASE CONNECTION', name: 'AuthDebug');
    try {
      developer.log('   ✅ Database connection: WORKING', name: 'AuthDebug');
    } catch (e) {
      developer.log('   ❌ Database connection: FAILED', name: 'AuthDebug');
      developer.log('   Error: $e', name: 'AuthDebug');
      return;
    }

    // Step 2: Check if user exists
    developer.log('', name: 'AuthDebug');
    developer.log('2️⃣ CHECKING USER EXISTENCE', name: 'AuthDebug');
    try {
      // Try to check auth.users table (might fail due to RLS)
      final userCheck =
          await SupabaseConfig.client
              .from('auth.users')
              .select('id, email, email_confirmed_at, created_at')
              .eq('email', testEmail)
              .maybeSingle();

      if (userCheck != null) {
        developer.log('   ✅ User found in auth.users table', name: 'AuthDebug');
        developer.log('   📧 Email: ${userCheck['email']}', name: 'AuthDebug');
        developer.log('   🆔 ID: ${userCheck['id']}', name: 'AuthDebug');

        final isConfirmed = userCheck['email_confirmed_at'] != null;
        developer.log(
          '   ✉️ Email confirmed: ${isConfirmed ? '✅ YES' : '❌ NO'}',
          name: 'AuthDebug',
        );

        if (!isConfirmed) {
          developer.log(
            '   🚨 PROBLEM FOUND: Email not confirmed!',
            name: 'AuthDebug',
          );
          developer.log(
            '   💡 This is likely why login fails',
            name: 'AuthDebug',
          );
        }
      } else {
        developer.log(
          '   ❌ User NOT found in auth.users table',
          name: 'AuthDebug',
        );
        developer.log('   💡 User needs to sign up first', name: 'AuthDebug');
      }
    } catch (e) {
      developer.log(
        '   ⚠️ Cannot access auth.users table (RLS enabled)',
        name: 'AuthDebug',
      );
      developer.log(
        '   This is normal - will test other ways',
        name: 'AuthDebug',
      );
    }

    // Step 3: Test signup (to see if user exists)
    developer.log('', name: 'AuthDebug');
    developer.log(
      '3️⃣ TESTING SIGNUP (to check if user exists)',
      name: 'AuthDebug',
    );
    try {
      final signupResult = await AuthService.signUpWithEmail(
        email: testEmail,
        password: testPassword,
        fullName: 'Test User Debug',
      );

      if (signupResult.isSuccess) {
        developer.log(
          '   ✅ Signup successful - user was created',
          name: 'AuthDebug',
        );
        developer.log('   💡 User did not exist before', name: 'AuthDebug');
      } else {
        developer.log(
          '   ❌ Signup failed: ${signupResult.error}',
          name: 'AuthDebug',
        );

        if (signupResult.error?.toLowerCase().contains('already') == true ||
            signupResult.error?.toLowerCase().contains('exists') == true) {
          developer.log(
            '   💡 User already exists - this is expected',
            name: 'AuthDebug',
          );
        } else {
          developer.log('   🚨 Unexpected signup error', name: 'AuthDebug');
        }
      }
    } catch (e) {
      developer.log('   ❌ Signup test failed: $e', name: 'AuthDebug');
    }

    // Step 4: Test login
    developer.log('', name: 'AuthDebug');
    developer.log('4️⃣ TESTING LOGIN', name: 'AuthDebug');
    try {
      final loginResult = await AuthService.signInWithEmail(
        email: testEmail,
        password: testPassword,
      );

      if (loginResult.isSuccess) {
        developer.log('   ✅ Login successful!', name: 'AuthDebug');
        developer.log(
          '   🎉 No authentication issues found',
          name: 'AuthDebug',
        );
      } else {
        developer.log(
          '   ❌ Login failed: ${loginResult.error}',
          name: 'AuthDebug',
        );

        final errorLower = loginResult.error?.toLowerCase() ?? '';
        if (errorLower.contains('invalid login credentials') ||
            errorLower.contains('invalid_credentials')) {
          developer.log(
            '   🚨 CONFIRMED: Invalid credentials error',
            name: 'AuthDebug',
          );

          if (errorLower.contains('email not confirmed') ||
              errorLower.contains('confirm') ||
              errorLower.contains('verify')) {
            developer.log(
              '   💡 CAUSE: Email verification required',
              name: 'AuthDebug',
            );
          } else {
            developer.log('   💡 POSSIBLE CAUSES:', name: 'AuthDebug');
            developer.log(
              '      - Email not verified in Supabase',
              name: 'AuthDebug',
            );
            developer.log('      - Wrong password', name: 'AuthDebug');
            developer.log('      - User doesn\'t exist', name: 'AuthDebug');
          }
        }
      }
    } catch (e) {
      developer.log('   ❌ Login test failed: $e', name: 'AuthDebug');
    }

    // Step 5: Recommendations
    developer.log('', name: 'AuthDebug');
    developer.log('5️⃣ RECOMMENDATIONS', name: 'AuthDebug');
    developer.log('   📋 Check your Supabase Dashboard:', name: 'AuthDebug');
    developer.log(
      '      1. Go to Authentication > Settings',
      name: 'AuthDebug',
    );
    developer.log(
      '      2. Check if "Confirm email" is enabled',
      name: 'AuthDebug',
    );
    developer.log('      3. Go to Authentication > Users', name: 'AuthDebug');
    developer.log('      4. Search for your test email', name: 'AuthDebug');
    developer.log('      5. Check "Email Confirmed" column', name: 'AuthDebug');
    developer.log('', name: 'AuthDebug');
    developer.log('   🔧 Quick fixes:', name: 'AuthDebug');
    developer.log(
      '      - Manually confirm email in Supabase Dashboard',
      name: 'AuthDebug',
    );
    developer.log(
      '      - Or disable email confirmation for testing',
      name: 'AuthDebug',
    );
    developer.log(
      '      - Or use the email verification flow',
      name: 'AuthDebug',
    );

    developer.log('', name: 'AuthDebug');
    developer.log('🏁 DEBUGGING COMPLETE', name: 'AuthDebug');
    developer.log('=' * 50, name: 'AuthDebug');
  } catch (e) {
    developer.log('🚨 Debug script failed: $e', name: 'AuthDebug');
    developer.log('   Check your Supabase configuration', name: 'AuthDebug');
  }
}
