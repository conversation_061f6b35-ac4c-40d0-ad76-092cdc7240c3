import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase configuration and client setup
/// Following enterprise security best practices
class SupabaseConfig {
  // TODO: Replace with your actual Supabase credentials
  // Get these from your Supabase project settings
  static const String supabaseUrl = 'https://xvnzbkllxdssgdpwwnjl.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2bnpia2xseGRzc2dkcHd3bmpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3ODUzNDYsImV4cCI6MjA2ODM2MTM0Nn0.P4fp9oE5RBTAq-iUghLmznI6B8o41Qy8DOnjPGqY0LY';

  /// Initialize Supabase client with security configurations
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      authOptions: const FlutterAuthClientOptions(
        authFlowType: AuthFlowType.pkce, // More secure than implicit flow
      ),
      realtimeClientOptions: const RealtimeClientOptions(
        logLevel: RealtimeLogLevel.info,
        timeout: Duration(seconds: 30),
      ),
      postgrestOptions: const PostgrestClientOptions(schema: 'public'),
      storageOptions: const StorageClientOptions(retryAttempts: 3),
    );
  }

  /// Get Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;

  /// Get current user
  static User? get currentUser => client.auth.currentUser;

  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  /// Get current user ID
  static String? get currentUserId => currentUser?.id;

  /// Auth state stream for reactive authentication
  static Stream<AuthState> get authStateStream => client.auth.onAuthStateChange;
}

/// Database table names - centralized for consistency
class DatabaseTables {
  static const String profiles = 'profiles';
  static const String shops = 'shops';
  static const String categories = 'categories';
  static const String products = 'products';
  static const String posts = 'posts';
  static const String follows = 'follows';
  static const String likes = 'likes';
  static const String comments = 'comments';
  static const String orders = 'orders';
  static const String orderItems = 'order_items';
  static const String chats = 'chats';
  static const String messages = 'messages';
  static const String reviews = 'reviews';
  static const String notifications = 'notifications';
}

/// Database functions - centralized function names
class DatabaseFunctions {
  static const String createUserProfile = 'create_user_profile';
  static const String updateShopStats = 'update_shop_stats';
  static const String calculateShopRating = 'calculate_shop_rating';
  static const String createNotification = 'create_notification';
  static const String updatePostCounters = 'update_post_counters';
  static const String processOrder = 'process_order';
}

/// RPC (Remote Procedure Call) helper
class SupabaseRPC {
  /// Execute RPC function with error handling
  static Future<T?> execute<T>(
    String functionName,
    Map<String, dynamic>? params,
  ) async {
    try {
      final response = await SupabaseConfig.client.rpc(
        functionName,
        params: params,
      );
      return response as T?;
    } catch (e) {
      throw SupabaseException('RPC Error: $functionName - $e');
    }
  }
}

/// Custom exception for Supabase operations
class SupabaseException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const SupabaseException(this.message, {this.code, this.details});

  @override
  String toString() => 'SupabaseException: $message';
}

/// Query builder helper for consistent querying
class QueryBuilder {
  /// Build select query with common patterns
  static PostgrestTransformBuilder select(
    String table, {
    String columns = '*',
    int? limit,
    String? orderBy,
    bool ascending = false,
  }) {
    dynamic query = SupabaseConfig.client.from(table).select(columns);

    if (orderBy != null) {
      query = query.order(orderBy, ascending: ascending);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return query;
  }

  /// Build insert query with error handling
  static PostgrestFilterBuilder insert(
    String table,
    Map<String, dynamic> data,
  ) {
    return SupabaseConfig.client.from(table).insert(data);
  }

  /// Build update query with error handling
  static PostgrestFilterBuilder update(
    String table,
    Map<String, dynamic> data,
  ) {
    return SupabaseConfig.client.from(table).update(data);
  }

  /// Build delete query
  static PostgrestFilterBuilder delete(String table) {
    return SupabaseConfig.client.from(table).delete();
  }
}

/// Real-time subscription helper
class RealtimeHelper {
  /// Subscribe to table changes
  static RealtimeChannel subscribeToTable(
    String table,
    void Function(PostgresChangePayload) callback, {
    String event = '*',
    String? schema = 'public',
  }) {
    return SupabaseConfig.client
        .channel('public:$table')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: schema ?? 'public',
          table: table,
          callback: callback,
        )
        .subscribe();
  }

  /// Subscribe to user-specific changes
  static RealtimeChannel subscribeToUserData(
    String userId,
    void Function(PostgresChangePayload) callback,
  ) {
    return SupabaseConfig.client
        .channel('user:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: DatabaseTables.notifications,
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: callback,
        )
        .subscribe();
  }
}
