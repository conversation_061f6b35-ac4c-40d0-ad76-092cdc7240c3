import 'package:equatable/equatable.dart';
import 'notification_event.dart';

enum NotificationStatus {
  initial,
  loading,
  loaded,
  refreshing,
  loadingMore,
  error,
}

class NotificationState extends Equatable {
  final NotificationStatus status;
  final List<NotificationModel> notifications;
  final List<NotificationModel> filteredNotifications;
  final String? currentFilter;
  final String searchQuery;
  final bool hasReachedMax;
  final String? errorMessage;
  final int unreadCount;
  final Map<String, bool> notificationSettings;
  final bool isSearching;

  const NotificationState({
    this.status = NotificationStatus.initial,
    this.notifications = const [],
    this.filteredNotifications = const [],
    this.currentFilter,
    this.searchQuery = '',
    this.hasReachedMax = false,
    this.errorMessage,
    this.unreadCount = 0,
    this.notificationSettings = const {
      'likes': true,
      'comments': true,
      'follows': true,
      'mentions': true,
      'messages': true,
      'posts': true,
      'business': true,
      'promotions': false,
      'reminders': true,
    },
    this.isSearching = false,
  });

  NotificationState copyWith({
    NotificationStatus? status,
    List<NotificationModel>? notifications,
    List<NotificationModel>? filteredNotifications,
    String? currentFilter,
    String? searchQuery,
    bool? hasReachedMax,
    String? errorMessage,
    int? unreadCount,
    Map<String, bool>? notificationSettings,
    bool? isSearching,
  }) {
    return NotificationState(
      status: status ?? this.status,
      notifications: notifications ?? this.notifications,
      filteredNotifications: filteredNotifications ?? this.filteredNotifications,
      currentFilter: currentFilter ?? this.currentFilter,
      searchQuery: searchQuery ?? this.searchQuery,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      errorMessage: errorMessage ?? this.errorMessage,
      unreadCount: unreadCount ?? this.unreadCount,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      isSearching: isSearching ?? this.isSearching,
    );
  }

  // Helper methods
  List<NotificationModel> get displayNotifications {
    if (isSearching && searchQuery.isNotEmpty) {
      return notifications.where((notification) {
        return notification.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
               notification.message.toLowerCase().contains(searchQuery.toLowerCase());
      }).toList();
    }
    
    if (currentFilter != null && currentFilter!.isNotEmpty) {
      return notifications.where((notification) {
        return notification.type == currentFilter;
      }).toList();
    }
    
    return notifications;
  }

  List<NotificationModel> get unreadNotifications {
    return notifications.where((notification) => !notification.isRead).toList();
  }

  List<NotificationModel> get todayNotifications {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    return notifications.where((notification) {
      final notificationDate = DateTime(
        notification.timestamp.year,
        notification.timestamp.month,
        notification.timestamp.day,
      );
      return notificationDate.isAtSameMomentAs(today);
    }).toList();
  }

  List<NotificationModel> get yesterdayNotifications {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    
    return notifications.where((notification) {
      final notificationDate = DateTime(
        notification.timestamp.year,
        notification.timestamp.month,
        notification.timestamp.day,
      );
      return notificationDate.isAtSameMomentAs(yesterday);
    }).toList();
  }

  List<NotificationModel> get olderNotifications {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    
    return notifications.where((notification) {
      final notificationDate = DateTime(
        notification.timestamp.year,
        notification.timestamp.month,
        notification.timestamp.day,
      );
      return notificationDate.isBefore(yesterday);
    }).toList();
  }

  @override
  List<Object?> get props => [
        status,
        notifications,
        filteredNotifications,
        currentFilter,
        searchQuery,
        hasReachedMax,
        errorMessage,
        unreadCount,
        notificationSettings,
        isSearching,
      ];
}
