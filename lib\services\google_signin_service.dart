import 'dart:io';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:business_app/models/auth/auth_result.dart';
import 'package:logger/logger.dart';

/// Service class to handle Google Sign-In authentication
/// Provides methods for signing in, signing out, and managing Google authentication
class GoogleSignInService {
  static const String _logTag = '[GoogleSignInService]';

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  // Google Sign-In singleton instance
  static GoogleSignIn get _googleSignIn => GoogleSignIn.instance;

  // Client IDs for different platforms
  static const String _iosClientId =
      '***********-p4k0lv7bov329e72527laqa4vq6gbg72.apps.googleusercontent.com'; // TODO: Replace with actual iOS client ID
  static const String _androidClientId =
      '***********-4jejl2ejqcdhibl72sisj49dguljjrh8.apps.googleusercontent.com'; // TODO: Replace with actual Android client ID
  static const String _webClientId =
      '***********-r7g2puqsa86u4ag10ij5n73jbbi0icj7.apps.googleusercontent.com'; // TODO: Replace with actual Web client ID

  /// Initialize Google Sign-In with platform-specific configuration
  static Future<void> initialize() async {
    try {
      _logger.i('$_logTag 🚀 Initializing Google Sign-In...');

      // Determine client ID based on platform
      String clientId;
      if (Platform.isIOS) {
        clientId = _iosClientId;
      } else if (Platform.isAndroid) {
        clientId = _androidClientId;
      } else {
        clientId = _webClientId;
      }

      await _googleSignIn.initialize(
        clientId: clientId,
        serverClientId: _webClientId, // For server auth codes
      );

      _logger.i('$_logTag ✅ Google Sign-In initialized successfully');
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to initialize Google Sign-In: $e');
      rethrow;
    }
  }

  /// Sign in with Google using the new API
  static Future<AuthResult<GoogleSignInAccount>> signIn() async {
    try {
      _logger.i('$_logTag 🔐 Starting Google Sign-In...');

      // Try lightweight authentication first
      GoogleSignInAccount? user =
          await _googleSignIn.attemptLightweightAuthentication();

      if (user == null) {
        // If lightweight auth fails, use interactive authentication
        if (_googleSignIn.supportsAuthenticate()) {
          user = await _googleSignIn.authenticate();
        } else {
          const error = 'Google Sign-In not supported on this platform';
          _logger.e('$_logTag ❌ $error');
          return AuthResult.error(error);
        }
      }

      _logger.i('$_logTag ✅ Google Sign-In successful for: ${user.email}');
      return AuthResult.success(user);
    } catch (e) {
      final error = _handleGoogleSignInError(e);
      _logger.e('$_logTag ❌ Google Sign-In failed: $error');
      return AuthResult.error(error);
    }
  }

  /// Get Google ID token for the current user
  static Future<AuthResult<String>> getIdToken([
    GoogleSignInAccount? user,
  ]) async {
    try {
      _logger.i('$_logTag 🎫 Getting Google ID token...');

      // Use provided user or attempt lightweight authentication
      user ??= await _googleSignIn.attemptLightweightAuthentication();

      if (user == null) {
        const error = 'No Google user signed in';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }

      final GoogleSignInAuthentication auth = user.authentication;

      if (auth.idToken == null) {
        const error = 'Failed to get Google ID token';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }

      _logger.i('$_logTag ✅ Google ID token retrieved successfully');
      return AuthResult.success(auth.idToken!);
    } catch (e) {
      final error = _handleGoogleSignInError(e);
      _logger.e('$_logTag ❌ Failed to get Google ID token: $error');
      return AuthResult.error(error);
    }
  }

  /// Get Google access token for the current user
  /// Note: In the new API, access tokens are not directly available
  /// This method returns the ID token which can be used for authentication
  static Future<AuthResult<String>> getAccessToken([
    GoogleSignInAccount? user,
  ]) async {
    try {
      _logger.i('$_logTag 🎫 Getting Google access token (ID token)...');

      // In the new API, we use the ID token for authentication
      return await getIdToken(user);
    } catch (e) {
      final error = _handleGoogleSignInError(e);
      _logger.e('$_logTag ❌ Failed to get Google access token: $error');
      return AuthResult.error(error);
    }
  }

  /// Sign out from Google
  static Future<AuthResult<void>> signOut() async {
    try {
      _logger.i('$_logTag 🚪 Signing out from Google...');

      await _googleSignIn.signOut();

      _logger.i('$_logTag ✅ Google Sign-Out successful');
      return AuthResult.success(null);
    } catch (e) {
      final error = _handleGoogleSignInError(e);
      _logger.e('$_logTag ❌ Google Sign-Out failed: $error');
      return AuthResult.error(error);
    }
  }

  /// Disconnect from Google (revoke access)
  static Future<AuthResult<void>> disconnect() async {
    try {
      _logger.i('$_logTag 🔌 Disconnecting from Google...');

      await _googleSignIn.disconnect();

      _logger.i('$_logTag ✅ Google disconnect successful');
      return AuthResult.success(null);
    } catch (e) {
      final error = _handleGoogleSignInError(e);
      _logger.e('$_logTag ❌ Google disconnect failed: $error');
      return AuthResult.error(error);
    }
  }

  /// Handle Google Sign-In specific errors
  static String _handleGoogleSignInError(dynamic error) {
    if (error.toString().contains('sign_in_canceled')) {
      return 'Google Sign-In was cancelled';
    } else if (error.toString().contains('sign_in_failed')) {
      return 'Google Sign-In failed. Please try again';
    } else if (error.toString().contains('network_error')) {
      return 'Network error. Please check your internet connection';
    } else if (error.toString().contains('sign_in_required')) {
      return 'Google Sign-In is required';
    } else {
      return 'Google Sign-In error: ${error.toString()}';
    }
  }
}
