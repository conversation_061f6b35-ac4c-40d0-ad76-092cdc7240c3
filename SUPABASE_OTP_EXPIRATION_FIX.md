# 🔧 Supabase OTP Expiration Configuration Guide

## 🚨 Issue: OTP Expires Too Quickly

Your password reset OTPs are expiring very quickly (within seconds), causing user frustration. This guide shows how to fix this.

## 🎯 Solution: Configure OTP Expiration in Supabase Dashboard

### Step 1: Access Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Select your project: `xvnzbkllxdssgdpwwnjl`
3. Navigate to **Authentication** → **Providers**

### Step 2: Configure Email OTP Settings
1. Click on **Email** provider
2. Look for **Email OTP Expiration** setting
3. Current default: `3600` seconds (1 hour)
4. **Recommended setting**: `600` seconds (10 minutes)
   - Long enough for users to check email and enter code
   - Short enough for security

### Step 3: Configure Password Recovery Settings
1. In the same Email provider settings
2. Look for **Password Recovery** or **Recovery OTP** settings
3. Set expiration to `600` seconds (10 minutes)

### Step 4: Save and Test
1. Click **Save** to apply changes
2. Test password reset flow
3. Verify OTP now lasts 10 minutes

## 🔧 Code Changes Made

### 1. Removed Countdown Timer
**Problem**: Countdown timer was interfering with user experience
**Solution**: Removed countdown, users can resend immediately if needed

### 2. Enhanced Error Detection
**Problem**: "OTP expired" errors were showing as "Invalid OTP"
**Solution**: Improved error detection to show specific "OTP expired" message

### 3. Added User Guidance
**Problem**: Users didn't know OTP expires quickly
**Solution**: Added timing guidance in UI

## 📱 UI Improvements Made

### Before:
- Countdown timer preventing resends
- Generic "Invalid OTP" error messages
- No timing guidance for users

### After:
- Immediate resend capability
- Specific "OTP expired" error messages
- Clear timing guidance: "Code expires in 60 seconds. Enter it quickly!"
- Simplified resend interface

## 🔍 Technical Details

### OTP Expiration Hierarchy:
1. **Supabase Dashboard Settings** (primary control)
2. **Email template configuration** (secondary)
3. **Client-side handling** (error detection only)

### Error Detection Logic:
```dart
if (e.toString().contains('code: otp_expired') ||
    e.toString().contains('Token has expired or is invalid') ||
    e.toString().contains('Token has expired') ||
    e.toString().contains('expired')) {
  specificError = 'OTP expired';
}
```

## 🎯 Recommended Settings

### For Production:
- **Email OTP Expiration**: `600` seconds (10 minutes)
- **Password Recovery OTP**: `600` seconds (10 minutes)
- **SMS OTP Expiration**: `300` seconds (5 minutes)

### For Development:
- **Email OTP Expiration**: `1800` seconds (30 minutes)
- **Password Recovery OTP**: `1800` seconds (30 minutes)

## 🚀 Testing the Fix

1. **Request password reset**
2. **Wait 2-3 minutes** (don't enter code immediately)
3. **Enter the OTP** - should work if configured correctly
4. **If it expires**, you should see "OTP expired" message
5. **Click "Resend Code"** - should work immediately

## 📋 Troubleshooting

### If OTP still expires quickly:
1. Check Supabase dashboard settings were saved
2. Try with a fresh email request
3. Check browser network tab for any caching issues

### If "Invalid OTP" still shows instead of "OTP expired":
1. Check console logs for exact error message
2. Verify error detection logic matches actual error format

### If resend doesn't work:
1. Check console for resend errors
2. Verify email is being passed correctly to resend function

## 🔗 Supabase Documentation

- [Email OTP Configuration](https://supabase.com/docs/guides/auth/auth-email-passwordless)
- [Auth Provider Settings](https://supabase.com/docs/guides/auth/auth-providers)
- [CLI Configuration](https://supabase.com/docs/guides/cli/config)

---

**Note**: Changes to OTP expiration settings in Supabase dashboard take effect immediately for new OTP requests.
