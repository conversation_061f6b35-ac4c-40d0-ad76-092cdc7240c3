import 'package:flutter/material.dart';
import 'package:business_app/models/auth/user_role.dart';

class SuperAdminPage extends StatelessWidget {
  const SuperAdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    final permissions = UserRole.superAdmin.permissions;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          UserRole.superAdmin.displayName,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        centerTitle: true,
        backgroundColor: Colors.red.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.red.shade600, Colors.red.shade50],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Role Header Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.security,
                          size: 48,
                          color: Colors.red.shade600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        UserRole.superAdmin.displayName,
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        UserRole.superAdmin.description,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.shade600,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Hierarchy Level: ${UserRole.superAdmin.hierarchyLevel}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Permissions Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.admin_panel_settings,
                            color: Colors.red.shade600,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'Super Admin Permissions',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Full System Access
                      _buildPermissionCategory(
                        'Full System Access',
                        Icons.settings,
                        Colors.red,
                        [
                          'Complete system configuration',
                          'Database management',
                          'Server administration',
                          'Security settings',
                          'System monitoring',
                        ],
                      ),

                      const SizedBox(height: 16),

                      // User Management
                      _buildPermissionCategory(
                        'User Management (All Operations)',
                        Icons.people,
                        Colors.blue,
                        [
                          'View all users: ${permissions.canViewUsers}',
                          'Edit user profiles: ${permissions.canEditUsers}',
                          'Delete user accounts: ${permissions.canDeleteUsers}',
                          'Assign admin roles: ${permissions.canAssignRoles}',
                          'Manage user permissions',
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Financial Management
                      _buildPermissionCategory(
                        'Financial Management',
                        Icons.attach_money,
                        Colors.green,
                        [
                          'View financial reports: ${permissions.canViewFinancials}',
                          'Manage financial data: ${permissions.canManageFinancials}',
                          'Revenue analytics',
                          'Payment processing',
                          'Transaction monitoring',
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Admin Role Assignment
                      _buildPermissionCategory(
                        'Admin Role Assignment',
                        Icons.assignment_ind,
                        Colors.purple,
                        [
                          'Promote users to admin',
                          'Demote admin users',
                          'Create moderator roles',
                          'Manage role hierarchy',
                          'Permission delegation',
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Security Notice
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.amber.shade300, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning_amber,
                        color: Colors.amber.shade700,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Security Notice',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.amber.shade800,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Super Admin access should be restricted to app owners only. This role has unrestricted access to all system functions.',
                              style: TextStyle(
                                color: Colors.amber.shade700,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPermissionCategory(
    String title,
    IconData icon,
    Color color,
    List<String> permissions,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color.lerp(color, Colors.black, 0.3),
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...permissions.map(
            (permission) => Padding(
              padding: const EdgeInsets.only(bottom: 6),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: color, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      permission,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
