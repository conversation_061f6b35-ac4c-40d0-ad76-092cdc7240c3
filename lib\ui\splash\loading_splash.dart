import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LoadingSplash extends StatefulWidget {
  final String message;
  final Duration duration;
  final VoidCallback? onComplete;
  final Widget? destination;

  const LoadingSplash({
    super.key,
    this.message = 'Loading your experience...',
    this.duration = const Duration(milliseconds: 1500),
    this.onComplete,
    this.destination,
  });

  @override
  State<LoadingSplash> createState() => _LoadingSplashState();
}

class _LoadingSplashState extends State<LoadingSplash>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _particleController;

  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _textOpacityAnimation;
  late Animation<double> _particleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _logoScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );

    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _logoController, curve: Curves.easeIn));

    _textController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _textController, curve: Curves.easeIn));

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );
  }

  void _startAnimationSequence() async {
    _particleController.repeat();
    _logoController.forward();

    await Future.delayed(const Duration(milliseconds: 300));
    _textController.forward();

    await Future.delayed(widget.duration);

    if (widget.onComplete != null) {
      widget.onComplete!();
    } else if (widget.destination != null) {
      _navigateToDestination();
    }
  }

  void _navigateToDestination() {
    if (mounted && widget.destination != null) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) => widget.destination!,
          transitionDuration: const Duration(milliseconds: 400),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            theme.brightness == Brightness.light
                ? Brightness.dark
                : Brightness.light,
        statusBarBrightness: theme.brightness,
      ),
    );

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors:
                theme.brightness == Brightness.light
                    ? [
                      theme.colorScheme.primary.withValues(alpha: 0.05),
                      Colors.white,
                      theme.colorScheme.primary.withValues(alpha: 0.02),
                    ]
                    : [
                      theme.colorScheme.primary.withValues(alpha: 0.1),
                      const Color(0xFF121212),
                      theme.colorScheme.primary.withValues(alpha: 0.05),
                    ],
          ),
        ),
        child: Stack(
          children: [
            // Animated particles
            AnimatedBuilder(
              animation: _particleAnimation,
              builder: (context, child) {
                return CustomPaint(
                  size: size,
                  painter: SimpleParticlesPainter(
                    animation: _particleAnimation.value,
                    theme: theme,
                  ),
                );
              },
            ),

            // Main content
            SafeArea(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo
                    AnimatedBuilder(
                      animation: _logoController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _logoOpacityAnimation,
                          child: Transform.scale(
                            scale: _logoScaleAnimation.value,
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: theme.colorScheme.primary.withValues(
                                      alpha: 0.2,
                                    ),
                                    blurRadius: 20,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      theme.colorScheme.primary.withValues(
                                        alpha: 0.1,
                                      ),
                                      Colors.white.withValues(alpha: 0.9),
                                    ],
                                  ),
                                ),
                                child: ClipOval(
                                  child: Container(
                                    width: 80,
                                    height: 80,
                                    padding: const EdgeInsets.all(12),
                                    child: ClipOval(
                                      child: Image.asset(
                                        'assets/images/Trademate-logo.png',
                                        width: 56,
                                        height: 56,
                                        fit: BoxFit.cover,
                                        errorBuilder: (
                                          context,
                                          error,
                                          stackTrace,
                                        ) {
                                          return Container(
                                            width: 56,
                                            height: 56,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: theme.colorScheme.primary
                                                  .withValues(alpha: 0.1),
                                            ),
                                            child: Icon(
                                              Icons.storefront_rounded,
                                              size: 32,
                                              color: theme.colorScheme.primary,
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 32),

                    // Loading indicator
                    SizedBox(
                      width: 32,
                      height: 32,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary,
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Message
                    AnimatedBuilder(
                      animation: _textController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _textOpacityAnimation,
                          child: Text(
                            widget.message,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                              letterSpacing: 0.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Simplified particles painter for loading splash
class SimpleParticlesPainter extends CustomPainter {
  final double animation;
  final ThemeData theme;

  SimpleParticlesPainter({required this.animation, required this.theme});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = theme.colorScheme.primary.withValues(alpha: 0.08)
          ..style = PaintingStyle.fill;

    // Create fewer, subtler particles
    for (int i = 0; i < 8; i++) {
      final x =
          (size.width * 0.2) +
          (size.width * 0.6 * ((i * 0.618) % 1.0)) +
          (20 * math.sin(animation * 2 * math.pi + i));

      final y =
          (size.height * 0.3) +
          (size.height * 0.4 * ((i * 0.382) % 1.0)) +
          (15 * math.cos(animation * 2 * math.pi + i * 0.7));

      final radius = 1.5 + (2 * math.sin(animation * math.pi + i));

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
