import 'package:equatable/equatable.dart';

enum SubscriptionStatus { initial, loading, loaded, error, subscribing, cancelling }

class SubscriptionPlan extends Equatable {
  final String id;
  final String title;
  final String price;
  final String duration;
  final List<String> benefits;
  final String color;
  final bool isPopular;
  final bool isCurrentPlan;

  const SubscriptionPlan({
    required this.id,
    required this.title,
    required this.price,
    required this.duration,
    required this.benefits,
    required this.color,
    this.isPopular = false,
    this.isCurrentPlan = false,
  });

  SubscriptionPlan copyWith({
    String? id,
    String? title,
    String? price,
    String? duration,
    List<String>? benefits,
    String? color,
    bool? isPopular,
    bool? isCurrentPlan,
  }) {
    return SubscriptionPlan(
      id: id ?? this.id,
      title: title ?? this.title,
      price: price ?? this.price,
      duration: duration ?? this.duration,
      benefits: benefits ?? this.benefits,
      color: color ?? this.color,
      isPopular: isPopular ?? this.isPopular,
      isCurrentPlan: isCurrentPlan ?? this.isCurrentPlan,
    );
  }

  @override
  List<Object> get props => [id, title, price, duration, benefits, color, isPopular, isCurrentPlan];
}

class PromoCode extends Equatable {
  final String code;
  final String description;
  final double discountPercentage;
  final bool isValid;

  const PromoCode({
    required this.code,
    required this.description,
    required this.discountPercentage,
    this.isValid = true,
  });

  @override
  List<Object> get props => [code, description, discountPercentage, isValid];
}

class SubscriptionState extends Equatable {
  final SubscriptionStatus status;
  final List<SubscriptionPlan> plans;
  final int selectedPlanIndex;
  final SubscriptionPlan? currentSubscription;
  final String selectedPaymentMethod;
  final PromoCode? appliedPromoCode;
  final bool hasActiveSubscription;
  final DateTime? subscriptionEndDate;
  final String? errorMessage;

  const SubscriptionState({
    this.status = SubscriptionStatus.initial,
    this.plans = const [],
    this.selectedPlanIndex = -1,
    this.currentSubscription,
    this.selectedPaymentMethod = 'credit_card',
    this.appliedPromoCode,
    this.hasActiveSubscription = false,
    this.subscriptionEndDate,
    this.errorMessage,
  });

  SubscriptionState copyWith({
    SubscriptionStatus? status,
    List<SubscriptionPlan>? plans,
    int? selectedPlanIndex,
    SubscriptionPlan? currentSubscription,
    String? selectedPaymentMethod,
    PromoCode? appliedPromoCode,
    bool? hasActiveSubscription,
    DateTime? subscriptionEndDate,
    String? errorMessage,
  }) {
    return SubscriptionState(
      status: status ?? this.status,
      plans: plans ?? this.plans,
      selectedPlanIndex: selectedPlanIndex ?? this.selectedPlanIndex,
      currentSubscription: currentSubscription ?? this.currentSubscription,
      selectedPaymentMethod: selectedPaymentMethod ?? this.selectedPaymentMethod,
      appliedPromoCode: appliedPromoCode ?? this.appliedPromoCode,
      hasActiveSubscription: hasActiveSubscription ?? this.hasActiveSubscription,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  bool get hasSelectedPlan => selectedPlanIndex >= 0 && selectedPlanIndex < plans.length;
  SubscriptionPlan? get selectedPlan => hasSelectedPlan ? plans[selectedPlanIndex] : null;
  
  double get finalPrice {
    if (!hasSelectedPlan) return 0.0;
    
    final basePrice = double.tryParse(selectedPlan!.price.replaceAll(RegExp(r'[^\d.]'), '')) ?? 0.0;
    
    if (appliedPromoCode != null && appliedPromoCode!.isValid) {
      return basePrice * (1 - appliedPromoCode!.discountPercentage / 100);
    }
    
    return basePrice;
  }

  @override
  List<Object?> get props => [
        status,
        plans,
        selectedPlanIndex,
        currentSubscription,
        selectedPaymentMethod,
        appliedPromoCode,
        hasActiveSubscription,
        subscriptionEndDate,
        errorMessage,
      ];
}
