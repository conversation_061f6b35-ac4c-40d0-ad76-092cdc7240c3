import 'package:phone_numbers_parser/phone_numbers_parser.dart';
import 'package:logger/logger.dart';

/// Utility class for phone number normalization and formatting
/// Ensures consistent phone number handling across authentication flows
class PhoneUtils {
  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  /// Normalizes a phone number to a consistent format for database storage and lookup
  /// Removes all spaces, dashes, parentheses, and other formatting characters
  /// Returns the phone number in international format: +[country_code][number]
  static String normalizePhoneNumber(String phoneNumber) {
    if (phoneNumber.trim().isEmpty) return '';

    try {
      // Parse the phone number using phone_numbers_parser
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      if (parsedNumber.isValid()) {
        // Return the international format without any spaces or formatting
        // This ensures consistency: +************ instead of +265 984 671 670
        return parsedNumber.international.replaceAll(RegExp(r'[\s\-\(\)]'), '');
      }
    } catch (e) {
      // If parsing fails, fall back to manual normalization
      _logger.w(
        'PhoneUtils: Failed to parse phone number "$phoneNumber", using fallback normalization',
      );
    }

    // Fallback normalization: remove all non-digit characters except +
    String normalized = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Ensure it starts with + if it doesn't already
    if (!normalized.startsWith('+') && normalized.isNotEmpty) {
      // If it doesn't start with +, assume it's a local number and needs country code
      // This is a fallback - ideally all numbers should include country code
      normalized = '+$normalized';
    }

    return normalized;
  }

  /// Formats a phone number for display purposes
  /// Returns a user-friendly format with spaces: +265 984 671 670
  static String formatPhoneNumberForDisplay(String phoneNumber) {
    if (phoneNumber.trim().isEmpty) return phoneNumber;

    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      if (parsedNumber.isValid()) {
        // Return the international format with proper spacing
        return parsedNumber.international;
      }
    } catch (e) {
      _logger.w(
        'PhoneUtils: Failed to format phone number "$phoneNumber" for display',
      );
    }

    // Fallback: return the original number
    return phoneNumber;
  }

  /// Validates if a phone number is valid
  static bool isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.trim().isEmpty) return false;

    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);
      return parsedNumber.isValid();
    } catch (e) {
      // Fallback validation for edge cases
      final digitsOnly = phoneNumber.replaceAll(RegExp(r'\D'), '');
      return digitsOnly.length >= 10 && digitsOnly.length <= 15;
    }
  }

  /// Gets a user-friendly error message for invalid phone numbers
  static String getPhoneValidationError(String phoneNumber) {
    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      if (!parsedNumber.isValid()) {
        if (phoneNumber.length < 8) {
          return 'Phone number is too short';
        } else if (phoneNumber.length > 15) {
          return 'Phone number is too long';
        } else {
          return 'Please enter a valid phone number (e.g., +265 984 671 670)';
        }
      }

      return 'Invalid phone number format';
    } catch (e) {
      if (!phoneNumber.startsWith('+')) {
        return 'Phone number must include country code (e.g., +265)';
      }
      return 'Please enter a valid phone number with country code';
    }
  }

  /// Converts a phone number to the email format used for phone-based accounts
  /// Example: +************ -> <EMAIL>
  static String phoneToTempEmail(String phoneNumber) {
    final normalized = normalizePhoneNumber(phoneNumber);
    // Remove the + sign for email format
    final emailPrefix =
        normalized.startsWith('+') ? normalized.substring(1) : normalized;
    return '$<EMAIL>';
  }

  /// Extracts phone number from temp email format
  /// Example: <EMAIL> -> +************
  static String tempEmailToPhone(String email) {
    if (email.endsWith('@phone.temp')) {
      final phoneDigits = email.split('@')[0];
      return '+$phoneDigits';
    }
    return email; // Return as-is if not a temp email
  }

  /// Checks if an email is a phone-based temp email
  static bool isTempPhoneEmail(String email) {
    return email.endsWith('@phone.temp');
  }

  /// Gets the country code from a phone number
  static String? getCountryCode(String phoneNumber) {
    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);
      return parsedNumber.isoCode.name;
    } catch (e) {
      return null;
    }
  }

  /// Compares two phone numbers for equality, ignoring formatting differences
  /// Example: "+265 984 671 670" equals "+************"
  static bool arePhoneNumbersEqual(String phone1, String phone2) {
    final normalized1 = normalizePhoneNumber(phone1);
    final normalized2 = normalizePhoneNumber(phone2);
    return normalized1 == normalized2;
  }

  /// Debug method to show phone number normalization
  static void debugPhoneNormalization(String phoneNumber) {
    _logger.d('🔍 PhoneUtils Debug:');
    _logger.d('   Original: "$phoneNumber"');
    _logger.d('   Normalized: "${normalizePhoneNumber(phoneNumber)}"');
    _logger.d('   Display: "${formatPhoneNumberForDisplay(phoneNumber)}"');
    _logger.d('   Valid: ${isValidPhoneNumber(phoneNumber)}');
    _logger.d('   Temp Email: "${phoneToTempEmail(phoneNumber)}"');
  }
}
