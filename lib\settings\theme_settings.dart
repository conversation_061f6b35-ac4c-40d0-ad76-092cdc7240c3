//for testing purposes for flutter bloc
import 'package:business_app/bloc/theme_bloc/theme_bloc.dart';
import 'package:business_app/bloc/theme_bloc/theme_event.dart';
import 'package:business_app/bloc/theme_bloc/theme_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ThemeSettings extends StatelessWidget {
  const ThemeSettings({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text(
          'Themes',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
      body: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, state) {
          final currentMode = state.mode;

          return ListView(
            children: [
              _buildThemeOption(
                context,
                title: 'System Default',
                subtitle: 'Use device settings',
                value: AppThemeMode.system,
                groupValue: currentMode,
                onChanged: (value) {
                  context.read<ThemeBloc>().add(SetThemeModeEvent(value!));
                },
              ),
              const Divider(height: 1),
              _buildThemeOption(
                context,
                title: 'Light Mode',
                subtitle: 'Always use light theme',
                value: AppThemeMode.light,
                groupValue: currentMode,
                onChanged: (value) {
                  context.read<ThemeBloc>().add(SetThemeModeEvent(value!));
                },
              ),
              const Divider(height: 1),
              _buildThemeOption(
                context,
                title: 'Dark Mode',
                subtitle: 'Always use dark theme',
                value: AppThemeMode.dark,
                groupValue: currentMode,
                onChanged: (value) {
                  context.read<ThemeBloc>().add(SetThemeModeEvent(value!));
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildThemeOption(
    BuildContext context, {
    required String title,
    required String subtitle,
    required AppThemeMode value,
    required AppThemeMode groupValue,
    required ValueChanged<AppThemeMode?> onChanged,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      title: Text(title, style: Theme.of(context).textTheme.titleMedium),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
        ),
      ),
      trailing: Radio<AppThemeMode>(
        value: value,
        groupValue: groupValue,
        onChanged: onChanged,
      ),
      onTap: () => onChanged(value),
    );
  }
}
