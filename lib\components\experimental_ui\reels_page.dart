import 'package:business_app/const/assets.dart';
import 'package:flutter/material.dart';

class AnotherReelUi extends StatelessWidget {
  const AnotherReelUi({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background with checkerboard pattern
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/checkerboard.png',
                ), // Ensure this asset is added
                fit: BoxFit.cover,
              ),
            ),
          ),
          // Main content and icons layout
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Text content on the left
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '@username • 3h ago',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Description goes here',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '#hashtags #music #dance Reloaded 4 of 1531 libraries in 1,658ms (compile: 211 ms, reload: 479 ms, reassemble: 637 ms).78E/libEGL  ( 8960): called unimplemented OpenGL ES APIReloaded 1 of 1531 libraries in 1,412ms (compile: 114 ms, reload: 481 ms, reassemble: 430 ms).93',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'See translation',
                            style: TextStyle(
                              color: Colors.blue,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // Icons on the right
              Padding(
                padding: const EdgeInsets.only(right: 16.0, bottom: 16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    GestureDetector(
                      onTap: () {
                        // Handle follow action
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(Icons.add, color: Colors.black),
                      ),
                    ),
                    const SizedBox(height: 16),
                    GestureDetector(
                      onTap: () {
                        // Handle like action
                      },
                      child: const Icon(
                        Icons.favorite,
                        color: Colors.red,
                        size: 32,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '365',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 16),
                    GestureDetector(
                      onTap: () {
                        // Handle comment action
                      },
                      child: const Icon(
                        Icons.chat_bubble_outline,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '105',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 16),
                    GestureDetector(
                      onTap: () {
                        // Handle share action
                      },
                      child: const Icon(
                        Icons.share,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(height: 16),
                    GestureDetector(
                      onTap: () {
                        // Handle sound action
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.music_note,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Bottom sound/mute toggle
          Positioned(
            bottom: 16,
            left: 16,
            child: Row(
              children: [
                const Icon(Icons.volume_up, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Original sound',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    // Handle mute action
                  },
                  child: const Icon(Icons.mic_off, color: Colors.red, size: 16),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

//reel ui #2

class ReelUI extends StatelessWidget {
  const ReelUI({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background (Video placeholder or blurred image here)
          Positioned.fill(
            child: Image.network(
              'https://cdni.pornpics.com/1280/7/548/75557612/75557612_042_cdab.jpg',
              fit: BoxFit.cover,
            ),
          ),
          //background image/video ends here
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                //colors: [Colors.black87, Colors.black54],
                colors: [Colors.transparent, Colors.transparent],
              ),
            ),
          ),

          // Right-side Column Icons
          Positioned(
            right: 12,
            bottom: 100,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Profile Icon
                /* 
               CircleAvatar(
                  radius: 25,
                  backgroundColor: Colors.white,
                  child: Stack(
                    children: [
                      const CircleAvatar(
                        radius: 23,
                        backgroundColor: Colors.black,
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.red,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.add,
                              color: Colors.white,
                              size: 10,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                */
                // const SizedBox(height: 25),
                // Like Icon
                const Icon(Icons.favorite, color: Colors.pinkAccent, size: 32),
                const SizedBox(height: 5),
                const Text('10,861', style: TextStyle(color: Colors.white)),
                const SizedBox(height: 25),
                // Comment Icon
                const Icon(
                  Icons.chat_bubble_outline,
                  color: Colors.white70,
                  size: 28,
                ),
                // const SizedBox(height: 5),
                //const Text('', style: TextStyle(color: Colors.white)),
                const SizedBox(height: 25),
                // Share Icon
                const Icon(Icons.send, color: Colors.white, size: 28),
                const SizedBox(height: 5),
                const Text('164K', style: TextStyle(color: Colors.white)),
                const SizedBox(height: 25),
                const Icon(
                  Icons.more_vert_outlined,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(height: 25),
                // Music Icon
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.redAccent,
                  ),
                  padding: const EdgeInsets.all(10),
                  child: const Icon(
                    Icons.music_note,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ],
            ),
          ),

          // Bottom Left Text Section
          Positioned(
            left: 12,
            bottom: 30,
            child: SizedBox(
              width: 250, // Set this to your desired max width
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 22,
                        backgroundColor: Colors.white,
                        child: Stack(
                          children: [
                            const CircleAvatar(
                              radius: 20,
                              backgroundColor: Colors.black,
                            ),
                            Positioned(
                              bottom: 0,
                              right: -1,
                              child: Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.red,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                                child: const Center(
                                  child: Icon(
                                    Icons.add,
                                    color: Colors.white,
                                    size: 10,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 2),
                      const Text(
                        'Chris Lungu',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const SizedBox(width: 2),
                      const Icon(Icons.verified, color: Colors.amber, size: 14),

                      const SizedBox(width: 2),
                      const Text(
                        '• 16h ago',
                        style: TextStyle(
                          color: Colors.grey,
                          //fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  const Text(
                    'Description goes here #hashtags #music #dance',
                    style: TextStyle(color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                  const SizedBox(height: 6),
                  Wrap(
                    spacing: 2,
                    runSpacing: 8,
                    children: const [
                      TagChip('#slowed'),
                      TagChip('#future garage'),
                      TagChip('#chillstep'),
                    ],
                  ),
                  const Text(
                    'See translation',
                    style: TextStyle(color: Colors.white60, fontSize: 13),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 6),
                  //rectangle container
                  Container(
                    width: double.infinity,
                    // height: 50,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Center(
                      child: const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'View profile',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Row(
                    children: [
                      Icon(Icons.music_note, color: Colors.white, size: 14),
                      SizedBox(width: 2),
                      Flexible(
                        child: Text(
                          'Original sound',
                          style: TextStyle(color: Colors.white),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      SizedBox(width: 6),
                      Icon(Icons.music_off, color: Colors.redAccent, size: 14),
                      SizedBox(width: 2),
                      Flexible(
                        child: Text(
                          'Mute',
                          style: TextStyle(color: Colors.white),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TagChip extends StatelessWidget {
  final String label;
  const TagChip(this.label, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        label,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }
}

// ui #2 ends here

//sample post ui #1
class PostUI extends StatelessWidget {
  const PostUI({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      //backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // Background Blur + Transparent Gradient
          Container(
            decoration: BoxDecoration(
              image: const DecorationImage(
                // image: AssetImage(Assets.product4),
                image: NetworkImage(
                  'https://cdni.pornpics.com/1280/7/548/75557612/75557612_042_cdab.jpg',
                ),

                fit: BoxFit.cover,
              ),
              borderRadius: BorderRadius.circular(30),
            ),
            //removed background opacity
            // child: BackdropFilter(
            // filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.2),
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
          //),

          // Foreground Content
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Spacer(),

                  // User Info & Tags
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left Container
                      Expanded(
                        flex: 4,
                        child: Container(
                          padding: const EdgeInsets.all(0),
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  CircleAvatar(
                                    backgroundImage: AssetImage(
                                      Assets.newProfile,
                                    ),
                                    radius: 20,
                                  ),
                                  const SizedBox(width: 10),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: const [
                                      Text(
                                        'Christina Lungu',
                                        style: TextStyle(
                                          // color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        '15.6M customers',
                                        style: TextStyle(color: Colors.grey),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: const Text(
                                      'Follow',
                                      style: TextStyle(
                                        fontWeight:
                                            FontWeight
                                                .normal, //color: Colors.white
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              Wrap(
                                spacing: 6,
                                runSpacing: 8,
                                children: const [
                                  TagChip('#slowed'),
                                  TagChip('#future garage'),
                                  TagChip('#chillstep'),
                                ],
                              ),
                              const SizedBox(height: 20),

                              // Track Info
                              Container(
                                width: double.infinity,
                                height: 50,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: Image.asset(
                                        Assets.trademateLogo,
                                        width: 30,
                                        height: 30,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    const Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'View profile',
                                            style: TextStyle(
                                              // color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Icon(
                                      // Icons.add_circle_outline,
                                      Icons.send,
                                      // color: Colors.white,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Right Container
                      // Right-side Instagram-style floating column
                      Spacer(),
                      Positioned(
                        right: 20,
                        bottom: 100,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: const [
                            // Bottom-most icon first
                            Column(
                              children: [
                                Icon(
                                  Icons.favorite_border,
                                  // color: Colors.white,
                                  size: 32,
                                ),
                                Text('11,818'),
                              ],
                            ),

                            SizedBox(height: 16),
                            Icon(
                              Icons.chat_bubble_outline,
                              //color: Colors.white,
                              size: 32,
                            ),
                            SizedBox(height: 16),
                            Column(
                              children: [
                                Icon(
                                  Icons.send, //color: Colors.white,
                                  size: 32,
                                ),
                                Text('861'),
                              ],
                            ),
                            SizedBox(height: 16),
                            Icon(
                              Icons.more_vert,
                              // color: Colors.white,
                              size: 32,
                            ),
                            SizedBox(height: 16),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Controls
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: const [
                  //     Icon(Icons.volume_up, color: Colors.white),
                  //     Icon(Icons.share, color: Colors.white),
                  //     Icon(Icons.more_vert, color: Colors.white),
                  //   ],
                  // ),
                  const SizedBox(height: 10),
                  const Divider(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/*class TagChip extends StatelessWidget {
  final String label;
  const TagChip(this.label, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        label,
        style: const TextStyle(
          //color: Colors.white,
          fontSize: 12,
        ),
      ),
    );
  }
}
*/

// search ui-----------------------------------------------------
