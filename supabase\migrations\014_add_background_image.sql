-- Add background image field to profiles table
-- This migration adds support for user background/cover images

-- Add background_image_url column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS background_image_url TEXT;

-- Add comment for documentation
COMMENT ON COLUMN public.profiles.background_image_url IS 'URL for user profile background/cover image';

-- Create index for performance (optional, but good practice)
CREATE INDEX IF NOT EXISTS idx_profiles_background_image 
ON public.profiles(background_image_url) 
WHERE background_image_url IS NOT NULL;
