import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../bloc/shop_creation_bloc/shop_creation_bloc.dart';
import '../../../bloc/shop_creation_bloc/shop_creation_event.dart';
import '../../../bloc/shop_creation_bloc/shop_creation_state.dart';
import 'widgets/shop_form_field.dart';
import 'widgets/shop_image_upload_widget.dart';
import 'widgets/gradient_button.dart';
import 'my_shop_page.dart';

class ShopCreationPage extends StatefulWidget {
  const ShopCreationPage({super.key});

  @override
  State<ShopCreationPage> createState() => _ShopCreationPageState();
}

class _ShopCreationPageState extends State<ShopCreationPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return BlocProvider(
      create: (context) => ShopCreationBloc(),
      child: Scaffold(
        body: BlocConsumer<ShopCreationBloc, ShopCreationState>(
          listener: (context, state) {
            if (state.errorMessage != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.errorMessage!),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            }

            if (state.status == ShopCreationStatus.success &&
                state.createdShop != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    state.successMessage ?? 'Shop created successfully!',
                  ),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );

              // Navigate to MyShopPage after successful creation
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const MyShopPage()),
              );
            }
          },
          builder: (context, state) {
            return CustomScrollView(
              slivers: [
                // Custom App Bar
                SliverAppBar(
                  expandedHeight: 200,
                  floating: false,
                  pinned: true,
                  elevation: 0,
                  backgroundColor: Colors.transparent,
                  flexibleSpace: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors:
                            isDark
                                ? [
                                  const Color(0xFF1A1A2E),
                                  const Color(0xFF16213E),
                                ]
                                : [
                                  const Color(0xFF667eea),
                                  const Color(0xFF764ba2),
                                ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: FlexibleSpaceBar(
                      title: FadeTransition(
                        opacity: _fadeAnimation,
                        child: const Text(
                          'Create Your Shop',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      centerTitle: true,
                      background: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      const Color(0xFF1A1A2E),
                                      const Color(0xFF16213E),
                                    ]
                                    : [
                                      const Color(0xFF667eea),
                                      const Color(0xFF764ba2),
                                    ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Center(
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: FadeTransition(
                              opacity: _fadeAnimation,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const SizedBox(height: 40),
                                  Icon(
                                    Icons.store_rounded,
                                    size: 64,
                                    color: Colors.white.withOpacity(0.9),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Start Your Business Journey',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),

                // Form Content
                SliverToBoxAdapter(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Welcome Text
                            Text(
                              'Let\'s set up your shop',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: isDark ? Colors.white : Colors.grey[800],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Fill in the basic information to get started with your online store.',
                              style: TextStyle(
                                fontSize: 16,
                                color:
                                    isDark
                                        ? Colors.grey[300]
                                        : Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 32),

                            // Shop Name Field
                            ShopFormField(
                              label: 'Shop Name',
                              hint: 'Enter your shop name',
                              value: state.shopName,
                              onChanged:
                                  (value) => context
                                      .read<ShopCreationBloc>()
                                      .add(UpdateShopNameEvent(value)),
                              errorText: state.shopNameError,
                              prefixIcon: Icons.store,
                              isRequired: true,
                              maxLength: 100,
                            ),
                            const SizedBox(height: 24),

                            // Shop Description Field
                            ShopFormField(
                              label: 'Shop Description',
                              hint: 'Describe what your shop offers...',
                              value: state.shopDescription,
                              onChanged:
                                  (value) => context
                                      .read<ShopCreationBloc>()
                                      .add(UpdateShopDescriptionEvent(value)),
                              errorText: state.shopDescriptionError,
                              prefixIcon: Icons.description,
                              maxLines: 4,
                              isRequired: true,
                            ),
                            const SizedBox(height: 32),

                            // Shop Logo Upload
                            ShopImageUploadWidget(
                              imageUrl: state.shopLogoUrl,
                              label: 'Shop Logo',
                              hint: 'Upload your shop logo\n(Optional)',
                              icon: Icons.image,
                              height: 150,
                              onImageSelected:
                                  (imagePath) => context
                                      .read<ShopCreationBloc>()
                                      .add(UploadShopLogoEvent(imagePath)),
                              onRemoveImage:
                                  () => context.read<ShopCreationBloc>().add(
                                    const UpdateShopLogoEvent(null),
                                  ),
                              isLoading: state.isUploading,
                              uploadProgress: state.uploadProgress,
                            ),
                            const SizedBox(height: 24),

                            // Shop Banner Upload
                            ShopImageUploadWidget(
                              imageUrl: state.shopBannerUrl,
                              label: 'Shop Banner',
                              hint: 'Upload your shop banner\n(Optional)',
                              icon: Icons.panorama,
                              height: 120,
                              onImageSelected:
                                  (imagePath) => context
                                      .read<ShopCreationBloc>()
                                      .add(UploadShopBannerEvent(imagePath)),
                              onRemoveImage:
                                  () => context.read<ShopCreationBloc>().add(
                                    const UpdateShopBannerEvent(null),
                                  ),
                              isLoading: state.isUploading,
                              uploadProgress: state.uploadProgress,
                            ),
                            const SizedBox(height: 40),

                            // Create Shop Button
                            GradientButton(
                              text: 'Create My Shop',
                              icon: Icons.rocket_launch,
                              onPressed:
                                  state.isFormValid && !state.isLoading
                                      ? () => context
                                          .read<ShopCreationBloc>()
                                          .add(const CreateShopEvent())
                                      : null,
                              isLoading: state.isLoading,
                              isEnabled: state.isFormValid,
                              height: 60,
                            ),
                            const SizedBox(height: 16),

                            // Cancel Button
                            OutlineGradientButton(
                              text: 'Cancel',
                              icon: Icons.close,
                              onPressed: () => Navigator.pop(context),
                              height: 50,
                            ),
                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
