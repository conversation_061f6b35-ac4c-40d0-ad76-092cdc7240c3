class ShoeModel {
  final String id;
  final String name;
  final String brand;
  final String category; // Men, Women, Unisex
  final double price;
  final double originalPrice;
  final double rating;
  final int reviewCount;
  final List<String> colors;
  final List<String> sizes;
  final List<String> images;
  final String description;
  final bool isOnSale;
  final int discount; // percentage

  ShoeModel({
    required this.id,
    required this.name,
    required this.brand,
    required this.category,
    required this.price,
    required this.originalPrice,
    required this.rating,
    required this.reviewCount,
    required this.colors,
    required this.sizes,
    required this.images,
    required this.description,
    required this.isOnSale,
    required this.discount,
  });

  // Factory constructor for creating from JSON (for future backend integration)
  factory ShoeModel.fromJson(Map<String, dynamic> json) {
    return ShoeModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      brand: json['brand'] ?? '',
      category: json['category'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      originalPrice: (json['originalPrice'] ?? 0).toDouble(),
      rating: (json['rating'] ?? 0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      colors: List<String>.from(json['colors'] ?? []),
      sizes: List<String>.from(json['sizes'] ?? []),
      images: List<String>.from(json['images'] ?? []),
      description: json['description'] ?? '',
      isOnSale: json['isOnSale'] ?? false,
      discount: json['discount'] ?? 0,
    );
  }

  // Convert to JSON (for future backend integration)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'category': category,
      'price': price,
      'originalPrice': originalPrice,
      'rating': rating,
      'reviewCount': reviewCount,
      'colors': colors,
      'sizes': sizes,
      'images': images,
      'description': description,
      'isOnSale': isOnSale,
      'discount': discount,
    };
  }

  // Copy with method for updating properties
  ShoeModel copyWith({
    String? id,
    String? name,
    String? brand,
    String? category,
    double? price,
    double? originalPrice,
    double? rating,
    int? reviewCount,
    List<String>? colors,
    List<String>? sizes,
    List<String>? images,
    String? description,
    bool? isOnSale,
    int? discount,
  }) {
    return ShoeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      category: category ?? this.category,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      colors: colors ?? this.colors,
      sizes: sizes ?? this.sizes,
      images: images ?? this.images,
      description: description ?? this.description,
      isOnSale: isOnSale ?? this.isOnSale,
      discount: discount ?? this.discount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShoeModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ShoeModel(id: $id, name: $name, brand: $brand, price: $price)';
  }
}
