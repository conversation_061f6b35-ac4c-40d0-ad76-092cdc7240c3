import 'package:flutter/material.dart';
import 'package:business_app/widgets/security/role_guard.dart';
import 'package:business_app/utils/security_utils.dart';

/// Example: Enhanced Shop Page with Role-Based Features
class SecureShopPage extends StatelessWidget {
  const SecureShopPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shop'),
        actions: [
          // Admin tools for shop management
          AdminOnlyWidget(
            child: PopupMenuButton<String>(
              icon: const Icon(Icons.admin_panel_settings),
              onSelected: (value) => _handleAdminAction(context, value),
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'analytics',
                      child: ListTile(
                        leading: Icon(Icons.analytics),
                        title: Text('Shop Analytics'),
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'moderate',
                      child: ListTile(
                        leading: Icon(Icons.flag),
                        title: Text('Moderate Products'),
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'manage',
                      child: ListTile(
                        leading: Icon(Icons.settings),
                        title: Text('Manage Shop'),
                      ),
                    ),
                  ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Admin notification banner
          AdminOnlyWidget(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              color: Colors.orange.shade100,
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Admin Mode: You can moderate and manage all shop content',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Shop content
          Expanded(
            child: ListView(
              children: [
                // Featured products section
                _buildFeaturedProducts(context),

                // Admin analytics section
                AdminOnlyWidget(child: _buildAdminAnalytics(context)),

                // Moderator tools section
                ModeratorOnlyWidget(child: _buildModeratorTools(context)),

                // Regular products grid
                _buildProductsGrid(context),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildRoleBasedFAB(context),
    );
  }

  Widget _buildFeaturedProducts(BuildContext context) {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Featured Products',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 5,
              itemBuilder:
                  (context, index) => _buildProductCard(context, index),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, int index) {
    return Container(
      width: 150,
      margin: const EdgeInsets.only(right: 12),
      child: Card(
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 100,
                  color: Colors.grey.shade300,
                  child: const Center(child: Icon(Icons.image)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Product ${index + 1}'),
                      Text('\$${(index + 1) * 10}'),
                    ],
                  ),
                ),
              ],
            ),
            // Admin overlay for product management
            AdminOnlyWidget(
              child: Positioned(
                top: 4,
                right: 4,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: PopupMenuButton<String>(
                    icon: const Icon(
                      Icons.more_vert,
                      color: Colors.white,
                      size: 16,
                    ),
                    onSelected:
                        (value) => _handleProductAction(context, value, index),
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Text('Edit Product'),
                          ),
                          const PopupMenuItem(
                            value: 'hide',
                            child: Text('Hide Product'),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Text('Delete Product'),
                          ),
                        ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminAnalytics(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Shop Analytics',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildAnalyticItem('Total Sales', '1,234'),
                _buildAnalyticItem('Products', '56'),
                _buildAnalyticItem('Vendors', '12'),
                _buildAnalyticItem('Reports', '3'),
              ],
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () => _viewDetailedAnalytics(context),
              icon: const Icon(Icons.bar_chart),
              label: const Text('View Detailed Analytics'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        Text(label, style: TextStyle(color: Colors.grey.shade600)),
      ],
    );
  }

  Widget _buildModeratorTools(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.shield, color: Colors.amber.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Moderation Tools',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _reviewFlaggedProducts(context),
                  icon: const Icon(Icons.flag),
                  label: const Text('Review Flagged (3)'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _moderateReviews(context),
                  icon: const Icon(Icons.rate_review),
                  label: const Text('Moderate Reviews'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _checkVendorReports(context),
                  icon: const Icon(Icons.report),
                  label: const Text('Vendor Reports'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsGrid(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'All Products',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: 10,
            itemBuilder:
                (context, index) => _buildGridProductCard(context, index),
          ),
        ],
      ),
    );
  }

  Widget _buildGridProductCard(BuildContext context, int index) {
    return Card(
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  color: Colors.grey.shade300,
                  child: const Center(child: Icon(Icons.image)),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Product ${index + 1}'),
                    Text('\$${(index + 1) * 15}'),
                  ],
                ),
              ),
            ],
          ),
          // Moderator flag for inappropriate content
          ModeratorOnlyWidget(
            child: Positioned(
              top: 4,
              left: 4,
              child: GestureDetector(
                onTap: () => _flagProduct(context, index),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.red.shade600,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.flag, color: Colors.white, size: 16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleBasedFAB(BuildContext context) {
    return RoleBasedWidget(
      userWidget: FloatingActionButton(
        onPressed: () => _addToCart(context),
        child: const Icon(Icons.shopping_cart),
      ),
      moderatorWidget: FloatingActionButton.extended(
        onPressed: () => _moderateShop(context),
        icon: const Icon(Icons.shield),
        label: const Text('Moderate'),
      ),
      adminWidget: FloatingActionButton.extended(
        onPressed: () => _manageShop(context),
        icon: const Icon(Icons.admin_panel_settings),
        label: const Text('Manage'),
      ),
    );
  }

  // Action handlers
  void _handleAdminAction(BuildContext context, String action) {
    switch (action) {
      case 'analytics':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canAccessAnalytics',
          () => _viewShopAnalytics(context),
        );
        break;
      case 'moderate':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canModerateContent',
          () => _moderateShopContent(context),
        );
        break;
      case 'manage':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canManageSystem',
          () => _manageShopSettings(context),
        );
        break;
    }
  }

  void _handleProductAction(
    BuildContext context,
    String action,
    int productId,
  ) {
    switch (action) {
      case 'edit':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canEditUsers',
          () => _editProduct(context, productId),
        );
        break;
      case 'hide':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canModerateContent',
          () => _hideProduct(context, productId),
        );
        break;
      case 'delete':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canDeleteContent',
          () => _deleteProduct(context, productId),
        );
        break;
    }
  }

  // Implementation methods (add your actual logic here)
  void _viewDetailedAnalytics(BuildContext context) {}
  void _reviewFlaggedProducts(BuildContext context) {}
  void _moderateReviews(BuildContext context) {}
  void _checkVendorReports(BuildContext context) {}
  void _flagProduct(BuildContext context, int productId) {}
  void _addToCart(BuildContext context) {}
  void _moderateShop(BuildContext context) {}
  void _manageShop(BuildContext context) {}
  void _viewShopAnalytics(BuildContext context) {}
  void _moderateShopContent(BuildContext context) {}
  void _manageShopSettings(BuildContext context) {}
  void _editProduct(BuildContext context, int productId) {}
  void _hideProduct(BuildContext context, int productId) {}
  void _deleteProduct(BuildContext context, int productId) {}
}
