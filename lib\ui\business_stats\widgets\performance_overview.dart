import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:google_fonts/google_fonts.dart';

import '../models/business_metrics.dart';
import 'metric_card.dart';

class PerformanceOverview extends StatefulWidget {
  final BusinessMetrics metrics;
  final TimeRange timeRange;

  const PerformanceOverview({
    super.key,
    required this.metrics,
    required this.timeRange,
  });

  @override
  State<PerformanceOverview> createState() => _PerformanceOverviewState();
}

class _PerformanceOverviewState extends State<PerformanceOverview> {
  int selectedMetricIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: 20),
        _buildKPICards(context),
        const SizedBox(height: 24),
        _buildPerformanceChart(context),
        const SizedBox(height: 24),
        _buildGrowthMetrics(context),
        const SizedBox(height: 24),
        _buildComparisonSection(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue, Colors.blue.shade300],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            LucideIcons.activity,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Performance Metrics',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              Text(
                'Key performance indicators',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.green.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(LucideIcons.trendingUp, color: Colors.green, size: 12),
              const SizedBox(width: 4),
              Text(
                'Improving',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildKPICards(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = constraints.maxWidth > 800 ? 4 : 2;
        final childAspectRatio = constraints.maxWidth > 800 ? 1.3 : 1.2;

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: childAspectRatio,
          children: [
            MetricCard(
              title: 'Customer Acquisition',
              value: '${widget.metrics.totalCustomers}',
              subtitle: 'Total customers',
              icon: LucideIcons.userPlus,
              color: Colors.blue,
              changePercentage: 18.5,
              isPositiveChange: true,
            ),
            MetricCard(
              title: 'Avg Order Value',
              value: '\$${widget.metrics.averageOrderValue.toStringAsFixed(0)}',
              subtitle: 'Per transaction',
              icon: LucideIcons.shoppingBag,
              color: Colors.green,
              changePercentage: 7.2,
              isPositiveChange: true,
            ),
            MetricCard(
              title: 'Customer Satisfaction',
              value: '${widget.metrics.customerSatisfaction}/5.0',
              subtitle: 'Rating score',
              icon: LucideIcons.star,
              color: Colors.orange,
              changePercentage: 3.1,
              isPositiveChange: true,
            ),
            MetricCard(
              title: 'Growth Rate',
              value: '${widget.metrics.growthRate.toStringAsFixed(1)}%',
              subtitle: 'Monthly growth',
              icon: LucideIcons.trendingUp,
              color: Colors.purple,
              changePercentage: 2.8,
              isPositiveChange: true,
            ),
          ],
        );
      },
    );
  }

  Widget _buildPerformanceChart(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      height: 350,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Performance Trends',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: selectedMetricIndex,
                    icon: Icon(
                      LucideIcons.chevronDown,
                      size: 16,
                      color: Colors.blue,
                    ),
                    style: GoogleFonts.inter(
                      color: Colors.blue,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                    onChanged: (int? newValue) {
                      if (newValue != null) {
                        setState(() {
                          selectedMetricIndex = newValue;
                        });
                      }
                    },
                    items: const [
                      DropdownMenuItem(value: 0, child: Text('Revenue')),
                      DropdownMenuItem(value: 1, child: Text('Orders')),
                      DropdownMenuItem(value: 2, child: Text('Customers')),
                      DropdownMenuItem(value: 3, child: Text('Satisfaction')),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: _getInterval(),
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: isDark ? Colors.grey[800] : Colors.grey[200],
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        const labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
                        if (value.toInt() >= 0 &&
                            value.toInt() < labels.length) {
                          return SideTitleWidget(
                            meta: meta,
                            child: Text(
                              labels[value.toInt()],
                              style: const TextStyle(fontSize: 10),
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: _getInterval(),
                      getTitlesWidget: (value, meta) {
                        return Text(
                          _formatYAxisLabel(value),
                          style: const TextStyle(fontSize: 10),
                        );
                      },
                      reservedSize: 42,
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                minX: 0,
                maxX: 3,
                minY: 0,
                maxY: _getMaxY(),
                lineBarsData: [
                  LineChartBarData(
                    spots: _getChartData(),
                    isCurved: true,
                    gradient: LinearGradient(
                      colors: [
                        _getMetricColor(),
                        _getMetricColor().withOpacity(0.7),
                      ],
                    ),
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 4,
                          color: _getMetricColor(),
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          _getMetricColor().withOpacity(0.3),
                          _getMetricColor().withOpacity(0.1),
                          _getMetricColor().withOpacity(0.05),
                        ],
                      ),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  enabled: true,
                  touchTooltipData: LineTouchTooltipData(
                    getTooltipColor: (touchedSpot) => _getMetricColor(),
                    tooltipBorderRadius: BorderRadius.circular(8),
                    getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                      return touchedBarSpots.map((barSpot) {
                        return LineTooltipItem(
                          _formatTooltip(barSpot.y),
                          const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        );
                      }).toList();
                    },
                  ),
                ),
              ),
            ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.3, end: 0),
          ),
        ],
      ),
    );
  }

  Widget _buildGrowthMetrics(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Growth Metrics',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildGrowthItem(
                  'Revenue Growth',
                  '+12.5%',
                  'vs last month',
                  Colors.green,
                  LucideIcons.trendingUp,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildGrowthItem(
                  'Customer Growth',
                  '+18.3%',
                  'new customers',
                  Colors.blue,
                  LucideIcons.users,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildGrowthItem(
                  'Order Growth',
                  '+15.2%',
                  'more orders',
                  Colors.purple,
                  LucideIcons.shoppingCart,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildGrowthItem(
                  'Engagement',
                  '+22.1%',
                  'user activity',
                  Colors.orange,
                  LucideIcons.activity,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildGrowthItem(
    String title,
    String value,
    String subtitle,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: GoogleFonts.inter(fontSize: 10, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonSection(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.indigo.withOpacity(0.1),
            Colors.blue.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.indigo.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.barChart, color: Colors.indigo, size: 20),
              const SizedBox(width: 8),
              Text(
                'Performance Comparison',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'How you\'re performing compared to industry benchmarks:',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          ..._getComparisonItems().map((item) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: item['color'] as Color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      item['text'] as String,
                      style: GoogleFonts.inter(
                        fontSize: 13,
                        color: isDark ? Colors.grey[300] : Colors.grey[700],
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: (item['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item['status'] as String,
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: item['color'] as Color,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    ).animate().fadeIn(duration: 700.ms).slideY(begin: 0.2, end: 0);
  }

  List<FlSpot> _getChartData() {
    switch (selectedMetricIndex) {
      case 0: // Revenue
        return [
          const FlSpot(0, 100),
          const FlSpot(1, 120),
          const FlSpot(2, 115),
          const FlSpot(3, 135),
        ];
      case 1: // Orders
        return [
          const FlSpot(0, 80),
          const FlSpot(1, 95),
          const FlSpot(2, 88),
          const FlSpot(3, 110),
        ];
      case 2: // Customers
        return [
          const FlSpot(0, 60),
          const FlSpot(1, 72),
          const FlSpot(2, 68),
          const FlSpot(3, 85),
        ];
      case 3: // Satisfaction
        return [
          const FlSpot(0, 4.2),
          const FlSpot(1, 4.4),
          const FlSpot(2, 4.3),
          const FlSpot(3, 4.6),
        ];
      default:
        return [
          const FlSpot(0, 100),
          const FlSpot(1, 120),
          const FlSpot(2, 115),
          const FlSpot(3, 135),
        ];
    }
  }

  Color _getMetricColor() {
    switch (selectedMetricIndex) {
      case 0:
        return Colors.green;
      case 1:
        return Colors.blue;
      case 2:
        return Colors.purple;
      case 3:
        return Colors.orange;
      default:
        return Colors.green;
    }
  }

  double _getMaxY() {
    switch (selectedMetricIndex) {
      case 0:
        return 150;
      case 1:
        return 120;
      case 2:
        return 100;
      case 3:
        return 5;
      default:
        return 150;
    }
  }

  double _getInterval() {
    switch (selectedMetricIndex) {
      case 0:
        return 30;
      case 1:
        return 20;
      case 2:
        return 20;
      case 3:
        return 1;
      default:
        return 30;
    }
  }

  String _formatYAxisLabel(double value) {
    switch (selectedMetricIndex) {
      case 0:
        return '\$${value.toInt()}K';
      case 1:
        return '${value.toInt()}';
      case 2:
        return '${value.toInt()}';
      case 3:
        return '${value.toStringAsFixed(1)}';
      default:
        return '${value.toInt()}';
    }
  }

  String _formatTooltip(double value) {
    switch (selectedMetricIndex) {
      case 0:
        return '\$${value.toStringAsFixed(1)}K';
      case 1:
        return '${value.toInt()} orders';
      case 2:
        return '${value.toInt()} customers';
      case 3:
        return '${value.toStringAsFixed(1)}/5.0';
      default:
        return '${value.toStringAsFixed(1)}';
    }
  }

  List<Map<String, dynamic>> _getComparisonItems() {
    return [
      {
        'text': 'Conversion rate is 25% above industry average',
        'status': 'Excellent',
        'color': Colors.green,
      },
      {
        'text': 'Customer satisfaction matches top performers',
        'status': 'Great',
        'color': Colors.blue,
      },
      {
        'text': 'Revenue growth exceeds market trends',
        'status': 'Strong',
        'color': Colors.purple,
      },
      {
        'text': 'Customer acquisition cost is optimized',
        'status': 'Good',
        'color': Colors.orange,
      },
    ];
  }
}
