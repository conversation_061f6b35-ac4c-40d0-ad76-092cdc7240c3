import 'package:business_app/ui/user_profiles/components/bottom_bar_pages/messages/new_message.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/chat_bloc/chat_bloc.dart';
import 'package:business_app/bloc/chat_bloc/chat_event.dart';
import 'package:business_app/bloc/chat_bloc/chat_state.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';

class MyMessagesPage extends StatefulWidget {
  final void Function(bool isVisible)? onScrollBottomBarVisibility;

  const MyMessagesPage({Key? key, this.onScrollBottomBarVisibility})
    : super(key: key);

  @override
  State<MyMessagesPage> createState() => _MyMessagesPageState();
}

class _MyMessagesPageState extends State<MyMessagesPage> {
  @override
  void initState() {
    super.initState();
    // Load conversations when page opens
    context.read<ChatBloc>().add(LoadConversationsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        return NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification notification) {
            if (notification is UserScrollNotification &&
                widget.onScrollBottomBarVisibility != null) {
              if (notification.direction == ScrollDirection.reverse) {
                widget.onScrollBottomBarVisibility!(false);
              } else if (notification.direction == ScrollDirection.forward) {
                widget.onScrollBottomBarVisibility!(true);
              }
            }
            return false;
          },
          child: Scaffold(
            // backgroundColor: isDark ? Colors.black : Colors.white,
            appBar: AppBar(
              //backgroundColor: Colors.transparent,
              elevation: 0,
              // leading: const BackButton(),
              centerTitle: true,

              /* title: Flexible(
            child: Row(
              children: [
                Text(
                  'Messages',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    //,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                // const Icon(
                //   Icons.arrow_drop_down, //
                // ),
              ],
            ),
          ),*/
              title: Text(
                'Messages',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  //,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              // actions: const [
              //   //  Icon(
              //   //    Icons.face_6_outlined,
              //   //  ),
              //   SizedBox(width: 16),
              //   Icon(Icons.edit),
              //   SizedBox(width: 16),
              // ],
            ),
            body: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                GestureDetector(
                  onTap:
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const SearchPage()),
                      ),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    margin: const EdgeInsets.only(top: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.search, //60
                        ),
                        const SizedBox(width: 10),
                        Text('Search', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Column(
                      children: [
                        Stack(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundImage: AssetImage('assets/meta_ai.jpg'),
                            ),
                            const Positioned(
                              bottom: 0,
                              right: 0,
                              child: CircleAvatar(
                                radius: 6,
                                backgroundColor: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Reva AI',
                          style: TextStyle(
                            fontSize: 10, //
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                //  Messages and Requests
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Messages',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        // ,
                      ),
                    ),
                    Text(
                      'Requests',
                      style: TextStyle(fontSize: 14, color: Colors.blue),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Messages - Dynamic content from ChatBloc
                if (state.status == ChatStatus.loading)
                  const Center(child: CircularProgressIndicator())
                else if (state.status == ChatStatus.error)
                  Center(child: Text('Error: ${state.errorMessage}'))
                else if (state.conversations.isEmpty)
                  const Center(child: Text('No conversations yet'))
                else
                  ...state.conversations.map(
                    (conversation) => Column(
                      children: [
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          leading: Stack(
                            children: [
                              CircleAvatar(
                                radius: 25,
                                backgroundImage: NetworkImage(
                                  conversation.participantProfileImage,
                                ),
                              ),
                              if (conversation.isOnline)
                                const Positioned(
                                  bottom: 0,
                                  right: 0,
                                  child: CircleAvatar(
                                    radius: 6,
                                    backgroundColor: Colors.green,
                                  ),
                                ),
                            ],
                          ),
                          title: Row(
                            children: [
                              Text(
                                conversation.participantName,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 4),
                              // Remove isVerified check as it's not in the model
                            ],
                          ),
                          subtitle: Text(
                            conversation.lastMessage?.content ?? 'Active now',
                          ),
                          trailing:
                              conversation.unreadCount > 0
                                  ? CircleAvatar(
                                    radius: 10,
                                    backgroundColor: Colors.blue,
                                    child: Text(
                                      '${conversation.unreadCount}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                  )
                                  : null,
                          onTap: () {
                            // Navigate to chat screen
                            context.read<ChatBloc>().add(
                              LoadMessagesEvent(conversation.id),
                            );
                          },
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                // suggested messages
                const SizedBox(height: 16),
                Text(
                  'Suggestions',
                  style: TextStyle(
                    //70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 10),
                _buildUserTile(
                  'Mtende Munthali',
                  'munthalimtende',
                  'assets/profile1.jpg',
                ),
                _buildUserTile('Takondwa Austen', 'takondwaausten', null),
                _buildUserTile('Louis Phiri', 'phiri204', null),
                _buildUserTile('Louis Phiri', 'phiri204', null),
                _buildUserTile('Louis Phiri', 'phiri204', null),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Accounts to follow', style: TextStyle(fontSize: 14)),
                    Text(
                      'See all',
                      style: TextStyle(color: Colors.blue, fontSize: 14),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                _buildFollowTile(
                  'jef.f5825',
                  'Jalufu Jeff',
                  'assets/profile2.jpg',
                ),
                _buildFollowTile(
                  'mkwawa691',
                  'Frank MC Kamtambe',
                  'assets/profile3.jpg',
                ),
                _buildFollowTile(
                  'mkwawa691',
                  'Frank MC Kamtambe',
                  'assets/profile3.jpg',
                ),
                _buildFollowTile(
                  'mkwawa691',
                  'Frank MC Kamtambe',
                  'assets/profile3.jpg',
                ),
              ],
            ),
            // Custom FAB for Messages page
            // floatingActionButton: MessagesCustomFAB(),
            // floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,

            //~floating action button for all pages
            floatingActionButton: MessageCustomExpandableFab(),
            floatingActionButtonLocation: ExpandableFab.location,
          ),
        );
      },
    );
  }

  Widget _buildUserTile(String name, String username, String? img) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        radius: 22,
        backgroundImage: img != null ? AssetImage(img) : null,
        child: img == null ? const Icon(Icons.person) : null,
      ),
      title: Text(name, style: TextStyle()),
      subtitle: Text(username, style: TextStyle(fontSize: 12)),
      trailing: const Icon(Icons.camera_alt_outlined),
    );
  }

  Widget _buildFollowTile(String username, String name, String imagePath) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(radius: 22, backgroundImage: AssetImage(imagePath)),
      title: Text(username, style: TextStyle()),
      subtitle: Text(name, style: TextStyle(fontSize: 12)),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextButton(
            onPressed: () {},
            style: TextButton.styleFrom(backgroundColor: Colors.blue),
            child: const Text('Follow'),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.close),
        ],
      ),
    );
  }
}

// Custom FAB for Messages Page
class MessagesCustomFAB extends StatelessWidget {
  const MessagesCustomFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        _showMessageOptions(context);
      },
      backgroundColor: Colors.purple,
      child: const Icon(Icons.message, color: Colors.white),
    );
  }

  void _showMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Message Actions',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                _buildMessageAction(
                  context,
                  'New Conversation',
                  Icons.chat_bubble_outline,
                  () {
                    Navigator.pop(context);
                    context.read<ChatBloc>().add(
                      StartNewConversationEvent('new_user', 'New User'),
                    );
                  },
                ),
                _buildMessageAction(
                  context,
                  'Broadcast Message',
                  Icons.campaign,
                  () {
                    Navigator.pop(context);
                    // Add broadcast logic here
                  },
                ),
                _buildMessageAction(
                  context,
                  'Message Requests',
                  Icons.inbox,
                  () {
                    Navigator.pop(context);
                    // Add message requests logic here
                  },
                ),
                _buildMessageAction(context, 'Archive', Icons.archive, () {
                  Navigator.pop(context);
                  // Add archive logic here
                }),
                const SizedBox(height: 10),
              ],
            ),
          ),
    );
  }

  Widget _buildMessageAction(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.purple),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      onTap: onTap,
    );
  }
}

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text('Search', style: TextStyle()),
        leading: const BackButton(),
      ),
      body: Center(child: Text('Search Page Content Here', style: TextStyle())),
    );
  }
}

//custom FAB for messages page -------------------------------------------------------

class MessageCustomExpandableFab extends StatelessWidget {
  const MessageCustomExpandableFab({super.key});

  @override
  Widget build(BuildContext context) {
    final GlobalKey<ExpandableFabState> fabKey =
        GlobalKey<ExpandableFabState>();

    return ExpandableFab(
      key: fabKey,
      type: ExpandableFabType.up,
      distance: 60,
      childrenAnimation: ExpandableFabAnimation.none,
      // fanAngle: 40,
      openButtonBuilder: RotateFloatingActionButtonBuilder(
        child: const Icon(Icons.add, size: 40),
        fabSize: ExpandableFabSize.regular,
        foregroundColor: Colors.amber,
        backgroundColor: Colors.blue,
        shape: const CircleBorder(),
        angle: 3.14 * 2,
        elevation: 2,
      ),
      closeButtonBuilder: FloatingActionButtonBuilder(
        size: 24,
        builder: (
          BuildContext context,
          void Function()? onPressed,
          Animation<double> progress,
        ) {
          return IconButton(
            onPressed: onPressed,
            icon: const Icon(
              Icons.check_circle_outline,
              size: 40,
              color: Colors.amber,
            ),
          );
        },
      ),
      overlayStyle: ExpandableFabOverlayStyle(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withOpacity(0.9)
                : Colors.white.withOpacity(0.9),
      ),

      children: [
        _buildActionRow(
          label: 'New Message',
          icon: Icons.message,
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const NewMessage()),
            );
          },
        ),
        /*FloatingActionButton.small(
          heroTag: null,
          tooltip: 'Add',
          onPressed: () => _handleAction('Add'),
          child: const Icon(Icons.add),
        ),*/
      ],
    );
  }

  Widget _buildActionRow({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
            //color: Colors.deepPurple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(32),
          ),
          child: Text(label, style: const TextStyle(fontSize: 16)),
        ),
        FloatingActionButton.small(
          heroTag: null,
          tooltip: label,
          onPressed: onPressed,
          backgroundColor: Colors.white,
          foregroundColor: Colors.blue, //Colors.deepPurple,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(32),
          ),
          // elevation: 2,
          highlightElevation: 4,
          focusElevation: 4,
          hoverElevation: 4,

          // splashColor: Colors.deepPurple.withOpacity(0.2),
          // highlightColor: Colors.deepPurple.withOpacity(0.2),
          child: Icon(icon),
        ),
      ],
    );
  }
}
