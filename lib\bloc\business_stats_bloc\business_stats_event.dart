import 'package:equatable/equatable.dart';

abstract class BusinessStatsEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class LoadBusinessStatsEvent extends BusinessStatsEvent {}

class RefreshBusinessStatsEvent extends BusinessStatsEvent {}

class ChangeStatsTimeRangeEvent extends BusinessStatsEvent {
  final String timeRange; // 'day', 'week', 'month', 'year'

  ChangeStatsTimeRangeEvent(this.timeRange);

  @override
  List<Object> get props => [timeRange];
}

class ChangeStatsTypeEvent extends BusinessStatsEvent {
  final String statsType; // 'overview', 'sales', 'customers', 'products'

  ChangeStatsTypeEvent(this.statsType);

  @override
  List<Object> get props => [statsType];
}

class LoadSalesDataEvent extends BusinessStatsEvent {
  final String timeRange;

  LoadSalesDataEvent(this.timeRange);

  @override
  List<Object> get props => [timeRange];
}

class LoadCustomerDataEvent extends BusinessStatsEvent {
  final String timeRange;

  LoadCustomerDataEvent(this.timeRange);

  @override
  List<Object> get props => [timeRange];
}

class LoadProductDataEvent extends BusinessStatsEvent {
  final String timeRange;

  LoadProductDataEvent(this.timeRange);

  @override
  List<Object> get props => [timeRange];
}

class ExportStatsEvent extends BusinessStatsEvent {
  final String format; // 'pdf', 'excel', 'csv'

  ExportStatsEvent(this.format);

  @override
  List<Object> get props => [format];
}

class ToggleStatsCardEvent extends BusinessStatsEvent {
  final String cardId;

  ToggleStatsCardEvent(this.cardId);

  @override
  List<Object> get props => [cardId];
}
