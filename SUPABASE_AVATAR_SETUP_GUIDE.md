# Supabase Avatar Storage Setup Guide

This guide provides step-by-step instructions for setting up Supabase Storage buckets for profile and background images with proper security policies.

## Table of Contents
1. [Create Storage Buckets](#create-storage-buckets)
2. [Set Up Row Level Security (RLS) Policies](#set-up-row-level-security-rls-policies)
3. [Configure File Size Limits](#configure-file-size-limits)
4. [Test the Setup](#test-the-setup)
5. [Flutter Integration](#flutter-integration)

## Create Storage Buckets

### Step 1: Access Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to **Storage** in the left sidebar
3. Click on **Create a new bucket**

### Step 2: Create Profile Images Bucket
1. **Bucket name**: `profile-images`
2. **Public bucket**: ✅ Enable (for public access to profile images)
3. **File size limit**: 5MB
4. **Allowed MIME types**: `image/jpeg,image/png,image/webp`
5. Click **Create bucket**

### Step 3: Create Background Images Bucket
1. **Bucket name**: `background-images`
2. **Public bucket**: ✅ Enable (for public access to background images)
3. **File size limit**: 10MB
4. **Allowed MIME types**: `image/jpeg,image/png,image/webp`
5. Click **Create bucket**

### Step 4: Create Product Images Bucket (Optional)
1. **Bucket name**: `product-images`
2. **Public bucket**: ✅ Enable
3. **File size limit**: 8MB
4. **Allowed MIME types**: `image/jpeg,image/png,image/webp`
5. Click **Create bucket**

## Set Up Row Level Security (RLS) Policies

### Step 1: Enable RLS on Storage Objects
Go to **SQL Editor** in your Supabase dashboard and run:

```sql
-- Enable RLS on storage.objects table
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
```

### Step 2: Create Profile Images Policies

```sql
-- Policy: Allow authenticated users to upload profile images to their own folder
CREATE POLICY "Users can upload profile images to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own profile images
CREATE POLICY "Users can view own profile images" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to profile images (for viewing other users' profiles)
CREATE POLICY "Public can view profile images" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'profile-images');

-- Policy: Allow users to update their own profile images
CREATE POLICY "Users can update own profile images" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own profile images
CREATE POLICY "Users can delete own profile images" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
```

### Step 3: Create Background Images Policies

```sql
-- Policy: Allow authenticated users to upload background images to their own folder
CREATE POLICY "Users can upload background images to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own background images
CREATE POLICY "Users can view own background images" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to background images
CREATE POLICY "Public can view background images" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'background-images');

-- Policy: Allow users to update their own background images
CREATE POLICY "Users can update own background images" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own background images
CREATE POLICY "Users can delete own background images" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
```

### Step 4: Create Product Images Policies (Optional)

```sql
-- Policy: Allow authenticated users to upload product images to their own folder
CREATE POLICY "Users can upload product images to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'product-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to product images
CREATE POLICY "Public can view product images" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'product-images');

-- Policy: Allow users to manage their own product images
CREATE POLICY "Users can manage own product images" 
ON storage.objects 
FOR ALL 
TO authenticated 
USING (
  bucket_id = 'product-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
```

## Configure File Size Limits

### Global File Size Limits
1. Go to **Settings** → **Storage** in your Supabase dashboard
2. Set **Global file size limit**:
   - **Free Plan**: Max 50MB
   - **Pro Plan**: Up to 500GB (set to 50MB for reasonable limits)

### Per-Bucket File Size Limits
The file size limits are already configured when creating buckets, but you can modify them:

1. Go to **Storage** → Select bucket → **Settings**
2. Modify **File size limit**:
   - Profile images: 5MB
   - Background images: 10MB
   - Product images: 8MB

## Test the Setup

### Step 1: Test Upload Functionality
```sql
-- Test if a user can upload to their own folder
-- This should work when authenticated
SELECT storage.objects.* FROM storage.objects 
WHERE bucket_id = 'profile-images' 
AND name LIKE 'your-user-id/%';
```

### Step 2: Test Public Access
```sql
-- Test if public can view images
-- This should return results
SELECT storage.objects.* FROM storage.objects 
WHERE bucket_id = 'profile-images' 
LIMIT 5;
```

### Step 3: Verify Folder Structure
Your storage should follow this structure:
```
profile-images/
├── user-id-1/
│   ├── profile_1234567890.jpg
│   └── profile_1234567891.png
└── user-id-2/
    └── profile_1234567892.webp

background-images/
├── user-id-1/
│   └── background_1234567890.jpg
└── user-id-2/
    └── background_1234567891.png
```

## Flutter Integration

### Step 1: Import Avatar Package
```dart
import 'package:business_app/avatar/avatar.dart';
```

### Step 2: Use Avatar Widgets
```dart
// Profile Image Widget
ProfileImageWidget(
  currentImageUrl: user.profileImageUrl,
  onImageChanged: (url) => updateUserProfile(profileImageUrl: url),
  onError: (error) => showErrorMessage(error),
  userId: user.id,
  radius: 50,
  isEditable: true,
)

// Background Image Widget
BackgroundImageWidget(
  currentImageUrl: user.backgroundImageUrl,
  onImageChanged: (url) => updateUserProfile(backgroundImageUrl: url),
  onError: (error) => showErrorMessage(error),
  userId: user.id,
  height: 200,
  isEditable: true,
)
```

### Step 3: Handle Image Upload in BLoC
```dart
// In your ProfileBloc
final profileResult = await AvatarService.uploadProfileImage(
  userId: currentUser.id,
  imageFile: event.profileImageFile!,
);

final backgroundResult = await AvatarService.uploadBackgroundImage(
  userId: currentUser.id,
  imageFile: event.backgroundImageFile!,
);
```

## Security Best Practices

1. **Always use authenticated requests** for uploads
2. **Validate file types** on both client and server side
3. **Implement file size limits** to prevent abuse
4. **Use folder structure** to organize files by user
5. **Enable image transformations** for optimized delivery
6. **Monitor storage usage** to prevent unexpected costs
7. **Implement proper error handling** for failed uploads
8. **Use optimized URLs** for better performance

## Troubleshooting

### Common Issues

1. **"new row violates row-level security policy"**
   - Ensure user is authenticated
   - Check if user ID matches folder structure
   - Verify RLS policies are correctly applied

2. **"File too large"**
   - Check global and per-bucket file size limits
   - Implement client-side validation

3. **"Invalid file type"**
   - Verify MIME type restrictions in bucket settings
   - Check file extension validation in Flutter code

4. **Images not loading**
   - Verify bucket is public
   - Check if RLS policies allow SELECT for public
   - Ensure correct URL format

### Debug Commands

```sql
-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'objects';

-- Check bucket configuration
SELECT * FROM storage.buckets;

-- Check recent uploads
SELECT * FROM storage.objects 
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC;
```

## Performance Optimization

1. **Use image transformations** for different sizes
2. **Implement caching** with CachedNetworkImage
3. **Compress images** before upload
4. **Use WebP format** when possible
5. **Implement lazy loading** for image galleries
6. **Monitor CDN performance** through Supabase dashboard

This setup provides a secure, scalable, and performant image storage solution for your Flutter business app using Supabase Storage with proper RLS policies and best practices.
