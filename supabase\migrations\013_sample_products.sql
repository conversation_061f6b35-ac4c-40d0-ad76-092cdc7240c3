-- =====================================================
-- SAMPLE PRODUCTS WITH DEFAULT IMAGES
-- Add sample products to demonstrate the image system
-- =====================================================

-- Function to create sample products for a shop
CREATE OR REPLACE FUNCTION public.create_sample_products_for_shop(shop_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    electronics_category_id UUID;
    fashion_category_id UUID;
    home_category_id UUID;
BEGIN
    -- Get category IDs (create them if they don't exist)
    SELECT id INTO electronics_category_id FROM public.categories WHERE name = 'Electronics & Technology' LIMIT 1;
    SELECT id INTO fashion_category_id FROM public.categories WHERE name = 'Fashion & Clothing' LIMIT 1;
    SELECT id INTO home_category_id FROM public.categories WHERE name = 'Home & Garden' LIMIT 1;

    -- If categories don't exist, create basic ones
    IF electronics_category_id IS NULL THEN
        INSERT INTO public.categories (name, description, is_active, sort_order)
        VALUES ('Electronics & Technology', 'Phones, laptops, gadgets, tech accessories', TRUE, 1)
        RETURNING id INTO electronics_category_id;
    END IF;

    IF fashion_category_id IS NULL THEN
        INSERT INTO public.categories (name, description, is_active, sort_order)
        VALUES ('Fashion & Clothing', 'Clothing, shoes, accessories, fashion items', TRUE, 2)
        RETURNING id INTO fashion_category_id;
    END IF;

    IF home_category_id IS NULL THEN
        INSERT INTO public.categories (name, description, is_active, sort_order)
        VALUES ('Home & Garden', 'Furniture, home decor, garden tools, appliances', TRUE, 3)
        RETURNING id INTO home_category_id;
    END IF;

    -- Insert sample products
    INSERT INTO public.products (
        shop_id,
        category_id,
        name,
        description,
        price,
        images,
        stock_quantity,
        condition,
        brand,
        tags,
        status
    ) VALUES
    -- Electronics Products
    (
        shop_uuid,
        electronics_category_id,
        'Wireless Bluetooth Headphones',
        'High-quality wireless headphones with noise cancellation and 30-hour battery life.',
        89.99,
        ARRAY[
            'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=400&fit=crop'
        ],
        25,
        'new',
        'TechSound',
        ARRAY['wireless', 'bluetooth', 'headphones', 'audio'],
        'active'
    ),
    (
        shop_uuid,
        electronics_category_id,
        'Smart Phone Case',
        'Protective case with wireless charging support and premium materials.',
        24.99,
        ARRAY[
            'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1542393545-10f5cde2c810?w=400&h=400&fit=crop'
        ],
        50,
        'new',
        'CasePro',
        ARRAY['phone', 'case', 'protection', 'wireless charging'],
        'active'
    ),
    -- Fashion Products
    (
        shop_uuid,
        fashion_category_id,
        'Premium Cotton T-Shirt',
        'Comfortable and stylish cotton t-shirt perfect for casual wear.',
        19.99,
        ARRAY[
            'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=400&fit=crop'
        ],
        100,
        'new',
        'StyleWear',
        ARRAY['t-shirt', 'cotton', 'casual', 'comfortable'],
        'active'
    ),
    (
        shop_uuid,
        fashion_category_id,
        'Designer Sneakers',
        'Trendy sneakers with premium materials and comfortable sole.',
        79.99,
        ARRAY[
            'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1490481651871-ab68de25d43d?w=400&h=400&fit=crop'
        ],
        30,
        'new',
        'FootStyle',
        ARRAY['sneakers', 'shoes', 'fashion', 'comfortable'],
        'active'
    ),
    -- Home & Garden Products
    (
        shop_uuid,
        home_category_id,
        'Modern Table Lamp',
        'Elegant table lamp with adjustable brightness and modern design.',
        45.99,
        ARRAY[
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=400&fit=crop'
        ],
        15,
        'new',
        'HomeLight',
        ARRAY['lamp', 'lighting', 'home decor', 'modern'],
        'active'
    ),
    (
        shop_uuid,
        home_category_id,
        'Indoor Plant Pot Set',
        'Beautiful ceramic plant pots perfect for indoor gardening.',
        32.99,
        ARRAY[
            'https://images.unsplash.com/photo-1493663284031-b7e3aaa4cab7?w=400&h=400&fit=crop',
            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=400&h=400&fit=crop'
        ],
        40,
        'new',
        'GreenHome',
        ARRAY['plants', 'pots', 'gardening', 'home decor'],
        'active'
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Note: This function can be called manually when needed
-- Example: SELECT public.create_sample_products_for_shop('your-shop-uuid-here');
