import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/signup_page.dart';
import 'package:business_app/ui/auth/signup/signup_basic_info_page.dart';
import 'package:business_app/ui/auth/signup/signup_password_page.dart';

void main() {
  group('Signup Flow Tests', () {
    late AuthBloc authBloc;

    setUp(() {
      authBloc = AuthBloc();
    });

    tearDown(() {
      authBloc.close();
    });

    testWidgets('should navigate from step 1 to password step when next is clicked', (WidgetTester tester) async {
      // Build the signup page with AuthBloc
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>(
            create: (context) => authBloc,
            child: const SignupPage(),
          ),
        ),
      );

      // Wait for the initial build
      await tester.pump();

      // Verify we start at step 1 (basic info)
      expect(find.byType(SignupBasicInfoPage), findsOneWidget);
      expect(find.text('Step 1 of 5'), findsOneWidget);

      // Fill in the form fields
      await tester.enterText(find.byKey(const Key('name_field')), 'Test User');
      await tester.enterText(find.byKey(const Key('contact_field')), '<EMAIL>');
      
      // Select a date of birth (simulate date picker)
      final dateOfBirth = DateTime.now().subtract(const Duration(days: 365 * 25)); // 25 years old
      authBloc.add(SignupBasicInfoSubmitted(
        name: 'Test User',
        emailOrPhone: '<EMAIL>',
        dateOfBirth: dateOfBirth,
        isEmail: true,
      ));

      // Wait for the state change
      await tester.pump();

      // Verify we moved to the password step
      expect(find.byType(SignupPasswordPage), findsOneWidget);
      expect(find.byType(SignupBasicInfoPage), findsNothing);
    });

    test('AuthBloc should transition from basicInfo to password step', () async {
      // Start the signup process
      authBloc.add(SignupStarted());
      await expectLater(
        authBloc.stream,
        emits(predicate<AuthState>((state) => state.signupStep == SignupStep.basicInfo)),
      );

      // Submit basic info
      final dateOfBirth = DateTime.now().subtract(const Duration(days: 365 * 25));
      authBloc.add(SignupBasicInfoSubmitted(
        name: 'Test User',
        emailOrPhone: '<EMAIL>',
        dateOfBirth: dateOfBirth,
        isEmail: true,
      ));

      // Expect transition to password step
      await expectLater(
        authBloc.stream,
        emits(predicate<AuthState>((state) => state.signupStep == SignupStep.password)),
      );
    });

    test('SignupPage should not reset signup step on rebuild', () async {
      // Start the signup process
      authBloc.add(SignupStarted());
      await expectLater(
        authBloc.stream,
        emits(predicate<AuthState>((state) => state.signupStep == SignupStep.basicInfo)),
      );

      // Move to password step
      final dateOfBirth = DateTime.now().subtract(const Duration(days: 365 * 25));
      authBloc.add(SignupBasicInfoSubmitted(
        name: 'Test User',
        emailOrPhone: '<EMAIL>',
        dateOfBirth: dateOfBirth,
        isEmail: true,
      ));

      await expectLater(
        authBloc.stream,
        emits(predicate<AuthState>((state) => state.signupStep == SignupStep.password)),
      );

      // Verify the current state is password step
      expect(authBloc.state.signupStep, SignupStep.password);
      
      // The SignupPage should not call SignupStarted again if signupStep is already set
      // This test verifies our fix works correctly
    });
  });
}
