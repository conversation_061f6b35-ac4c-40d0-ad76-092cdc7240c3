import '../supabase/config.dart';
import 'supabase_service.dart';

/// Service for managing orders and e-commerce transactions
class OrderService extends BaseSupabaseService {
  /// Create a new order
  static Future<Order> createOrder({
    required String shopId,
    required List<OrderItemInput> items,
    required Map<String, dynamic> shippingAddress,
    Map<String, dynamic>? billingAddress,
    String paymentMethod = 'card',
    String? notes,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      final orderId =
          await BaseSupabaseService.executeRPC<String>('create_order', {
            'p_buyer_id': userId,
            'p_shop_id': shopId,
            'p_items': items.map((item) => item.toJson()).toList(),
            'p_shipping_address': shippingAddress,
            'p_billing_address': billingAddress,
            'p_payment_method': paymentMethod,
            'p_notes': notes,
          });

      if (orderId == null) {
        throw const SupabaseException('Failed to create order');
      }

      // Get the created order
      final orderDetails = await getOrderDetails(orderId);
      return orderDetails;
    });
  }

  /// Get order details
  static Future<Order> getOrderDetails(String orderId) async {
    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.executeRPC<Map<String, dynamic>>(
            'get_order_details',
            {'p_order_id': orderId},
          );

      if (response == null) throw const SupabaseException('Order not found');

      return Order.fromJson(response);
    });
  }

  /// Get user's order history
  static Future<List<OrderSummary>> getUserOrders({
    int limit = 20,
    int offset = 0,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return [];

    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_user_orders',
        {'p_user_id': userId, 'p_limit': limit, 'p_offset': offset},
      );

      return response?.map((json) => OrderSummary.fromJson(json)).toList() ??
          [];
    });
  }

  /// Get shop's order history
  static Future<List<OrderSummary>> getShopOrders({
    required String shopId,
    String? status,
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_shop_orders',
        {
          'p_shop_id': shopId,
          'p_status': status,
          'p_limit': limit,
          'p_offset': offset,
        },
      );

      return response?.map((json) => OrderSummary.fromJson(json)).toList() ??
          [];
    });
  }

  /// Update order status (for shop owners)
  static Future<void> updateOrderStatus({
    required String orderId,
    required String status,
    String? trackingNumber,
    String? internalNotes,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.executeRPC('update_order_status', {
        'p_order_id': orderId,
        'p_status': status,
        'p_tracking_number': trackingNumber,
        'p_internal_notes': internalNotes,
      });
    });
  }

  /// Cancel order
  static Future<void> cancelOrder(String orderId) async {
    return BaseSupabaseService.executeQuery(() async {
      await updateOrderStatus(orderId: orderId, status: 'cancelled');
    });
  }

  /// Get order statuses
  static List<String> getOrderStatuses() {
    return [
      'pending',
      'confirmed',
      'processing',
      'shipped',
      'delivered',
      'cancelled',
      'refunded',
      'disputed',
    ];
  }

  /// Get payment statuses
  static List<String> getPaymentStatuses() {
    return [
      'pending',
      'processing',
      'completed',
      'failed',
      'refunded',
      'partially_refunded',
    ];
  }
}

/// Order item input for creating orders
class OrderItemInput {
  final String productId;
  final int quantity;
  final double unitPrice;

  const OrderItemInput({
    required this.productId,
    required this.quantity,
    required this.unitPrice,
  });

  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'quantity': quantity,
      'unit_price': unitPrice,
    };
  }
}

/// Order model
class Order {
  final String id;
  final String orderNumber;
  final String buyerName;
  final String buyerEmail;
  final String shopName;
  final String status;
  final String paymentStatus;
  final double totalAmount;
  final DateTime createdAt;
  final List<OrderItem> items;

  const Order({
    required this.id,
    required this.orderNumber,
    required this.buyerName,
    required this.buyerEmail,
    required this.shopName,
    required this.status,
    required this.paymentStatus,
    required this.totalAmount,
    required this.createdAt,
    required this.items,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['order_id'] as String,
      orderNumber: json['order_number'] as String,
      buyerName: json['buyer_name'] as String,
      buyerEmail: json['buyer_email'] as String,
      shopName: json['shop_name'] as String,
      status: json['status'] as String,
      paymentStatus: json['payment_status'] as String,
      totalAmount: (json['total_amount'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      items:
          (json['items'] as List<dynamic>)
              .map((item) => OrderItem.fromJson(item))
              .toList(),
    );
  }
}

/// Order summary model for lists
class OrderSummary {
  final String id;
  final String orderNumber;
  final String shopName;
  final String? buyerName; // For shop owners
  final String status;
  final String? paymentStatus; // For shop owners
  final double totalAmount;
  final DateTime createdAt;
  final int itemCount;

  const OrderSummary({
    required this.id,
    required this.orderNumber,
    required this.shopName,
    this.buyerName,
    required this.status,
    this.paymentStatus,
    required this.totalAmount,
    required this.createdAt,
    required this.itemCount,
  });

  factory OrderSummary.fromJson(Map<String, dynamic> json) {
    return OrderSummary(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String,
      shopName: json['shop_name'] as String,
      buyerName: json['buyer_name'] as String?,
      status: json['status'] as String,
      paymentStatus: json['payment_status'] as String?,
      totalAmount: (json['total_amount'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      itemCount: json['item_count'] as int,
    );
  }
}

/// Order item model
class OrderItem {
  final String productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? imageUrl;

  const OrderItem({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.imageUrl,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      productId: json['product_id'] as String,
      productName: json['product_name'] as String,
      quantity: json['quantity'] as int,
      unitPrice: (json['unit_price'] as num).toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
      imageUrl: json['image_url'] as String?,
    );
  }
}

/// Order status enum
class OrderStatus {
  static const String pending = 'pending';
  static const String confirmed = 'confirmed';
  static const String processing = 'processing';
  static const String shipped = 'shipped';
  static const String delivered = 'delivered';
  static const String cancelled = 'cancelled';
  static const String refunded = 'refunded';
  static const String disputed = 'disputed';

  static String getDisplayName(String status) {
    switch (status) {
      case pending:
        return 'Pending';
      case confirmed:
        return 'Confirmed';
      case processing:
        return 'Processing';
      case shipped:
        return 'Shipped';
      case delivered:
        return 'Delivered';
      case cancelled:
        return 'Cancelled';
      case refunded:
        return 'Refunded';
      case disputed:
        return 'Disputed';
      default:
        return status.toUpperCase();
    }
  }

  static bool canCancel(String status) {
    return [pending, confirmed].contains(status);
  }

  static bool canTrack(String status) {
    return [shipped, delivered].contains(status);
  }
}

/// Payment status enum
class PaymentStatus {
  static const String pending = 'pending';
  static const String processing = 'processing';
  static const String completed = 'completed';
  static const String failed = 'failed';
  static const String refunded = 'refunded';
  static const String partiallyRefunded = 'partially_refunded';

  static String getDisplayName(String status) {
    switch (status) {
      case pending:
        return 'Pending';
      case processing:
        return 'Processing';
      case completed:
        return 'Completed';
      case failed:
        return 'Failed';
      case refunded:
        return 'Refunded';
      case partiallyRefunded:
        return 'Partially Refunded';
      default:
        return status.toUpperCase();
    }
  }
}
