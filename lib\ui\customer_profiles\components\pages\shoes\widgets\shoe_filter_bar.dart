import 'package:flutter/material.dart';

class ShoeFilterBar extends StatelessWidget {
  final String selectedCategory;
  final String selectedBrand;
  final String sortBy;
  final Function(String) onCategoryChanged;
  final Function(String) onBrandChanged;
  final Function(String) onSortChanged;

  const ShoeFilterBar({
    Key? key,
    required this.selectedCategory,
    required this.selectedBrand,
    required this.sortBy,
    required this.onCategoryChanged,
    required this.onBrandChanged,
    required this.onSortChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[900] : Colors.grey[50],
        border: Border(
          bottom: BorderSide(
            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Category Filter
          Row(
            children: [
              const Text(
                'Category: ',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        context,
                        'All',
                        selectedCategory == 'All',
                        () => onCategoryChanged('All'),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        context,
                        'Men',
                        selectedCategory == 'Men',
                        () => onCategoryChanged('Men'),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        context,
                        'Women',
                        selectedCategory == 'Women',
                        () => onCategoryChanged('Women'),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        context,
                        'Unisex',
                        selectedCategory == 'Unisex',
                        () => onCategoryChanged('Unisex'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Brand and Sort Row
          Row(
            children: [
              // Brand Filter
              Expanded(
                child: Row(
                  children: [
                    const Text(
                      'Brand: ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: DropdownButton<String>(
                        value: selectedBrand,
                        isExpanded: true,
                        underline: Container(),
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? Colors.white : Colors.black,
                        ),
                        items: [
                          'All',
                          'Pure',
                          'Dr. Martens',
                          'SportMax',
                          'Elegance',
                          'ComfortWalk',
                          'RunFast',
                        ].map((brand) {
                          return DropdownMenuItem<String>(
                            value: brand,
                            child: Text(brand),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            onBrandChanged(value);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Sort Filter
              Expanded(
                child: Row(
                  children: [
                    const Text(
                      'Sort: ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: DropdownButton<String>(
                        value: sortBy,
                        isExpanded: true,
                        underline: Container(),
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? Colors.white : Colors.black,
                        ),
                        items: [
                          'Popular',
                          'Price: Low to High',
                          'Price: High to Low',
                          'Rating',
                          'Newest',
                        ].map((sort) {
                          return DropdownMenuItem<String>(
                            value: sort,
                            child: Text(sort),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            onSortChanged(value);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue
              : isDark
                  ? Colors.grey[800]
                  : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Colors.blue
                : isDark
                    ? Colors.grey[600]!
                    : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected
                ? Colors.white
                : isDark
                    ? Colors.white
                    : Colors.black,
          ),
        ),
      ),
    );
  }
}
