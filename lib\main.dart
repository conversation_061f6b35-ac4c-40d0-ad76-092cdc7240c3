// Import all BLoCs

// Twilio Phone # +***********
// +***********

import 'package:business_app/app.dart';
import 'package:business_app/bloc/theme_bloc/theme_bloc.dart';
import 'package:business_app/bloc/navigation_bloc/navigation_bloc.dart';
import 'package:business_app/bloc/profile_bloc/profile_bloc.dart';
import 'package:business_app/bloc/posts_bloc/posts_bloc.dart';
import 'package:business_app/bloc/search_bloc/search_bloc.dart';
import 'package:business_app/bloc/chat_bloc/chat_bloc.dart';
import 'package:business_app/bloc/business_stats_bloc/business_stats_bloc.dart';
import 'package:business_app/bloc/subscription_bloc/subscription_bloc.dart';
import 'package:business_app/bloc/create_post_bloc/create_post_bloc.dart';
import 'package:business_app/bloc/new_message_bloc/new_message_bloc.dart';
import 'package:business_app/bloc/notification_bloc/notification_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/category_selection_bloc/category_selection_bloc.dart';
import 'package:business_app/bloc/product_bloc/product_bloc.dart';
import 'package:business_app/bloc/connectivity_bloc/connectivity_bloc.dart';
import 'package:business_app/supabase/config.dart';
import 'package:business_app/services/session_service.dart';
import 'package:business_app/utils/performance_optimizations.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

// Initialize logger instance
final Logger _logger = Logger(
  printer: PrettyPrinter(
    methodCount: 0,
    errorMethodCount: 8,
    lineLength: 120,
    colors: true,
    printEmojis: true,
    printTime: false,
  ),
);

// phone # +***********

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await SupabaseConfig.initialize();

  // Initialize Session Management
  await SessionService.initialize();

  // Initialize Performance Optimizations
  await PerformanceOptimizations.initialize();

  // Test Supabase connection
  await _testSupabaseConnection();

  // ✅ This is the fix
  // device overlay issue solved
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider<ThemeBloc>(create: (_) => ThemeBloc()),

        BlocProvider<NavigationBloc>(create: (_) => NavigationBloc()),

        BlocProvider<ProfileBloc>(create: (_) => ProfileBloc()),

        BlocProvider<PostsBloc>(create: (_) => PostsBloc()),

        BlocProvider<SearchBloc>(create: (_) => SearchBloc()),

        BlocProvider<ChatBloc>(create: (_) => ChatBloc()),

        BlocProvider<BusinessStatsBloc>(create: (_) => BusinessStatsBloc()),

        BlocProvider<SubscriptionBloc>(create: (_) => SubscriptionBloc()),

        BlocProvider<CreatePostBloc>(create: (_) => CreatePostBloc()),

        BlocProvider<NewMessageBloc>(create: (_) => NewMessageBloc()),

        BlocProvider<NotificationBloc>(create: (_) => NotificationBloc()),

        BlocProvider<AuthBloc>(
          create: (_) => AuthBloc()..add(AuthCheckRequested()),
        ),

        BlocProvider<CategorySelectionBloc>(
          create: (_) => CategorySelectionBloc(),
        ),

        BlocProvider<ProductBloc>(create: (_) => ProductBloc()),

        BlocProvider<ConnectivityBloc>(create: (_) => ConnectivityBloc()),
      ],
      child: const MyApp(),
    ),
  );
}

/// Test Supabase connection and database access
Future<void> _testSupabaseConnection() async {
  _logger.i('🔄 Testing Supabase connection...');

  try {
    // Test 1: Basic connection
    final client = SupabaseConfig.client;
    _logger.i('✅ Supabase client initialized');
    _logger.i('📍 URL: ${SupabaseConfig.supabaseUrl}');

    // Test 2: Database query
    final response = await client
        .from(DatabaseTables.profiles)
        .select('count')
        .count(CountOption.exact);
    _logger.i('✅ Database connected! Profiles count: ${response.count}');

    // Test 3: Check authentication status
    final currentUser = SupabaseConfig.currentUser;
    if (currentUser != null) {
      _logger.i('✅ User already authenticated: ${currentUser.email}');
    } else {
      _logger.i('ℹ️ No user currently authenticated');
    }

    // Test 4: Check required tables exist
    await _checkRequiredTables();

    _logger.i('🎉 All Supabase tests passed!');
  } catch (e) {
    _logger.e('❌ Supabase connection failed: $e');
    _logger.w('💡 Check your credentials in lib/supabase/config.dart');
    _logger.w('💡 Ensure database migrations are run');
  }
}

/// Check if required database tables exist
Future<void> _checkRequiredTables() async {
  final requiredTables = [
    DatabaseTables.profiles,
    DatabaseTables.shops,
    DatabaseTables.categories,
    DatabaseTables.products,
    DatabaseTables.posts,
  ];

  for (final table in requiredTables) {
    try {
      await SupabaseConfig.client
          .from(table)
          .select('count')
          .count(CountOption.exact);
      _logger.i('✅ Table exists: $table');
    } catch (e) {
      _logger.e('❌ Table missing or inaccessible: $table');
      throw Exception(
        'Required table $table not found. Run database migrations.',
      );
    }
  }
}
