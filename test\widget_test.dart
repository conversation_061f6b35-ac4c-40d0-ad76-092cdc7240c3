// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:business_app/bloc/category_selection_bloc/category_selection_bloc.dart';
import 'package:business_app/bloc/category_selection_bloc/category_selection_event.dart';
import 'package:business_app/bloc/category_selection_bloc/category_selection_state.dart';
import 'package:business_app/models/category/product_category.dart';

void main() {
  group('CategorySelectionBloc', () {
    late CategorySelectionBloc categorySelectionBloc;

    setUp(() {
      categorySelectionBloc = CategorySelectionBloc();
    });

    tearDown(() {
      categorySelectionBloc.close();
    });

    test('initial state is correct', () {
      expect(
        categorySelectionBloc.state,
        equals(CategorySelectionState.initial()),
      );
    });

    test('loads categories successfully', () async {
      categorySelectionBloc.add(const LoadCategories());

      await expectLater(
        categorySelectionBloc.stream,
        emitsInOrder([
          isA<CategorySelectionState>().having(
            (state) => state.status,
            'status',
            CategorySelectionStatus.loading,
          ),
          isA<CategorySelectionState>()
              .having(
                (state) => state.status,
                'status',
                CategorySelectionStatus.loaded,
              )
              .having(
                (state) => state.categories.length,
                'categories length',
                greaterThan(0),
              ),
        ]),
      );
    });

    test('toggles category selection correctly', () async {
      // First load categories
      categorySelectionBloc.add(const LoadCategories());
      await categorySelectionBloc.stream.firstWhere(
        (state) => state.status == CategorySelectionStatus.loaded,
      );

      final categoryId = 'electronics_technology';

      // Select category
      categorySelectionBloc.add(
        ToggleCategorySelection(categoryId: categoryId),
      );

      await expectLater(
        categorySelectionBloc.stream,
        emits(
          isA<CategorySelectionState>().having(
            (state) => state.selectedCategoryIds.contains(categoryId),
            'contains selected category',
            true,
          ),
        ),
      );
    });

    test('validates minimum selection requirement', () async {
      // First load categories
      categorySelectionBloc.add(const LoadCategories());
      await categorySelectionBloc.stream.firstWhere(
        (state) => state.status == CategorySelectionStatus.loaded,
      );

      // Select 4 categories (minimum required)
      final categories = [
        'electronics_technology',
        'vehicles_automotive',
        'home_garden',
        'fashion_clothing',
      ];
      for (final categoryId in categories) {
        categorySelectionBloc.add(SelectCategory(categoryId: categoryId));
        await categorySelectionBloc.stream.first;
      }

      // Validate selection
      categorySelectionBloc.add(const ValidateSelection());

      await expectLater(
        categorySelectionBloc.stream,
        emitsInOrder([
          isA<CategorySelectionState>().having(
            (state) => state.status,
            'status',
            CategorySelectionStatus.validating,
          ),
          isA<CategorySelectionState>()
              .having(
                (state) => state.status,
                'status',
                CategorySelectionStatus.valid,
              )
              .having((state) => state.isValid, 'isValid', true),
        ]),
      );
    });
  });

  group('ProductCategory', () {
    test('creates category from default data', () {
      final categories = CategoryData.getDefaultCategories();

      expect(categories.isNotEmpty, true);
      expect(categories.length, greaterThan(5));

      final electronics = categories.firstWhere(
        (cat) => cat.id == 'electronics_technology',
      );
      expect(electronics.name, 'Electronics & Technology');
      expect(electronics.description.isNotEmpty, true);
      expect(electronics.imageUrl.isNotEmpty, true);
    });

    test('copyWith works correctly', () {
      const category = ProductCategory(
        id: 'test',
        name: 'Test',
        description: 'Test description',
        imageUrl: 'test.jpg',
        iconPath: 'test.png',
        isSelected: false,
      );

      final selectedCategory = category.copyWith(isSelected: true);

      expect(selectedCategory.isSelected, true);
      expect(selectedCategory.id, category.id);
      expect(selectedCategory.name, category.name);
    });
  });
}
