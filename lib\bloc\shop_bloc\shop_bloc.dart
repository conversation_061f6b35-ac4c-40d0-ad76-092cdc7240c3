import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/shop_service.dart';
import 'shop_event.dart';
import 'shop_state.dart';

class ShopBloc extends Bloc<ShopEvent, ShopState> {
  ShopBloc() : super(const ShopState()) {
    on<LoadShopEvent>(_onLoadShop);
    on<RefreshShopEvent>(_onRefreshShop);
  }

  Future<void> _onLoadShop(LoadShopEvent event, Emitter<ShopState> emit) async {
    if (state.status == ShopStatus.loading) return;

    emit(state.copyWith(status: ShopStatus.loading));

    try {
      final shop = await ShopService.getUserShop();
      emit(state.copyWith(
        status: ShopStatus.success,
        shop: shop,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ShopStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onRefreshShop(RefreshShopEvent event, Emitter<ShopState> emit) async {
    emit(state.copyWith(status: ShopStatus.loading));

    try {
      final shop = await ShopService.getUserShop();
      emit(state.copyWith(
        status: ShopStatus.success,
        shop: shop,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ShopStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }
}
