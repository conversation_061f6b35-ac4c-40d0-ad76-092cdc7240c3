import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/create_post_bloc/create_post_bloc.dart';
import 'package:business_app/bloc/create_post_bloc/create_post_event.dart';
import 'package:business_app/bloc/create_post_bloc/create_post_state.dart';
import 'package:business_app/models/category/product_category.dart';

class CreatePost extends StatefulWidget {
  final void Function(bool isVisible)? onScrollBottomBarVisibility;

  const CreatePost({Key? key, this.onScrollBottomBarVisibility})
    : super(key: key);

  @override
  State<CreatePost> createState() => _CreatePostState();
}

class _CreatePostState extends State<CreatePost> {
  String? selectedCategory;
  String? selectedAvailability;
  String postType = 'marketplace'; // 'text' or 'marketplace'

  //for hide bottom nav bar

  //for hide bottom nav bar ends

  // Get standardized categories from ProductCategory model
  List<ProductCategory> get categories => CategoryData.getDefaultCategories();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController priceController = TextEditingController(
    text: '0',
  );
  final TextEditingController locationController = TextEditingController(
    text: 'Mzuzu',
  );
  final TextEditingController descriptionController = TextEditingController();
  bool offerShipping = false;
  bool hideFromFriends = false;
  bool allowComments = true;

  void showCategoryBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (_) => DraggableScrollableSheet(
            expand: false,
            builder:
                (_, controller) => Column(
                  children: [
                    const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'Select Category',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        controller: controller,
                        itemCount: categories.length,
                        itemBuilder: (context, index) {
                          final category = categories[index];
                          return ListTile(
                            leading: Icon(
                              Icons.category,
                              color: Theme.of(context).primaryColor,
                            ),
                            title: Text(category.name),
                            subtitle: Text(
                              category.description,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            onTap: () {
                              setState(() {
                                selectedCategory = category.name;
                              });
                              Navigator.pop(context);
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  void showAvailabilitySheet() {
    showModalBottomSheet(
      context: context,
      builder:
          (_) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Availability',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                title: const Text('List as Single Item'),
                onTap: () {
                  setState(() {
                    selectedAvailability = 'Single Item';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('List as In Stock'),
                onTap: () {
                  setState(() {
                    selectedAvailability = 'In Stock';
                  });
                  Navigator.pop(context);
                },
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreatePostBloc, CreatePostState>(
      listener: (context, state) {
        if (state.status == CreatePostStatus.submitted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Post created successfully!')),
          );
          // Clear form
          if (postType == 'marketplace') {
            nameController.clear();
            priceController.text = '0';
            locationController.text = 'Mzuzu';
            setState(() {
              selectedCategory = null;
              selectedAvailability = null;
            });
          }
          descriptionController.clear();
          setState(() {
            postType = 'marketplace'; // Reset to marketplace post
          });
        } else if (state.status == CreatePostStatus.error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${state.errorMessage}')),
          );
        }
      },
      child: BlocBuilder<CreatePostBloc, CreatePostState>(
        builder: (context, state) {
          return NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification notification) {
              if (notification is UserScrollNotification &&
                  widget.onScrollBottomBarVisibility != null) {
                if (notification.direction == ScrollDirection.reverse) {
                  widget.onScrollBottomBarVisibility!(false);
                } else if (notification.direction == ScrollDirection.forward) {
                  widget.onScrollBottomBarVisibility!(true);
                }
              }
              return false;
            },
            child: Scaffold(
              //appBar: AppBar(title: const Text('Create Post')),
              //for pure dark & white👇
              // backgroundColor: isDark ? Colors.black : Colors.white,
              appBar: AppBar(
                // backgroundColor: Colors.transparent,
                title: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      postType == 'text' ? 'Create Post' : 'Create New List',
                      style: TextStyle(fontSize: 16),
                    ),
                    Spacer(),
                    GestureDetector(
                      onTap: () {
                        // Update content and submit the post
                        context.read<CreatePostBloc>().add(
                          UpdatePostContentEvent(descriptionController.text),
                        );
                        context.read<CreatePostBloc>().add(SubmitPostEvent());
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color:
                              state.status == CreatePostStatus.submitting
                                  ? Colors.grey
                                  : Colors.blue,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child:
                            state.status == CreatePostStatus.submitting
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : Text(
                                  'Post',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                      ),
                    ),
                  ],
                ),
              ),
              body: SingleChildScrollView(
                physics: PageScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Post Type Selector
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap:
                                  () =>
                                      setState(() => postType = 'marketplace'),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      postType == 'marketplace'
                                          ? Colors.blue
                                          : Colors.grey[200],
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Text(
                                  'Marketplace',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color:
                                        postType == 'marketplace'
                                            ? Colors.white
                                            : Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => setState(() => postType = 'text'),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      postType == 'text'
                                          ? Colors.blue
                                          : Colors.grey[200],
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Text(
                                  'Text Post',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color:
                                        postType == 'text'
                                            ? Colors.white
                                            : Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Conditional Content Based on Post Type
                    if (postType == 'text') ...[
                      // Text Post Fields
                      TextField(
                        controller: descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Wants advertise ?',
                          hintText: 'Market yourself...',
                        ),
                        maxLines: 5,
                        maxLength: 280,
                      ),
                      const SizedBox(height: 16),
                      OutlinedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.camera_alt_outlined),
                        label: const Text('Add photos (optional)'),
                      ),
                    ] else ...[
                      // Marketplace Fields
                      GestureDetector(
                        onTap: showCategoryBottomSheet,
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Category',
                          ),
                          child: Text(
                            selectedCategory ?? 'Select',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      TextField(
                        controller: nameController,
                        decoration: const InputDecoration(
                          labelText: 'What are you selling?',
                        ),
                      ),
                      const SizedBox(height: 12),
                      TextField(
                        controller: priceController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Price (\$)',
                        ),
                      ),
                      const SizedBox(height: 12),
                      TextField(
                        controller: locationController,
                        decoration: const InputDecoration(
                          labelText: 'Location',
                        ),
                      ),
                      const SizedBox(height: 12),
                      TextField(
                        controller: descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 12),
                      GestureDetector(
                        onTap: showAvailabilitySheet,
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Availability',
                          ),
                          child: Text(
                            selectedAvailability ?? 'List as Single Item',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      SwitchListTile(
                        title: const Text('Offer shipping'),
                        value: offerShipping,
                        onChanged: (val) => setState(() => offerShipping = val),
                      ),
                      SwitchListTile(
                        title: const Text('Hide from friends'),
                        subtitle: const Text(
                          "This listing is still public. If you hide this listing from friends, they won't see it in most cases.",
                        ),
                        value: hideFromFriends,
                        onChanged:
                            (val) => setState(() => hideFromFriends = val),
                      ),
                      SwitchListTile(
                        title: const Text('Turn on commenting on listing'),
                        value: allowComments,
                        onChanged: (val) => setState(() => allowComments = val),
                      ),
                      const SizedBox(height: 20),
                      OutlinedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.camera_alt_outlined),
                        label: const Text('Add photos'),
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton(
                          onPressed: () {},
                          style: OutlinedButton.styleFrom(
                            // side: const BorderSide(color: Colors.grey),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                          child: const Text(
                            'Post',
                            style: TextStyle(
                              //color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ],

                    // Common submit button for both post types
                    const SizedBox(height: 20),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // Update content and submit the post
                          context.read<CreatePostBloc>().add(
                            UpdatePostContentEvent(descriptionController.text),
                          );
                          context.read<CreatePostBloc>().add(SubmitPostEvent());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          postType == 'text' ? 'Share Post' : 'List Item',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Custom FAB for Create Post page
              /*
              floatingActionButton: CreatePostCustomFAB(),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.endFloat,
                  */
            ),
          );
        },
      ),
    );
  }
}

// Custom FAB for Create Post Page
class CreatePostCustomFAB extends StatelessWidget {
  const CreatePostCustomFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreatePostBloc, CreatePostState>(
      builder: (context, state) {
        return FloatingActionButton.extended(
          onPressed: () {
            _showPostOptions(context);
          },
          backgroundColor: Colors.green,
          icon: const Icon(Icons.add_circle_outline, color: Colors.white),
          label: const Text(
            'Quick Post',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        );
      },
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Quick Actions',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                _buildQuickAction(
                  context,
                  'Add Photos',
                  Icons.photo_camera,
                  () {
                    Navigator.pop(context);
                    context.read<CreatePostBloc>().add(AddImageEvent('camera'));
                  },
                ),
                _buildQuickAction(context, 'Add Video', Icons.videocam, () {
                  Navigator.pop(context);
                  context.read<CreatePostBloc>().add(AddVideoEvent('camera'));
                }),
                _buildQuickAction(
                  context,
                  'Add Location',
                  Icons.location_on,
                  () {
                    Navigator.pop(context);
                    context.read<CreatePostBloc>().add(
                      ToggleLocationEvent(true),
                    );
                  },
                ),
                _buildQuickAction(context, 'Save Draft', Icons.save, () {
                  Navigator.pop(context);
                  context.read<CreatePostBloc>().add(SaveDraftEvent());
                }),
                const SizedBox(height: 10),
              ],
            ),
          ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.green),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      onTap: onTap,
    );
  }
}



/*class CreatePostPage extends StatelessWidget {
  final void Function(bool isVisible)? onScrollBottomBarVisibility;

  const CreatePostPage({Key? key, this.onScrollBottomBarVisibility})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    //for pure dark & white👇
    //final isDark = Theme.of(context).brightness == Brightness.dark;
    final TextEditingController _captionController = TextEditingController();

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is UserScrollNotification &&
            onScrollBottomBarVisibility != null) {
          if (notification.direction == ScrollDirection.reverse) {
            onScrollBottomBarVisibility!(false);
          } else if (notification.direction == ScrollDirection.forward) {
            onScrollBottomBarVisibility!(true);
          }
        }
        return false;
      },
      child: Scaffold(
        //for pure dark & white👇
        //backgroundColor: isDark ? Colors.black : Colors.white,
        appBar: AppBar(
          title: const Text(
            'Create',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          actions: [
            TextButton(
              onPressed: () {
                // Navigator.pop(context);
              },
              child: const Text(
                'Post',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User profile section
              Row(
                children: [
                  const CircleAvatar(
                    radius: 24,
                    backgroundImage: AssetImage(
                      'assets/avatar_placeholder.png',
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'blessings chilemba',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Caption input
              TextField(
                controller: _captionController,
                maxLines: null,
                decoration: const InputDecoration(
                  hintText: "Write a caption...",
                  border: InputBorder.none,
                ),
              ),
              const SizedBox(height: 16),
              // Image preview placeholder
              Container(
                height: 200,
                width: double.infinity,
                color: Colors.grey[300],
                child: const Center(
                  child: Icon(Icons.image, size: 60, color: Colors.grey),
                ),
              ),
              //-----------
              TextField(
                controller: _captionController,
                maxLines: null,
                decoration: const InputDecoration(
                  hintText: "Add background cover",
                  border: InputBorder.none,
                ),
              ),
              const SizedBox(height: 16),
              // Image preview placeholder
              Container(
                height: 200,
                width: double.infinity,
                color: Colors.grey[300],
                child: const Center(
                  child: Icon(Icons.image, size: 60, color: Colors.grey),
                ),
              ),

              //-----------
              const SizedBox(height: 16),
              // Add location
              Row(
                children: [
                  const Icon(Icons.location_on_outlined, color: Colors.grey),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () {
                      // Handle add location
                    },
                    child: const Text('Add Location'),
                  ),
                ],
              ),
              const Divider(height: 32),
              // Tag people
              Row(
                children: [
                  const Icon(
                    Icons.person_add_alt_1_outlined,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () {
                      // Handle tag people
                    },
                    child: const Text('Tag People'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
*/