# Shop Image Form Fields Implementation Guide

This guide explains how to use the new comprehensive shop image upload form fields that provide full device access, automatic uploads, and database updating functionality.

## Overview

The new shop image form fields provide:
- ✅ **Device Access**: Automatic camera and gallery permissions
- ✅ **Image Upload**: Direct upload to Supabase storage
- ✅ **Database Updates**: Automatic shop record updates
- ✅ **Error Handling**: Comprehensive error management
- ✅ **Loading States**: Visual feedback during operations
- ✅ **Form Integration**: Seamless integration with existing forms

## Components

### 1. ShopLogoFormField
A form field widget for uploading and managing shop logos.

```dart
ShopLogoFormField(
  label: 'Shop Logo',
  currentLogoUrl: shop.shopLogoUrl,
  onLogoChanged: (url) {
    // Handle logo URL change
    setState(() {
      shopLogoUrl = url;
    });
  },
  onError: (error) {
    // Handle errors
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(error)),
    );
  },
  shopId: shop.id, // Optional: for immediate database updates
  size: 120,
  isEditable: true,
  isRequired: false,
)
```

### 2. ShopBannerFormField
A form field widget for uploading and managing shop banner images.

```dart
ShopBannerFormField(
  label: 'Shop Banner',
  currentBannerUrl: shop.shopBannerUrl,
  onBannerChanged: (url) {
    // Handle banner URL change
    setState(() {
      shopBannerUrl = url;
    });
  },
  onError: (error) {
    // Handle errors
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(error)),
    );
  },
  shopId: shop.id, // Optional: for immediate database updates
  height: 200,
  isEditable: true,
  isRequired: false,
  overlayWidget: Container(
    // Optional overlay content (e.g., shop name)
    padding: EdgeInsets.all(16),
    child: Text(
      shop.shopName,
      style: TextStyle(color: Colors.white, fontSize: 20),
    ),
  ),
)
```

## Key Features

### Automatic Database Updates
When a `shopId` is provided, the form fields automatically update the database:

```dart
// Logo upload automatically calls:
await ShopService.updateShopLogo(shopId: shopId, logoUrl: newUrl);

// Banner upload automatically calls:
await ShopService.updateShopBanner(shopId: shopId, bannerUrl: newUrl);
```

### Device Permissions
The widgets automatically handle:
- Camera permissions for taking photos
- Gallery permissions for selecting images
- Permission denied dialogs with user guidance

### Image Processing
Images are automatically:
- Resized to optimal dimensions
- Compressed for efficient storage
- Uploaded to appropriate Supabase buckets
- Given unique timestamped names

### Error Handling
Comprehensive error handling for:
- Permission denials
- Upload failures
- Network issues
- Invalid file types
- File size limits

## Usage Examples

### 1. Shop Creation Form
```dart
class ShopCreationPage extends StatefulWidget {
  @override
  State<ShopCreationPage> createState() => _ShopCreationPageState();
}

class _ShopCreationPageState extends State<ShopCreationPage> {
  String? _logoUrl;
  String? _bannerUrl;
  String _shopName = '';
  String _description = '';

  @override
  Widget build(BuildContext context) {
    return Form(
      child: Column(
        children: [
          // Logo Upload
          ShopLogoFormField(
            currentLogoUrl: _logoUrl,
            onLogoChanged: (url) => setState(() => _logoUrl = url),
            onError: _showError,
          ),
          
          // Banner Upload
          ShopBannerFormField(
            currentBannerUrl: _bannerUrl,
            onBannerChanged: (url) => setState(() => _bannerUrl = url),
            onError: _showError,
          ),
          
          // Other form fields...
          ShopFormField(
            label: 'Shop Name',
            value: _shopName,
            onChanged: (value) => setState(() => _shopName = value),
          ),
          
          // Save button
          ElevatedButton(
            onPressed: _createShop,
            child: Text('Create Shop'),
          ),
        ],
      ),
    );
  }

  Future<void> _createShop() async {
    final shop = await ShopService.createShop(
      shopName: _shopName,
      shopDescription: _description,
    );
    
    // Update with uploaded images
    if (_logoUrl != null || _bannerUrl != null) {
      await ShopService.updateShop(
        shopId: shop.id,
        shopLogoUrl: _logoUrl,
        shopBannerUrl: _bannerUrl,
      );
    }
  }
}
```

### 2. Shop Edit Form
```dart
class ShopEditPage extends StatefulWidget {
  final Shop shop;
  
  const ShopEditPage({required this.shop});

  @override
  State<ShopEditPage> createState() => _ShopEditPageState();
}

class _ShopEditPageState extends State<ShopEditPage> {
  late String? _logoUrl;
  late String? _bannerUrl;

  @override
  void initState() {
    super.initState();
    _logoUrl = widget.shop.shopLogoUrl;
    _bannerUrl = widget.shop.shopBannerUrl;
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      child: Column(
        children: [
          // Logo Upload with immediate database updates
          ShopLogoFormField(
            currentLogoUrl: _logoUrl,
            onLogoChanged: (url) => setState(() => _logoUrl = url),
            onError: _showError,
            shopId: widget.shop.id, // Enables automatic database updates
          ),
          
          // Banner Upload with immediate database updates
          ShopBannerFormField(
            currentBannerUrl: _bannerUrl,
            onBannerChanged: (url) => setState(() => _bannerUrl = url),
            onError: _showError,
            shopId: widget.shop.id, // Enables automatic database updates
            overlayWidget: Container(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.shop.shopName,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
```

## Integration with Existing Code

### Import the Widgets
```dart
import 'package:your_app/ui/drawer_items/my_shop/widgets/shop_form_field.dart';
```

The export statements in `shop_form_field.dart` make both widgets available:
- `ShopLogoFormField`
- `ShopBannerFormField`

### Replace Existing Image Upload Widgets
If you're currently using `ShopImageUploadWidget`, you can replace it with these new form fields for better functionality:

```dart
// Old way
ShopImageUploadWidget(
  imageUrl: logoUrl,
  onImageSelected: (path) => uploadImage(path),
  // Manual upload handling required
)

// New way
ShopLogoFormField(
  currentLogoUrl: logoUrl,
  onLogoChanged: (url) => setState(() => logoUrl = url),
  shopId: shopId, // Automatic upload and database update
)
```

## Requirements

### Dependencies
The form fields use the existing avatar system, so ensure you have:
- `image_picker` for device access
- `permission_handler` for permissions
- Supabase storage buckets configured
- Avatar service properly set up

### Supabase Setup
Ensure you have the required storage buckets:
- `shop-logo` bucket
- `shop-banner` bucket

Follow the setup guide in `SUPABASE_SHOP_IMAGES_SETUP_GUIDE.md`.

## Best Practices

1. **Always handle errors**: Provide `onError` callbacks to show user-friendly error messages
2. **Use shopId for editing**: When editing existing shops, pass the `shopId` for automatic database updates
3. **Provide loading feedback**: The widgets show loading states automatically
4. **Validate forms**: Use form validation for required fields
5. **Test permissions**: Test on real devices to ensure permission handling works correctly

## Example Implementation

See `lib/examples/shop_image_form_example.dart` for a complete working example that demonstrates:
- Shop creation with image uploads
- Shop editing with existing images
- Error handling and user feedback
- Form validation and submission
- Loading states and user experience
