import 'package:flutter/material.dart';
import '../skeletons.dart';

/// Demo page to showcase all skeleton components
class SkeletonDemoPage extends StatefulWidget {
  const SkeletonDemoPage({super.key});

  @override
  State<SkeletonDemoPage> createState() => _SkeletonDemoPageState();
}

class _SkeletonDemoPageState extends State<SkeletonDemoPage> {
  bool _showSkeletons = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Skeleton Demo'),
        backgroundColor: const Color(0xFF1DA1F2),
        foregroundColor: Colors.white,
        actions: [
          Switch(
            value: _showSkeletons,
            onChanged: (value) {
              setState(() {
                _showSkeletons = value;
              });
            },
            activeColor: Colors.white,
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            'Profile Skeleton',
            _showSkeletons
                ? const SizedBox(
                    height: 300,
                    child: ProfileSkeleton(),
                  )
                : Container(
                    height: 300,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Text('Actual Profile Content'),
                    ),
                  ),
          ),
          
          _buildSection(
            'List Items',
            Column(
              children: [
                SkeletonListItem(isLoading: _showSkeletons),
                SkeletonListItem(isLoading: _showSkeletons, showTrailing: true),
                SkeletonListItem(isLoading: _showSkeletons, showAvatar: false),
              ],
            ),
          ),
          
          _buildSection(
            'Grid Items',
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
              ),
              itemCount: 4,
              itemBuilder: (context, index) {
                return SkeletonGridItem(isLoading: _showSkeletons);
              },
            ),
          ),
          
          _buildSection(
            'Post Items',
            Column(
              children: [
                SkeletonPostItem(isLoading: _showSkeletons),
                SkeletonPostItem(isLoading: _showSkeletons, showImage: false),
              ],
            ),
          ),
          
          _buildSection(
            'Shop Items',
            Column(
              children: [
                SkeletonShopItem(isLoading: _showSkeletons),
                SkeletonShopItem(isLoading: _showSkeletons),
              ],
            ),
          ),
          
          _buildSection(
            'Error Dialog',
            Center(
              child: ElevatedButton(
                onPressed: () {
                  SkeletonErrorDialog.show(
                    context,
                    title: 'Something went wrong',
                    message: 'This is a demo of the Spotify-style error dialog.',
                    onRetry: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Retry pressed!')),
                      );
                    },
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1DA1F2),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Show Error Dialog'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        child,
        const SizedBox(height: 24),
      ],
    );
  }
}
