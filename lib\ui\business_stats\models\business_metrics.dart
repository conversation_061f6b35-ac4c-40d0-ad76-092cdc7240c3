import 'package:equatable/equatable.dart';

class BusinessMetrics extends Equatable {
  final double totalRevenue;
  final double totalProfit;
  final double totalLoss;
  final int totalOrders;
  final int totalViews;
  final int totalCustomers;
  final double conversionRate;
  final double averageOrderValue;
  final double customerSatisfaction;
  final double growthRate;
  final DateTime lastUpdated;

  const BusinessMetrics({
    required this.totalRevenue,
    required this.totalProfit,
    required this.totalLoss,
    required this.totalOrders,
    required this.totalViews,
    required this.totalCustomers,
    required this.conversionRate,
    required this.averageOrderValue,
    required this.customerSatisfaction,
    required this.growthRate,
    required this.lastUpdated,
  });

  double get netProfit => totalProfit - totalLoss;
  double get profitMargin => totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;
  
  @override
  List<Object?> get props => [
        totalRevenue,
        totalProfit,
        totalLoss,
        totalOrders,
        totalViews,
        totalCustomers,
        conversionRate,
        averageOrderValue,
        customerSatisfaction,
        growthRate,
        lastUpdated,
      ];

  BusinessMetrics copyWith({
    double? totalRevenue,
    double? totalProfit,
    double? totalLoss,
    int? totalOrders,
    int? totalViews,
    int? totalCustomers,
    double? conversionRate,
    double? averageOrderValue,
    double? customerSatisfaction,
    double? growthRate,
    DateTime? lastUpdated,
  }) {
    return BusinessMetrics(
      totalRevenue: totalRevenue ?? this.totalRevenue,
      totalProfit: totalProfit ?? this.totalProfit,
      totalLoss: totalLoss ?? this.totalLoss,
      totalOrders: totalOrders ?? this.totalOrders,
      totalViews: totalViews ?? this.totalViews,
      totalCustomers: totalCustomers ?? this.totalCustomers,
      conversionRate: conversionRate ?? this.conversionRate,
      averageOrderValue: averageOrderValue ?? this.averageOrderValue,
      customerSatisfaction: customerSatisfaction ?? this.customerSatisfaction,
      growthRate: growthRate ?? this.growthRate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  // Mock data for demonstration
  static BusinessMetrics get mockData => BusinessMetrics(
        totalRevenue: 125000.0,
        totalProfit: 45000.0,
        totalLoss: 8500.0,
        totalOrders: 1250,
        totalViews: 28600,
        totalCustomers: 890,
        conversionRate: 4.37,
        averageOrderValue: 100.0,
        customerSatisfaction: 4.6,
        growthRate: 12.5,
        lastUpdated: DateTime.now(),
      );
}

class ChartDataPoint extends Equatable {
  final DateTime date;
  final double value;
  final String label;

  const ChartDataPoint({
    required this.date,
    required this.value,
    required this.label,
  });

  @override
  List<Object?> get props => [date, value, label];
}

class BusinessInsight extends Equatable {
  final String title;
  final String description;
  final String category;
  final InsightPriority priority;
  final InsightType type;
  final String actionText;
  final double impactScore;

  const BusinessInsight({
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.type,
    required this.actionText,
    required this.impactScore,
  });

  @override
  List<Object?> get props => [
        title,
        description,
        category,
        priority,
        type,
        actionText,
        impactScore,
      ];
}

enum InsightPriority { low, medium, high, critical }
enum InsightType { opportunity, warning, recommendation, trend }

class TimeRange extends Equatable {
  final String label;
  final int days;
  final String key;

  const TimeRange({
    required this.label,
    required this.days,
    required this.key,
  });

  @override
  List<Object?> get props => [label, days, key];

  static const List<TimeRange> ranges = [
    TimeRange(label: '7 Days', days: 7, key: 'week'),
    TimeRange(label: '30 Days', days: 30, key: 'month'),
    TimeRange(label: '90 Days', days: 90, key: 'quarter'),
    TimeRange(label: '365 Days', days: 365, key: 'year'),
  ];
}
