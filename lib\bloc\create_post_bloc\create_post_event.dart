import 'package:equatable/equatable.dart';

abstract class CreatePostEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class UpdatePostContentEvent extends CreatePostEvent {
  final String content;

  UpdatePostContentEvent(this.content);

  @override
  List<Object> get props => [content];
}

class AddImageEvent extends CreatePostEvent {
  final String imagePath;

  AddImageEvent(this.imagePath);

  @override
  List<Object> get props => [imagePath];
}

class RemoveImageEvent extends CreatePostEvent {
  final int imageIndex;

  RemoveImageEvent(this.imageIndex);

  @override
  List<Object> get props => [imageIndex];
}

class AddVideoEvent extends CreatePostEvent {
  final String videoPath;

  AddVideoEvent(this.videoPath);

  @override
  List<Object> get props => [videoPath];
}

class RemoveVideoEvent extends CreatePostEvent {}

class ToggleLocationEvent extends CreatePostEvent {
  final bool includeLocation;

  ToggleLocationEvent(this.includeLocation);

  @override
  List<Object> get props => [includeLocation];
}

class UpdateLocationEvent extends CreatePostEvent {
  final String location;

  UpdateLocationEvent(this.location);

  @override
  List<Object> get props => [location];
}

class AddTagEvent extends CreatePostEvent {
  final String tag;

  AddTagEvent(this.tag);

  @override
  List<Object> get props => [tag];
}

class RemoveTagEvent extends CreatePostEvent {
  final String tag;

  RemoveTagEvent(this.tag);

  @override
  List<Object> get props => [tag];
}

class TogglePrivacyEvent extends CreatePostEvent {
  final String privacy; // 'public', 'friends', 'private'

  TogglePrivacyEvent(this.privacy);

  @override
  List<Object> get props => [privacy];
}

class SubmitPostEvent extends CreatePostEvent {}

class SaveDraftEvent extends CreatePostEvent {}

class LoadDraftEvent extends CreatePostEvent {
  final String draftId;

  LoadDraftEvent(this.draftId);

  @override
  List<Object> get props => [draftId];
}

class ClearPostEvent extends CreatePostEvent {}

class ValidatePostEvent extends CreatePostEvent {}

class SchedulePostEvent extends CreatePostEvent {
  final DateTime scheduledTime;

  SchedulePostEvent(this.scheduledTime);

  @override
  List<Object> get props => [scheduledTime];
}
