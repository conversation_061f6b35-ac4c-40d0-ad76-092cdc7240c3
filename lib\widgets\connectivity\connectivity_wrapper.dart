import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:business_app/bloc/connectivity_bloc/connectivity_bloc.dart';
import 'package:business_app/widgets/connectivity/offline_banner.dart';
import 'package:business_app/widgets/connectivity/connectivity_snackbar.dart';

/// Professional connectivity wrapper that handles offline/online states
/// Implements UX patterns from big apps like Facebook, Instagram, WhatsApp
class ConnectivityWrapper extends StatefulWidget {
  final Widget child;
  final bool showBanner;
  final bool showSnackBar;
  final VoidCallback? onConnectivityRestored;
  final VoidCallback? onConnectivityLost;

  const ConnectivityWrapper({
    super.key,
    required this.child,
    this.showBanner = true,
    this.showSnackBar = true,
    this.onConnectivityRestored,
    this.onConnectivityLost,
  });

  @override
  State<ConnectivityWrapper> createState() => _ConnectivityWrapperState();
}

class _ConnectivityWrapperState extends State<ConnectivityWrapper>
    with TickerProviderStateMixin {
  bool _wasOffline = false;
  bool _isSnackBarVisible = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: _handleConnectivityChange,
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
        builder: (context, state) {
          return Column(
            children: [
              // Offline banner (like Instagram/Facebook)
              if (widget.showBanner && _shouldShowBanner(state))
                OfflineBanner(
                  connectivityState: state,
                  onRetry: () => _retryConnection(context),
                  onOpenSettings: () => _openSettings(context, state),
                ),

              // Main content
              Expanded(child: widget.child),
            ],
          );
        },
      ),
    );
  }

  /// Handle connectivity state changes
  void _handleConnectivityChange(
    BuildContext context,
    ConnectivityState state,
  ) {
    final isCurrentlyOffline =
        state.isDisconnected || state.hasLimitedConnection;

    // Track connectivity changes for callbacks
    if (_wasOffline && state.isConnected) {
      // Connection restored
      widget.onConnectivityRestored?.call();
      _hideOfflineSnackBar(context);
      _showConnectionRestoredSnackBar(context);
    } else if (!_wasOffline && isCurrentlyOffline) {
      // Connection lost
      widget.onConnectivityLost?.call();
      if (widget.showSnackBar) {
        _showOfflineSnackBar(context, state);
      }
    }

    _wasOffline = isCurrentlyOffline;
  }

  /// Show offline SnackBar (Facebook-style)
  void _showOfflineSnackBar(BuildContext context, ConnectivityState state) {
    if (_isSnackBarVisible) return;

    _isSnackBarVisible = true;

    ScaffoldMessenger.of(context).showSnackBar(
      ConnectivitySnackBar.offline(
        state: state,
        onRetry: () => _retryConnection(context),
        onOpenSettings: () => _openSettings(context, state),
        onDismiss: () => _isSnackBarVisible = false,
      ),
    );
  }

  /// Show connection restored SnackBar
  void _showConnectionRestoredSnackBar(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(ConnectivitySnackBar.connected(onDismiss: () {}));
  }

  /// Hide offline SnackBar
  void _hideOfflineSnackBar(BuildContext context) {
    if (_isSnackBarVisible) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      _isSnackBarVisible = false;
    }
  }

  /// Determine if banner should be shown
  bool _shouldShowBanner(ConnectivityState state) {
    return state.isDisconnected || state.hasLimitedConnection;
  }

  /// Retry connection
  void _retryConnection(BuildContext context) {
    context.read<ConnectivityBloc>().add(const ConnectivityRetryRequested());
  }

  /// Open device settings
  void _openSettings(BuildContext context, ConnectivityState state) {
    ConnectivitySettingsType settingsType;

    switch (state.connectivityResult) {
      case ConnectivityResult.wifi:
        settingsType = ConnectivitySettingsType.wifi;
        break;
      case ConnectivityResult.mobile:
        settingsType = ConnectivitySettingsType.mobileData;
        break;
      default:
        settingsType = ConnectivitySettingsType.general;
    }

    context.read<ConnectivityBloc>().add(
      ConnectivityOpenSettingsRequested(settingsType: settingsType),
    );
  }
}

/// Simplified connectivity wrapper for basic usage
class SimpleConnectivityWrapper extends StatelessWidget {
  final Widget child;

  const SimpleConnectivityWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      showBanner: false,
      showSnackBar: true,
      child: child,
    );
  }
}

/// Connectivity wrapper with custom callbacks
class CallbackConnectivityWrapper extends StatelessWidget {
  final Widget child;
  final VoidCallback? onOnline;
  final VoidCallback? onOffline;

  const CallbackConnectivityWrapper({
    super.key,
    required this.child,
    this.onOnline,
    this.onOffline,
  });

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      onConnectivityRestored: onOnline,
      onConnectivityLost: onOffline,
      child: child,
    );
  }
}
