import 'package:business_app/ui/chat_interface/new_chat_ui.dart';
import 'package:business_app/components/whatsapp_edit_dialog.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MyBio extends StatefulWidget {
  const MyBio({super.key});

  @override
  State<MyBio> createState() => _MyBioState();
}

// Reusable UseBioCard
class UseBioCard extends StatelessWidget {
  const UseBioCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.symmetric(vertical: 12),
      //child property
    );
  }
}

class _MyBioState extends State<MyBio> {
  final ScrollController _scrollController = ScrollController();
  bool _showUserName = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      setState(() {
        _showUserName = _scrollController.offset > 50;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          // Dynamic Sliver AppBar
          SliverAppBar(
            expandedHeight: 56.0,
            floating: true,
            pinned: true,
            snap: false,

            // backgroundColor: Colors.black.withOpacity(0.9),
            flexibleSpace: FlexibleSpaceBar(
              title: AnimatedSwitcher(
                duration: const Duration(milliseconds: 0),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(opacity: animation, child: child);
                },
                child: Text(
                  _showUserName ? 'Christina Lungu' : 'About',
                  key: ValueKey<bool>(_showUserName),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    //color: Colors.white,
                  ),
                ),
              ),

              /*background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.green.withOpacity(0.5), Colors.black],
                  ),
                ),
              ),*/
            ),
          ),

          // Bio Cards in a Column
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 0,
              ), // Reduced horizontal padding
              child: Column(
                children: const [BusinessBio(), LocationCard(), PersonalBio()],
              ),
            ),
          ),

          // Playlist Grid (unchanged)
        ],
      ),
    );
  }
}

//demo card info

//business bio
class BusinessBio extends StatefulWidget {
  const BusinessBio({Key? key}) : super(key: key);

  @override
  _BusinessBioState createState() => _BusinessBioState();
}

class _BusinessBioState extends State<BusinessBio> {
  // Business info data with character limits
  final Map<String, String> _businessInfo = {
    'category': 'Beauty, Fashions and Design',
    'location': 'Mitundu',
    'ads': 'Business has no ads',
    'joined': 'Joined July 2025',
    'views': '16,861,000 Views',
    'verification': 'Unverified Business',
  };

  // Character limits for different fields (only for editable fields)
  final Map<String, int> _characterLimits = {'category': 50, 'location': 40};

  // Non-editable fields (read-only)

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Business Info",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              const Text(
                "About Business Information",
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 20),
              _buildEditableCardRow("category", Icons.work),
              const SizedBox(height: 14),
              _buildEditableCardRow("location", Icons.location_on),
              const SizedBox(height: 14),
              _buildReadOnlyCardRow("ads", Icons.ads_click),
              const SizedBox(height: 14),
              _buildReadOnlyCardRow("joined", Icons.date_range),
              const SizedBox(height: 14),
              _buildReadOnlyCardRow("views", Icons.timeline),
              const SizedBox(height: 14),
              _buildReadOnlyCardRow("verification", Icons.verified_outlined),
              const SizedBox(height: 14),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEditableCardRow(String key, IconData icon) {
    return InkWell(
      onTap: () => _showEditDialog(key, icon),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Icon(icon, size: 18, color: Colors.grey[600]),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                _businessInfo[key] ?? '',
                style: const TextStyle(fontSize: 16),
              ),
            ),
            Icon(Icons.edit, size: 16, color: Colors.grey[500]),
          ],
        ),
      ),
    );
  }

  Widget _buildReadOnlyCardRow(String key, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[500]),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              _businessInfo[key] ?? '',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[700], // Slightly muted color for read-only
              ),
            ),
          ),
          Icon(Icons.lock_outline, size: 16, color: Colors.grey[400]),
        ],
      ),
    );
  }

  void _showEditDialog(String key, IconData icon) {
    WhatsAppEditDialog.show(
      context: context,
      title: _getFieldTitle(key),
      initialValue: _businessInfo[key] ?? '',
      maxLength: _characterLimits[key] ?? 50,
      icon: icon,
      accentColor: const Color(0xFF1DA1F2), // Business blue color
      onSave: (String newValue) {
        setState(() {
          _businessInfo[key] = newValue;
        });
      },
    );
  }

  String _getFieldTitle(String key) {
    switch (key) {
      case 'category':
        return 'Business Category';
      case 'location':
        return 'Business Location';
      case 'ads':
        return 'Advertisement Status';
      case 'joined':
        return 'Join Date';
      case 'views':
        return 'Total Views';
      case 'verification':
        return 'Verification Status';
      default:
        return 'Edit';
    }
  }
}

//personal info

//business bio
class PersonalBio extends StatefulWidget {
  const PersonalBio({Key? key}) : super(key: key);

  @override
  _PersonalBioState createState() => _PersonalBioState();
}

class _PersonalBioState extends State<PersonalBio> {
  // Personal info data with character limits
  final Map<String, String> _personalInfo = {
    'location': 'Lives in Lilongwe, Malawi',
    'education': 'Student at Catholic University',
    'website': 'www.chris-fashions.com',
    'birthday': 'Birthday, 16 September',
    'business': 'Chris Fashions',
    'facebook': 'Facebook',
    'tiktok': 'TickTock',
    'instagram': 'Instagram',
  };

  // Character limits for different fields
  final Map<String, int> _characterLimits = {
    'location': 60,
    'education': 80,
    'website': 50,
    'birthday': 30,
    'business': 40,
    'facebook': 30,
    'tiktok': 30,
    'instagram': 30,
  };

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Personal Info",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              const Text(
                "About Christina Lungu",
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 20),
              _buildEditableCardRow("location", Icons.home),
              const SizedBox(height: 14),
              _buildEditableCardRow("education", Icons.school),
              const SizedBox(height: 14),
              _buildEditableCardRow("website", Icons.link),
              const SizedBox(height: 14),
              _buildEditableCardRow("birthday", Icons.cake),
              const SizedBox(height: 14),
              _buildEditableCardRow("business", Icons.store),
              const SizedBox(height: 14),
              const Text(
                "Social Links",
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 14),
              _buildEditableCardRow("facebook", Icons.facebook),
              const SizedBox(height: 14),
              _buildEditableCardRow("tiktok", Icons.tiktok),
              const SizedBox(height: 14),
              _buildEditableCardRow("instagram", FontAwesomeIcons.instagram),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEditableCardRow(String key, IconData icon) {
    return InkWell(
      onTap: () => _showEditDialog(key, icon),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Icon(icon, size: 18, color: Colors.grey[600]),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                _personalInfo[key] ?? '',
                style: const TextStyle(fontSize: 16),
              ),
            ),
            Icon(Icons.mode_edit_outline, size: 16, color: Colors.grey[500]),
          ],
        ),
      ),
    );
  }

  void _showEditDialog(String key, IconData icon) {
    WhatsAppEditDialog.show(
      context: context,
      title: _getFieldTitle(key),
      initialValue: _personalInfo[key] ?? '',
      maxLength: _characterLimits[key] ?? 50,
      icon: icon,
      accentColor: const Color(0xFF25D366), // WhatsApp green color
      onSave: (String newValue) {
        setState(() {
          _personalInfo[key] = newValue;
        });
      },
    );
  }

  String _getFieldTitle(String key) {
    switch (key) {
      case 'location':
        return 'Location';
      case 'education':
        return 'Education';
      case 'website':
        return 'Website';
      case 'birthday':
        return 'Birthday';
      case 'business':
        return 'Business';
      case 'facebook':
        return 'Facebook';
      case 'tiktok':
        return 'TikTok';
      case 'instagram':
        return 'Instagram';
      default:
        return 'Edit';
    }
  }
}

//business location card

class LocationCard extends StatelessWidget {
  const LocationCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      //color: Colors.grey[900],
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Quick Access",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Row(
              children: [
                // Icon(Icons.location_on, color: Colors.blue[300], size: 40),
                // Place this where you want the card to appear
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.2),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.location_on,
                      //color: Colors.blue[300],
                      size: 40,
                    ),
                  ),
                ),

                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'linktr.ee/chris-fashions',
                        style: TextStyle(
                          color: Colors.blue[300],
                          //decoration: TextDecoration.underline,
                        ),
                      ),
                      const SizedBox(height: 1),
                      const Text(
                        'Christina Lungu Rever',
                        style: TextStyle(
                          //color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 1),
                      const Text(
                        "Open 8AM - 4PM • Mon to Fri",
                        style: TextStyle(
                          color: Colors.green,

                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            // ...existing code...
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle directions
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        'Get directions',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ChatInterfaceUI(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text('Contact', textAlign: TextAlign.center),
                    ),
                  ),
                ),
              ],
            ),
            // ...existing code...
          ],
        ),
      ),
    );
  }
}

//business location card ends here
