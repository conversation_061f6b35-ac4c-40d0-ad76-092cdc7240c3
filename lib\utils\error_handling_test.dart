import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import '../services/error_handler_service.dart';
import '../models/auth/auth_result.dart';

/// Test file to demonstrate the improved error handling for same password error
/// This file can be used for testing and debugging error handling scenarios
class ErrorHandlingTest {
  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  /// Test the same password error handling
  static void testSamePasswordError() {
    _logger.i('🧪 Testing Same Password Error Handling...');

    // Simulate the AuthApiException that occurs when user tries to set same password
    final authApiException = AuthApiException(
      'New password should be different from the old password.',
      statusCode: '422',
      code: 'same_password',
    );

    // Test 1: ErrorHandlerService.getDisplayMessage
    _logger.i('1️⃣ Testing ErrorHandlerService.getDisplayMessage:');
    final displayMessage = ErrorHandlerService.getDisplayMessage(
      authApiException,
    );
    _logger.i('   Result: "$displayMessage"');
    _logger.i(
      '   ✅ Expected: User-friendly message about choosing different password',
    );

    // Test 2: AuthError.fromException
    _logger.i('2️⃣ Testing AuthError.fromException:');
    final authError = AuthError.fromException(authApiException);
    _logger.i('   Type: ${authError.type}');
    _logger.i('   Message: "${authError.message}"');
    _logger.i(
      '   ✅ Expected: AuthErrorType.samePassword with user-friendly message',
    );

    // Test 3: ErrorHandlerService.handleAuthError
    _logger.i('3️⃣ Testing ErrorHandlerService.handleAuthError:');
    final handledError = ErrorHandlerService.handleAuthError(
      authApiException,
      context: 'Password Update Test',
    );
    _logger.i('   Type: ${handledError.type}');
    _logger.i('   Message: "${handledError.message}"');
    _logger.i('   ✅ Expected: Proper error handling with logging');

    _logger.i(
      '🎉 All tests completed! The same password error should now show user-friendly messages.',
    );
  }

  /// Test other common AuthApiException scenarios
  static void testOtherAuthApiErrors() {
    _logger.i('🧪 Testing Other AuthApiException Scenarios...');

    final testCases = [
      {
        'name': 'Weak Password',
        'exception': AuthApiException(
          'Password is too weak',
          statusCode: '422',
          code: 'weak_password',
        ),
        'expectedType': AuthErrorType.weakPassword,
      },
      {
        'name': 'Invalid Credentials',
        'exception': AuthApiException(
          'Invalid login credentials',
          statusCode: '400',
          code: 'invalid_credentials',
        ),
        'expectedType': AuthErrorType.invalidCredentials,
      },
      {
        'name': 'Rate Limit',
        'exception': AuthApiException(
          'Email rate limit exceeded',
          statusCode: '429',
          code: 'over_email_send_rate_limit',
        ),
        'expectedType': AuthErrorType.tooManyRequests,
      },
    ];

    for (int i = 0; i < testCases.length; i++) {
      final testCase = testCases[i];
      _logger.i('${i + 1}️⃣ Testing ${testCase['name']}:');

      final exception = testCase['exception'] as AuthApiException;
      final authError = AuthError.fromException(exception);
      final displayMessage = ErrorHandlerService.getDisplayMessage(exception);

      _logger.i('   AuthError Type: ${authError.type}');
      _logger.i('   Display Message: "$displayMessage"');
      _logger.i('   ✅ User-friendly message generated');
    }
  }

  /// Demonstrate the complete error flow
  static void demonstrateErrorFlow() {
    _logger.i('🔄 Demonstrating Complete Error Flow...');

    _logger.i('Scenario: User tries to update password to the same password');
    _logger.i('1. Supabase returns: AuthApiException(code: same_password)');
    _logger.i('2. AuthService._handleAuthError() processes the exception');
    _logger.i('3. ErrorHandlerService.handleAuthError() creates AuthError');
    _logger.i(
      '4. AuthError.fromException() identifies it as samePassword type',
    );
    _logger.i(
      '5. UI shows user-friendly message: "New password must be different..."',
    );
    _logger.i(
      '6. User understands what to do and chooses a different password',
    );
    _logger.i('✅ Error flow is now user-friendly and informative!');
  }

  /// Run all tests
  static void runAllTests() {
    _logger.i('🚀 Starting Error Handling Tests...');
    _logger.i('=' * 60);

    testSamePasswordError();
    _logger.i('=' * 60);

    testOtherAuthApiErrors();
    _logger.i('=' * 60);

    demonstrateErrorFlow();
    _logger.i('=' * 60);

    _logger.i('🎯 Summary:');
    _logger.i('✅ Same password error now shows user-friendly message');
    _logger.i('✅ AuthApiException errors are properly handled');
    _logger.i('✅ Error types are correctly identified');
    _logger.i('✅ Users get clear guidance on what to do');
    _logger.i(
      '🔧 Implementation complete! Users will no longer see technical errors.',
    );
  }
}

/// Extension to make testing easier
extension ErrorTestingExtension on AuthApiException {
  /// Create a test AuthApiException for same password error
  static AuthApiException samePasswordError() {
    return AuthApiException(
      'New password should be different from the old password.',
      statusCode: '422',
      code: 'same_password',
    );
  }

  /// Create a test AuthApiException for weak password error
  static AuthApiException weakPasswordError() {
    return AuthApiException(
      'Password is too weak',
      statusCode: '422',
      code: 'weak_password',
    );
  }
}
