import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase configuration and initialization
class SupabaseConfig {
  static late SupabaseClient _client;

  // Get these from your Supabase project settings
  static const String supabaseUrl = 'https://xvnzbkllxdssgdpwwnjl.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh2bnpia2xseGRzc2dkcHd3bmpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3ODUzNDYsImV4cCI6MjA2ODM2MTM0Nn0.P4fp9oE5RBTAq-iUghLmznI6B8o41Qy8DOnjPGqY0LY';

  /// Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      authOptions: const FlutterAuthClientOptions(
        authFlowType: AuthFlowType.pkce, // More secure than implicit flow
      ),
    );
    _client = Supabase.instance.client;
  }

  /// Get the Supabase client instance
  static SupabaseClient get client => _client;

  /// Get current authenticated user
  static User? get currentUser => _client.auth.currentUser;

  /// Get current session
  static Session? get currentSession => _client.auth.currentSession;

  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  /// Sign out current user
  static Future<void> signOut() async {
    await _client.auth.signOut();
  }
}

/// Database table names for consistent referencing
class DatabaseTables {
  static const String profiles = 'profiles';
  static const String shops = 'shops';
  static const String categories = 'categories';
  static const String products = 'products';
  static const String posts = 'posts';
  static const String follows = 'follows';
  static const String likes = 'likes';
  static const String comments = 'comments';
  static const String commentLikes = 'comment_likes';
  static const String orders = 'orders';
  static const String orderItems = 'order_items';
  static const String chats = 'chats';
  static const String messages = 'messages';
  static const String reviews = 'reviews';
  static const String reviewHelpfulness = 'review_helpfulness';
  static const String notifications = 'notifications';
}

/// Common database operations and utilities
class DatabaseUtils {
  /// Execute a stored procedure/function
  static Future<T?> executeFunction<T>(
    String functionName,
    Map<String, dynamic>? params,
  ) async {
    try {
      final response = await SupabaseConfig.client.rpc(
        functionName,
        params: params,
      );
      return response as T?;
    } catch (e) {
      throw SupabaseException('Function execution failed: $e');
    }
  }

  /// Get paginated results
  static Future<List<Map<String, dynamic>>> getPaginatedResults(
    String table, {
    String columns = '*',
    int limit = 20,
    int offset = 0,
    String? orderBy,
    bool ascending = false,
    Map<String, dynamic>? filters,
  }) async {
    try {
      dynamic query = SupabaseConfig.client.from(table).select(columns);

      // Apply filters
      if (filters != null) {
        filters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      // Apply ordering
      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }

      // Apply pagination
      final response = await query.range(offset, offset + limit - 1);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw SupabaseException('Query failed: $e');
    }
  }
}

/// Query builder helper for consistent querying
class QueryBuilder {
  /// Build select query with common patterns
  static PostgrestTransformBuilder select(
    String table, {
    String columns = '*',
    int? limit,
    String? orderBy,
    bool ascending = false,
  }) {
    dynamic query = SupabaseConfig.client.from(table).select(columns);

    if (orderBy != null) {
      query = query.order(orderBy, ascending: ascending);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return query;
  }

  /// Build insert query
  static PostgrestFilterBuilder insert(
    String table,
    Map<String, dynamic> data,
  ) {
    return SupabaseConfig.client.from(table).insert(data);
  }

  /// Build update query
  static PostgrestFilterBuilder update(
    String table,
    Map<String, dynamic> data,
  ) {
    return SupabaseConfig.client.from(table).update(data);
  }

  /// Build delete query
  static PostgrestFilterBuilder delete(String table) {
    return SupabaseConfig.client.from(table).delete();
  }
}

/// Real-time subscription helper
class RealtimeHelper {
  /// Subscribe to table changes
  static RealtimeChannel subscribeToTable(
    String table,
    void Function(PostgresChangePayload) callback, {
    String event = '*',
    String? schema = 'public',
  }) {
    return SupabaseConfig.client
        .channel('public:$table')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: schema ?? 'public',
          table: table,
          callback: callback,
        )
        .subscribe();
  }

  /// Subscribe to user-specific changes
  static RealtimeChannel subscribeToUserData(
    String userId,
    void Function(PostgresChangePayload) callback,
  ) {
    return SupabaseConfig.client
        .channel('user:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: DatabaseTables.notifications,
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: callback,
        )
        .subscribe();
  }
}

/// Custom exception for Supabase operations
class SupabaseException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  const SupabaseException(this.message, {this.code, this.details});

  @override
  String toString() => 'SupabaseException: $message';
}

// Note: AuthStatus and SignupStep enums are now defined in lib/bloc/auth_bloc/auth_state.dart
// to avoid duplication and ensure consistency across the app
