import 'package:equatable/equatable.dart';

enum ProfileStatus { initial, loading, loaded, error, updating }

enum UsernameValidationStatus {
  initial,
  checking,
  available,
  unavailable,
  error,
}

class ProfileState extends Equatable {
  final ProfileStatus status;
  final String name;
  final String username;
  final String bio;
  final String location;
  final String website;
  final String profileImageUrl;
  final String backgroundImageUrl;
  final int followersCount;
  final int followingCount;
  final int postsCount;
  final bool isFollowing;
  final bool isOwnProfile;
  final int selectedTabIndex;
  final bool showOptions;
  final String? errorMessage;
  final UsernameValidationStatus usernameValidationStatus;
  final String? usernameValidationError;

  const ProfileState({
    this.status = ProfileStatus.initial,
    this.name = '',
    this.username = '',
    this.bio = '',
    this.location = '',
    this.website = '',
    this.profileImageUrl = '',
    this.backgroundImageUrl = '',
    this.followersCount = 0,
    this.followingCount = 0,
    this.postsCount = 0,
    this.isFollowing = false,
    this.isOwnProfile = true,
    this.selectedTabIndex = 0,
    this.showOptions = false,
    this.errorMessage,
    this.usernameValidationStatus = UsernameValidationStatus.initial,
    this.usernameValidationError,
  });

  ProfileState copyWith({
    ProfileStatus? status,
    String? name,
    String? username,
    String? bio,
    String? location,
    String? website,
    String? profileImageUrl,
    String? backgroundImageUrl,
    int? followersCount,
    int? followingCount,
    int? postsCount,
    bool? isFollowing,
    bool? isOwnProfile,
    int? selectedTabIndex,
    bool? showOptions,
    String? errorMessage,
    UsernameValidationStatus? usernameValidationStatus,
    String? usernameValidationError,
  }) {
    return ProfileState(
      status: status ?? this.status,
      name: name ?? this.name,
      username: username ?? this.username,
      bio: bio ?? this.bio,
      location: location ?? this.location,
      website: website ?? this.website,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      backgroundImageUrl: backgroundImageUrl ?? this.backgroundImageUrl,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      postsCount: postsCount ?? this.postsCount,
      isFollowing: isFollowing ?? this.isFollowing,
      isOwnProfile: isOwnProfile ?? this.isOwnProfile,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
      showOptions: showOptions ?? this.showOptions,
      errorMessage: errorMessage ?? this.errorMessage,
      usernameValidationStatus:
          usernameValidationStatus ?? this.usernameValidationStatus,
      usernameValidationError:
          usernameValidationError ?? this.usernameValidationError,
    );
  }

  @override
  List<Object?> get props => [
    status,
    name,
    username,
    bio,
    location,
    website,
    profileImageUrl,
    backgroundImageUrl,
    followersCount,
    followingCount,
    postsCount,
    isFollowing,
    isOwnProfile,
    selectedTabIndex,
    showOptions,
    errorMessage,
    usernameValidationStatus,
    usernameValidationError,
  ];
}
