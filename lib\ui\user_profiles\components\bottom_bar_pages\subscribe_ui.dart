import 'package:business_app/const/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/subscription_bloc/subscription_bloc.dart';
import 'package:business_app/bloc/subscription_bloc/subscription_event.dart';
import 'package:business_app/bloc/subscription_bloc/subscription_state.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';

//new upgrade page

class SubscriptionPage extends StatefulWidget {
  final void Function(bool isVisible)? onScrollBottomBarVisibility;

  const SubscriptionPage({Key? key, this.onScrollBottomBarVisibility})
    : super(key: key);

  @override
  State<SubscriptionPage> createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  @override
  void initState() {
    super.initState();
    // Load subscription plans when page opens
    context.read<SubscriptionBloc>().add(LoadSubscriptionPlansEvent());
  }

  Color _getPlanColor(int index) {
    final colors = [
      Colors.pinkAccent,
      Colors.deepPurpleAccent,
      Colors.cyanAccent,
      Colors.orangeAccent,
    ];
    return colors[index % colors.length];
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SubscriptionBloc, SubscriptionState>(
      listener: (context, state) {
        if (state.hasActiveSubscription &&
            state.status == SubscriptionStatus.loaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Successfully subscribed!')),
          );
        } else if (state.status == SubscriptionStatus.error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${state.errorMessage}')),
          );
        }
      },
      child: BlocBuilder<SubscriptionBloc, SubscriptionState>(
        builder: (context, state) {
          return NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification notification) {
              if (notification is UserScrollNotification &&
                  widget.onScrollBottomBarVisibility != null) {
                // <-- use widget.
                if (notification.direction == ScrollDirection.reverse) {
                  widget.onScrollBottomBarVisibility!(false); // <-- use widget.
                } else if (notification.direction == ScrollDirection.forward) {
                  widget.onScrollBottomBarVisibility!(true); // <-- use widget.
                }
              }
              return false;
            },

            child: Scaffold(
              extendBodyBehindAppBar: true,
              body: CustomScrollView(
                physics: const BouncingScrollPhysics(),
                slivers: [
                  SliverAppBar(
                    backgroundColor: Colors.transparent,
                    expandedHeight: 300,
                    floating: false,
                    pinned: true,
                    stretch: true,
                    elevation: 0,
                    leading: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.arrow_back_ios_new,
                          color: Colors.white,
                          size: 20,
                        ),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ),
                    actions: [
                      Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.more_vert,
                            color: Colors.white,
                            size: 20,
                          ),
                          onPressed: () {},
                        ),
                      ),
                    ],
                    flexibleSpace: FlexibleSpaceBar(
                      stretchModes: const [
                        StretchMode.zoomBackground,
                        StretchMode.blurBackground,
                      ],
                      centerTitle: true,
                      titlePadding: const EdgeInsets.only(
                        left: 16,
                        right: 16,
                        bottom: 16,
                      ),
                      title: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.purple.withValues(alpha: 0.9),
                              Colors.blue.withValues(alpha: 0.9),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: const Text(
                          'Trademate Premium',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                      background: Stack(
                        fit: StackFit.expand,
                        children: [
                          // Background gradient
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.purple.shade900,
                                  Colors.blue.shade900,
                                  Colors.black,
                                ],
                                stops: const [0.0, 0.6, 1.0],
                              ),
                            ),
                          ),
                          // Background image with overlay
                          Container(
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: NetworkImage(
                                  'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
                                ),
                                fit: BoxFit.cover,
                                colorFilter: ColorFilter.mode(
                                  Colors.black.withValues(alpha: 0.4),
                                  BlendMode.overlay,
                                ),
                              ),
                            ),
                          ),
                          // Spotify-style floating elements
                          Positioned(
                            top: 80,
                            left: 30,
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(30),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                Icons.attach_money,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 60,
                            right: 40,
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.amber.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.amber.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: 20,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 120,
                            right: 80,
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(15),
                                border: Border.all(
                                  color: Colors.green.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                Icons.attach_money,
                                color: Colors.green,
                                size: 15,
                              ),
                            ),
                          ),
                          // Central logo
                          Center(
                            child: Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: ClipOval(
                                child: Image.asset(
                                  Assets.trademateLogo,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                          // Bottom gradient overlay
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: 100,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withValues(alpha: 0.8),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Text(
                        'Available Plans',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.normal,
                          color: Colors.amber,
                        ),
                      ),
                    ),
                  ),
                  if (state.status == SubscriptionStatus.loading)
                    const SliverToBoxAdapter(
                      child: Center(child: CircularProgressIndicator()),
                    )
                  else if (state.status == SubscriptionStatus.error)
                    SliverToBoxAdapter(
                      child: Center(
                        child: Text('Error: ${state.errorMessage}'),
                      ),
                    )
                  else if (state.plans.isEmpty)
                    const SliverToBoxAdapter(
                      child: Center(
                        child: Text('No subscription plans available'),
                      ),
                    )
                  else
                    SliverList(
                      delegate: SliverChildBuilderDelegate((context, index) {
                        final plan = state.plans[index];
                        return SubscriptionCard(
                          title: plan.title,
                          price: '${plan.price} / ${plan.duration}',
                          color: _getPlanColor(index),
                          benefits: plan.benefits,
                          index: index,
                          isSelected: state.selectedPlanIndex == index,
                          onTap: () {
                            context.read<SubscriptionBloc>().add(
                              SelectPlanEvent(index),
                            );
                          },
                        );
                      }, childCount: state.plans.length),
                    ),
                ],
              ),

              //~floating action button for all pages
              floatingActionButton: SubscribeCustomExpandableFab(),
              floatingActionButtonLocation: ExpandableFab.location,
            ),
          );
        },
      ),
    );
  }
}

class SubscriptionCard extends StatelessWidget {
  final String title;
  final String price;
  final Color color;
  final List<String> benefits;
  final VoidCallback onTap;
  final int index;
  final bool isSelected;

  const SubscriptionCard({
    super.key,
    required this.title,
    required this.price,
    required this.color,
    required this.benefits,
    required this.onTap,
    required this.index,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Animate(
      effects: [
        FadeEffect(duration: 400.ms),
        SlideEffect(
          duration: 500.ms,
          curve: Curves.easeOut,
          begin: const Offset(0.1, 0),
        ),
      ],
      delay: (100 * index).ms,
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: 300.ms,
          margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? color.withValues(alpha: 0.2)
                    : Colors.black.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(10),
            border: isSelected ? Border.all(color: color, width: 2) : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // const Icon(Icons.star, color: Colors.white),
                  ClipOval(
                    child: Image.asset(
                      Assets.trademateLogo,
                      width: 30.0,
                      height: 30.0,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                price,
                style: const TextStyle(fontSize: 16, color: Colors.white),
              ),
              const SizedBox(height: 12),
              ...benefits.map(
                (b) => Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      size: 18,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        b,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color.withValues(alpha: 0.9),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  onPressed: onTap,
                  child: Text(
                    'Get Premium $title',
                    style: const TextStyle(color: Colors.black),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

//for main FAB ---------------------------------------------------------------------------------------------------------
class SubscribeCustomExpandableFab extends StatelessWidget {
  const SubscribeCustomExpandableFab({super.key});

  @override
  Widget build(BuildContext context) {
    final GlobalKey<ExpandableFabState> fabKey =
        GlobalKey<ExpandableFabState>();

    return ExpandableFab(
      key: fabKey,
      type: ExpandableFabType.up,
      distance: 60,
      childrenAnimation: ExpandableFabAnimation.none,
      // fanAngle: 40,
      openButtonBuilder: RotateFloatingActionButtonBuilder(
        child: const Icon(Icons.add, size: 40),
        fabSize: ExpandableFabSize.regular,
        foregroundColor: Colors.amber,
        backgroundColor: Colors.blue,
        shape: const CircleBorder(),
        angle: 3.14 * 2,
        elevation: 2,
      ),
      closeButtonBuilder: FloatingActionButtonBuilder(
        size: 24,
        builder: (
          BuildContext context,
          void Function()? onPressed,
          Animation<double> progress,
        ) {
          return IconButton(
            onPressed: onPressed,
            icon: const Icon(
              Icons.close_outlined,
              size: 40,
              color: Colors.amber,
            ),
          );
        },
      ),
      overlayStyle: ExpandableFabOverlayStyle(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.9)
                : Colors.white.withValues(alpha: 0.9),
      ),

      children: [
        _buildActionRow(
          label: 'Promo Code',
          icon: Icons.local_offer,
          onPressed: () {},
        ),
        _buildActionRow(
          label: 'Gift Premium',
          icon: Icons.card_giftcard,
          onPressed: () {
            // Navigator.of(
            // context,
            // ).push(MaterialPageRoute(builder: (context) => BusinessStats()));
          },
        ),
        _buildActionRow(
          label: 'Contact Support',
          icon: Icons.help_outline,
          onPressed: () {},
        ),
        _buildActionRow(
          label: 'Restore Purchase',
          icon: Icons.restore,
          onPressed: () {},
        ),

        /*FloatingActionButton.small(
          heroTag: null,
          tooltip: 'Add',
          onPressed: () => _handleAction('Add'),
          child: const Icon(Icons.add),
        ),*/
      ],
    );
  }

  Widget _buildActionRow({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
            //color: Colors.deepPurple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(32),
          ),
          child: Text(label, style: const TextStyle(fontSize: 16)),
        ),
        FloatingActionButton.small(
          heroTag: null,
          tooltip: label,
          onPressed: onPressed,
          backgroundColor: Colors.white,
          foregroundColor: Colors.blue, //Colors.deepPurple,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(32),
          ),
          // elevation: 2,
          highlightElevation: 4,
          focusElevation: 4,
          hoverElevation: 4,

          // splashColor: Colors.deepPurple.withOpacity(0.2),
          // highlightColor: Colors.deepPurple.withOpacity(0.2),
          child: Icon(icon),
        ),
      ],
    );
  }
}
