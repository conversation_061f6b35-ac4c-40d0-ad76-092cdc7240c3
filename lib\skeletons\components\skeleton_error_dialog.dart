import 'package:flutter/material.dart';

/// Spotify-style error dialog
class SkeletonErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final String buttonText;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;

  const SkeletonErrorDialog({
    super.key,
    this.title = 'Something went wrong',
    this.message = 'We couldn\'t load your content. Please try again.',
    this.buttonText = 'OK',
    this.onRetry,
    this.onDismiss,
  });

  /// Show the error dialog
  static Future<void> show(
    BuildContext context, {
    String? title,
    String? message,
    String? buttonText,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SkeletonErrorDialog(
          title: title ?? 'Something went wrong',
          message:
              message ?? 'We couldn\'t load your content. Please try again.',
          buttonText: buttonText ?? 'OK',
          onRetry: onRetry,
          onDismiss: onDismiss,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 320),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF2A2A2A) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Error icon
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF6B6B).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline_rounded,
                  size: 32,
                  color: Color(0xFFFF6B6B),
                ),
              ),

              const SizedBox(height: 20),

              // Title
              Text(
                title,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              // Message
              Text(
                message,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark ? Colors.white70 : Colors.black54,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Buttons
              Row(
                children: [
                  if (onRetry != null) ...[
                    Expanded(
                      child: _buildButton(
                        context,
                        text: 'Retry',
                        onPressed: () {
                          Navigator.of(context).pop();
                          onRetry?.call();
                        },
                        isPrimary: false,
                        isDark: isDark,
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: _buildButton(
                      context,
                      text: buttonText,
                      onPressed: () {
                        Navigator.of(context).pop();
                        onDismiss?.call();
                      },
                      isPrimary: true,
                      isDark: isDark,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildButton(
    BuildContext context, {
    required String text,
    required VoidCallback onPressed,
    required bool isPrimary,
    required bool isDark,
  }) {
    return SizedBox(
      height: 48,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isPrimary
                  ? const Color(0xFF1DB954) // Spotify green
                  : (isDark
                      ? const Color(0xFF3E3E3E)
                      : const Color(0xFFF5F5F5)),
          foregroundColor:
              isPrimary
                  ? Colors.white
                  : (isDark ? Colors.white : Colors.black87),
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
        ),
      ),
    );
  }
}
