import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

import 'package:business_app/ui/home_page.dart';
import 'package:business_app/ui/splash/loading_splash.dart';

class OtpLoginPage extends StatefulWidget {
  const OtpLoginPage({super.key});

  @override
  State<OtpLoginPage> createState() => _OtpLoginPageState();
}

class _OtpLoginPageState extends State<OtpLoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _identifierController = TextEditingController();
  final _codeController = TextEditingController();

  String? _identifierError;
  String? _codeError;
  bool _isResending = false;
  bool _isVerifying = false; // Race condition prevention
  int _resendCooldown = 0;
  Timer? _cooldownTimer;

  @override
  void dispose() {
    _identifierController.dispose();
    _codeController.dispose();
    _cooldownTimer?.cancel();
    super.dispose();
  }

  void _onSendOtp() {
    final identifier = _identifierController.text.trim();

    if (identifier.isEmpty) {
      setState(() {
        _identifierError = 'Please enter your email or phone number';
      });
      return;
    }

    if (!_isValidIdentifier(identifier)) {
      setState(() {
        if (identifier.contains('@')) {
          _identifierError = 'Please enter a valid email address';
        } else {
          _identifierError = _getPhoneValidationError(identifier);
        }
      });
      return;
    }

    final isEmail = identifier.contains('@');

    context.read<AuthBloc>().add(
      LoginOtpRequested(emailOrPhone: identifier, isEmail: isEmail),
    );
  }

  void _onVerifyOtp() {
    // Prevent multiple simultaneous verification attempts
    if (_isVerifying) return;

    final code = _codeController.text.trim();

    // Validate code format more strictly
    if (code.isEmpty) {
      setState(() {
        _codeError = 'Please enter the verification code';
      });
      return;
    }

    if (code.length != 6) {
      setState(() {
        _codeError = 'Please enter the 6-digit code';
      });
      return;
    }

    // Ensure code contains only numbers
    if (!RegExp(r'^\d{6}$').hasMatch(code)) {
      setState(() {
        _codeError = 'Code must contain only numbers';
      });
      return;
    }

    setState(() {
      _isVerifying = true;
      _codeError = null;
    });

    context.read<AuthBloc>().add(LoginOtpVerificationSubmitted(code: code));
  }

  void _onResendCode() async {
    if (_isResending || _resendCooldown > 0) return;

    setState(() {
      _isResending = true;
    });

    final identifier = _identifierController.text.trim();
    final isEmail = identifier.contains('@');

    context.read<AuthBloc>().add(
      LoginOtpRequested(emailOrPhone: identifier, isEmail: isEmail),
    );

    // Start cooldown
    setState(() {
      _isResending = false;
      _resendCooldown = 60;
    });

    _cooldownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _resendCooldown--;
      });
      if (_resendCooldown <= 0) {
        timer.cancel();
      }
    });
  }

  bool _isValidIdentifier(String identifier) {
    if (identifier.trim().isEmpty) return false;

    // Email validation
    if (identifier.contains('@')) {
      return RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      ).hasMatch(identifier);
    }

    // Phone validation using phone_numbers_parser
    return _isValidPhoneNumber(identifier);
  }

  bool _isValidPhoneNumber(String phoneNumber) {
    // Handle empty or null input
    if (phoneNumber.trim().isEmpty) return false;

    try {
      // Use phone_numbers_parser for professional validation
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      // Check if the phone number is valid
      final isValid = parsedNumber.isValid();

      // For OTP login, we accept both mobile and fixed line numbers
      return isValid;
    } catch (e) {
      // If parsing fails, try fallback validation for local numbers
      final digitsOnly = phoneNumber.replaceAll(RegExp(r'\D'), '');

      // Basic fallback: must have at least 10 digits and not exceed 15
      if (digitsOnly.length >= 10 && digitsOnly.length <= 15) {
        return true;
      }

      return false;
    }
  }

  String _getPhoneValidationError(String phoneNumber) {
    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      if (!parsedNumber.isValid()) {
        // Try to provide more specific error messages
        if (phoneNumber.length < 8) {
          return 'Phone number is too short';
        } else if (phoneNumber.length > 15) {
          return 'Phone number is too long';
        } else {
          return 'Please enter a valid phone number (e.g., +265 123 456 789)';
        }
      }

      return 'Invalid phone number format';
    } catch (e) {
      // Handle parsing errors
      if (!phoneNumber.startsWith('+')) {
        return 'Phone number must include country code (e.g., +265)';
      }
      return 'Please enter a valid phone number with country code';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          // Reset verification state on any state change
          if (_isVerifying) {
            setState(() {
              _isVerifying = false;
            });
          }

          if (state.status == AuthStatus.authenticated) {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder:
                    (context) => LoadingSplash(
                      message: 'Welcome back! Setting up your workspace...',
                      duration: const Duration(milliseconds: 1800),
                      destination: const MyHomePage(),
                    ),
              ),
              (route) => false,
            );
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),

                        // Header
                        const AuthHeader(
                          title: 'Sign in with OTP',
                          subtitle:
                              'Enter your email or phone to receive a verification code',
                        ),

                        const SizedBox(height: 40),

                        // Show different UI based on login step
                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            if (state.loginStep == LoginStep.otpVerification) {
                              return _buildOtpVerificationUI(state);
                            } else {
                              return _buildIdentifierInputUI(state);
                            }
                          },
                        ),

                        const Spacer(),

                        // Back to login button
                        Center(
                          child: TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: Text(
                              'Back to Password Login',
                              style: TextStyle(
                                color: const Color(0xFF1DA1F2),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIdentifierInputUI(AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Email/Phone input
        AuthTextField(
          controller: _identifierController,
          label: 'Email or Phone',
          hintText: 'Enter your email or phone number',
          keyboardType: TextInputType.emailAddress,
          errorText: _identifierError,
          onChanged: (value) {
            if (_identifierError != null) {
              setState(() {
                _identifierError = null;
              });
            }
          },
        ),

        const SizedBox(height: 24),

        // Error display
        if (state.errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      state.errorMessage!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Send OTP button
        AuthPrimaryButton(
          text: 'Send Verification Code',
          isLoading: state.isLoading,
          onPressed: _onSendOtp,
        ),
      ],
    );
  }

  Widget _buildOtpVerificationUI(AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Info text
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1DA1F2).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF1DA1F2).withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFF1DA1F2),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'We sent a 6-digit code to ${state.tempEmailOrPhone}',
                  style: TextStyle(
                    color: const Color(0xFF1DA1F2),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 32),

        // Code input field
        AuthCodeField(
          controller: _codeController,
          label: 'Verification code',
          hintText: 'Enter 6-digit code',
          errorText: _codeError,
          autofocus: true,
          onChanged: (value) {
            if (_codeError != null) {
              setState(() {
                _codeError = null;
              });
            }
          },
        ),

        const SizedBox(height: 24),

        // Resend code link with cooldown
        Center(
          child: Column(
            children: [
              Text(
                'Didn\'t receive the code?',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              if (_resendCooldown > 0)
                Text(
                  'Resend in ${_resendCooldown}s',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                )
              else
                TextButton(
                  onPressed: _isResending ? null : _onResendCode,
                  child: Text(
                    _isResending ? 'Sending...' : 'Resend Code',
                    style: TextStyle(
                      color:
                          _isResending
                              ? Colors.grey[500]
                              : const Color(0xFF1DA1F2),
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Error display
        if (state.errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      state.errorMessage!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Verify button
        AuthPrimaryButton(
          text: 'Verify & Sign In',
          isLoading: state.isLoading,
          onPressed: _onVerifyOtp,
        ),
      ],
    );
  }
}
