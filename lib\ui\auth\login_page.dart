import 'package:business_app/ui/home_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';
import 'package:business_app/ui/auth/components/social_login_button.dart';
import 'package:business_app/ui/auth/forgot_password_page.dart';
import 'package:business_app/ui/splash/loading_splash.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _identifierController = TextEditingController();
  final _passwordController = TextEditingController();
  final _identifierFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _showPasswordField = false;
  bool _rememberMe = false;
  String? _identifierError;
  String? _passwordError;

  @override
  void dispose() {
    _identifierController.dispose();
    _passwordController.dispose();
    _identifierFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _onIdentifierNext() {
    final identifier = _identifierController.text.trim();

    if (identifier.isEmpty) {
      setState(() {
        _identifierError = 'Please enter your email, phone, or username';
      });
      return;
    }

    // Basic validation
    if (!_isValidIdentifier(identifier)) {
      setState(() {
        _identifierError = 'Please enter a valid email, phone, or username';
      });
      return;
    }

    setState(() {
      _identifierError = null;
      _showPasswordField = true;
    });

    // Focus on password field
    Future.delayed(const Duration(milliseconds: 300), () {
      _passwordFocusNode.requestFocus();
    });
  }

  void _onLogin() {
    final identifier = _identifierController.text.trim();
    final password = _passwordController.text;

    // Validate identifier
    if (identifier.isEmpty) {
      setState(() {
        _identifierError = 'Please enter your email, phone, or username';
      });
      return;
    }

    if (!_isValidIdentifier(identifier)) {
      setState(() {
        if (identifier.contains('@')) {
          _identifierError = 'Please enter a valid email address';
        } else if (RegExp(r'^\+?[0-9]').hasMatch(identifier)) {
          _identifierError = _getPhoneValidationError(identifier);
        } else {
          _identifierError =
              'Username must be 3-30 characters (letters, numbers, underscore)';
        }
      });
      return;
    }

    // Validate password
    if (password.isEmpty) {
      setState(() {
        _passwordError = 'Please enter your password';
      });
      return;
    }

    if (password.length < 8) {
      setState(() {
        _passwordError = 'Password must be at least 8 characters';
      });
      return;
    }

    context.read<AuthBloc>().add(
      LoginRequested(identifier: identifier, password: password),
    );
  }

  bool _isValidIdentifier(String identifier) {
    if (identifier.trim().isEmpty) return false;

    // Email validation
    if (identifier.contains('@')) {
      return RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      ).hasMatch(identifier);
    }

    // Phone validation (enhanced for international numbers)
    if (RegExp(r'^\+?[0-9]').hasMatch(identifier)) {
      return _isValidPhoneNumber(identifier);
    }

    // Username validation
    return RegExp(r'^[a-zA-Z0-9_]{3,30}$').hasMatch(identifier);
  }

  bool _isValidPhoneNumber(String phoneNumber) {
    // Handle empty or null input
    if (phoneNumber.trim().isEmpty) return false;

    try {
      // Use phone_numbers_parser for professional validation
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      // Check if the phone number is valid
      final isValid = parsedNumber.isValid();

      // For login, we accept both mobile and fixed line numbers
      return isValid;
    } catch (e) {
      // If parsing fails, try fallback validation for local numbers
      final digitsOnly = phoneNumber.replaceAll(RegExp(r'\D'), '');

      // Basic fallback: must have at least 10 digits and not exceed 15
      if (digitsOnly.length >= 10 && digitsOnly.length <= 15) {
        return true;
      }

      return false;
    }
  }

  String _getPhoneValidationError(String phoneNumber) {
    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      if (!parsedNumber.isValid()) {
        // Try to provide more specific error messages
        if (phoneNumber.length < 8) {
          return 'Phone number is too short';
        } else if (phoneNumber.length > 15) {
          return 'Phone number is too long';
        } else {
          return 'Please enter a valid phone number (e.g., +265 123 456 789)';
        }
      }

      return 'Invalid phone number format';
    } catch (e) {
      // Handle parsing errors
      if (!phoneNumber.startsWith('+')) {
        return 'Phone number must include country code (e.g., +265)';
      }
      return 'Please enter a valid phone number with country code';
    }
  }

  String _getIdentifierHint() {
    final text = _identifierController.text.trim();
    if (text.isEmpty) return 'Phone, email, or username';

    if (text.contains('@')) return 'Email address';
    if (RegExp(r'^\+?[0-9]').hasMatch(text)) return 'Phone number';
    return 'Username';
  }

  IconData _getIdentifierIcon() {
    final text = _identifierController.text.trim();
    if (text.isEmpty) return Icons.person_outline;

    if (text.contains('@')) return Icons.email_outlined;
    if (RegExp(r'^\+?[0-9]').hasMatch(text)) return Icons.phone_outlined;
    return Icons.alternate_email;
  }

  Color _getIdentifierIconColor() {
    final text = _identifierController.text.trim();
    if (text.isEmpty) return Colors.grey;

    if (_isValidIdentifier(text)) {
      return const Color(0xFF1DA1F2);
    } else {
      return Colors.red;
    }
  }

  String _getIdentifierType() {
    final text = _identifierController.text.trim();
    if (text.isEmpty) return 'input';

    if (text.contains('@')) return 'email';
    if (RegExp(r'^\+?[0-9]').hasMatch(text)) return 'phone number';
    return 'username';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state.status == AuthStatus.authenticated) {
            // Show loading splash before navigating to main app
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder:
                    (context) => LoadingSplash(
                      message: 'Welcome back! Setting up your workspace...',
                      duration: const Duration(milliseconds: 1800),
                      destination: const MyHomePage(),
                    ),
              ),
              (route) => false,
            );
          } else if (state.status == AuthStatus.error) {
            // Reset password field if error to show the actual error
            setState(() {
              _passwordError = state.errorMessage;
            });
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),

                        // Header
                        const AuthLoginHeader(),

                        const SizedBox(height: 40),

                        // Social login buttons
                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            return GoogleSignInButtonIcon(
                              isLoading: state.isLoading,
                              onPressed:
                                  state.isLoading
                                      ? null
                                      : () {
                                        context.read<AuthBloc>().add(
                                          GoogleSignInRequested(),
                                        );
                                      },
                            );
                          },
                        ),

                        const SizedBox(height: 12),

                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            return AppleSignInButtonIcon(
                              isLoading: state.isLoading,
                              onPressed:
                                  state.isLoading
                                      ? null
                                      : () {
                                        context.read<AuthBloc>().add(
                                          AppleSignInRequested(),
                                        );
                                      },
                            );
                          },
                        ),

                        const SizedBox(height: 20),

                        // Divider
                        Row(
                          children: [
                            Expanded(
                              child: Divider(
                                color: Colors.grey[400],
                                thickness: 1,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Text(
                                'or',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Divider(
                                color: Colors.grey[400],
                                thickness: 1,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Enhanced identifier field with dynamic icon
                        AuthTextField(
                          controller: _identifierController,
                          focusNode: _identifierFocusNode,
                          hintText: _getIdentifierHint(),
                          keyboardType: TextInputType.text,
                          errorText: _identifierError,
                          prefixIcon: Icon(
                            _getIdentifierIcon(),
                            color: _getIdentifierIconColor(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              if (_identifierError != null) {
                                _identifierError = null;
                              }
                            });
                          },
                          onTap: () {
                            if (_showPasswordField) {
                              setState(() {
                                _showPasswordField = false;
                              });
                            }
                          },
                        ),

                        // Helper text for identifier type
                        if (_identifierController.text.trim().isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                _getIdentifierIcon(),
                                size: 16,
                                color: _getIdentifierIconColor(),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _isValidIdentifier(
                                      _identifierController.text.trim(),
                                    )
                                    ? 'Valid ${_getIdentifierType()}'
                                    : 'Invalid ${_getIdentifierType()}',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: _getIdentifierIconColor(),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],

                        const SizedBox(height: 20),

                        // Password field (animated)
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          height: _showPasswordField ? null : 0,
                          child: AnimatedOpacity(
                            duration: const Duration(milliseconds: 300),
                            opacity: _showPasswordField ? 1.0 : 0.0,
                            child: Column(
                              children: [
                                AuthPasswordField(
                                  controller: _passwordController,
                                  focusNode: _passwordFocusNode,
                                  hintText: 'Password',
                                  errorText: _passwordError,
                                  onChanged: (value) {
                                    if (_passwordError != null) {
                                      setState(() {
                                        _passwordError = null;
                                      });
                                    }
                                  },
                                ),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),

                        // Remember me checkbox (only show when password field is visible)
                        if (_showPasswordField)
                          Row(
                            children: [
                              Checkbox(
                                value: _rememberMe,
                                onChanged: (value) {
                                  setState(() {
                                    _rememberMe = value ?? false;
                                  });
                                },
                                activeColor: const Color(0xFF1DA1F2),
                              ),
                              Text(
                                'Remember me',
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),

                        const SizedBox(height: 20),

                        // Action button
                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            return AuthPrimaryButton(
                              text: _showPasswordField ? 'Sign in' : 'Next',
                              isLoading: state.isLoading,
                              onPressed:
                                  _showPasswordField
                                      ? _onLogin
                                      : _onIdentifierNext,
                            );
                          },
                        ),

                        const SizedBox(height: 20),

                        // Forgot password link
                        if (_showPasswordField)
                          Center(
                            child: AuthTextButton(
                              text: 'Forgot password?',
                              onPressed: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const ForgotPasswordPage(),
                                  ),
                                );
                              },
                            ),
                          ),

                        const Spacer(),

                        // OTP Login option
                        /* Center(
                          child: TextButton(
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const OtpLoginPage(),
                                ),
                              );
                            },
                            child: Text(
                              'Sign in with OTP instead',
                              style: TextStyle(
                                color: const Color(0xFF1DA1F2),
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),*/
                        const SizedBox(height: 16),

                        // Sign up link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Don\'t have an account? ',
                              style: TextStyle(
                                fontSize: 15,
                                color: Colors.grey[600],
                              ),
                            ),
                            AuthTextButton(
                              text: 'Sign up',
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
