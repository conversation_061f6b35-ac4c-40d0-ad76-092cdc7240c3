import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase/config.dart';
import 'supabase_service.dart';

/// Service for managing real-time chat and messaging
class ChatService extends BaseSupabaseService {
  /// Get or create a chat between two users
  static Future<Chat> getOrCreateChat({
    required String otherUserId,
    String? productId,
    String? orderId,
  }) async {
    final currentUserId = BaseSupabaseService.client.auth.currentUser?.id;
    if (currentUserId == null) {
      throw const SupabaseException('Not authenticated');
    }

    return BaseSupabaseService.executeQuery(() async {
      final chatId =
          await BaseSupabaseService.executeRPC<String>('get_or_create_chat', {
            'p_user1_id': currentUserId,
            'p_user2_id': otherUserId,
            'p_product_id': productId,
            'p_order_id': orderId,
          });

      if (chatId == null) {
        throw const SupabaseException('Failed to create chat');
      }

      // Get the chat details
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.chats)
              .select()
              .eq('id', chatId)
              .single();

      return Chat.fromJson(response);
    });
  }

  /// Get user's chat list
  static Future<List<ChatListItem>> getUserChats({
    int limit = 20,
    int offset = 0,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return [];

    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_user_chats',
        {'p_user_id': userId, 'p_limit': limit, 'p_offset': offset},
      );

      return response?.map((json) => ChatListItem.fromJson(json)).toList() ??
          [];
    });
  }

  /// Send a message
  static Future<Message> sendMessage({
    required String chatId,
    required String content,
    String messageType = 'text',
    String? mediaUrl,
    String? replyToId,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      final messageId =
          await BaseSupabaseService.executeRPC<String>('send_message', {
            'p_chat_id': chatId,
            'p_sender_id': userId,
            'p_content': content,
            'p_message_type': messageType,
            'p_media_url': mediaUrl,
            'p_reply_to_id': replyToId,
          });

      if (messageId == null) {
        throw const SupabaseException('Failed to send message');
      }

      // Get the message details
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.messages)
              .select('''
            id, chat_id, sender_id, content, message_type, media_url,
            reply_to_id, created_at,
            profiles!messages_sender_id_fkey(username, full_name, avatar_url)
          ''')
              .eq('id', messageId)
              .single();

      return Message.fromJson(response);
    });
  }

  /// Get chat messages
  static Future<List<Message>> getChatMessages({
    required String chatId,
    int limit = 50,
    int offset = 0,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return [];

    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_chat_messages',
        {
          'p_chat_id': chatId,
          'p_user_id': userId,
          'p_limit': limit,
          'p_offset': offset,
        },
      );

      return response?.map((json) => Message.fromJson(json)).toList() ?? [];
    });
  }

  /// Mark messages as read
  static Future<void> markMessagesAsRead(String chatId) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return;

    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.executeRPC('mark_messages_read', {
        'p_chat_id': chatId,
        'p_user_id': userId,
      });
    });
  }

  /// Subscribe to chat messages (real-time)
  static RealtimeChannel subscribeToChat({
    required String chatId,
    required void Function(Message) onNewMessage,
  }) {
    return BaseSupabaseService.client
        .channel('chat:$chatId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: DatabaseTables.messages,
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'chat_id',
            value: chatId,
          ),
          callback: (payload) {
            final message = Message.fromJson(payload.newRecord);
            onNewMessage(message);
          },
        )
        .subscribe();
  }

  /// Subscribe to user's chats (real-time)
  static RealtimeChannel subscribeToUserChats({
    required void Function(Map<String, dynamic>) onChatUpdate,
  }) {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.client
        .channel('user_chats:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: DatabaseTables.chats,
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'participant_1_id',
            value: userId,
          ),
          callback: (payload) {
            onChatUpdate(payload.newRecord);
          },
        )
        .subscribe();
  }
}

/// Chat model
class Chat {
  final String id;
  final String participant1Id;
  final String participant2Id;
  final String? productId;
  final String? orderId;
  final String chatType;
  final String? subject;
  final bool isActive;
  final DateTime lastMessageAt;
  final String? lastMessageBy;
  final DateTime createdAt;

  const Chat({
    required this.id,
    required this.participant1Id,
    required this.participant2Id,
    this.productId,
    this.orderId,
    required this.chatType,
    this.subject,
    this.isActive = true,
    required this.lastMessageAt,
    this.lastMessageBy,
    required this.createdAt,
  });

  factory Chat.fromJson(Map<String, dynamic> json) {
    return Chat(
      id: json['id'] as String,
      participant1Id: json['participant_1_id'] as String,
      participant2Id: json['participant_2_id'] as String,
      productId: json['product_id'] as String?,
      orderId: json['order_id'] as String?,
      chatType: json['chat_type'] as String,
      subject: json['subject'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      lastMessageAt: DateTime.parse(json['last_message_at'] as String),
      lastMessageBy: json['last_message_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }
}

/// Chat list item model
class ChatListItem {
  final String chatId;
  final String otherParticipantId;
  final String otherParticipantName;
  final String? otherParticipantAvatar;
  final String? lastMessage;
  final DateTime lastMessageAt;
  final String? lastMessageBy;
  final int unreadCount;
  final String chatType;
  final String? productName;
  final bool isOnline;

  const ChatListItem({
    required this.chatId,
    required this.otherParticipantId,
    required this.otherParticipantName,
    this.otherParticipantAvatar,
    this.lastMessage,
    required this.lastMessageAt,
    this.lastMessageBy,
    this.unreadCount = 0,
    required this.chatType,
    this.productName,
    this.isOnline = false,
  });

  factory ChatListItem.fromJson(Map<String, dynamic> json) {
    return ChatListItem(
      chatId: json['chat_id'] as String,
      otherParticipantId: json['other_participant_id'] as String,
      otherParticipantName: json['other_participant_name'] as String,
      otherParticipantAvatar: json['other_participant_avatar'] as String?,
      lastMessage: json['last_message'] as String?,
      lastMessageAt: DateTime.parse(json['last_message_at'] as String),
      lastMessageBy: json['last_message_by'] as String?,
      unreadCount: json['unread_count'] as int? ?? 0,
      chatType: json['chat_type'] as String,
      productName: json['product_name'] as String?,
      isOnline: json['is_online'] as bool? ?? false,
    );
  }
}

/// Message model
class Message {
  final String id;
  final String chatId;
  final String senderId;
  final String senderName;
  final String? senderAvatar;
  final String? content;
  final String messageType;
  final String? mediaUrl;
  final String? replyToId;
  final String? replyToContent;
  final bool isRead;
  final DateTime createdAt;
  final bool isOwnMessage;

  const Message({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    this.content,
    required this.messageType,
    this.mediaUrl,
    this.replyToId,
    this.replyToContent,
    this.isRead = false,
    required this.createdAt,
    this.isOwnMessage = false,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['message_id'] as String? ?? json['id'] as String,
      chatId: json['chat_id'] as String,
      senderId: json['sender_id'] as String,
      senderName: json['sender_name'] as String? ?? json['full_name'] as String,
      senderAvatar:
          json['sender_avatar'] as String? ?? json['avatar_url'] as String?,
      content: json['content'] as String?,
      messageType: json['message_type'] as String,
      mediaUrl: json['media_url'] as String?,
      replyToId: json['reply_to_id'] as String?,
      replyToContent: json['reply_to_content'] as String?,
      isRead: json['is_read'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      isOwnMessage: json['is_own_message'] as bool? ?? false,
    );
  }
}
