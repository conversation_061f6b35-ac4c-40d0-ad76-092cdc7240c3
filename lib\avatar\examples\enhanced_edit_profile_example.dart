import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/profile_bloc/profile_bloc.dart';
import '../../bloc/profile_bloc/profile_event.dart';
import '../../bloc/profile_bloc/profile_state.dart';
import '../avatar.dart';

/// Enhanced edit profile page example using the new avatar system
class EnhancedEditProfileExample extends StatefulWidget {
  const EnhancedEditProfileExample({super.key});

  @override
  State<EnhancedEditProfileExample> createState() =>
      _EnhancedEditProfileExampleState();
}

class _EnhancedEditProfileExampleState
    extends State<EnhancedEditProfileExample> {
  late TextEditingController _nameController;
  late TextEditingController _usernameController;
  late TextEditingController _bioController;

  // Image handling
  File? _selectedProfileImage;
  File? _selectedBackgroundImage;
  String? _currentProfileImageUrl;
  String? _currentBackgroundImageUrl;
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _usernameController = TextEditingController();
    _bioController = TextEditingController();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  void _handleProfileImageChanged(String? imageUrl) {
    setState(() {
      _currentProfileImageUrl = imageUrl;
    });
  }

  void _handleBackgroundImageChanged(String? imageUrl) {
    setState(() {
      _currentBackgroundImageUrl = imageUrl;
    });
  }

  void _handleImageError(String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(error)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        duration: const Duration(milliseconds: 3000),
      ),
    );
  }

  void _saveProfile() {
    // Trigger profile update with BLoC
    context.read<ProfileBloc>().add(
      UpdateProfileEvent(
        name: _nameController.text.trim(),
        username: _usernameController.text.trim(),
        bio: _bioController.text.trim(),
        profileImageFile: _selectedProfileImage,
        backgroundImageFile: _selectedBackgroundImage,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
        actions: [
          BlocBuilder<ProfileBloc, ProfileState>(
            builder: (context, state) {
              return TextButton(
                onPressed:
                    state.status == ProfileStatus.loading ? null : _saveProfile,
                child:
                    state.status == ProfileStatus.loading
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Save'),
              );
            },
          ),
        ],
      ),
      body: BlocListener<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state.status == ProfileStatus.loaded) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Profile updated successfully!'),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
            Navigator.pop(context);
          } else if (state.status == ProfileStatus.error) {
            _handleImageError(state.errorMessage ?? 'Failed to update profile');
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Background Image Section
              const Text(
                'Background Image',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              BackgroundImageWidget(
                currentImageUrl: _currentBackgroundImageUrl,
                onImageChanged: _handleBackgroundImageChanged,
                onError: _handleImageError,
                height: 200,
                isEditable: true,
                userId: _currentUserId,
                overlayWidget: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _nameController.text.isNotEmpty
                            ? _nameController.text
                            : 'Your Name',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 3,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '@${_usernameController.text.isNotEmpty ? _usernameController.text : 'username'}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 3,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Profile Image Section
              const Text(
                'Profile Image',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              Center(
                child: ProfileImageWidget(
                  currentImageUrl: _currentProfileImageUrl,
                  onImageChanged: _handleProfileImageChanged,
                  onError: _handleImageError,
                  radius: 60,
                  isEditable: true,
                  userId: _currentUserId,
                ),
              ),

              const SizedBox(height: 32),

              // Form Fields
              _buildTextField(
                controller: _nameController,
                label: 'Name',
                hintText: 'Enter your name',
                prefixIcon: Icons.person_outline,
                maxLength: 50,
              ),

              const SizedBox(height: 16),

              _buildTextField(
                controller: _usernameController,
                label: 'Username',
                hintText: 'Enter your username',
                prefixIcon: Icons.alternate_email,
                maxLength: 30,
              ),

              const SizedBox(height: 16),

              _buildTextField(
                controller: _bioController,
                label: 'Bio',
                hintText: 'Tell people about yourself...',
                prefixIcon: Icons.description_outlined,
                maxLength: 160,
                maxLines: 4,
              ),

              const SizedBox(height: 32),

              // Image Configuration Info
              _buildImageConfigInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hintText,
    required IconData prefixIcon,
    int? maxLength,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLength: maxLength,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: Icon(prefixIcon),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
          ),
          onChanged: (value) {
            setState(() {}); // Rebuild to update preview
          },
        ),
      ],
    );
  }

  Widget _buildImageConfigInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Image Requirements',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildConfigRow('Profile Image', ImageConfig.profile),
            const SizedBox(height: 8),
            _buildConfigRow('Background Image', ImageConfig.background),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigRow(String title, ImageConfig config) {
    final maxSizeMB = (config.maxFileSizeBytes / (1024 * 1024)).toStringAsFixed(
      1,
    );
    final formats = config.allowedExtensions.join(', ');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        Text(
          'Max size: ${maxSizeMB}MB • Formats: $formats',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }
}
