import 'package:business_app/services/supabase_service.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../avatar/widgets/shop_banner_widget.dart';
import '../../../../services/shop_service.dart';

/// Shop banner form field widget that integrates with shop forms
class ShopBannerFormField extends StatefulWidget {
  final String label;
  final String? currentBannerUrl;
  final Function(String?) onBannerChanged;
  final Function(String)? onError;
  final String? shopId;
  final bool isRequired;
  final double height;
  final bool isEditable;
  final Widget? overlayWidget;

  const ShopBannerFormField({
    super.key,
    this.label = 'Shop Banner',
    this.currentBannerUrl,
    required this.onBannerChanged,
    this.onError,
    this.shopId,
    this.isRequired = false,
    this.height = 200,
    this.isEditable = true,
    this.overlayWidget,
  });

  @override
  State<ShopBannerFormField> createState() => _ShopBannerFormFieldState();
}

class _ShopBannerFormFieldState extends State<ShopBannerFormField> {
  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  String? _currentUserId;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final user = await AuthService.getCurrentUserProfile();
      if (mounted) {
        setState(() {
          _currentUserId = user?.id;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        widget.onError?.call('Failed to load user information');
      }
    }
  }

  void _handleBannerChanged(String? bannerUrl) {
    widget.onBannerChanged(bannerUrl);

    // Update database immediately if we have a shop ID
    if (bannerUrl != null && widget.shopId != null) {
      _updateBannerInDatabase(bannerUrl);
    }
  }

  Future<void> _updateBannerInDatabase(String bannerUrl) async {
    try {
      await ShopService.updateShopBanner(
        shopId: widget.shopId!,
        bannerUrl: bannerUrl,
      );
      _logger.i('✅ Shop banner updated in database: $bannerUrl');
    } catch (e) {
      _logger.e('❌ Failed to update shop banner in database: $e');
      widget.onError?.call('Failed to update shop banner: $e');
    }
  }

  void _handleError(String error) {
    widget.onError?.call(error);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isDark ? Colors.white : Colors.grey[800],
              ),
            ),
            if (widget.isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.red[600],
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),

        // Banner Widget Container
        Container(
          width: double.infinity,
          height: widget.height + 40, // Add padding
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: isDark ? Colors.grey[850] : Colors.white,
            border: Border.all(
              color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child:
              _isLoading
                  ? Center(
                    child: SizedBox(
                      height: widget.height,
                      child: const CircularProgressIndicator(),
                    ),
                  )
                  : _currentUserId != null
                  ? ShopBannerWidget(
                    userId: _currentUserId!,
                    shopId: widget.shopId,
                    currentBannerUrl: widget.currentBannerUrl,
                    height: widget.height,
                    isEditable: widget.isEditable,
                    onImageChanged: _handleBannerChanged,
                    onError: _handleError,
                  )
                  : Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.red[400],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Unable to load user information',
                          style: TextStyle(
                            color: Colors.red[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
        ),

        // Helper text
        if (widget.isEditable)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'Tap the banner area to upload or change your shop banner image',
              style: TextStyle(
                fontSize: 12,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ),
      ],
    );
  }
}
