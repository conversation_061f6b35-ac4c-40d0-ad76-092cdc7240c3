import 'package:equatable/equatable.dart';
import '../../services/shop_service.dart';

enum ShopStatus { initial, loading, success, error }

class ShopState extends Equatable {
  final ShopStatus status;
  final Shop? shop;
  final String? errorMessage;

  const ShopState({
    this.status = ShopStatus.initial,
    this.shop,
    this.errorMessage,
  });

  ShopState copyWith({
    ShopStatus? status,
    Shop? shop,
    String? errorMessage,
  }) {
    return ShopState(
      status: status ?? this.status,
      shop: shop ?? this.shop,
      errorMessage: errorMessage,
    );
  }

  bool get hasShop => shop != null;
  bool get isLoading => status == ShopStatus.loading;
  bool get hasError => status == ShopStatus.error;

  @override
  List<Object?> get props => [status, shop, errorMessage];
}
