import 'package:equatable/equatable.dart';

class Product extends Equatable {
  final String id;
  final String shopId;
  final String? categoryId;
  final String name;
  final String? description;
  final double price;
  final String currency;
  final double? discountPrice;
  final List<String> images;
  final int stockQuantity;
  final String? sku;
  final String condition;
  final String status;
  final String? brand;
  final double? weight;
  final Map<String, dynamic>? dimensions;
  final List<String> tags;
  final Map<String, dynamic>? specifications;
  final int viewCount;
  final int likeCount;
  final double rating;
  final int reviewCount;
  final bool isFeatured;
  final bool isPromoted;
  final DateTime? promotedUntil;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? publishedAt;

  // Related data
  final String? shopName;
  final String? categoryName;

  const Product({
    required this.id,
    required this.shopId,
    this.categoryId,
    required this.name,
    this.description,
    required this.price,
    this.currency = 'USD',
    this.discountPrice,
    this.images = const [],
    this.stockQuantity = 0,
    this.sku,
    this.condition = 'new',
    this.status = 'active',
    this.brand,
    this.weight,
    this.dimensions,
    this.tags = const [],
    this.specifications,
    this.viewCount = 0,
    this.likeCount = 0,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isFeatured = false,
    this.isPromoted = false,
    this.promotedUntil,
    required this.createdAt,
    required this.updatedAt,
    this.publishedAt,
    this.shopName,
    this.categoryName,
  });

  // Factory constructor for creating Product from JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id']?.toString() ?? '',
      shopId: json['shop_id']?.toString() ?? '',
      categoryId: json['category_id']?.toString(),
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString(),
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency']?.toString() ?? 'USD',
      discountPrice: (json['discount_price'] as num?)?.toDouble(),
      images: List<String>.from(json['images'] ?? []),
      stockQuantity: json['stock_quantity'] as int? ?? 0,
      sku: json['sku']?.toString(),
      condition: json['condition']?.toString() ?? 'new',
      status: json['status']?.toString() ?? 'active',
      brand: json['brand']?.toString(),
      weight: (json['weight'] as num?)?.toDouble(),
      dimensions: json['dimensions'] as Map<String, dynamic>?,
      tags: List<String>.from(json['tags'] ?? []),
      specifications: json['specifications'] as Map<String, dynamic>?,
      viewCount: json['view_count'] as int? ?? 0,
      likeCount: json['like_count'] as int? ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: json['review_count'] as int? ?? 0,
      isFeatured: json['is_featured'] as bool? ?? false,
      isPromoted: json['is_promoted'] as bool? ?? false,
      promotedUntil:
          json['promoted_until'] != null
              ? DateTime.tryParse(json['promoted_until'].toString())
              : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'].toString()) ??
                  DateTime.now()
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'].toString()) ??
                  DateTime.now()
              : DateTime.now(),
      publishedAt:
          json['published_at'] != null
              ? DateTime.tryParse(json['published_at'].toString())
              : null,
      shopName: json['shop_name']?.toString(),
      categoryName: json['category_name']?.toString(),
    );
  }

  // Convert Product to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'shop_id': shopId,
      'category_id': categoryId,
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'discount_price': discountPrice,
      'images': images,
      'stock_quantity': stockQuantity,
      'sku': sku,
      'condition': condition,
      'status': status,
      'brand': brand,
      'weight': weight,
      'dimensions': dimensions,
      'tags': tags,
      'specifications': specifications,
      'view_count': viewCount,
      'like_count': likeCount,
      'rating': rating,
      'review_count': reviewCount,
      'is_featured': isFeatured,
      'is_promoted': isPromoted,
      'promoted_until': promotedUntil?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'published_at': publishedAt?.toIso8601String(),
    };
  }

  // Compatibility getters for existing code
  String get category => categoryName ?? 'Unknown';
  bool get isAvailable => status == 'active' && stockQuantity > 0;
  String get businessOwnerId => shopId; // For backward compatibility

  // Copy with method for immutable updates
  Product copyWith({
    String? id,
    String? shopId,
    String? categoryId,
    String? name,
    String? description,
    double? price,
    String? currency,
    double? discountPrice,
    List<String>? images,
    int? stockQuantity,
    String? sku,
    String? condition,
    String? status,
    String? brand,
    double? weight,
    Map<String, dynamic>? dimensions,
    List<String>? tags,
    Map<String, dynamic>? specifications,
    int? viewCount,
    int? likeCount,
    double? rating,
    int? reviewCount,
    bool? isFeatured,
    bool? isPromoted,
    DateTime? promotedUntil,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? publishedAt,
    String? shopName,
    String? categoryName,
  }) {
    return Product(
      id: id ?? this.id,
      shopId: shopId ?? this.shopId,
      categoryId: categoryId ?? this.categoryId,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      discountPrice: discountPrice ?? this.discountPrice,
      images: images ?? this.images,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      sku: sku ?? this.sku,
      condition: condition ?? this.condition,
      status: status ?? this.status,
      brand: brand ?? this.brand,
      weight: weight ?? this.weight,
      dimensions: dimensions ?? this.dimensions,
      tags: tags ?? this.tags,
      specifications: specifications ?? this.specifications,
      viewCount: viewCount ?? this.viewCount,
      likeCount: likeCount ?? this.likeCount,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isFeatured: isFeatured ?? this.isFeatured,
      isPromoted: isPromoted ?? this.isPromoted,
      promotedUntil: promotedUntil ?? this.promotedUntil,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      publishedAt: publishedAt ?? this.publishedAt,
      shopName: shopName ?? this.shopName,
      categoryName: categoryName ?? this.categoryName,
    );
  }

  // Helper methods
  bool get hasDiscount => discountPrice != null && discountPrice! < price;

  double get finalPrice => discountPrice ?? price;

  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((price - discountPrice!) / price) * 100;
  }

  bool get isInStock => stockQuantity > 0;

  bool get isLowStock => stockQuantity > 0 && stockQuantity <= 5;

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    price,
    category,
    images,
    stockQuantity,
    isAvailable,
    createdAt,
    updatedAt,
    businessOwnerId,
    specifications,
    discountPrice,
    brand,
    weight,
    dimensions,
    tags,
    viewCount,
    rating,
    reviewCount,
  ];

  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $price, category: $category)';
  }
}
