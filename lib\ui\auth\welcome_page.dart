import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/social_login_button.dart';
import 'package:business_app/ui/auth/components/spotify_error_popup.dart';
import 'package:business_app/ui/auth/login_page.dart';
import 'package:business_app/ui/auth/signup_page.dart';
import 'package:business_app/ui/splash/loading_splash.dart';
import 'package:business_app/ui/home_page.dart';
import 'package:logger/logger.dart';

class WelcomePage extends StatelessWidget {
  const WelcomePage({super.key});

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        // Handle successful authentication - navigate to home
        if (state.status == AuthStatus.authenticated) {
          // Handle successful authentication - navigate to home
          _logger.i('✅ Welcome page: User authenticated, navigating to home');
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder:
                  (context) => LoadingSplash(
                    message: 'Welcome! Setting up your workspace...',
                    duration: const Duration(milliseconds: 1800),
                    destination: const MyHomePage(),
                  ),
            ),
            (route) => false,
          );
        } else if (state.signupStep != null && state.isGoogleSignup == true) {
          // Handle Google signup flow - navigate to signup page for onboarding steps
          _logger.i(
            '🎯 Welcome page: Google user needs onboarding, navigating to signup',
          );
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const SignupPage()),
            (route) => false,
          );
        } else if (state.errorMessage != null) {
          // Handle Google sign-in errors only - other errors are handled by individual pages
          _logger.e('❌ Welcome page: Error: ${state.errorMessage}');

          // Check if this is a Google sign-in related error
          final errorMessage = state.errorMessage!.toLowerCase();
          if (errorMessage.contains('google') ||
              errorMessage.contains('sign-in') ||
              errorMessage.contains('authentication failed') ||
              errorMessage.contains('id token') ||
              errorMessage.contains('supabase')) {
            // Show Spotify-style error popup for Google sign-in errors
            GoogleSignInErrorPopup.show(context: context);
          }
          // Removed regular SnackBar to prevent duplicate error messages
          // Individual pages (signup_basic_info_page.dart, login_page.dart) handle their own errors
        }
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Column(
                    children: [
                      // Top section with logo and title
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Icon(Icons.arrow_back, size: 26),
                          ),
                        ],
                      ),
                      Expanded(
                        flex: 1, // 3
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const AuthWelcomeHeader(),
                            const SizedBox(height: 40),

                            // Decorative illustration or additional branding
                            Container(
                              width: screenWidth * 0.6,
                              height: screenWidth * 0.4,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    const Color(0xFF1DA1F2).withOpacity(0.1),
                                    const Color(0xFF1DA1F2).withOpacity(0.05),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.public,
                                  size: 80,
                                  color: const Color(
                                    0xFF1DA1F2,
                                  ).withOpacity(0.3),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Bottom section with buttons
                      Expanded(
                        flex: 1, //2
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // Social login buttons
                            BlocBuilder<AuthBloc, AuthState>(
                              builder: (context, state) {
                                return GoogleSignInButtonIcon(
                                  isLoading: state.isLoading,
                                  onPressed:
                                      state.isLoading
                                          ? null
                                          : () {
                                            context.read<AuthBloc>().add(
                                              const GoogleSignInRequested(),
                                            );
                                          },
                                );
                              },
                            ),

                            const SizedBox(height: 12),

                            BlocBuilder<AuthBloc, AuthState>(
                              builder: (context, state) {
                                return AppleSignInButtonIcon(
                                  isLoading: state.isLoading,
                                  onPressed:
                                      state.isLoading
                                          ? null
                                          : () {
                                            context.read<AuthBloc>().add(
                                              AppleSignInRequested(),
                                            );
                                          },
                                );
                              },
                            ),

                            const SizedBox(height: 20),

                            // Divider
                            Row(
                              children: [
                                Expanded(
                                  child: Divider(
                                    color: Colors.grey[400],
                                    thickness: 1,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                  ),
                                  child: Text(
                                    'or',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Divider(
                                    color: Colors.grey[400],
                                    thickness: 1,
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 20),

                            // Create account button
                            AuthPrimaryButton(
                              text: 'Create account',
                              onPressed: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => const SignupPage(),
                                  ),
                                );
                              },
                            ),

                            const SizedBox(height: 32),

                            // Terms and privacy
                            RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  height: 1.4,
                                ),
                                children: [
                                  const TextSpan(
                                    text: 'By signing up, you agree to the ',
                                  ),
                                  TextSpan(
                                    text: 'Terms of Service',
                                    style: TextStyle(
                                      color: const Color(0xFF1DA1F2),
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                  const TextSpan(text: ' and '),
                                  TextSpan(
                                    text: 'Privacy Policy',
                                    style: TextStyle(
                                      color: const Color(0xFF1DA1F2),
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                  const TextSpan(text: ', including '),
                                  TextSpan(
                                    text: 'Cookie Use',
                                    style: TextStyle(
                                      color: const Color(0xFF1DA1F2),
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                  const TextSpan(text: '.'),
                                ],
                              ),
                            ),

                            const SizedBox(height: 40),

                            // Sign in section
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Have an account already? ',
                                  style: TextStyle(
                                    fontSize: 15,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                AuthTextButton(
                                  text: 'Sign in',
                                  onPressed: () {
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) => const LoginPage(),
                                      ),
                                    );
                                  },
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                ),
                              ],
                            ),

                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// alternative page has been made sa main welcome page as foe now

class WelcomePageAlternative extends StatelessWidget {
  const WelcomePageAlternative({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.scaffoldBackgroundColor,
              theme.scaffoldBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Column(
                    children: [
                      // Hero section
                      Expanded(
                        flex: 2,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Large logo
                            Hero(
                              tag: 'app_logo',
                              child: Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: const LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Color(0xFF1DA1F2),
                                      Color(0xFF0D8BD9),
                                    ],
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(
                                        0xFF1DA1F2,
                                      ).withOpacity(0.3),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(20),
                                  child: ClipOval(
                                    child: Image.asset(
                                      'assets/images/Trademate-logo.png',
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.contain,
                                      filterQuality: FilterQuality.high,
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 40),

                            // Welcome text
                            Text(
                              'Welcome to Trademate',
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: theme.textTheme.headlineLarge?.color,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            const SizedBox(height: 16),

                            Text(
                              'Connect with traders worldwide\nand grow your business',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                                height: 1.4,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                      // Action buttons section
                      Column(
                        children: [
                          // Get started button
                          Container(
                            width: double.infinity,
                            height: 56,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [Color(0xFF1DA1F2), Color(0xFF0D8BD9)],
                              ),
                              borderRadius: BorderRadius.circular(28),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(
                                    0xFF1DA1F2,
                                  ).withOpacity(0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 6),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => const WelcomePage(),
                                  ),
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(28),
                                ),
                              ),
                              child: const Text(
                                'Get Started',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Sign in button
                          AuthSecondaryButton(
                            text: 'I already have an account',
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const LoginPage(),
                                ),
                              );
                            },
                          ),

                          const SizedBox(height: 40),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
