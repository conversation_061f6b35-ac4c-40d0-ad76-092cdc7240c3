import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AuthTextField extends StatefulWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final bool obscureText;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final int? maxLength;
  final int maxLines;
  final bool autofocus;
  final String? errorText;
  final bool enabled;
  final FocusNode? focusNode;

  const AuthTextField({
    super.key,
    this.label,
    this.hintText,
    this.controller,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.suffixIcon,
    this.prefixIcon,
    this.maxLength,
    this.maxLines = 1,
    this.autofocus = false,
    this.errorText,
    this.enabled = true,
    this.focusNode,
  });

  @override
  State<AuthTextField> createState() => _AuthTextFieldState();
}

class _AuthTextFieldState extends State<AuthTextField> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          height: 49, // Match phone field height for consistency
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color:
                  widget.errorText != null
                      ? Colors.red
                      : _isFocused
                      ? const Color(0xFF1DA1F2)
                      : isDark
                      ? Colors.grey[700]!
                      : Colors.grey[300]!,
              width: _isFocused ? 2 : 1,
            ),
            color: isDark ? Colors.grey[900] : Colors.grey[50],
          ),
          child: TextFormField(
            controller: widget.controller,
            focusNode: _focusNode,
            obscureText: widget.obscureText,
            keyboardType: widget.keyboardType,
            inputFormatters: widget.inputFormatters,
            validator: widget.validator,
            onChanged: widget.onChanged,
            onTap: widget.onTap,
            readOnly: widget.readOnly,
            maxLength: widget.maxLength,
            maxLines: widget.maxLines,
            autofocus: widget.autofocus,
            enabled: widget.enabled,
            style: TextStyle(
              fontSize: 17,
              color: theme.textTheme.bodyLarge?.color,
            ),
            decoration: InputDecoration(
              hintText: widget.hintText,
              hintStyle: TextStyle(fontSize: 17, color: Colors.grey[500]),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12, // Reduced to center text in fixed height
              ),
              suffixIcon: widget.suffixIcon,
              prefixIcon: widget.prefixIcon,
              counterText: '', // Hide character counter
            ),
          ),
        ),
        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: const TextStyle(
              fontSize: 13,
              color: Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }
}

class AuthPasswordField extends StatefulWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool autofocus;
  final String? errorText;
  final bool enabled;
  final FocusNode? focusNode;

  const AuthPasswordField({
    super.key,
    this.label,
    this.hintText,
    this.controller,
    this.validator,
    this.onChanged,
    this.autofocus = false,
    this.errorText,
    this.enabled = true,
    this.focusNode,
  });

  @override
  State<AuthPasswordField> createState() => _AuthPasswordFieldState();
}

class _AuthPasswordFieldState extends State<AuthPasswordField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    return AuthTextField(
      label: widget.label,
      hintText: widget.hintText,
      controller: widget.controller,
      obscureText: _obscureText,
      validator: widget.validator,
      onChanged: widget.onChanged,
      autofocus: widget.autofocus,
      errorText: widget.errorText,
      enabled: widget.enabled,
      focusNode: widget.focusNode,
      suffixIcon: IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off : Icons.visibility,
          color: Colors.grey[600],
        ),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      ),
    );
  }
}

class AuthPhoneField extends StatefulWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool autofocus;
  final String? errorText;
  final bool enabled;
  final FocusNode? focusNode;

  const AuthPhoneField({
    super.key,
    this.label,
    this.hintText,
    this.controller,
    this.validator,
    this.onChanged,
    this.autofocus = false,
    this.errorText,
    this.enabled = true,
    this.focusNode,
  });

  @override
  State<AuthPhoneField> createState() => _AuthPhoneFieldState();
}

class _AuthPhoneFieldState extends State<AuthPhoneField> {
  String _selectedCountryCode =
      '+265'; // Default to Malawi for better regional support
  final List<Map<String, String>> _countryCodes = [
    // North America
    {'code': '+1', 'country': 'US', 'flag': '🇺🇸', 'name': 'United States'},
    {'code': '+1', 'country': 'CA', 'flag': '🇨🇦', 'name': 'Canada'},
    {'code': '+52', 'country': 'MX', 'flag': '🇲🇽', 'name': 'Mexico'},

    // Europe
    {'code': '+44', 'country': 'UK', 'flag': '🇬🇧', 'name': 'United Kingdom'},
    {'code': '+49', 'country': 'DE', 'flag': '🇩🇪', 'name': 'Germany'},
    {'code': '+33', 'country': 'FR', 'flag': '🇫🇷', 'name': 'France'},
    {'code': '+39', 'country': 'IT', 'flag': '🇮🇹', 'name': 'Italy'},
    {'code': '+34', 'country': 'ES', 'flag': '🇪🇸', 'name': 'Spain'},
    {'code': '+31', 'country': 'NL', 'flag': '🇳🇱', 'name': 'Netherlands'},
    {'code': '+41', 'country': 'CH', 'flag': '🇨🇭', 'name': 'Switzerland'},
    {'code': '+43', 'country': 'AT', 'flag': '🇦🇹', 'name': 'Austria'},
    {'code': '+32', 'country': 'BE', 'flag': '🇧🇪', 'name': 'Belgium'},
    {'code': '+45', 'country': 'DK', 'flag': '🇩🇰', 'name': 'Denmark'},
    {'code': '+46', 'country': 'SE', 'flag': '🇸🇪', 'name': 'Sweden'},
    {'code': '+47', 'country': 'NO', 'flag': '🇳🇴', 'name': 'Norway'},
    {'code': '+358', 'country': 'FI', 'flag': '🇫🇮', 'name': 'Finland'},
    {'code': '+48', 'country': 'PL', 'flag': '🇵🇱', 'name': 'Poland'},
    {'code': '+7', 'country': 'RU', 'flag': '🇷🇺', 'name': 'Russia'},

    // Asia
    {'code': '+91', 'country': 'IN', 'flag': '🇮🇳', 'name': 'India'},
    {'code': '+86', 'country': 'CN', 'flag': '🇨🇳', 'name': 'China'},
    {'code': '+81', 'country': 'JP', 'flag': '🇯🇵', 'name': 'Japan'},
    {'code': '+82', 'country': 'KR', 'flag': '🇰🇷', 'name': 'South Korea'},
    {'code': '+65', 'country': 'SG', 'flag': '🇸🇬', 'name': 'Singapore'},
    {'code': '+60', 'country': 'MY', 'flag': '🇲🇾', 'name': 'Malaysia'},
    {'code': '+66', 'country': 'TH', 'flag': '🇹🇭', 'name': 'Thailand'},
    {'code': '+84', 'country': 'VN', 'flag': '🇻🇳', 'name': 'Vietnam'},
    {'code': '+63', 'country': 'PH', 'flag': '🇵🇭', 'name': 'Philippines'},
    {'code': '+62', 'country': 'ID', 'flag': '🇮🇩', 'name': 'Indonesia'},
    {'code': '+92', 'country': 'PK', 'flag': '🇵🇰', 'name': 'Pakistan'},
    {'code': '+880', 'country': 'BD', 'flag': '🇧🇩', 'name': 'Bangladesh'},

    // Middle East
    {'code': '+971', 'country': 'AE', 'flag': '🇦🇪', 'name': 'UAE'},
    {'code': '+966', 'country': 'SA', 'flag': '🇸🇦', 'name': 'Saudi Arabia'},
    {'code': '+972', 'country': 'IL', 'flag': '🇮🇱', 'name': 'Israel'},
    {'code': '+90', 'country': 'TR', 'flag': '🇹🇷', 'name': 'Turkey'},

    // Africa (Including Malawi and comprehensive African coverage)
    {'code': '+27', 'country': 'ZA', 'flag': '🇿🇦', 'name': 'South Africa'},
    {'code': '+234', 'country': 'NG', 'flag': '🇳🇬', 'name': 'Nigeria'},
    {'code': '+254', 'country': 'KE', 'flag': '🇰🇪', 'name': 'Kenya'},
    {'code': '+233', 'country': 'GH', 'flag': '🇬🇭', 'name': 'Ghana'},
    {'code': '+20', 'country': 'EG', 'flag': '🇪🇬', 'name': 'Egypt'},
    {'code': '+212', 'country': 'MA', 'flag': '🇲🇦', 'name': 'Morocco'},
    {'code': '+216', 'country': 'TN', 'flag': '🇹🇳', 'name': 'Tunisia'},
    {'code': '+213', 'country': 'DZ', 'flag': '🇩🇿', 'name': 'Algeria'},
    {'code': '+251', 'country': 'ET', 'flag': '🇪🇹', 'name': 'Ethiopia'},
    {'code': '+256', 'country': 'UG', 'flag': '🇺🇬', 'name': 'Uganda'},
    {'code': '+255', 'country': 'TZ', 'flag': '🇹🇿', 'name': 'Tanzania'},
    {'code': '+260', 'country': 'ZM', 'flag': '🇿🇲', 'name': 'Zambia'},
    {
      'code': '+265',
      'country': 'MW',
      'flag': '🇲🇼',
      'name': 'Malawi',
    }, // MALAWI ADDED!
    {'code': '+263', 'country': 'ZW', 'flag': '🇿🇼', 'name': 'Zimbabwe'},
    {'code': '+267', 'country': 'BW', 'flag': '🇧🇼', 'name': 'Botswana'},
    {'code': '+264', 'country': 'NA', 'flag': '🇳🇦', 'name': 'Namibia'},
    {'code': '+268', 'country': 'SZ', 'flag': '🇸🇿', 'name': 'Eswatini'},
    {'code': '+266', 'country': 'LS', 'flag': '🇱🇸', 'name': 'Lesotho'},
    {'code': '+250', 'country': 'RW', 'flag': '🇷🇼', 'name': 'Rwanda'},
    {'code': '+257', 'country': 'BI', 'flag': '🇧🇮', 'name': 'Burundi'},
    {'code': '+258', 'country': 'MZ', 'flag': '🇲🇿', 'name': 'Mozambique'},
    {'code': '+261', 'country': 'MG', 'flag': '🇲🇬', 'name': 'Madagascar'},
    {'code': '+230', 'country': 'MU', 'flag': '🇲🇺', 'name': 'Mauritius'},
    {'code': '+248', 'country': 'SC', 'flag': '🇸🇨', 'name': 'Seychelles'},

    // South America
    {'code': '+55', 'country': 'BR', 'flag': '🇧🇷', 'name': 'Brazil'},
    {'code': '+54', 'country': 'AR', 'flag': '🇦🇷', 'name': 'Argentina'},
    {'code': '+56', 'country': 'CL', 'flag': '🇨🇱', 'name': 'Chile'},
    {'code': '+57', 'country': 'CO', 'flag': '🇨🇴', 'name': 'Colombia'},
    {'code': '+51', 'country': 'PE', 'flag': '🇵🇪', 'name': 'Peru'},

    // Oceania
    {'code': '+61', 'country': 'AU', 'flag': '🇦🇺', 'name': 'Australia'},
    {'code': '+64', 'country': 'NZ', 'flag': '🇳🇿', 'name': 'New Zealand'},
  ];

  String _formatPhoneNumber(String value) {
    // Remove all non-digits
    final digits = value.replaceAll(RegExp(r'\D'), '');

    // Format based on country code
    switch (_selectedCountryCode) {
      case '+1': // US/Canada format: (XXX) XXX-XXXX
        if (digits.length <= 3) {
          return '($digits';
        } else if (digits.length <= 6) {
          return '(${digits.substring(0, 3)}) ${digits.substring(3)}';
        } else {
          return '(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6, digits.length > 10 ? 10 : digits.length)}';
        }

      case '+44': // UK format: XXXX XXX XXXX
        if (digits.length >= 10) {
          return '${digits.substring(0, 4)} ${digits.substring(4, 7)} ${digits.substring(7)}';
        } else if (digits.length >= 7) {
          return '${digits.substring(0, 4)} ${digits.substring(4, 7)} ${digits.substring(7)}';
        } else if (digits.length >= 4) {
          return '${digits.substring(0, 4)} ${digits.substring(4)}';
        }
        break;

      case '+265': // Malawi format: XXX XXX XXX
        if (digits.length >= 9) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6, 9)}';
        } else if (digits.length >= 6) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
        } else if (digits.length >= 3) {
          return '${digits.substring(0, 3)} ${digits.substring(3)}';
        }
        break;

      case '+234': // Nigeria format: XXX XXX XXXX
        if (digits.length >= 10) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6, 10)}';
        } else if (digits.length >= 6) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
        } else if (digits.length >= 3) {
          return '${digits.substring(0, 3)} ${digits.substring(3)}';
        }
        break;

      case '+254': // Kenya format: XXX XXX XXX
        if (digits.length >= 9) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6, 9)}';
        } else if (digits.length >= 6) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
        } else if (digits.length >= 3) {
          return '${digits.substring(0, 3)} ${digits.substring(3)}';
        }
        break;

      case '+27': // South Africa format: XX XXX XXXX
        if (digits.length >= 9) {
          return '${digits.substring(0, 2)} ${digits.substring(2, 5)} ${digits.substring(5, 9)}';
        } else if (digits.length >= 5) {
          return '${digits.substring(0, 2)} ${digits.substring(2, 5)} ${digits.substring(5)}';
        } else if (digits.length >= 2) {
          return '${digits.substring(0, 2)} ${digits.substring(2)}';
        }
        break;

      case '+91': // India format: XXXXX XXXXX
        if (digits.length >= 10) {
          return '${digits.substring(0, 5)} ${digits.substring(5, 10)}';
        } else if (digits.length >= 5) {
          return '${digits.substring(0, 5)} ${digits.substring(5)}';
        }
        break;

      default:
        // Default formatting: add spaces every 3 digits
        if (digits.length >= 9) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
        } else if (digits.length >= 6) {
          return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
        } else if (digits.length >= 3) {
          return '${digits.substring(0, 3)} ${digits.substring(3)}';
        }
        break;
    }

    // Return unformatted if no specific formatting applies
    return digits;
  }

  void _onPhoneChanged(String value) {
    // Only format the national number (without country code)
    final formatted = _formatPhoneNumber(value);

    // Create the full international number with space for readability
    final fullNumber = '$_selectedCountryCode $formatted';

    if (widget.onChanged != null) {
      widget.onChanged!(fullNumber);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Row(
          children: [
            // Country code selector
            GestureDetector(
              onTap: widget.enabled ? _showCountryPicker : null,
              child: Container(
                height: 49, // Match the exact height of TextFormField
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 0, // Remove vertical padding to use height instead
                ),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                  ),
                  border: Border.all(
                    color:
                        widget.errorText != null
                            ? Colors.red
                            : theme.brightness == Brightness.dark
                            ? Colors.grey[700]!
                            : Colors.grey[300]!,
                  ),
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.grey[900]
                          : Colors.grey[50],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _countryCodes.firstWhere(
                        (country) => country['code'] == _selectedCountryCode,
                        orElse: () => _countryCodes.first,
                      )['flag']!,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _selectedCountryCode,
                      style: TextStyle(
                        fontSize: 16,
                        color: theme.textTheme.bodyLarge?.color,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.arrow_drop_down,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
            // Phone number input
            Expanded(
              child: Container(
                height: 49, // Match the exact height of country code selector
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                  border: Border.all(
                    color:
                        widget.errorText != null
                            ? Colors.red
                            : theme.brightness == Brightness.dark
                            ? Colors.grey[700]!
                            : Colors.grey[300]!,
                  ),
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.grey[900]
                          : Colors.grey[50],
                ),
                child: TextFormField(
                  controller: widget.controller,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[\d\s\-\(\)]')),
                    LengthLimitingTextInputFormatter(15),
                  ],
                  validator: widget.validator,
                  onChanged: _onPhoneChanged,
                  autofocus: widget.autofocus,
                  enabled: widget.enabled,
                  focusNode: widget.focusNode,
                  style: TextStyle(
                    fontSize: 17,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    hintText: widget.hintText ?? 'Phone number',
                    hintStyle: TextStyle(color: Colors.grey[500], fontSize: 17),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12, // Reduced to center text in fixed height
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showCountryPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => _CountryPickerModal(
            countryCodes: _countryCodes,
            onCountrySelected: (countryCode) {
              setState(() {
                _selectedCountryCode = countryCode;
              });
              Navigator.pop(context);
            },
          ),
    );
  }
}

class _CountryPickerModal extends StatefulWidget {
  final List<Map<String, String>> countryCodes;
  final Function(String) onCountrySelected;

  const _CountryPickerModal({
    required this.countryCodes,
    required this.onCountrySelected,
  });

  @override
  State<_CountryPickerModal> createState() => _CountryPickerModalState();
}

class _CountryPickerModalState extends State<_CountryPickerModal> {
  String _searchQuery = '';
  late List<Map<String, String>> _filteredCountries;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _filteredCountries = widget.countryCodes;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCountries(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredCountries = widget.countryCodes;
      } else {
        _filteredCountries =
            widget.countryCodes.where((country) {
              final name = country['name']?.toLowerCase() ?? '';
              final code = country['code']?.toLowerCase() ?? '';
              final countryCode = country['country']?.toLowerCase() ?? '';
              final searchQuery = query.toLowerCase();

              // Enhanced search: support searching by country name, country code, or phone code
              return name.contains(searchQuery) ||
                  code.contains(searchQuery) ||
                  countryCode.contains(searchQuery) ||
                  // Also search by phone code without the + symbol
                  code.replaceAll('+', '').contains(searchQuery) ||
                  // Search by partial matches at the beginning of words
                  name.split(' ').any((word) => word.startsWith(searchQuery));
            }).toList();

        // Sort results: exact matches first, then partial matches
        final queryLower = query.toLowerCase();
        _filteredCountries.sort((a, b) {
          final aName = a['name']?.toLowerCase() ?? '';
          final bName = b['name']?.toLowerCase() ?? '';
          final aCode = a['code']?.toLowerCase() ?? '';
          final bCode = b['code']?.toLowerCase() ?? '';

          // Exact name matches first
          if (aName == queryLower && bName != queryLower) return -1;
          if (bName == queryLower && aName != queryLower) return 1;

          // Exact code matches next
          if (aCode == queryLower && bCode != queryLower) return -1;
          if (bCode == queryLower && aCode != queryLower) return 1;

          // Names starting with query
          if (aName.startsWith(queryLower) && !bName.startsWith(queryLower)) {
            return -1;
          }
          if (bName.startsWith(queryLower) && !aName.startsWith(queryLower)) {
            return 1;
          }

          // Alphabetical order for the rest
          return aName.compareTo(bName);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Header
          Text(
            'Select Country Code',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Search field
          TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: 'Search countries...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterCountries('');
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: _filterCountries,
          ),
          const SizedBox(height: 16),

          // Results count
          if (_searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Text(
                '${_filteredCountries.length} countries found',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),

          // Countries list
          Expanded(
            child:
                _filteredCountries.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No countries found',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Try searching with a different term',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey[500]),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      itemCount: _filteredCountries.length,
                      itemBuilder: (context, index) {
                        final country = _filteredCountries[index];
                        return ListTile(
                          leading: Text(
                            country['flag']!,
                            style: const TextStyle(fontSize: 24),
                          ),
                          title: Text(
                            country['name'] ??
                                '${country['country']} ${country['code']}',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          subtitle: Text(
                            country['code']!,
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          onTap:
                              () => widget.onCountrySelected(country['code']!),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 4,
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }
}

class AuthEmailField extends StatelessWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool autofocus;
  final String? errorText;
  final bool enabled;
  final FocusNode? focusNode;

  const AuthEmailField({
    super.key,
    this.label,
    this.hintText,
    this.controller,
    this.validator,
    this.onChanged,
    this.autofocus = false,
    this.errorText,
    this.enabled = true,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return AuthTextField(
      label: label,
      hintText: hintText,
      controller: controller,
      keyboardType: TextInputType.emailAddress,
      validator: validator,
      onChanged: onChanged,
      autofocus: autofocus,
      errorText: errorText,
      enabled: enabled,
      focusNode: focusNode,
      prefixIcon: const Icon(Icons.email_outlined, color: Colors.grey),
    );
  }
}

class AuthCodeField extends StatelessWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool autofocus;
  final String? errorText;
  final bool enabled;
  final FocusNode? focusNode;

  const AuthCodeField({
    super.key,
    this.label,
    this.hintText,
    this.controller,
    this.validator,
    this.onChanged,
    this.autofocus = false,
    this.errorText,
    this.enabled = true,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return AuthTextField(
      label: label,
      hintText: hintText,
      controller: controller,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(6),
      ],
      validator: validator,
      onChanged: onChanged,
      autofocus: autofocus,
      errorText: errorText,
      enabled: enabled,
      focusNode: focusNode,
      maxLength: 6,
    );
  }
}
