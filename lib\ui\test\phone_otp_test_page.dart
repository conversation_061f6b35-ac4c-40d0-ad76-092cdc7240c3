import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';
import 'package:business_app/services/supabase_service.dart';
import 'package:business_app/utils/phone_utils.dart';

/// Test page for phone OTP functionality with Twilio integration
/// This page helps verify that your Twilio service (+***********) is working correctly
class PhoneOtpTestPage extends StatefulWidget {
  const PhoneOtpTestPage({super.key});

  @override
  State<PhoneOtpTestPage> createState() => _PhoneOtpTestPageState();
}

class _PhoneOtpTestPageState extends State<PhoneOtpTestPage> {
  final _phoneController = TextEditingController();
  final _codeController = TextEditingController();
  String? _phoneError;
  String? _codeError;
  bool _showOtpField = false;
  bool _isLoading = false;
  String? _lastResult;
  String? _normalizedPhone;

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  void _onSendOTP() async {
    final phone = _phoneController.text.trim();

    if (phone.isEmpty) {
      setState(() {
        _phoneError = 'Please enter your phone number';
      });
      return;
    }

    // Normalize phone number
    try {
      _normalizedPhone = PhoneUtils.normalizePhoneNumber(phone);
      if (_normalizedPhone!.isEmpty) {
        setState(() {
          _phoneError = 'Please enter a valid phone number';
        });
        return;
      }
    } catch (e) {
      setState(() {
        _phoneError = 'Invalid phone number format';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _phoneError = null;
      _lastResult = null;
    });

    // Send OTP using your Twilio service
    final result = await AuthService.sendPhoneOTP(phone: _normalizedPhone!);

    setState(() {
      _isLoading = false;
      if (result.isSuccess) {
        _showOtpField = true;
        final tempEmail = PhoneUtils.phoneToTempEmail(_normalizedPhone!);
        _lastResult =
            '✅ OTP sent successfully to $_normalizedPhone\n'
            'Using Twilio service: +***********\n'
            'Temp email format: $tempEmail\n'
            '\n📝 Note: Phone users are stored with temp email format for signin compatibility.';
      } else {
        _lastResult = '❌ Failed to send OTP: ${result.error}';
      }
    });
  }

  void _onVerifyOTP() async {
    final code = _codeController.text.trim();

    if (code.length != 6) {
      setState(() {
        _codeError = 'Please enter the 6-digit code';
      });
      return;
    }

    if (_normalizedPhone == null) {
      setState(() {
        _codeError = 'Please send OTP first';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _codeError = null;
      _lastResult = null;
    });

    // Verify OTP
    final result = await AuthService.verifyPhoneOTP(
      phone: _normalizedPhone!,
      token: code,
    );

    setState(() {
      _isLoading = false;
      if (result.isSuccess) {
        final userId = result.data?.user?.id;
        final userPhone = result.data?.user?.phone;
        final userEmail = result.data?.user?.email;
        final tempEmail = PhoneUtils.phoneToTempEmail(_normalizedPhone!);

        _lastResult =
            '✅ OTP verified successfully!\n'
            'User ID: $userId\n'
            'Phone: $userPhone\n'
            'Email: $userEmail\n'
            'Expected temp email: $tempEmail\n'
            '\n📝 For signin, use temp email format with password.';
      } else {
        _lastResult = '❌ OTP verification failed: ${result.error}';
      }
    });
  }

  void _onTestForgotPassword() async {
    final phone = _phoneController.text.trim();

    if (phone.isEmpty) {
      setState(() {
        _phoneError = 'Please enter your phone number';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _phoneError = null;
      _lastResult = null;
    });

    // Test forgot password flow using BLoC (like the real app does)
    context.read<AuthBloc>().add(ForgotPasswordRequested(emailOrPhone: phone));
  }

  void _onReset() {
    setState(() {
      _phoneController.clear();
      _codeController.clear();
      _phoneError = null;
      _codeError = null;
      _showOtpField = false;
      _isLoading = false;
      _lastResult = null;
      _normalizedPhone = null;
    });
  }

  Widget _buildTestCaseButton(String phone, String label) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () {
          _phoneController.text = phone;
        },
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          side: BorderSide(color: Colors.blue.shade300),
        ),
        child: Text('$label: $phone', style: const TextStyle(fontSize: 14)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state.isLoading) {
          setState(() {
            _isLoading = true;
            _lastResult = null;
          });
        } else {
          setState(() {
            _isLoading = false;
          });

          if (state.errorMessage != null) {
            setState(() {
              _lastResult = '❌ Forgot password failed: ${state.errorMessage}';
            });
          } else if (state.successMessage != null) {
            setState(() {
              _lastResult =
                  '✅ ${state.successMessage}\n'
                  'Resolved email: ${state.resolvedEmail}\n'
                  '\n📝 BLoC Fix Applied: Phone normalization now works in forgot password flow.';
            });
          }
        }
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Phone OTP Test'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                const AuthHeader(
                  title: 'Phone OTP Test',
                  subtitle: 'Test your Twilio integration (+***********)',
                ),

                const SizedBox(height: 32),

                // Twilio Service Info
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Twilio Service Configuration',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Phone Number: +***********\nMake sure this number is configured in your Supabase Auth settings.\n\n'
                        '🔧 Fix Applied: Phone signin now uses temp email format for compatibility.',
                        style: TextStyle(color: Colors.blue.shade600),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Phone input field
                AuthPhoneField(
                  controller: _phoneController,
                  label: 'Phone number',
                  hintText: 'Enter phone number to test',
                  errorText: _phoneError,
                  enabled: !_isLoading,
                  onChanged: (value) {
                    if (_phoneError != null) {
                      setState(() {
                        _phoneError = null;
                      });
                    }
                  },
                ),

                const SizedBox(height: 16),

                // Test phone numbers
                Text(
                  'Quick Test Numbers:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
                const SizedBox(height: 8),

                _buildTestCaseButton('+1234567890', 'US Number'),
                const SizedBox(height: 6),
                _buildTestCaseButton('+265984671670', 'Malawi Number'),
                const SizedBox(height: 6),
                _buildTestCaseButton('+44123456789', 'UK Number'),

                const SizedBox(height: 24),

                // OTP input field (shown after sending OTP)
                if (_showOtpField) ...[
                  AuthCodeField(
                    controller: _codeController,
                    label: 'Verification code',
                    hintText: 'Enter 6-digit code from SMS',
                    errorText: _codeError,
                    enabled: !_isLoading,
                    autofocus: true,
                    onChanged: (value) {
                      if (_codeError != null) {
                        setState(() {
                          _codeError = null;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                ],

                // Action Button
                AuthPrimaryButton(
                  text: _showOtpField ? 'Verify OTP' : 'Send OTP',
                  isLoading: _isLoading,
                  onPressed: _showOtpField ? _onVerifyOTP : _onSendOTP,
                ),

                const SizedBox(height: 16),

                // Test Forgot Password Button
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : _onTestForgotPassword,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.orange.shade400),
                    ),
                    child: Text(
                      'Test Forgot Password',
                      style: TextStyle(color: Colors.orange.shade700),
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // Reset Button
                if (_showOtpField)
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _onReset,
                      child: const Text('Reset Test'),
                    ),
                  ),

                const SizedBox(height: 24),

                // Result display
                if (_lastResult != null) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color:
                          _lastResult!.startsWith('✅')
                              ? Colors.green.shade50
                              : Colors.red.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            _lastResult!.startsWith('✅')
                                ? Colors.green.shade200
                                : Colors.red.shade200,
                      ),
                    ),
                    child: Text(
                      _lastResult!,
                      style: TextStyle(
                        color:
                            _lastResult!.startsWith('✅')
                                ? Colors.green.shade700
                                : Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
