// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:business_app/themes/theme_provider.dart';

// class ThemeSelectionDialog extends StatelessWidget {
//   const ThemeSelectionDialog({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final themeProvider = Provider.of<ThemeProvider>(context, listen: true);
//     final isDarkMode = Theme.of(context).brightness == Brightness.dark;

//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
//       elevation: 0,
//       //backgroundColor: Colors.transparent,
//       child: Container(
//         padding: const EdgeInsets.all(24.0),
//         decoration: BoxDecoration(
//           color: Theme.of(context).dialogBackgroundColor,
//           borderRadius: BorderRadius.circular(16.0),
//           boxShadow: [
//             BoxShadow(
//               // color: Colors.black.withOpacity(0.2),
//               blurRadius: 10.0,
//               spreadRadius: 1.0,
//             ),
//           ],
//         ),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.stretch,
//           children: [
//             Text(
//               'Appearance',
//               style: Theme.of(
//                 context,
//               ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 24),
//             _buildThemeOption(
//               context,
//               title: 'System',
//               isSelected: themeProvider.themeMode == AppThemeMode.system,
//               onTap: () {
//                 themeProvider.setThemeMode(AppThemeMode.system);
//               },
//             ),
//             const SizedBox(height: 12),
//             _buildThemeOption(
//               context,
//               title: 'Light',
//               isSelected: themeProvider.themeMode == AppThemeMode.light,
//               onTap: () {
//                 themeProvider.setThemeMode(AppThemeMode.light);
//               },
//             ),
//             const SizedBox(height: 12),
//             _buildThemeOption(
//               context,
//               title: 'Dark',
//               isSelected: themeProvider.themeMode == AppThemeMode.dark,
//               onTap: () {
//                 themeProvider.setThemeMode(AppThemeMode.dark);
//               },
//             ),
//             const SizedBox(height: 24),
//             ElevatedButton(
//               style: ElevatedButton.styleFrom(
//                 backgroundColor:
//                     isDarkMode
//                         ? Colors.blueAccent
//                         : Theme.of(context).primaryColor,
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(12.0),
//                 ),
//                 padding: const EdgeInsets.symmetric(vertical: 16.0),
//               ),
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: const Text(
//                 'Confirm',
//                 style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildThemeOption(
//     BuildContext context, {
//     required String title,
//     required bool isSelected,
//     required VoidCallback onTap,
//   }) {
//     return InkWell(
//       onTap: onTap,
//       borderRadius: BorderRadius.circular(12.0),
//       child: Container(
//         padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
//         decoration: BoxDecoration(
//           color:
//               isSelected
//                   ? Theme.of(context).primaryColor.withOpacity(0.1)
//                   : Colors.transparent,
//           borderRadius: BorderRadius.circular(12.0),
//           border: Border.all(
//             color:
//                 isSelected
//                     ? Theme.of(context).primaryColor
//                     : Theme.of(context).dividerColor,
//             width: 1.5,
//           ),
//         ),
//         child: Row(
//           children: [
//             Expanded(
//               child: Text(
//                 title,
//                 style: Theme.of(
//                   context,
//                 ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
//               ),
//             ),
//             if (isSelected)
//               Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
//           ],
//         ),
//       ),
//     );
//   }
// }

import 'package:business_app/bloc/theme_bloc/theme_bloc.dart';
import 'package:business_app/bloc/theme_bloc/theme_event.dart';
import 'package:business_app/bloc/theme_bloc/theme_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ThemeSelectionDialog extends StatelessWidget {
  const ThemeSelectionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
      elevation: 0,
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, state) {
          final currentMode = state.mode;

          return Container(
            padding: const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              color: Theme.of(context).dialogBackgroundColor,
              borderRadius: BorderRadius.circular(16.0),
              boxShadow: [BoxShadow(blurRadius: 10.0, spreadRadius: 1.0)],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  'Appearance',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                _buildThemeOption(
                  context,
                  title: 'System',
                  isSelected: currentMode == AppThemeMode.system,
                  onTap: () {
                    context.read<ThemeBloc>().add(
                      SetThemeModeEvent(AppThemeMode.system),
                    );
                  },
                ),
                const SizedBox(height: 12),
                _buildThemeOption(
                  context,
                  title: 'Light',
                  isSelected: currentMode == AppThemeMode.light,
                  onTap: () {
                    context.read<ThemeBloc>().add(
                      SetThemeModeEvent(AppThemeMode.light),
                    );
                  },
                ),
                const SizedBox(height: 12),
                _buildThemeOption(
                  context,
                  title: 'Dark',
                  isSelected: currentMode == AppThemeMode.dark,
                  onTap: () {
                    context.read<ThemeBloc>().add(
                      SetThemeModeEvent(AppThemeMode.dark),
                    );
                  },
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isDarkMode
                            ? Colors.blueAccent
                            : Theme.of(context).primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    'Confirm',
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildThemeOption(
    BuildContext context, {
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.0),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
            color:
                isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).dividerColor,
            width: 1.5,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ),
            if (isSelected)
              Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
          ],
        ),
      ),
    );
  }
}
