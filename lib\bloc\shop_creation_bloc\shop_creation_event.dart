import 'package:equatable/equatable.dart';

abstract class ShopCreationEvent extends Equatable {
  const ShopCreationEvent();

  @override
  List<Object?> get props => [];
}

// Form field update events
class UpdateShopNameEvent extends ShopCreationEvent {
  final String shopName;

  const UpdateShopNameEvent(this.shopName);

  @override
  List<Object?> get props => [shopName];
}

class UpdateShopDescriptionEvent extends ShopCreationEvent {
  final String shopDescription;

  const UpdateShopDescriptionEvent(this.shopDescription);

  @override
  List<Object?> get props => [shopDescription];
}

class UpdateShopLogoEvent extends ShopCreationEvent {
  final String? logoUrl;

  const UpdateShopLogoEvent(this.logoUrl);

  @override
  List<Object?> get props => [logoUrl];
}

class UpdateShopBannerEvent extends ShopCreationEvent {
  final String? bannerUrl;

  const UpdateShopBannerEvent(this.bannerUrl);

  @override
  List<Object?> get props => [bannerUrl];
}

// Image upload events
class UploadShopLogoEvent extends ShopCreationEvent {
  final String imagePath;

  const UploadShopLogoEvent(this.imagePath);

  @override
  List<Object?> get props => [imagePath];
}

class UploadShopBannerEvent extends ShopCreationEvent {
  final String imagePath;

  const UploadShopBannerEvent(this.imagePath);

  @override
  List<Object?> get props => [imagePath];
}

// Validation events
class ValidateFormEvent extends ShopCreationEvent {
  const ValidateFormEvent();
}

class ClearValidationErrorsEvent extends ShopCreationEvent {
  const ClearValidationErrorsEvent();
}

// Shop creation events
class CreateShopEvent extends ShopCreationEvent {
  const CreateShopEvent();
}

class ResetFormEvent extends ShopCreationEvent {
  const ResetFormEvent();
}

// Check existing shop event
class CheckExistingShopEvent extends ShopCreationEvent {
  const CheckExistingShopEvent();
}
