import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/notification_bloc/notification_bloc.dart';
import 'package:business_app/bloc/notification_bloc/notification_event.dart';
import 'package:business_app/bloc/notification_bloc/notification_state.dart';
import 'notification_widgets/notification_settings_page.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  @override
  void initState() {
    super.initState();
    // Load notifications when page opens
    context.read<NotificationBloc>().add(LoadNotificationsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, state) {
              return IconButton(
                icon: const Icon(Icons.mark_email_read_outlined),
                onPressed:
                    state.unreadCount > 0
                        ? () {
                          context.read<NotificationBloc>().add(
                            MarkAllAsReadEvent(),
                          );
                          _showSnackBar('All notifications marked as read');
                        }
                        : null,
                tooltip: 'Mark all as read',
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () => _showNotificationSettings(),
            tooltip: 'Notification settings',
          ),
        ],
      ),
      body: _buildNotificationsList(),
    );
  }

  Widget _buildNotificationsList() {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, state) {
        if (state.status == NotificationStatus.loading) {
          return _buildLoadingState();
        }

        if (state.status == NotificationStatus.error) {
          return _buildErrorState(state.errorMessage ?? 'Unknown error');
        }

        if (state.notifications.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            context.read<NotificationBloc>().add(RefreshNotificationsEvent());
            await Future.delayed(const Duration(milliseconds: 500));
          },
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: state.notifications.length,
            itemBuilder: (context, index) {
              final notification = state.notifications[index];
              return _buildNotificationItem(notification, index);
            },
          ),
        );
      },
    );
  }

  Widget _buildNotificationItem(NotificationModel notification, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color:
            notification.isRead
                ? Theme.of(context).cardColor
                : Theme.of(context).primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              notification.isRead
                  ? Colors.transparent
                  : Theme.of(context).primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _handleNotificationTap(notification),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildNotificationAvatar(notification),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildNotificationContent(notification),
                      const SizedBox(height: 8),
                      _buildNotificationFooter(notification),
                    ],
                  ),
                ),
                _buildNotificationActions(notification),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationAvatar(NotificationModel notification) {
    return Stack(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: _getNotificationColor(notification.type),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: Colors.white,
            size: 20,
          ),
        ),
        if (!notification.isRead)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNotificationContent(NotificationModel notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
            fontSize: 16,
            color: Theme.of(context).textTheme.titleLarge?.color,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          notification.message,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(
              context,
            ).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildNotificationFooter(NotificationModel notification) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getNotificationColor(
              notification.type,
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getNotificationTypeLabel(notification.type),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: _getNotificationColor(notification.type),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          _formatTimeAgo(notification.timestamp),
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationActions(NotificationModel notification) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Theme.of(
          context,
        ).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
        size: 20,
      ),
      onSelected: (value) => _handleNotificationAction(value, notification),
      itemBuilder:
          (context) => [
            PopupMenuItem(
              value: notification.isRead ? 'mark_unread' : 'mark_read',
              child: Row(
                children: [
                  Icon(
                    notification.isRead
                        ? Icons.mark_email_unread
                        : Icons.mark_email_read,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(notification.isRead ? 'Mark as unread' : 'Mark as read'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outline, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading notifications...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Oops! Something went wrong',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(message, textAlign: TextAlign.center),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed:
                () => context.read<NotificationBloc>().add(
                  LoadNotificationsEvent(),
                ),
            icon: const Icon(Icons.refresh),
            label: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.notifications_none_outlined, size: 80, color: Colors.grey),
          SizedBox(height: 24),
          Text(
            'No notifications yet',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'When you get notifications, they\'ll show up here',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'like':
        return Colors.red;
      case 'comment':
        return Colors.blue;
      case 'follow':
        return Colors.green;
      case 'mention':
        return Colors.orange;
      case 'message':
        return Colors.purple;
      case 'post':
        return Colors.teal;
      case 'business':
        return Colors.indigo;
      case 'system':
        return Colors.grey;
      case 'promotion':
        return Colors.amber;
      case 'reminder':
        return Colors.cyan;
      default:
        return Theme.of(context).primaryColor;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'like':
        return Icons.favorite;
      case 'comment':
        return Icons.comment;
      case 'follow':
        return Icons.person_add;
      case 'mention':
        return Icons.alternate_email;
      case 'message':
        return Icons.message;
      case 'post':
        return Icons.article;
      case 'business':
        return Icons.business;
      case 'system':
        return Icons.settings;
      case 'promotion':
        return Icons.local_offer;
      case 'reminder':
        return Icons.schedule;
      default:
        return Icons.notifications;
    }
  }

  String _getNotificationTypeLabel(String type) {
    switch (type) {
      case 'like':
        return 'Like';
      case 'comment':
        return 'Comment';
      case 'follow':
        return 'Follow';
      case 'mention':
        return 'Mention';
      case 'message':
        return 'Message';
      case 'post':
        return 'Post';
      case 'business':
        return 'Business';
      case 'system':
        return 'System';
      case 'promotion':
        return 'Promotion';
      case 'reminder':
        return 'Reminder';
      default:
        return 'Notification';
    }
  }

  String _formatTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 7) {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _handleNotificationTap(NotificationModel notification) {
    if (!notification.isRead) {
      context.read<NotificationBloc>().add(
        MarkNotificationAsReadEvent(notification.id),
      );
    }
    _showSnackBar('Opening ${notification.title}');
  }

  void _handleNotificationAction(
    String action,
    NotificationModel notification,
  ) {
    switch (action) {
      case 'mark_read':
        context.read<NotificationBloc>().add(
          MarkNotificationAsReadEvent(notification.id),
        );
        _showSnackBar('Marked as read');
        break;
      case 'mark_unread':
        context.read<NotificationBloc>().add(
          MarkNotificationAsUnreadEvent(notification.id),
        );
        _showSnackBar('Marked as unread');
        break;
      case 'delete':
        context.read<NotificationBloc>().add(
          DeleteNotificationEvent(notification.id),
        );
        _showSnackBar('Notification deleted');
        break;
    }
  }

  void _showNotificationSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NotificationSettingsPage()),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(milliseconds: 300),
      ),
    );
  }
}
