part of 'connectivity_bloc.dart';

/// Events for connectivity state management
@immutable
sealed class ConnectivityEvent extends Equatable {
  const ConnectivityEvent();

  @override
  List<Object?> get props => [];
}

/// Event triggered when connectivity status changes
class ConnectivityStatusChanged extends ConnectivityEvent {
  final ConnectivityResult connectivityResult;
  
  const ConnectivityStatusChanged(this.connectivityResult);

  @override
  List<Object?> get props => [connectivityResult];
}

/// Event triggered when internet connection status changes
class InternetStatusChanged extends ConnectivityEvent {
  final InternetStatus internetStatus;
  
  const InternetStatusChanged(this.internetStatus);

  @override
  List<Object?> get props => [internetStatus];
}

/// Event to manually check connectivity
class ConnectivityCheckRequested extends ConnectivityEvent {
  const ConnectivityCheckRequested();
}

/// Event to retry connection
class ConnectivityRetryRequested extends ConnectivityEvent {
  const ConnectivityRetryRequested();
}

/// Event to open device settings
class ConnectivityOpenSettingsRequested extends ConnectivityEvent {
  final ConnectivitySettingsType settingsType;
  
  const ConnectivityOpenSettingsRequested({
    this.settingsType = ConnectivitySettingsType.wifi,
  });

  @override
  List<Object?> get props => [settingsType];
}

/// Types of settings that can be opened
enum ConnectivitySettingsType {
  wifi,
  mobileData,
  general,
}
