import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/image_config.dart';
import '../services/image_picker_service.dart';
import '../services/supabase_storage_service.dart';

/// Reusable loading widget for image operations
class ImageLoadingWidget extends StatelessWidget {
  final double size;

  const ImageLoadingWidget({super.key, this.size = 20});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).primaryColor,
        ),
      ),
    );
  }
}

/// Professional profile image widget with upload functionality
class ProfileImageWidget extends StatefulWidget {
  final String? currentImageUrl;
  final Function(String?) onImageChanged;
  final Function(String)? onError;
  final double radius;
  final bool isEditable;
  final String? userId;

  const ProfileImageWidget({
    super.key,
    this.currentImageUrl,
    required this.onImageChanged,
    this.onError,
    this.radius = 50,
    this.isEditable = true,
    this.userId,
  });

  @override
  State<ProfileImageWidget> createState() => _ProfileImageWidgetState();
}

class _ProfileImageWidgetState extends State<ProfileImageWidget>
    with SingleTickerProviderStateMixin {
  File? _selectedImage;
  bool _isUploading = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickAndUploadImage() async {
    if (!widget.isEditable || widget.userId == null) return;

    // Animate button press
    await _animationController.forward();
    await _animationController.reverse();

    if (!mounted) return;

    final imageFile = await ImagePickerService.showImageSourceDialog(
      context: context,
      config: ImageConfig.profile,
      title: 'Select Profile Image',
    );

    if (imageFile != null) {
      setState(() {
        _selectedImage = imageFile;
        _isUploading = true;
      });

      try {
        final result = await SupabaseStorageService.uploadProfileImage(
          userId: widget.userId!,
          imageFile: imageFile,
          replaceExisting: true,
        );

        if (result.isSuccess && result.imageUrl != null) {
          widget.onImageChanged(result.imageUrl);

          // Show success feedback
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Profile image updated successfully!'),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(milliseconds: 2000),
              ),
            );
          }
        } else {
          widget.onError?.call(result.error ?? 'Failed to upload image');
        }
      } catch (e) {
        widget.onError?.call('Upload failed: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isUploading = false;
          });
        }
      }
    }
  }

  ImageProvider? _getImageProvider() {
    if (_selectedImage != null) {
      return FileImage(_selectedImage!);
    } else if (widget.currentImageUrl != null &&
        widget.currentImageUrl!.isNotEmpty) {
      // Use optimized URL if available
      final fileName = SupabaseStorageService.extractFileNameFromUrl(
        widget.currentImageUrl!,
        SupabaseStorageService.profileImagesBucket,
      );

      if (fileName != null) {
        final optimizedUrl = SupabaseStorageService.getOptimizedProfileImageUrl(
          fileName,
        );
        return CachedNetworkImageProvider(optimizedUrl);
      } else {
        return CachedNetworkImageProvider(widget.currentImageUrl!);
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isEditable ? _pickAndUploadImage : null,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Stack(
              children: [
                // Main avatar
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: widget.radius,
                    backgroundColor: Colors.grey[200],
                    backgroundImage: _getImageProvider(),
                    child:
                        _getImageProvider() == null
                            ? Icon(
                              Icons.person,
                              size: widget.radius * 0.8,
                              color: Colors.grey[400],
                            )
                            : null,
                  ),
                ),

                // Loading indicator
                if (_isUploading)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black.withOpacity(0.5),
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                  ),

                // Edit icon
                if (widget.isEditable && !_isUploading)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.camera_alt,
                        size: widget.radius * 0.3,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
