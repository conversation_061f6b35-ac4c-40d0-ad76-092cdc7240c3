import 'package:equatable/equatable.dart';
import 'dart:developer' as developer;

/// Authentication result wrapper for better error handling and type safety
class AuthResult<T> extends Equatable {
  final T? data;
  final String? error;
  final bool isSuccess;
  final String? message;

  const AuthResult({
    this.data,
    this.error,
    required this.isSuccess,
    this.message,
  });

  /// Create a successful result
  factory AuthResult.success(T data, {String? message}) {
    return AuthResult<T>(
      data: data,
      error: null,
      isSuccess: true,
      message: message,
    );
  }

  /// Create an error result
  factory AuthResult.error(String error) {
    return AuthResult<T>(
      data: null,
      error: error,
      isSuccess: false,
      message: null,
    );
  }

  /// Create a loading result (for UI states)
  factory AuthResult.loading() {
    return AuthResult<T>(
      data: null,
      error: null,
      isSuccess: false,
      message: 'Loading...',
    );
  }

  /// Check if the result has data
  bool get hasData => data != null;

  /// Check if the result has an error
  bool get hasError => error != null;

  /// Get data or throw if error
  T get dataOrThrow {
    if (hasError) {
      throw Exception(error);
    }
    if (!hasData) {
      throw Exception('No data available');
    }
    return data!;
  }

  /// Get data or return default value
  T? dataOrDefault(T? defaultValue) {
    return hasData ? data : defaultValue;
  }

  @override
  List<Object?> get props => [data, error, isSuccess, message];

  @override
  String toString() {
    if (isSuccess) {
      return 'AuthResult.success(data: $data, message: $message)';
    } else {
      return 'AuthResult.error(error: $error)';
    }
  }
}

/// Authentication error types for better error handling
enum AuthErrorType {
  invalidCredentials,
  userNotFound,
  emailAlreadyExists,
  phoneAlreadyExists,
  weakPassword,
  invalidEmail,
  invalidPhone,
  emailNotConfirmed,
  phoneNotConfirmed,
  invalidOtp,
  otpExpired,
  samePassword,
  tooManyRequests,
  networkError,
  serverError,
  unknown,
}

/// Authentication error with type and user-friendly message
class AuthError extends Equatable {
  final AuthErrorType type;
  final String message;
  final String? technicalMessage;
  final String? code;

  const AuthError({
    required this.type,
    required this.message,
    this.technicalMessage,
    this.code,
  });

  /// Create error from Supabase exception
  factory AuthError.fromException(dynamic exception) {
    // Enhanced debugging - log the exact exception details
    developer.log(
      '🔍 AuthError.fromException Debug:\n'
      '   Exception Type: ${exception.runtimeType}\n'
      '   Exception String: $exception\n'
      '   Exception Details: ${exception.toString()}',
      name: 'AuthError',
    );

    final String message = exception.toString().toLowerCase();
    developer.log('   Lowercase Message: $message', name: 'AuthError');

    // Check for invalid credentials with multiple patterns
    if (message.contains('invalid login credentials') ||
        message.contains('invalid email or password') ||
        message.contains('invalid credentials') ||
        message.contains('authentication failed') ||
        message.contains('login failed') ||
        message.contains('signin failed') ||
        message.contains('sign in failed') ||
        (message.contains('invalid') && message.contains('password')) ||
        (message.contains('invalid') && message.contains('email')) ||
        message.contains('wrong password') ||
        message.contains('incorrect password')) {
      return const AuthError(
        type: AuthErrorType.invalidCredentials,
        message: 'Invalid email or password. Please try again.',
      );
    }

    if (message.contains('user not found')) {
      return const AuthError(
        type: AuthErrorType.userNotFound,
        message: 'No account found with this email. Please sign up first.',
      );
    }

    if (message.contains('email already registered') ||
        message.contains('user already registered')) {
      return const AuthError(
        type: AuthErrorType.emailAlreadyExists,
        message:
            'An account with this email already exists. Please sign in instead.',
      );
    }

    if (message.contains('phone already registered')) {
      return const AuthError(
        type: AuthErrorType.phoneAlreadyExists,
        message:
            'An account with this phone number already exists. Please sign in instead.',
      );
    }

    if (message.contains('password') && message.contains('weak')) {
      return const AuthError(
        type: AuthErrorType.weakPassword,
        message:
            'Password is too weak. Please use at least 8 characters with letters and numbers.',
      );
    }

    if (message.contains('invalid email')) {
      return const AuthError(
        type: AuthErrorType.invalidEmail,
        message: 'Please enter a valid email address.',
      );
    }

    if (message.contains('invalid phone') ||
        message.contains('phone number is invalid') ||
        message.contains('invalid phone number format')) {
      return const AuthError(
        type: AuthErrorType.invalidPhone,
        message:
            'Please enter a valid phone number with country code (e.g., +265 123 456 789).',
      );
    }

    if (message.contains('phone number not supported') ||
        message.contains('sms not supported') ||
        message.contains('country not supported')) {
      return const AuthError(
        type: AuthErrorType.invalidPhone,
        message:
            'SMS verification is not supported for this phone number. Please try a different number or use email instead.',
      );
    }

    if (message.contains('email not confirmed') ||
        message.contains('email confirmation')) {
      return const AuthError(
        type: AuthErrorType.emailNotConfirmed,
        message: 'Please verify your email address before signing in.',
      );
    }

    if (message.contains('phone not confirmed') ||
        message.contains('phone confirmation')) {
      return const AuthError(
        type: AuthErrorType.phoneNotConfirmed,
        message: 'Please verify your phone number before signing in.',
      );
    }

    if (message.contains('invalid otp') || message.contains('invalid token')) {
      return const AuthError(
        type: AuthErrorType.invalidOtp,
        message: 'Invalid verification code. Please try again.',
      );
    }

    if (message.contains('expired') && message.contains('otp')) {
      return const AuthError(
        type: AuthErrorType.otpExpired,
        message: 'Verification code has expired. Please request a new one.',
      );
    }

    // Handle same password error
    if (message.contains('same_password') ||
        message.contains('new password should be different') ||
        message.contains(
          'password should be different from the old password',
        ) ||
        (message.contains('new password') && message.contains('different'))) {
      return const AuthError(
        type: AuthErrorType.samePassword,
        message: 'New password can not be the same as old password',
      );
    }

    if (message.contains('too many requests') ||
        message.contains('rate limit') ||
        message.contains('over_email_send_rate_limit') ||
        message.contains(
          'for security purposes, you can only request this after',
        )) {
      return const AuthError(
        type: AuthErrorType.tooManyRequests,
        message:
            'Please wait a few seconds before requesting another verification code.',
      );
    }

    // Handle OTP disabled error
    if (message.contains('otp_disabled') ||
        message.contains('signups not allowed for otp') ||
        message.contains('otp not allowed')) {
      return const AuthError(
        type: AuthErrorType.serverError,
        message:
            'Password reset is temporarily unavailable. Please try again later.',
      );
    }

    if (message.contains('network') || message.contains('connection')) {
      return const AuthError(
        type: AuthErrorType.networkError,
        message: 'Network error. Please check your connection and try again.',
      );
    }

    // Enhanced logging for debugging unknown errors
    developer.log(
      '🚨 UNKNOWN AUTH ERROR DEBUG:\n'
      '   Exception Type: ${exception.runtimeType}\n'
      '   Exception String: ${exception.toString()}\n'
      '   Exception Message: $message',
      name: 'AuthError',
      level: 1000, // Warning level
    );

    // Check for more specific Supabase error patterns
    if (message.contains('authexception') ||
        message.contains('auth exception')) {
      return const AuthError(
        type: AuthErrorType.invalidCredentials,
        message: 'Authentication failed. Please check your credentials.',
      );
    }

    if (message.contains('postgrestexception') ||
        message.contains('database')) {
      return const AuthError(
        type: AuthErrorType.serverError,
        message: 'Database error. Please try again later.',
      );
    }

    if (message.contains('timeout') || message.contains('connection')) {
      return const AuthError(
        type: AuthErrorType.networkError,
        message:
            'Connection timeout. Please check your internet and try again.',
      );
    }

    // Log the unhandled error for debugging
    developer.log(
      '🚨 UNHANDLED AUTH ERROR:\n'
      '   Full Exception: $exception\n'
      '   Message: $message\n'
      '   This error pattern needs to be added to AuthError.fromException()',
      name: 'AuthError',
      level: 1200, // Severe level
      error: exception,
    );

    // Default to unknown error
    return AuthError(
      type: AuthErrorType.unknown,
      message: 'An unexpected error occurred. Please try again.',
      technicalMessage: exception.toString(),
    );
  }

  @override
  List<Object?> get props => [type, message, technicalMessage, code];

  @override
  String toString() {
    return 'AuthError(type: $type, message: $message)';
  }
}
