import 'package:flutter_bloc/flutter_bloc.dart';
import 'notification_event.dart';
import 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  NotificationBloc() : super(const NotificationState()) {
    on<LoadNotificationsEvent>(_onLoadNotifications);
    on<RefreshNotificationsEvent>(_onRefreshNotifications);
    on<LoadMoreNotificationsEvent>(_onLoadMoreNotifications);
    on<MarkNotificationAsReadEvent>(_onMarkNotificationAsRead);
    on<MarkNotificationAsUnreadEvent>(_onMarkNotificationAsUnread);
    on<MarkAllAsReadEvent>(_onMarkAllAsRead);
    on<DeleteNotificationEvent>(_onDeleteNotification);
    on<FilterNotificationsByTypeEvent>(_onFilterNotificationsByType);
    on<ToggleNotificationSettingsEvent>(_onToggleNotificationSettings);
    on<SearchNotificationsEvent>(_onSearchNotifications);
    on<ClearSearchEvent>(_onClearSearch);
    on<AddNewNotificationEvent>(_onAddNewNotification);
  }

  Future<void> _onLoadNotifications(
    LoadNotificationsEvent event,
    Emitter<NotificationState> emit,
  ) async {
    emit(state.copyWith(status: NotificationStatus.loading));
    
    try {
      await Future.delayed(const Duration(milliseconds: 800));
      
      final notifications = _generateMockNotifications();
      final unreadCount = notifications.where((n) => !n.isRead).length;
      
      emit(state.copyWith(
        status: NotificationStatus.loaded,
        notifications: notifications,
        unreadCount: unreadCount,
        hasReachedMax: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: NotificationStatus.error,
        errorMessage: 'Failed to load notifications: ${e.toString()}',
      ));
    }
  }

  Future<void> _onRefreshNotifications(
    RefreshNotificationsEvent event,
    Emitter<NotificationState> emit,
  ) async {
    emit(state.copyWith(status: NotificationStatus.refreshing));
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final notifications = _generateMockNotifications();
      final unreadCount = notifications.where((n) => !n.isRead).length;
      
      emit(state.copyWith(
        status: NotificationStatus.loaded,
        notifications: notifications,
        unreadCount: unreadCount,
        hasReachedMax: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: NotificationStatus.error,
        errorMessage: 'Failed to refresh notifications: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadMoreNotifications(
    LoadMoreNotificationsEvent event,
    Emitter<NotificationState> emit,
  ) async {
    if (state.hasReachedMax) return;
    
    emit(state.copyWith(status: NotificationStatus.loadingMore));
    
    try {
      await Future.delayed(const Duration(milliseconds: 800));
      
      final moreNotifications = _generateMockNotifications(startIndex: state.notifications.length);
      final allNotifications = [...state.notifications, ...moreNotifications];
      final unreadCount = allNotifications.where((n) => !n.isRead).length;
      
      emit(state.copyWith(
        status: NotificationStatus.loaded,
        notifications: allNotifications,
        unreadCount: unreadCount,
        hasReachedMax: moreNotifications.length < 10,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: NotificationStatus.error,
        errorMessage: 'Failed to load more notifications: ${e.toString()}',
      ));
    }
  }

  void _onMarkNotificationAsRead(
    MarkNotificationAsReadEvent event,
    Emitter<NotificationState> emit,
  ) {
    final updatedNotifications = state.notifications.map((notification) {
      if (notification.id == event.notificationId) {
        return notification.copyWith(isRead: true);
      }
      return notification;
    }).toList();
    
    final unreadCount = updatedNotifications.where((n) => !n.isRead).length;
    
    emit(state.copyWith(
      notifications: updatedNotifications,
      unreadCount: unreadCount,
    ));
  }

  void _onMarkNotificationAsUnread(
    MarkNotificationAsUnreadEvent event,
    Emitter<NotificationState> emit,
  ) {
    final updatedNotifications = state.notifications.map((notification) {
      if (notification.id == event.notificationId) {
        return notification.copyWith(isRead: false);
      }
      return notification;
    }).toList();
    
    final unreadCount = updatedNotifications.where((n) => !n.isRead).length;
    
    emit(state.copyWith(
      notifications: updatedNotifications,
      unreadCount: unreadCount,
    ));
  }

  void _onMarkAllAsRead(
    MarkAllAsReadEvent event,
    Emitter<NotificationState> emit,
  ) {
    final updatedNotifications = state.notifications.map((notification) {
      return notification.copyWith(isRead: true);
    }).toList();
    
    emit(state.copyWith(
      notifications: updatedNotifications,
      unreadCount: 0,
    ));
  }

  void _onDeleteNotification(
    DeleteNotificationEvent event,
    Emitter<NotificationState> emit,
  ) {
    final updatedNotifications = state.notifications
        .where((notification) => notification.id != event.notificationId)
        .toList();
    
    final unreadCount = updatedNotifications.where((n) => !n.isRead).length;
    
    emit(state.copyWith(
      notifications: updatedNotifications,
      unreadCount: unreadCount,
    ));
  }

  void _onFilterNotificationsByType(
    FilterNotificationsByTypeEvent event,
    Emitter<NotificationState> emit,
  ) {
    emit(state.copyWith(currentFilter: event.filterType));
  }

  void _onToggleNotificationSettings(
    ToggleNotificationSettingsEvent event,
    Emitter<NotificationState> emit,
  ) {
    final updatedSettings = Map<String, bool>.from(state.notificationSettings);
    updatedSettings[event.settingType] = event.enabled;
    
    emit(state.copyWith(notificationSettings: updatedSettings));
  }

  void _onSearchNotifications(
    SearchNotificationsEvent event,
    Emitter<NotificationState> emit,
  ) {
    emit(state.copyWith(
      searchQuery: event.query,
      isSearching: event.query.isNotEmpty,
    ));
  }

  void _onClearSearch(
    ClearSearchEvent event,
    Emitter<NotificationState> emit,
  ) {
    emit(state.copyWith(
      searchQuery: '',
      isSearching: false,
    ));
  }

  void _onAddNewNotification(
    AddNewNotificationEvent event,
    Emitter<NotificationState> emit,
  ) {
    final updatedNotifications = [event.notification, ...state.notifications];
    final unreadCount = updatedNotifications.where((n) => !n.isRead).length;
    
    emit(state.copyWith(
      notifications: updatedNotifications,
      unreadCount: unreadCount,
    ));
  }

  // Mock data generation
  List<NotificationModel> _generateMockNotifications({int startIndex = 0}) {
    final now = DateTime.now();
    
    return List.generate(15, (index) {
      final notificationIndex = startIndex + index;
      final types = [
        NotificationTypes.like,
        NotificationTypes.comment,
        NotificationTypes.follow,
        NotificationTypes.mention,
        NotificationTypes.message,
        NotificationTypes.post,
        NotificationTypes.business,
        NotificationTypes.system,
        NotificationTypes.promotion,
        NotificationTypes.reminder,
      ];
      
      final type = types[notificationIndex % types.length];
      final isRead = notificationIndex % 3 == 0;
      
      return NotificationModel(
        id: 'notification_$notificationIndex',
        title: _getNotificationTitle(type, notificationIndex),
        message: _getNotificationMessage(type, notificationIndex),
        type: type,
        timestamp: now.subtract(Duration(
          hours: notificationIndex * 2,
          minutes: notificationIndex * 15,
        )),
        isRead: isRead,
        imageUrl: _getNotificationImage(type, notificationIndex),
        actionUrl: '/notification/$notificationIndex',
        metadata: {
          'userId': 'user_${notificationIndex % 5}',
          'postId': type == NotificationTypes.post ? 'post_$notificationIndex' : null,
        },
      );
    });
  }

  String _getNotificationTitle(String type, int index) {
    switch (type) {
      case NotificationTypes.like:
        return 'Sarah Johnson liked your post';
      case NotificationTypes.comment:
        return 'Mike Chen commented on your post';
      case NotificationTypes.follow:
        return 'Alex Rivera started following you';
      case NotificationTypes.mention:
        return 'Emma Davis mentioned you in a post';
      case NotificationTypes.message:
        return 'New message from John Smith';
      case NotificationTypes.post:
        return 'David Wilson shared a new post';
      case NotificationTypes.business:
        return 'Your business profile was viewed 25 times';
      case NotificationTypes.system:
        return 'App update available';
      case NotificationTypes.promotion:
        return 'Special offer: 50% off premium features';
      case NotificationTypes.reminder:
        return 'Don\'t forget to post today!';
      default:
        return 'New notification';
    }
  }

  String _getNotificationMessage(String type, int index) {
    switch (type) {
      case NotificationTypes.like:
        return 'Your post about business growth strategies received a like';
      case NotificationTypes.comment:
        return '"Great insights! This really helped me understand the market better."';
      case NotificationTypes.follow:
        return 'You have a new follower interested in your business content';
      case NotificationTypes.mention:
        return 'You were mentioned in a discussion about entrepreneurship';
      case NotificationTypes.message:
        return 'Hey! I saw your latest post and wanted to discuss a potential collaboration...';
      case NotificationTypes.post:
        return 'Check out this amazing business opportunity in your area';
      case NotificationTypes.business:
        return 'Your profile is gaining traction! Consider upgrading to premium for more features.';
      case NotificationTypes.system:
        return 'Version 2.1.0 includes new notification features and performance improvements';
      case NotificationTypes.promotion:
        return 'Limited time offer! Upgrade now and get access to advanced analytics and priority support.';
      case NotificationTypes.reminder:
        return 'Consistent posting helps grow your audience. Share something inspiring today!';
      default:
        return 'You have a new notification';
    }
  }

  String? _getNotificationImage(String type, int index) {
    switch (type) {
      case NotificationTypes.like:
      case NotificationTypes.comment:
      case NotificationTypes.follow:
      case NotificationTypes.mention:
      case NotificationTypes.message:
        return 'https://i.pravatar.cc/150?img=${(index % 20) + 1}';
      case NotificationTypes.post:
        return 'assets/images/kitchen_util.jpg';
      case NotificationTypes.business:
        return 'assets/images/Trademate-logo.png';
      default:
        return null;
    }
  }
}
