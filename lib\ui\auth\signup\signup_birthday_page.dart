import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';

class SignupBirthdayPage extends StatefulWidget {
  const SignupBirthdayPage({super.key});

  @override
  State<SignupBirthdayPage> createState() => _SignupBirthdayPageState();
}

class _SignupBirthdayPageState extends State<SignupBirthdayPage> {
  DateTime? _selectedDate;
  String? _dateError;

  @override
  void initState() {
    super.initState();
    // Pre-fill with existing date if available (for Google users)
    final state = context.read<AuthBloc>().state;
    if (state.tempDateOfBirth != null) {
      _selectedDate = state.tempDateOfBirth;
    }
  }

  void _selectDate() async {
    final now = DateTime.now();
    final eighteenYearsAgo = DateTime(now.year - 18, now.month, now.day);
    final hundredYearsAgo = DateTime(now.year - 100, now.month, now.day);

    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? eighteenYearsAgo,
      firstDate: hundredYearsAgo,
      lastDate: now,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: const Color(0xFF1DA1F2)),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
        _dateError = null;
      });
    }
  }

  void _onNext() {
    // Validate date
    if (_selectedDate == null) {
      setState(() {
        _dateError = 'Date of birth is required';
      });
      return;
    }

    final age = DateTime.now().year - _selectedDate!.year;
    if (age < 13) {
      setState(() {
        _dateError = 'You must be at least 13 years old';
      });
      return;
    }

    context.read<AuthBloc>().add(
      SignupBirthdaySelected(dateOfBirth: _selectedDate!),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final state = context.watch<AuthBloc>().state;
    final isGoogleUser = state.isGoogleSignup == true;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: IntrinsicHeight(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),

                    // Header
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (isGoogleUser) ...[
                          // Google user header
                          Text(
                            'What\'s your birthday?',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: theme.textTheme.headlineLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'This helps us personalize your experience and ensure you meet age requirements.',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                              height: 1.4,
                            ),
                          ),
                        ] else ...[
                          // Regular signup header
                          Text(
                            'When\'s your birthday?',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: theme.textTheme.headlineLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Your birthday helps us create a better experience for you.',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                              height: 1.4,
                            ),
                          ),
                        ],
                      ],
                    ),

                    const SizedBox(height: 60),

                    // Birthday selection
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Date of birth',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: _selectDate,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color:
                                    _dateError != null
                                        ? Colors.red
                                        : Colors.grey[300]!,
                                width: 1.5,
                              ),
                              borderRadius: BorderRadius.circular(12),
                              color: theme.scaffoldBackgroundColor,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    _selectedDate != null
                                        ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                                        : 'Select your date of birth',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color:
                                          _selectedDate != null
                                              ? theme.textTheme.bodyLarge?.color
                                              : Colors.grey[500],
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.calendar_today,
                                  color: Colors.grey[500],
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (_dateError != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            _dateError!,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),

                    const Spacer(),

                    // Next button
                    AuthPrimaryButton(
                      text: 'Next',
                      isLoading: state.isLoading,
                      onPressed: _onNext,
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
