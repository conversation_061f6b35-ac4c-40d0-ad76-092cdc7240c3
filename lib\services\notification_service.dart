import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase/config.dart';
import 'supabase_service.dart';

/// Service for managing notifications
class NotificationService extends BaseSupabaseService {
  /// Get user notifications
  static Future<List<AppNotification>> getUserNotifications({
    String? category,
    bool unreadOnly = false,
    int limit = 50,
    int offset = 0,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return [];

    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_user_notifications',
        {
          'p_user_id': userId,
          'p_category': category,
          'p_unread_only': unreadOnly,
          'p_limit': limit,
          'p_offset': offset,
        },
      );

      return response?.map((json) => AppNotification.fromJson(json)).toList() ??
          [];
    });
  }

  /// Get notification counts
  static Future<NotificationCounts> getNotificationCounts() async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return const NotificationCounts();

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.executeRPC<Map<String, dynamic>>(
            'get_notification_counts',
            {'p_user_id': userId},
          );

      return response != null
          ? NotificationCounts.fromJson(response)
          : const NotificationCounts();
    });
  }

  /// Mark notifications as read
  static Future<int> markNotificationsAsRead({
    List<String>? notificationIds,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return 0;

    return BaseSupabaseService.executeQuery(() async {
      final count = await BaseSupabaseService.executeRPC<int>(
        'mark_notifications_read',
        {'p_user_id': userId, 'p_notification_ids': notificationIds},
      );

      return count ?? 0;
    });
  }

  /// Create a notification (typically called by system)
  static Future<String?> createNotification({
    required String recipientId,
    required String type,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    String priority = 'normal',
    String category = 'general',
    String? actionUrl,
    String? groupKey,
    DateTime? expiresAt,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final notificationId =
          await BaseSupabaseService.executeRPC<String>('create_notification', {
            'recipient_id': recipientId,
            'notification_type': type,
            'title': title,
            'message': message,
            'data': data ?? {},
            'priority': priority,
            'category': category,
            'action_url': actionUrl,
            'group_key': groupKey,
            'expires_at': expiresAt?.toIso8601String(),
          });

      return notificationId;
    });
  }

  /// Delete notification
  static Future<void> deleteNotification(String notificationId) async {
    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.notifications)
          .delete()
          .eq('id', notificationId);
    });
  }

  /// Dismiss notification
  static Future<void> dismissNotification(String notificationId) async {
    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.notifications)
          .update({
            'is_dismissed': true,
            'dismissed_at': DateTime.now().toIso8601String(),
          })
          .eq('id', notificationId);
    });
  }

  /// Subscribe to user notifications (real-time)
  static RealtimeChannel subscribeToNotifications({
    required void Function(AppNotification) onNewNotification,
  }) {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.client
        .channel('notifications:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: DatabaseTables.notifications,
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) {
            final notification = AppNotification.fromJson(payload.newRecord);
            onNewNotification(notification);
          },
        )
        .subscribe();
  }

  /// Clear all notifications for user
  static Future<void> clearAllNotifications() async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return;

    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.notifications)
          .update({
            'is_dismissed': true,
            'dismissed_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('is_dismissed', false);
    });
  }
}

/// Notification model
class AppNotification {
  final String id;
  final String userId;
  final String type;
  final String title;
  final String message;
  final Map<String, dynamic> data;
  final String priority;
  final String category;
  final String? actionUrl;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;

  const AppNotification({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.message,
    this.data = const {},
    this.priority = 'normal',
    this.category = 'general',
    this.actionUrl,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['notification_id'] as String? ?? json['id'] as String,
      userId: json['user_id'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      priority: json['priority'] as String? ?? 'normal',
      category: json['category'] as String? ?? 'general',
      actionUrl: json['action_url'] as String?,
      isRead: json['is_read'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      readAt:
          json['read_at'] != null
              ? DateTime.parse(json['read_at'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type,
      'title': title,
      'message': message,
      'data': data,
      'priority': priority,
      'category': category,
      'action_url': actionUrl,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
    };
  }
}

/// Notification counts model
class NotificationCounts {
  final int totalUnread;
  final int socialUnread;
  final int commerceUnread;
  final int systemUnread;

  const NotificationCounts({
    this.totalUnread = 0,
    this.socialUnread = 0,
    this.commerceUnread = 0,
    this.systemUnread = 0,
  });

  factory NotificationCounts.fromJson(Map<String, dynamic> json) {
    return NotificationCounts(
      totalUnread: json['total_unread'] as int? ?? 0,
      socialUnread: json['social_unread'] as int? ?? 0,
      commerceUnread: json['commerce_unread'] as int? ?? 0,
      systemUnread: json['system_unread'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_unread': totalUnread,
      'social_unread': socialUnread,
      'commerce_unread': commerceUnread,
      'system_unread': systemUnread,
    };
  }
}

/// Notification types enum
class NotificationTypes {
  static const String follow = 'follow';
  static const String like = 'like';
  static const String comment = 'comment';
  static const String reply = 'reply';
  static const String order = 'order';
  static const String review = 'review';
  static const String message = 'message';
  static const String system = 'system';
  static const String promotion = 'promotion';
}

/// Notification categories enum
class NotificationCategories {
  static const String social = 'social';
  static const String commerce = 'commerce';
  static const String system = 'system';
  static const String general = 'general';
}

/// Notification priorities enum
class NotificationPriorities {
  static const String low = 'low';
  static const String normal = 'normal';
  static const String high = 'high';
  static const String urgent = 'urgent';
}
