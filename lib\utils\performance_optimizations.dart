import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Performance optimizations for faster app startup and authentication flow
/// Following best practices from big apps like Instagram, WhatsApp, Twitter
class PerformanceOptimizations {
  /// Initialize performance optimizations during app startup
  static Future<void> initialize() async {
    // Preload critical assets during splash
    await _preloadCriticalAssets();

    // Optimize memory usage
    _optimizeMemoryUsage();

    // Set optimal system UI settings
    _setOptimalSystemUI();
  }

  /// Preload critical assets that will be needed immediately after splash
  static Future<void> _preloadCriticalAssets() async {
    // Preload images that appear on home page or welcome page
    // This prevents loading delays after authentication

    // Example: Preload logo, icons, and critical images
    // await precacheImage(AssetImage('assets/images/home_background.png'), context);
    // await precacheImage(AssetImage('assets/icons/bottom_nav_icons.png'), context);
  }

  /// Optimize memory usage for faster performance
  static void _optimizeMemoryUsage() {
    // Enable hardware acceleration
    // This is automatically enabled in release builds

    // Optimize image cache
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50MB
  }

  /// Set optimal system UI for performance
  static void _setOptimalSystemUI() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Enable edge-to-edge display
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  /// Optimize authentication flow for maximum speed
  static Map<String, dynamic> getAuthOptimizations() {
    return {
      'enableFastAuth': true,
      'cacheUserProfile': true,
      'preloadHomeData': true,
      'minimizeNetworkCalls': true,
      'useLocalStorage': true,
    };
  }

  /// Best practices for splash screen timing
  static const Duration optimalSplashDuration = Duration(milliseconds: 1800);
  static const Duration fastTransitionDuration = Duration(milliseconds: 300);
  static const Duration authCheckTimeout = Duration(seconds: 3);

  /// Performance monitoring helpers
  static void logPerformanceMetric(String metric, Duration duration) {
    debugPrint('🚀 Performance: $metric took ${duration.inMilliseconds}ms');
  }

  /// Check if app startup is optimal
  static bool isStartupOptimal(Duration startupTime) {
    const maxOptimalStartup = Duration(seconds: 3);
    return startupTime <= maxOptimalStartup;
  }
}

/// Extension for context-based performance optimizations
extension PerformanceContext on BuildContext {
  /// Preload images for this context
  Future<void> preloadImages(List<String> imagePaths) async {
    for (final path in imagePaths) {
      await precacheImage(AssetImage(path), this);
    }
  }

  /// Get optimal animation duration based on device performance
  Duration getOptimalAnimationDuration() {
    final mediaQuery = MediaQuery.of(this);
    final isLowEndDevice = mediaQuery.size.width < 400;

    return isLowEndDevice
        ? const Duration(milliseconds: 200) // Faster for low-end devices
        : const Duration(milliseconds: 300); // Standard for modern devices
  }
}

/// Performance constants used throughout the app
class PerformanceConstants {
  // Splash screen timing
  static const Duration splashMinimumDuration = Duration(milliseconds: 1500);
  static const Duration splashMaximumDuration = Duration(milliseconds: 2000);

  // Animation durations
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration standardAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);

  // Network timeouts
  static const Duration authTimeout = Duration(seconds: 5);
  static const Duration apiTimeout = Duration(seconds: 10);

  // Cache settings
  static const int maxImageCacheSize = 100;
  static const int maxImageCacheSizeBytes = 50 * 1024 * 1024; // 50MB
}
