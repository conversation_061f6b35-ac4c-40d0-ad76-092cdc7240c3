-- =====================================================
-- FIX INFINITE RECURSION IN PROFILES DELETE POLICY
-- =====================================================

-- Drop the existing recursive policy that was causing infinite recursion
DROP POLICY IF EXISTS "profiles_delete_policy" ON public.profiles;

-- For now, disable profile deletion entirely to prevent recursion
-- This is the safest approach and most apps don't allow profile deletion anyway
-- Profiles will be soft-deleted or deactivated instead

-- If you need profile deletion later, you can:
-- 1. Set up JWT claims with user roles during authentication
-- 2. Use auth.jwt() ->> 'user_role' = 'super_admin' in the policy
-- 3. Or create a separate admin management system

-- Note: Without a DELETE policy, no one can delete profiles
-- This prevents the infinite recursion issue completely
