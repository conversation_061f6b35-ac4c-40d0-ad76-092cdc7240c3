import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:app_settings/app_settings.dart';

part 'connectivity_event.dart';
part 'connectivity_state.dart';

/// Professional connectivity BLoC for handling offline/online states
/// Implements dual-layer detection like big apps (Facebook, Instagram, WhatsApp)
class ConnectivityBloc extends Bloc<ConnectivityEvent, ConnectivityState> {
  // Stream subscriptions
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  StreamSubscription<InternetStatus>? _internetSubscription;

  // Services
  final Connectivity _connectivity = Connectivity();
  late final InternetConnection _internetConnection;

  // Performance optimization - debounce rapid changes
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 500);

  ConnectivityBloc() : super(ConnectivityState.initial()) {
    // Initialize internet connection checker with optimized settings
    _internetConnection = InternetConnection.createInstance(
      customCheckOptions: [
        InternetCheckOption(
          uri: Uri.parse('https://icanhazip.com/'),
          timeout: const Duration(seconds: 3),
        ),
        InternetCheckOption(
          uri: Uri.parse('https://jsonplaceholder.typicode.com/posts/1'),
          timeout: const Duration(seconds: 3),
        ),
      ],
      useDefaultOptions: false,
    );

    // Register event handlers
    on<ConnectivityStatusChanged>(_onConnectivityStatusChanged);
    on<InternetStatusChanged>(_onInternetStatusChanged);
    on<ConnectivityCheckRequested>(_onConnectivityCheckRequested);
    on<ConnectivityRetryRequested>(_onConnectivityRetryRequested);
    on<ConnectivityOpenSettingsRequested>(_onConnectivityOpenSettingsRequested);

    // Start monitoring connectivity
    _startConnectivityMonitoring();

    // Initial connectivity check
    add(const ConnectivityCheckRequested());
  }

  /// Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    // Monitor network connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        _debounceConnectivityChange(() {
          // Use the first result or none if empty
          final result =
              results.isNotEmpty ? results.first : ConnectivityResult.none;
          add(ConnectivityStatusChanged(result));
        });
      },
      onError: (error) {
        add(ConnectivityStatusChanged(ConnectivityResult.none));
      },
    );

    // Monitor internet connection changes
    _internetSubscription = _internetConnection.onStatusChange.listen(
      (InternetStatus status) {
        _debounceConnectivityChange(() {
          add(InternetStatusChanged(status));
        });
      },
      onError: (error) {
        // Handle internet check errors gracefully
        add(const InternetStatusChanged(InternetStatus.disconnected));
      },
    );
  }

  /// Debounce rapid connectivity changes for better performance
  void _debounceConnectivityChange(VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceDuration, callback);
  }

  /// Handle connectivity status changes
  Future<void> _onConnectivityStatusChanged(
    ConnectivityStatusChanged event,
    Emitter<ConnectivityState> emit,
  ) async {
    final connectivityResult = event.connectivityResult;

    if (connectivityResult == ConnectivityResult.none) {
      emit(state.disconnected());
      return;
    }

    // Check if we have actual internet access
    final hasInternet = await _checkInternetConnection();

    if (hasInternet) {
      emit(state.connected(connectivityResult: connectivityResult));
    } else {
      emit(state.limitedConnection(connectivityResult: connectivityResult));
    }
  }

  /// Handle internet status changes
  Future<void> _onInternetStatusChanged(
    InternetStatusChanged event,
    Emitter<ConnectivityState> emit,
  ) async {
    final internetStatus = event.internetStatus;
    final currentConnectivityList = await _connectivity.checkConnectivity();
    final currentConnectivity =
        currentConnectivityList.isNotEmpty
            ? currentConnectivityList.first
            : ConnectivityResult.none;

    if (currentConnectivity == ConnectivityResult.none) {
      emit(state.disconnected());
      return;
    }

    if (internetStatus == InternetStatus.connected) {
      emit(state.connected(connectivityResult: currentConnectivity));
    } else {
      emit(state.limitedConnection(connectivityResult: currentConnectivity));
    }
  }

  /// Handle manual connectivity check
  Future<void> _onConnectivityCheckRequested(
    ConnectivityCheckRequested event,
    Emitter<ConnectivityState> emit,
  ) async {
    try {
      final connectivityResultList = await _connectivity.checkConnectivity();
      final connectivityResult =
          connectivityResultList.isNotEmpty
              ? connectivityResultList.first
              : ConnectivityResult.none;

      if (connectivityResult == ConnectivityResult.none) {
        emit(state.disconnected());
        return;
      }

      final hasInternet = await _checkInternetConnection();

      if (hasInternet) {
        emit(state.connected(connectivityResult: connectivityResult));
      } else {
        emit(state.limitedConnection(connectivityResult: connectivityResult));
      }
    } catch (e) {
      emit(state.error('Failed to check connectivity: ${e.toString()}'));
    }
  }

  /// Handle retry connection
  Future<void> _onConnectivityRetryRequested(
    ConnectivityRetryRequested event,
    Emitter<ConnectivityState> emit,
  ) async {
    emit(state.retrying());

    // Wait a moment before retrying
    await Future.delayed(const Duration(seconds: 1));

    add(const ConnectivityCheckRequested());
  }

  /// Handle opening device settings
  Future<void> _onConnectivityOpenSettingsRequested(
    ConnectivityOpenSettingsRequested event,
    Emitter<ConnectivityState> emit,
  ) async {
    try {
      switch (event.settingsType) {
        case ConnectivitySettingsType.wifi:
          await AppSettings.openAppSettings(type: AppSettingsType.wifi);
          break;
        case ConnectivitySettingsType.mobileData:
          await AppSettings.openAppSettings(type: AppSettingsType.dataRoaming);
          break;
        case ConnectivitySettingsType.general:
          await AppSettings.openAppSettings(type: AppSettingsType.settings);
          break;
      }
    } catch (e) {
      // Fallback to general settings if specific setting fails
      try {
        await AppSettings.openAppSettings(type: AppSettingsType.settings);
      } catch (fallbackError) {
        emit(state.error('Could not open device settings'));
      }
    }
  }

  /// Check internet connection with timeout
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await _internetConnection.hasInternetAccess;
      return result;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> close() {
    _debounceTimer?.cancel();
    _connectivitySubscription?.cancel();
    _internetSubscription?.cancel();
    return super.close();
  }
}
