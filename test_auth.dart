import 'package:flutter/material.dart';
import 'dart:developer' as developer;
import 'lib/services/supabase_service.dart';
import 'lib/supabase/config.dart';

/// Simple test to verify authentication is working
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Supabase
    developer.log('🔧 Initializing Supabase...', name: 'TestAuth');
    await SupabaseConfig.initialize();
    developer.log('✅ Supabase initialized successfully', name: 'TestAuth');

    // Test connection
    developer.log('🔗 Testing Supabase connection...', name: 'TestAuth');
    final client = SupabaseConfig.client;
    developer.log(
      '✅ Client created: ${SupabaseConfig.supabaseUrl}',
      name: 'TestAuth',
    );

    // First, let's check if we can query the database
    developer.log('🗄️ Testing database connection...', name: 'TestAuth');
    try {
      final response = await client.from('profiles').select('count').count();
      developer.log('✅ Database connection successful', name: 'TestAuth');
      developer.log('   Profiles count: $response', name: 'TestAuth');
    } catch (e) {
      developer.log('❌ Database connection failed: $e', name: 'TestAuth');
    }

    // Check if there are any users in auth
    developer.log('👥 Checking auth users...', name: 'TestAuth');
    try {
      final currentUser = client.auth.currentUser;
      developer.log(
        '   Current user: ${currentUser?.id ?? 'None'}',
        name: 'TestAuth',
      );
    } catch (e) {
      developer.log('❌ Auth check failed: $e', name: 'TestAuth');
    }

    // Test authentication with a known email
    developer.log('🔐 Testing authentication...', name: 'TestAuth');

    // Replace with a test email and password from your database
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';

    // DIAGNOSTIC: Check if user exists in Supabase auth
    developer.log(
      '🔍 DIAGNOSTIC: Checking user in Supabase...',
      name: 'TestAuth',
    );
    try {
      final userCheck =
          await SupabaseConfig.client
              .from('auth.users')
              .select('id, email, email_confirmed_at, created_at')
              .eq('email', testEmail)
              .maybeSingle();

      if (userCheck != null) {
        developer.log('✅ User found in auth.users:', name: 'TestAuth');
        developer.log('   ID: ${userCheck['id']}', name: 'TestAuth');
        developer.log('   Email: ${userCheck['email']}', name: 'TestAuth');
        developer.log(
          '   Email Confirmed: ${userCheck['email_confirmed_at'] != null ? 'YES' : 'NO'}',
          name: 'TestAuth',
        );
        developer.log(
          '   Created: ${userCheck['created_at']}',
          name: 'TestAuth',
        );

        if (userCheck['email_confirmed_at'] == null) {
          developer.log(
            '🚨 ISSUE FOUND: Email not confirmed!',
            name: 'TestAuth',
          );
          developer.log(
            '   This is likely why login is failing',
            name: 'TestAuth',
          );
        }
      } else {
        developer.log('❌ User NOT found in auth.users table', name: 'TestAuth');
        developer.log(
          '   This means the user needs to sign up first',
          name: 'TestAuth',
        );
      }
    } catch (e) {
      developer.log(
        '⚠️ Could not check auth.users (this is normal if RLS is enabled)',
        name: 'TestAuth',
      );
      developer.log('   Error: $e', name: 'TestAuth');
    }

    // Test 1: Traditional Email + Password Login
    developer.log(
      '📧 Testing email+password signin with: $testEmail',
      name: 'TestAuth',
    );
    final result = await AuthService.signInWithEmail(
      email: testEmail,
      password: testPassword,
    );

    // Test 2: Email OTP Login (if email+password fails due to verification)
    developer.log(
      '📧 Testing email OTP signin with: $testEmail',
      name: 'TestAuth',
    );
    final otpResult = await AuthService.signInWithEmailOTP(
      email: testEmail,
      emailRedirectTo: 'myapp://login-callback',
    );

    // Check OTP result
    if (otpResult.isSuccess) {
      developer.log('✅ Email OTP sent successfully!', name: 'TestAuth');
      developer.log('   Check your email for the magic link', name: 'TestAuth');
    } else {
      developer.log('❌ Email OTP failed: ${otpResult.error}', name: 'TestAuth');
    }

    // Check traditional login result
    if (result.isSuccess) {
      developer.log(
        '✅ Email+Password authentication successful!',
        name: 'TestAuth',
      );
      developer.log('   User ID: ${result.data?.user?.id}', name: 'TestAuth');
      developer.log('   Email: ${result.data?.user?.email}', name: 'TestAuth');
    } else {
      developer.log(
        '❌ Authentication failed: ${result.error}',
        name: 'TestAuth',
      );

      // Check if it's an email verification issue
      final errorString = result.error?.toLowerCase() ?? '';
      if (errorString.contains('email not confirmed') ||
          errorString.contains('verify your email') ||
          errorString.contains('email confirmation')) {
        developer.log('📧 Email verification required!', name: 'TestAuth');

        // Test email verification status check
        developer.log(
          '🔍 Checking email verification status...',
          name: 'TestAuth',
        );
        final verificationResult = await AuthService.isEmailVerified(
          email: testEmail,
        );
        if (verificationResult.isSuccess) {
          developer.log(
            '   Email verified: ${verificationResult.data}',
            name: 'TestAuth',
          );
        } else {
          developer.log(
            '   Could not check verification status',
            name: 'TestAuth',
          );
        }

        // Test resend verification email
        developer.log(
          '📤 Testing resend verification email...',
          name: 'TestAuth',
        );
        final resendResult = await AuthService.resendEmailVerification(
          email: testEmail,
        );
        if (resendResult.isSuccess) {
          developer.log(
            '✅ Verification email resent successfully',
            name: 'TestAuth',
          );
        } else {
          developer.log(
            '❌ Failed to resend verification email: ${resendResult.error}',
            name: 'TestAuth',
          );
        }
      } else {
        developer.log(
          '   This is expected if the test user doesn\'t exist or has wrong credentials',
          name: 'TestAuth',
        );
      }
    }
    // Summary
    developer.log('', name: 'TestAuth');
    developer.log('📋 AUTHENTICATION TEST SUMMARY:', name: 'TestAuth');
    developer.log('=' * 50, name: 'TestAuth');
    developer.log('✅ Supabase connection: Working', name: 'TestAuth');
    developer.log('✅ Database access: Working', name: 'TestAuth');

    if (result.isSuccess) {
      developer.log('✅ Email+Password login: SUCCESS', name: 'TestAuth');
    } else {
      developer.log(
        '❌ Email+Password login: ${result.error}',
        name: 'TestAuth',
      );
    }

    if (otpResult.isSuccess) {
      developer.log('✅ Email OTP login: SUCCESS', name: 'TestAuth');
    } else {
      developer.log('❌ Email OTP login: ${otpResult.error}', name: 'TestAuth');
    }

    developer.log('', name: 'TestAuth');
    developer.log('🎯 TROUBLESHOOTING GUIDE:', name: 'TestAuth');
    developer.log('', name: 'TestAuth');

    if (!result.isSuccess) {
      developer.log('❌ EMAIL+PASSWORD LOGIN FAILED:', name: 'TestAuth');
      developer.log('', name: 'TestAuth');
      developer.log(
        '🔍 Check these in your Supabase Dashboard:',
        name: 'TestAuth',
      );
      developer.log('1. Go to Authentication > Settings', name: 'TestAuth');
      developer.log('2. Check if "Confirm email" is enabled', name: 'TestAuth');
      developer.log(
        '3. If enabled, user must verify email first',
        name: 'TestAuth',
      );
      developer.log('', name: 'TestAuth');
      developer.log(
        '🔍 Check these in Authentication > Users:',
        name: 'TestAuth',
      );
      developer.log('1. Search for your test email', name: 'TestAuth');
      developer.log('2. Check if user exists', name: 'TestAuth');
      developer.log('3. Check "Email Confirmed" column', name: 'TestAuth');
      developer.log('', name: 'TestAuth');
      developer.log('💡 Solutions:', name: 'TestAuth');
      developer.log(
        '- If user doesn\'t exist: Sign up first',
        name: 'TestAuth',
      );
      developer.log(
        '- If email not confirmed: Check email for verification link',
        name: 'TestAuth',
      );
      developer.log(
        '- If confirmed but still failing: Check password',
        name: 'TestAuth',
      );
      developer.log('', name: 'TestAuth');
    }

    developer.log('🎯 NEXT STEPS:', name: 'TestAuth');
    developer.log(
      '1. Check Supabase Dashboard Authentication settings',
      name: 'TestAuth',
    );
    developer.log(
      '2. Verify user exists and email is confirmed',
      name: 'TestAuth',
    );
    developer.log(
      '3. Use DualLoginWidget in your app for both login methods',
      name: 'TestAuth',
    );
    developer.log(
      '4. Configure deep linking for magic links',
      name: 'TestAuth',
    );
    developer.log('=' * 50, name: 'TestAuth');
  } catch (e) {
    developer.log('🚨 Test failed with error: $e', name: 'TestAuth');
    developer.log('   Error type: ${e.runtimeType}', name: 'TestAuth');
  }
}
