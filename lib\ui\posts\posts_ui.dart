//new post ui omega ----------------------------------------------------------
import 'dart:ui';

import 'package:business_app/ui/customer_profiles/customer_dashboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MainPost extends StatelessWidget {
  const MainPost({super.key});

  @override
  Widget build(BuildContext context) {
    // for the images in the post, first we have to count the number of images displayed
    // and then display them in a grid view
    // if there is only one image, display it in a single image view

    final List<String> images = [
      'assets/images/kitchen_util.jpg',
      'assets/images/dr_black_shoe.jpg',
      'assets/images/snickers_shoe.jpg',
      'assets/images/kitchen_util.jpg',
      'assets/images/dr_martens_shoe.jpg',
      'assets/images/dr_martens_shoe.jpg',

      // add or remove images as needed
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 👤 User Info and Location Row
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CustomerProfilePage(),
                          ),
                        );
                      },
                      child: Stack(
                        children: [
                          const CircleAvatar(
                            radius: 20,
                            backgroundColor: Colors.amber,
                            child: CircleAvatar(
                              radius: 18,
                              backgroundImage: NetworkImage(
                                'https://cdni.pornpics.com/1280/7/725/40669812/40669812_034_2bed.jpg',
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => CustomerProfilePage(),
                            ),
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Christina Lungu',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              '@chris-lungu',
                              style: TextStyle(
                                color: Colors.grey.shade500,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.location_on_outlined,
                          color: Colors.blue,
                          size: 16,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          "Luwinga • 61.8 km",
                          style: TextStyle(color: Colors.blue, fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // 📸 Image
                /* ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: AspectRatio(
                    aspectRatio: 16 / 12,
                    child: Image.asset(
                      'assets/images/kitchen_util.jpg',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),*/
                images.length == 1
                    ? /*ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: AspectRatio(
                        aspectRatio: 16 / 12,
                        child: Image.asset(images.first, fit: BoxFit.cover),
                      ),
                    )*/ StunningImageViewer(imageAssetPath: images.first)
                    : MoreImageGrid(imagePaths: images),

                // MoreImageGrid(
                //   imagePaths: [
                //     'assets/images/kitchen_util.jpg',
                //     'assets/images/dr_black_shoe.jpg',
                //     'assets/images/snickers_shoe.jpg',
                //     'assets/images/kitchen_util.jpg',
                //     'assets/images/dr_martens_shoe.jpg',
                //     // additional images if needed
                //   ],
                // ),
                const SizedBox(height: 12),

                // 💰 Price and Description
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "MWK 86,000.00",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    ExpandableText(
                      text:
                          'We offer original kitchen utensils starting at an affordable price😎✌️\nStarting from next month we will be offering a discount up to 26%. Come and buy now!',
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // ❤️ 🔁 💬 📈 Post Reactions (Use Wrap for responsiveness)
                Row(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.favorite,
                          color: Colors.pinkAccent,
                          size: 18,
                        ),
                        SizedBox(width: 2),
                        Text(
                          '10,116',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.pinkAccent,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 16),
                    Icon(
                      Icons.mode_comment_outlined,
                      color: Colors.grey,
                      size: 18,
                    ),
                    SizedBox(width: 16),
                    Row(
                      children: [
                        Icon(Icons.send_outlined, size: 18),
                        SizedBox(width: 2),
                        Text('60K', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                    Spacer(),
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              '80.6K',
                              style: TextStyle(fontSize: 12),
                              overflow: TextOverflow.ellipsis,
                              softWrap: false,
                            ),
                          ),
                          SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              'Views',
                              style: TextStyle(fontSize: 12),
                              overflow: TextOverflow.ellipsis,
                              softWrap: false,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 10),

                // 🕒 ⏱️ Time and Buttons (Use Wrap instead of Row)
                Row(
                  children: [
                    Text(
                      '26 d',
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                    ),
                    Spacer(),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        OutlinedButton(
                          onPressed: () {},
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Colors.grey),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            minimumSize: Size(60, 30),
                            backgroundColor: Colors.transparent.withOpacity(
                              0.1,
                            ),
                          ),
                          child: Text(
                            'Buy',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        SizedBox(width: 4),
                        OutlinedButton(
                          onPressed: () {},
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Colors.grey),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            minimumSize: Size(60, 30),
                            backgroundColor: Colors.transparent.withOpacity(
                              0.1,
                            ),
                          ),
                          child: Text(
                            'Visit Shop',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        const Divider(),
      ],
    );
  }
}

// code for showing images

class MoreImageGrid extends StatelessWidget {
  final List<String> imagePaths;

  const MoreImageGrid({super.key, required this.imagePaths});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Instagram-style layout logic
    if (imagePaths.length == 2) {
      return _buildTwoImages(context, isDark);
    } else if (imagePaths.length == 3) {
      return _buildThreeImages(context, isDark);
    } else {
      return _buildFourOrMoreImages(context, isDark);
    }
  }

  // Two images: side by side
  Widget _buildTwoImages(BuildContext context, bool isDark) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 280,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isDark ? Colors.black26 : Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: _buildImageContainer(
                context,
                imagePaths[0],
                0,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: _buildImageContainer(
                context,
                imagePaths[1],
                1,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Three images: one large on left, two stacked on right
  Widget _buildThreeImages(BuildContext context, bool isDark) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 280,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isDark ? Colors.black26 : Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: _buildImageContainer(
                context,
                imagePaths[0],
                0,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: _buildImageContainer(
                      context,
                      imagePaths[1],
                      1,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Expanded(
                    child: _buildImageContainer(
                      context,
                      imagePaths[2],
                      2,
                      borderRadius: const BorderRadius.only(
                        bottomRight: Radius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Four or more images: 2x2 grid with overlay on last image
  Widget _buildFourOrMoreImages(BuildContext context, bool isDark) {
    final displayImages = imagePaths.take(4).toList();
    final hasMore = imagePaths.length > 4;

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 280,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isDark ? Colors.black26 : Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: _buildImageContainer(
                      context,
                      displayImages[0],
                      0,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: _buildImageContainer(
                      context,
                      displayImages[1],
                      1,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 2),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: _buildImageContainer(
                      context,
                      displayImages[2],
                      2,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: _buildImageContainer(
                      context,
                      displayImages[3],
                      3,
                      borderRadius: const BorderRadius.only(
                        bottomRight: Radius.circular(12),
                      ),
                      showOverlay: hasMore,
                      overlayText: '+${imagePaths.length - 4}',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageContainer(
    BuildContext context,
    String imagePath,
    int index, {
    BorderRadius? borderRadius,
    bool showOverlay = false,
    String? overlayText,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact(); // Instagram-like haptic feedback
        showDialog(
          context: context,
          barrierColor: Colors.black87,
          builder:
              (_) => TikTokImageViewer(images: imagePaths, initialPage: index),
        );
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              Hero(
                tag: 'image_$index',
                child: Image.asset(
                  imagePath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: isDark ? Colors.grey[800] : Colors.grey[200],
                      child: Icon(
                        Icons.broken_image_outlined,
                        color: isDark ? Colors.grey[600] : Colors.grey[400],
                        size: 32,
                      ),
                    );
                  },
                ),
              ),
              if (showOverlay)
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        overlayText ?? '+ View All',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ),
                ),
              // Subtle hover effect overlay
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      showDialog(
                        context: context,
                        barrierColor: Colors.black87,
                        builder:
                            (_) => TikTokImageViewer(
                              images: imagePaths,
                              initialPage: index,
                            ),
                      );
                    },
                    splashColor: Colors.white.withOpacity(0.1),
                    highlightColor: Colors.white.withOpacity(0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TikTokImageViewer extends StatefulWidget {
  final List<String> images;
  final int initialPage;

  const TikTokImageViewer({
    super.key,
    required this.images,
    required this.initialPage,
  });

  @override
  State<TikTokImageViewer> createState() => _TikTokImageViewerState();
}

class _TikTokImageViewerState extends State<TikTokImageViewer> {
  late final PageController _controller;
  late int _currentPage;

  @override
  void initState() {
    _currentPage = widget.initialPage;
    _controller = PageController(initialPage: _currentPage);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.all(12),
      backgroundColor: Colors.transparent,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Blur background
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(color: Colors.black.withOpacity(0.7)),
            ),
            Column(
              children: [
                const SizedBox(height: 12),
                Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      '${_currentPage + 1}/${widget.images.length}',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: PageView.builder(
                    controller: _controller,
                    itemCount: widget.images.length,
                    onPageChanged: (index) {
                      setState(() => _currentPage = index);
                    },
                    itemBuilder: (_, index) {
                      return ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(
                          widget.images[index],
                          fit: BoxFit.contain,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 12),
                SmoothPageIndicator(
                  controller: _controller,
                  count: widget.images.length,
                  effect: WormEffect(
                    activeDotColor: Colors.amber,
                    dotHeight: 8,
                    dotWidth: 8,
                  ),
                ),
                const SizedBox(height: 12),
              ],
            ),
            // Close button
            Positioned(
              right: 10,
              top: 10,
              child: IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// code for showing images ends

/*import 'package:flutter/material.dart';

class MainPost extends StatelessWidget {
  const MainPost({super.key});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: Container(
            //width: width * 0.9,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              // color: const Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CustomerDashboard(),
                          ),
                        );
                      },
                      child: Stack(
                        children: [
                          const CircleAvatar(
                            radius: 20,
                            backgroundColor: Colors.amber,
                            child: CircleAvatar(
                              radius: 18,
                              backgroundImage: NetworkImage(
                                'https://cdni.pornpics.com/1280/7/725/40669812/40669812_034_2bed.jpg',
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color:
                                      Colors
                                          .white, // match your background or avatar border
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 10),
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CustomerDashboard(),
                          ),
                        );
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Christina Lungu',
                            style: TextStyle(
                              //color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '@chris-lungu',
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on_outlined,
                          color: Colors.blue,
                          size: 16,
                        ),
                        Text(
                          "Luwinga • 61.8 km",
                          style: TextStyle(color: Colors.blue),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 12),
                ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: AspectRatio(
                    aspectRatio: 16 / 12, //9
                    child: Image.asset(
                      'assets/images/kitchen_util.jpg',
                      fit: BoxFit.cover,
                    ),
                    //child: Image.network(
                    //  'https://i.pravatar.cc/150?img=8', // Replace with a real URL
                    //  fit: BoxFit.cover,
                    // ),
                  ),
                ),
                const SizedBox(height: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "MWK 68,000.00",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        // color: Colors.white70,
                      ),
                    ),
                    SizedBox(height: 4),
                    ExpandableText(
                      text:
                          'We offer original kitchen utensils starting at an affordable price😎✌️\nStarting from next month we will be offering a discount up to 26%. Come and buy now! ',
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.favorite,
                          color: Colors.pinkAccent,
                          size: 18,
                        ),
                        SizedBox(width: 2),
                        Text(
                          '10,814',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.pinkAccent,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.mode_comment_outlined,
                      color: Colors.grey,
                      size: 18,
                    ),
                    const SizedBox(width: 16),
                    Row(
                      children: [
                        Icon(
                          Icons.send_outlined, //color: Colors.white70,
                          size: 18,
                        ),
                        SizedBox(width: 2),
                        Text('68K', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                    Spacer(),
                    SizedBox(width: 4),
                    /*
                    Icon(
                      Icons.bookmark_border,
                      // color: Colors.white70,
                      size: 20,
                    ),
                    */
                    Row(
                      children: [
                        Text('128.6K', style: TextStyle(fontSize: 12)),
                        SizedBox(width: 2),
                        Text('Views', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 10),

                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '21 h',
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                    ),
                    Spacer(),
                    const Spacer(),
                    OutlinedButton(
                      onPressed: () {},

                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        backgroundColor: Colors.transparent.withOpacity(0.1),
                        minimumSize: Size(60, 30), // Width and height
                      ),
                      child: Text(
                        'Buy',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const SizedBox(width: 4),
                    OutlinedButton(
                      onPressed: () {},

                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        backgroundColor: Colors.transparent.withOpacity(0.1),
                        minimumSize: Size(60, 30), // Width and height
                      ),
                      child: Text(
                        'Visit Shop',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 8),
        Divider(),
      ],
    );
  }
}
*/
//class for expanded text in the post

class ExpandableText extends StatefulWidget {
  final String text;
  final int maxLines;

  const ExpandableText({super.key, required this.text, this.maxLines = 2});

  @override
  State<ExpandableText> createState() => _ExpandableTextState();
}

class _ExpandableTextState extends State<ExpandableText> {
  bool _expanded = false;

  @override
  Widget build(BuildContext context) {
    //final defaultStyle = GoogleFonts.poppins(fontSize: 14);
    final linkStyle = TextStyle(color: Colors.blue);

    return LayoutBuilder(
      builder: (context, constraints) {
        final span = TextSpan(text: widget.text);
        final tp = TextPainter(
          text: span,
          maxLines: widget.maxLines,
          textDirection: TextDirection.ltr,
        )..layout(maxWidth: constraints.maxWidth);

        final isOverflowing = tp.didExceedMaxLines;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.text,
              //style: defaultStyle,
              maxLines: _expanded ? null : widget.maxLines,
              overflow:
                  _expanded ? TextOverflow.visible : TextOverflow.ellipsis,
            ),
            if (isOverflowing)
              GestureDetector(
                onTap: () => setState(() => _expanded = !_expanded),
                child: Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    _expanded ? 'View less' : 'View more...',
                    style: linkStyle,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

//for single image--------------------------------

class StunningImageViewer extends StatelessWidget {
  final String imageAssetPath;

  const StunningImageViewer({super.key, required this.imageAssetPath});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          barrierColor: Colors.black.withOpacity(0.6),
          builder: (_) => _ImagePopupViewer(imageAssetPath: imageAssetPath),
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AspectRatio(
          //aspectRatio: 16 / 12,
          aspectRatio: 16 / 17,
          child: Image.asset(imageAssetPath, fit: BoxFit.cover),
        ),
      ),
    );
  }
}

class _ImagePopupViewer extends StatelessWidget {
  final String imageAssetPath;

  const _ImagePopupViewer({required this.imageAssetPath});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: Stack(
        children: [
          // Blurred Background
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(color: Colors.black.withOpacity(0.5)),
          ),

          // Image and Buttons
          Column(
            children: [
              const SizedBox(height: 40),
              Row(
                children: [
                  const SizedBox(width: 16),
                  IconButton(
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 28,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  const Spacer(),
                  const SizedBox(width: 16),
                ],
              ),
              const Spacer(),
              Center(
                child: Hero(
                  tag: imageAssetPath,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      imageAssetPath,
                      fit: BoxFit.contain,
                      width: MediaQuery.of(context).size.width * 0.9,
                      height: MediaQuery.of(context).size.height * 0.7,
                    ),
                  ),
                ),
              ),
              const Spacer(),
            ],
          ),
        ],
      ),
    );
  }
}
