import 'package:floating_bottom_navigation_bar/floating_bottom_navigation_bar.dart';
import 'package:flutter/material.dart';

class Floating<PERSON>ottom<PERSON>av extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const FloatingBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingNavbar(
      onTap: onTap,
      currentIndex: currentIndex,
      items: [
        FloatingNavbarItem(
          icon: Icons.home, //title: 'Home'
        ),
        FloatingNavbarItem(
          icon: Icons.explore, // title: 'Explore'
        ),
        FloatingNavbarItem(
          icon: Icons.chat_bubble_outline, //title: 'Chats'
        ),
        FloatingNavbarItem(
          icon: Icons.settings, // title: 'Settings'
        ),
      ],
    );
  }
}
