import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

import 'theme_event.dart';
import 'theme_state.dart';

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  static const String _prefKey = 'theme_mode';

  // Simple theme instances for state management
  static final ThemeData _lightTheme = ThemeData.light();
  static final ThemeData _darkTheme = ThemeData.dark();

  ThemeBloc() : super(ThemeState(AppThemeMode.system, _lightTheme)) {
    on<LoadThemeEvent>(_onLoadTheme);
    on<SetThemeModeEvent>(_onSetThemeMode);

    add(LoadThemeEvent()); // Load saved theme when Bloc starts
  }

  Future<void> _onLoadTheme(
    LoadThemeEvent event,
    Emitter<ThemeState> emit,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final index = prefs.getInt(_prefKey) ?? 0;
    final mode = AppThemeMode.values[index];
    emit(ThemeState(mode, _getThemeFromMode(mode)));
  }

  Future<void> _onSetThemeMode(
    SetThemeModeEvent event,
    Emitter<ThemeState> emit,
  ) async {
    // Emit immediately for instant UI response
    emit(ThemeState(event.mode, _getThemeFromMode(event.mode)));

    // Save to preferences asynchronously without blocking UI
    _saveThemePreference(event.mode);
  }

  void _saveThemePreference(AppThemeMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_prefKey, mode.index);
    } catch (e) {
      // Handle error silently to avoid disrupting theme change
      // Log error silently - theme change should still work
    }
  }

  ThemeData _getThemeFromMode(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.system:
        return WidgetsBinding.instance.window.platformBrightness ==
                Brightness.dark
            ? _darkTheme
            : _lightTheme;
      case AppThemeMode.light:
        return _lightTheme;
      case AppThemeMode.dark:
        return _darkTheme;
    }
  }
}
