import 'package:business_app/const/assets.dart';
import 'package:business_app/ui/customer_profiles/components/pages/shoes/widgets/shoe_filter_bar.dart';
import 'package:business_app/ui/customer_profiles/components/pages/shoes/widgets/shoe_grid_view.dart';
import 'package:business_app/ui/customer_profiles/components/pages/shoes/models/shoe_model.dart';
import 'package:flutter/material.dart';

class ShoesPage extends StatefulWidget {
  const ShoesPage({Key? key}) : super(key: key);

  @override
  State<ShoesPage> createState() => _ShoesPageState();
}

class _ShoesPageState extends State<ShoesPage> {
  String selectedCategory = 'All';
  String selectedBrand = 'All';
  String sortBy = 'Popular';

  // Mock data for shoes - in real app this would come from backend
  final List<ShoeModel> allShoes = [
    ShoeModel(
      id: '1',
      name: 'Pure Decent',
      brand: 'Pure',
      category: 'Men',
      price: 250000,
      originalPrice: 300000,
      rating: 5.0,
      reviewCount: 124,
      colors: ['Black', 'Brown', 'White', 'Navy', 'Gray'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      images: [Assets.product1],
      description: 'Premium leather shoes with exceptional comfort and style.',
      isOnSale: true,
      discount: 17,
    ),
    ShoeModel(
      id: '2',
      name: 'Dr. Martens Classic',
      brand: 'Dr. Martens',
      category: 'Men',
      price: 130500,
      originalPrice: 150000,
      rating: 4.8,
      reviewCount: 89,
      colors: [
        'Black',
        'Cherry Red',
        'Brown',
        'White',
        'Yellow',
        'Green',
        'Blue',
        'Purple',
      ],
      sizes: ['6', '7', '8', '9', '10', '11', '12', '13'],
      images: [Assets.product2],
      description:
          'Iconic boots with air-cushioned sole and durable construction.',
      isOnSale: true,
      discount: 13,
    ),
    ShoeModel(
      id: '3',
      name: 'Sneakers Sport',
      brand: 'SportMax',
      category: 'Unisex',
      price: 50000,
      originalPrice: 50000,
      rating: 4.2,
      reviewCount: 67,
      colors: ['White', 'Black', 'Red', 'Blue'],
      sizes: ['6', '7', '8', '9', '10', '11'],
      images: [Assets.product3],
      description: 'Comfortable athletic sneakers perfect for daily wear.',
      isOnSale: false,
      discount: 0,
    ),
    ShoeModel(
      id: '4',
      name: 'Elegant Heels',
      brand: 'Elegance',
      category: 'Women',
      price: 180000,
      originalPrice: 220000,
      rating: 4.6,
      reviewCount: 156,
      colors: ['Black', 'Red', 'Nude', 'Silver', 'Gold', 'Navy'],
      sizes: ['5', '6', '7', '8', '9', '10'],
      images: [Assets.product1],
      description:
          'Sophisticated heels for special occasions and professional wear.',
      isOnSale: true,
      discount: 18,
    ),
    ShoeModel(
      id: '5',
      name: 'Casual Flats',
      brand: 'ComfortWalk',
      category: 'Women',
      price: 75000,
      originalPrice: 75000,
      rating: 4.4,
      reviewCount: 203,
      colors: ['Black', 'Brown', 'Tan', 'White', 'Pink', 'Blue'],
      sizes: ['5', '6', '7', '8', '9', '10', '11'],
      images: [Assets.product2],
      description: 'Comfortable flat shoes perfect for everyday wear.',
      isOnSale: false,
      discount: 0,
    ),
    ShoeModel(
      id: '6',
      name: 'Running Shoes',
      brand: 'RunFast',
      category: 'Unisex',
      price: 120000,
      originalPrice: 140000,
      rating: 4.7,
      reviewCount: 98,
      colors: ['Black', 'White', 'Blue', 'Red', 'Green'],
      sizes: ['6', '7', '8', '9', '10', '11', '12'],
      images: [Assets.product3],
      description: 'High-performance running shoes with advanced cushioning.',
      isOnSale: true,
      discount: 14,
    ),
  ];

  List<ShoeModel> get filteredShoes {
    List<ShoeModel> filtered = allShoes;

    // Filter by category
    if (selectedCategory != 'All') {
      filtered =
          filtered.where((shoe) => shoe.category == selectedCategory).toList();
    }

    // Filter by brand
    if (selectedBrand != 'All') {
      filtered = filtered.where((shoe) => shoe.brand == selectedBrand).toList();
    }

    // Sort
    switch (sortBy) {
      case 'Price: Low to High':
        filtered.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'Price: High to Low':
        filtered.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'Rating':
        filtered.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'Newest':
        // For demo purposes, reverse the list
        filtered = filtered.reversed.toList();
        break;
      default: // Popular
        filtered.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'All Shoes',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        centerTitle: true,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors:
                  isDark
                      ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
                      : [const Color(0xFF667eea), const Color(0xFF764ba2)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {
              // TODO: Implement search functionality
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () {
              _showFilterBottomSheet(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Bar
          ShoeFilterBar(
            selectedCategory: selectedCategory,
            selectedBrand: selectedBrand,
            sortBy: sortBy,
            onCategoryChanged: (category) {
              setState(() {
                selectedCategory = category;
              });
            },
            onBrandChanged: (brand) {
              setState(() {
                selectedBrand = brand;
              });
            },
            onSortChanged: (sort) {
              setState(() {
                sortBy = sort;
              });
            },
          ),

          // Results count
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Row(
              children: [
                Text(
                  '${filteredShoes.length} shoes found',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Shoes Grid
          Expanded(
            child: ShoeGridView(
              shoes: filteredShoes,
              onShoeSelected: (shoe) {
                // TODO: Navigate to shoe details page
                _showShoeDetails(context, shoe);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Filter & Sort',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                // Add more filter options here
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Apply Filters'),
                ),
              ],
            ),
          ),
    );
  }

  void _showShoeDetails(BuildContext context, ShoeModel shoe) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(shoe.name),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Brand: ${shoe.brand}'),
                Text('Price: MWK ${shoe.price}'),
                Text('Rating: ${shoe.rating}/5.0'),
                Text('Colors: ${shoe.colors.length}'),
                const SizedBox(height: 10),
                Text(shoe.description),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Add to cart functionality
                },
                child: const Text('Add to Cart'),
              ),
            ],
          ),
    );
  }
}
