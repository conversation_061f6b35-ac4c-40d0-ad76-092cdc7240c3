# Storage RLS Setup Instructions

## Problem
You're getting a "row-level security policy" error when uploading images because the `product-images` bucket has RLS enabled but no policies are defined.

## Solution
You need to apply the RLS policies to your Supabase database. Here are two ways to do it:

## Option 1: Using Supabase Dashboard (Recommended)

### Step 1: Go to SQL Editor
1. Open your Supabase dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Click **New query**

### Step 2: Run the RLS Policies
Copy and paste the contents of `supabase/migrations/003_storage_rls_policies.sql` into the SQL editor and run it.

**Or copy this SQL directly:**

```sql
-- Enable RLS on storage.objects table (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to upload product images to their own folder
CREATE POLICY "Users can upload product images to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'product-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to product images (for viewing products)
CREATE POLICY "Public can view product images" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'product-images');

-- Policy: Allow users to manage their own product images
CREATE POLICY "Users can manage own product images" 
ON storage.objects 
FOR ALL 
TO authenticated 
USING (
  bucket_id = 'product-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
```

### Step 3: Verify the Policies
Run this query to check if the policies were created:

```sql
SELECT * FROM pg_policies WHERE tablename = 'objects' AND policyname LIKE '%product%';
```

## Option 2: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd g:\business_app

# Apply the migration
supabase db push
```

## What Was Fixed

### 1. **RLS Policies Added**
- ✅ Users can upload images to their own folder (`userId/filename.jpg`)
- ✅ Public can view product images (needed for displaying products)
- ✅ Users can manage (update/delete) their own images

### 2. **File Path Structure Fixed**
The `StorageService` now creates files with the correct folder structure:
- **Before**: `product_userId_timestamp.jpg` (flat structure)
- **After**: `userId/product_timestamp.jpg` (folder structure)

This matches what the RLS policies expect.

## Testing

After applying the policies, try uploading a product image again. The error should be resolved.

### Debug Commands

If you still have issues, run these in the SQL Editor:

```sql
-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'objects';

-- Check all storage policies
SELECT * FROM pg_policies WHERE tablename = 'objects';

-- Check your buckets
SELECT * FROM storage.buckets;

-- Check recent uploads (should show your user folder structure)
SELECT name, bucket_id, created_at 
FROM storage.objects 
WHERE bucket_id = 'product-images' 
ORDER BY created_at DESC 
LIMIT 10;
```

## Expected File Structure

After the fix, your uploaded images will be stored like this:

```
product-images/
├── user-id-1/
│   ├── product_1234567890.jpg
│   ├── product_1234567891.png
│   └── product_1234567892.webp
└── user-id-2/
    └── product_1234567893.jpg
```

This ensures:
- ✅ Each user can only access their own images
- ✅ Public can view all product images (for the marketplace)
- ✅ Proper security isolation between users
- ✅ Clean organization of files

## Next Steps

1. **Apply the RLS policies** using Option 1 or 2 above
2. **Test image upload** in your app
3. **Verify images display** correctly in the products page

The image upload functionality should now work perfectly! 🎉
