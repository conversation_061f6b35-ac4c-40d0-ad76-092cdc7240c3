import 'package:flutter/material.dart';

/// Configuration class for skeleton animations and styling
/// Provides Spotify-style colors and animation settings
class SkeletonConfig {
  // Spotify-inspired colors for light theme
  static const Color lightBaseColor = Color(0xFFE5E5E5);
  static const Color lightHighlightColor = Color(0xFFF5F5F5);
  static const Color lightShimmerColor = Color(0xFFFFFFFF);
  
  // Spotify-inspired colors for dark theme
  static const Color darkBaseColor = Color(0xFF2A2A2A);
  static const Color darkHighlightColor = Color(0xFF3E3E3E);
  static const Color darkShimmerColor = Color(0xFF4A4A4A);
  
  // Animation settings
  static const Duration animationDuration = Duration(milliseconds: 1500);
  static const Duration fadeInDuration = Duration(milliseconds: 300);
  static const Duration staggerDelay = Duration(milliseconds: 100);
  
  // Border radius settings
  static const double defaultBorderRadius = 8.0;
  static const double cardBorderRadius = 12.0;
  static const double buttonBorderRadius = 20.0;
  static const double avatarBorderRadius = 50.0;
  
  /// Get base color based on theme brightness
  static Color getBaseColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? darkBaseColor : lightBaseColor;
  }
  
  /// Get highlight color based on theme brightness
  static Color getHighlightColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? darkHighlightColor : lightHighlightColor;
  }
  
  /// Get shimmer color based on theme brightness
  static Color getShimmerColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? darkShimmerColor : lightShimmerColor;
  }
  
  /// Get colors for shimmer effect
  static List<Color> getShimmerColors(BuildContext context) {
    return [
      getBaseColor(context),
      getHighlightColor(context),
      getShimmerColor(context),
      getHighlightColor(context),
      getBaseColor(context),
    ];
  }
  
  /// Get gradient stops for shimmer effect
  static List<double> getShimmerStops() {
    return [0.0, 0.35, 0.5, 0.65, 1.0];
  }
}
