import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';
import 'package:logger/logger.dart';

class SignupBasicInfoPage extends StatefulWidget {
  const SignupBasicInfoPage({super.key});

  @override
  State<SignupBasicInfoPage> createState() => _SignupBasicInfoPageState();
}

class _SignupBasicInfoPageState extends State<SignupBasicInfoPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _contactController = TextEditingController();

  bool _useEmail = true;
  DateTime? _selectedDate;
  String? _nameError;
  String? _contactError;
  String? _dateError;
  String? _fullPhoneNumber; // Store the full formatted phone number

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  @override
  void initState() {
    super.initState();
    // Test phone validation with sample numbers
    _testPhoneValidation();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _contactController.dispose();
    super.dispose();
  }

  void _testPhoneValidation() {
    final testNumbers = [
      '+265 123 456 789',
      '+265123456789',
      '****** 567 8901',
      '+44 1234 567890',
      '+234 ************',
    ];

    _logger.d('🧪 Testing phone validation:');
    for (final number in testNumbers) {
      final isValid = _isValidPhoneNumber(number);
      _logger.d('📱 $number -> ${isValid ? "✅ Valid" : "❌ Invalid"}');
    }
  }

  void _selectDate() async {
    final now = DateTime.now();
    final eighteenYearsAgo = DateTime(now.year - 18, now.month, now.day);
    final hundredYearsAgo = DateTime(now.year - 100, now.month, now.day);

    final date = await showDatePicker(
      context: context,
      initialDate: eighteenYearsAgo,
      firstDate: hundredYearsAgo,
      lastDate: now,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: const Color(0xFF1DA1F2)),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
        _dateError = null;
      });
    }
  }

  void _onNext() {
    final name = _nameController.text.trim();
    final contact = _contactController.text.trim();

    // Validate name
    if (name.isEmpty) {
      setState(() {
        _nameError = 'Name is required';
      });
      return;
    }

    if (name.length < 2) {
      setState(() {
        _nameError = 'Name must be at least 2 characters';
      });
      return;
    }

    // Validate contact
    if (contact.isEmpty) {
      setState(() {
        _contactError =
            _useEmail ? 'Email is required' : 'Phone number is required';
      });
      return;
    }

    if (_useEmail) {
      if (!RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      ).hasMatch(contact)) {
        setState(() {
          _contactError = 'Please enter a valid email address';
        });
        return;
      }
    } else {
      // Enhanced phone validation for international numbers
      final phoneToValidate = _fullPhoneNumber ?? contact;
      _logger.d('🔍 Validating phone number: "$phoneToValidate"');
      if (!_isValidPhoneNumber(phoneToValidate)) {
        setState(() {
          _contactError = _getPhoneValidationError(phoneToValidate);
        });
        return;
      }
      _logger.d('✅ Phone validation passed for: "$phoneToValidate"');
    }

    // Validate date
    if (_selectedDate == null) {
      setState(() {
        _dateError = 'Date of birth is required';
      });
      return;
    }

    final age = DateTime.now().year - _selectedDate!.year;
    if (age < 13) {
      setState(() {
        _dateError = 'You must be at least 13 years old';
      });
      return;
    }

    // Submit basic info
    final contactToSubmit =
        _useEmail ? contact : _formatPhoneNumber(_fullPhoneNumber ?? contact);
    context.read<AuthBloc>().add(
      SignupBasicInfoSubmitted(
        name: name,
        emailOrPhone: contactToSubmit,
        dateOfBirth: _selectedDate!,
        isEmail: _useEmail,
      ),
    );
  }

  bool _isValidPhoneNumber(String phoneNumber) {
    // Handle empty or null input
    if (phoneNumber.isEmpty) return false;

    try {
      // Use phone_numbers_parser for professional validation
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      // Check if the phone number is valid
      final isValid = parsedNumber.isValid();

      // For signup, we accept both mobile and fixed line numbers
      return isValid;
    } catch (e) {
      // If parsing fails, the number is invalid
      return false;
    }
  }

  String _formatPhoneNumber(String phoneNumber) {
    try {
      // Parse and format the phone number in international format
      final parsedNumber = PhoneNumber.parse(phoneNumber);
      return parsedNumber.international;
    } catch (e) {
      // If parsing fails, return the original number
      return phoneNumber;
    }
  }

  String _getPhoneValidationError(String phoneNumber) {
    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      if (!parsedNumber.isValid()) {
        // Try to provide more specific error messages
        if (phoneNumber.length < 8) {
          return 'Phone number is too short';
        } else if (phoneNumber.length > 15) {
          return 'Phone number is too long';
        } else {
          return 'Please enter a valid phone number (e.g., +265 123 456 789)';
        }
      }

      return 'Invalid phone number format';
    } catch (e) {
      // Handle parsing errors
      if (!phoneNumber.startsWith('+')) {
        return 'Phone number must include country code (e.g., +265)';
      }
      return 'Please enter a valid phone number with country code';
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              duration: const Duration(milliseconds: 4000),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),

                        // Header with progress
                        Column(
                          children: [
                            const AuthProgressIndicator(
                              currentStep: 1,
                              totalSteps: 5,
                            ),
                            const SizedBox(height: 20),
                            const AuthSignupHeader(step: 'Step 1 of 5'),
                          ],
                        ),

                        const SizedBox(height: 40),

                        // Name field
                        AuthTextField(
                          controller: _nameController,
                          label: 'Name',
                          hintText: 'Enter your full name',
                          errorText: _nameError,
                          onChanged: (value) {
                            if (_nameError != null) {
                              setState(() {
                                _nameError = null;
                              });
                            }
                          },
                        ),

                        const SizedBox(height: 24),

                        // Contact type toggle
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _useEmail = true;
                                      _contactController.clear();
                                      _contactError = null;
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          _useEmail
                                              ? const Color(0xFF1DA1F2)
                                              : Colors.transparent,
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(4),
                                        bottomLeft: Radius.circular(4),
                                      ),
                                    ),
                                    child: Text(
                                      'Email',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color:
                                            _useEmail
                                                ? Colors.white
                                                : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _useEmail = false;
                                      _contactController.clear();
                                      _contactError = null;
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          !_useEmail
                                              ? const Color(0xFF1DA1F2)
                                              : Colors.transparent,
                                      borderRadius: const BorderRadius.only(
                                        topRight: Radius.circular(4),
                                        bottomRight: Radius.circular(4),
                                      ),
                                    ),
                                    child: Text(
                                      'Phone',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color:
                                            !_useEmail
                                                ? Colors.white
                                                : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Contact field
                        _useEmail
                            ? AuthEmailField(
                              controller: _contactController,
                              hintText: 'Enter your email',
                              errorText: _contactError,
                              onChanged: (value) {
                                if (_contactError != null) {
                                  setState(() {
                                    _contactError = null;
                                  });
                                }
                              },
                            )
                            : AuthPhoneField(
                              controller: _contactController,
                              hintText: 'Enter your phone number',
                              errorText: _contactError,
                              onChanged: (value) {
                                // Store the full formatted phone number
                                _fullPhoneNumber = value;
                                if (_contactError != null) {
                                  setState(() {
                                    _contactError = null;
                                  });
                                }
                              },
                            ),

                        const SizedBox(height: 24),

                        // Date of birth field
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Date of birth',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            GestureDetector(
                              onTap: _selectDate,
                              child: Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 16,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color:
                                        _dateError != null
                                            ? Colors.red
                                            : Colors.grey[300]!,
                                  ),
                                  borderRadius: BorderRadius.circular(4),
                                  color:
                                      theme.brightness == Brightness.dark
                                          ? Colors.grey[900]
                                          : Colors.grey[50],
                                ),
                                child: Text(
                                  _selectedDate != null
                                      ? _formatDate(_selectedDate!)
                                      : 'Select your date of birth',
                                  style: TextStyle(
                                    fontSize: 17,
                                    color:
                                        _selectedDate != null
                                            ? theme.textTheme.bodyLarge?.color
                                            : Colors.grey[500],
                                  ),
                                ),
                              ),
                            ),
                            if (_dateError != null) ...[
                              const SizedBox(height: 8),
                              Text(
                                _dateError!,
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ],
                        ),

                        const Spacer(),

                        // Next button
                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            return AuthPrimaryButton(
                              text: 'Next',
                              isLoading: state.isLoading,
                              onPressed: _onNext,
                            );
                          },
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
