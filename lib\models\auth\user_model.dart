import 'package:equatable/equatable.dart';
import 'package:business_app/models/auth/user_role.dart';

class UserModel extends Equatable {
  final String id;
  final String username;
  final String name;
  final String? email;
  final String? phone;
  final String? profileImageUrl;
  final String? backgroundImageUrl;
  final String? bio;
  final DateTime? dateOfBirth;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified;
  final bool isPrivate;
  final int followersCount;
  final int followingCount;
  final int postsCount;
  final UserRole role;

  const UserModel({
    required this.id,
    required this.username,
    required this.name,
    this.email,
    this.phone,
    this.profileImageUrl,
    this.backgroundImageUrl,
    this.bio,
    this.dateOfBirth,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.isPrivate = false,
    this.followersCount = 0,
    this.followingCount = 0,
    this.postsCount = 0,
    this.role = UserRole.user,
  });

  // Factory constructor for creating UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      username: json['username'] as String,
      name: json['full_name'] as String, // Database uses 'full_name'
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      profileImageUrl:
          json['avatar_url'] as String?, // Database uses 'avatar_url'
      backgroundImageUrl:
          json['background_image_url']
              as String?, // Database uses 'background_image_url'
      bio: json['bio'] as String?,
      dateOfBirth:
          json['date_of_birth'] != null
              ? DateTime.parse(json['date_of_birth'] as String)
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isVerified: json['is_verified'] as bool? ?? false,
      isPrivate: json['is_private'] as bool? ?? false,
      followersCount: json['followers_count'] as int? ?? 0,
      followingCount: json['following_count'] as int? ?? 0,
      postsCount: json['posts_count'] as int? ?? 0,
      role: UserRole.fromString(json['role'] as String? ?? 'user'),
    );
  }

  // Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'full_name': name, // Database uses 'full_name'
      'email': email,
      'phone': phone,
      'avatar_url': profileImageUrl, // Database uses 'avatar_url'
      'background_image_url':
          backgroundImageUrl, // Database uses 'background_image_url'
      'bio': bio,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_verified': isVerified,
      'is_private': isPrivate,
      'followers_count': followersCount,
      'following_count': followingCount,
      'posts_count': postsCount,
      'role': role.value,
    };
  }

  // Create a copy with updated fields
  UserModel copyWith({
    String? id,
    String? username,
    String? name,
    String? email,
    String? phone,
    String? profileImageUrl,
    String? backgroundImageUrl,
    String? bio,
    DateTime? dateOfBirth,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isPrivate,
    int? followersCount,
    int? followingCount,
    int? postsCount,
    UserRole? role,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      backgroundImageUrl: backgroundImageUrl ?? this.backgroundImageUrl,
      bio: bio ?? this.bio,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      isPrivate: isPrivate ?? this.isPrivate,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      postsCount: postsCount ?? this.postsCount,
      role: role ?? this.role,
    );
  }

  // Get display name (name or username)
  String get displayName => name.isNotEmpty ? name : username;

  // Get contact info (email or phone)
  String? get contactInfo => email ?? phone;

  // Check if user has profile image
  bool get hasProfileImage =>
      profileImageUrl != null && profileImageUrl!.isNotEmpty;

  // Get formatted followers count
  String get formattedFollowersCount {
    if (followersCount >= 1000000) {
      return '${(followersCount / 1000000).toStringAsFixed(1)}M';
    } else if (followersCount >= 1000) {
      return '${(followersCount / 1000).toStringAsFixed(1)}K';
    }
    return followersCount.toString();
  }

  // Get formatted following count
  String get formattedFollowingCount {
    if (followingCount >= 1000000) {
      return '${(followingCount / 1000000).toStringAsFixed(1)}M';
    } else if (followingCount >= 1000) {
      return '${(followingCount / 1000).toStringAsFixed(1)}K';
    }
    return followingCount.toString();
  }

  // Get formatted posts count
  String get formattedPostsCount {
    if (postsCount >= 1000000) {
      return '${(postsCount / 1000000).toStringAsFixed(1)}M';
    } else if (postsCount >= 1000) {
      return '${(postsCount / 1000).toStringAsFixed(1)}K';
    }
    return postsCount.toString();
  }

  // Get user permissions based on role
  UserPermissions get permissions => role.permissions;

  // Check if user has admin privileges
  bool get isAdmin => role.isAdmin;

  // Check if user has moderator privileges
  bool get isModerator => role.isModerator;

  // Check if user has super admin privileges
  bool get isSuperAdmin => role.isSuperAdmin;

  @override
  List<Object?> get props => [
    id,
    username,
    name,
    email,
    phone,
    profileImageUrl,
    backgroundImageUrl,
    bio,
    dateOfBirth,
    createdAt,
    updatedAt,
    isVerified,
    isPrivate,
    followersCount,
    followingCount,
    postsCount,
    role,
  ];

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, name: $name, email: $email)';
  }
}
