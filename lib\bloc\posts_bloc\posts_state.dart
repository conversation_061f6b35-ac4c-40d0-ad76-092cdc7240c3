import 'package:equatable/equatable.dart';

enum PostsStatus { initial, loading, loaded, error, refreshing, loadingMore }

class Post extends Equatable {
  final String id;
  final String userId;
  final String? productId;
  final String authorName;
  final String authorUsername;
  final String? authorProfileImage;
  final String? postContent;
  final List<String> mediaUrls;
  final String postType;
  final bool isPublic;
  final String? location;
  final List<String> tags;
  final List<String> mentions;
  final List<String> hashtags;
  final DateTime createdAt;
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final int viewsCount;
  final bool isLiked;
  final bool isBookmarked;

  // Compatibility getters for existing code
  String get content => postContent ?? '';
  List<String> get images => mediaUrls;

  const Post({
    required this.id,
    required this.userId,
    this.productId,
    required this.authorName,
    required this.authorUsername,
    this.authorProfileImage,
    this.postContent,
    this.mediaUrls = const [],
    this.postType = 'text',
    this.isPublic = true,
    this.location,
    this.tags = const [],
    this.mentions = const [],
    this.hashtags = const [],
    required this.createdAt,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.sharesCount = 0,
    this.viewsCount = 0,
    this.isLiked = false,
    this.isBookmarked = false,
  });

  // Factory constructor for creating Post from JSON
  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      productId: json['product_id'] as String?,
      authorName: json['full_name'] as String? ?? json['username'] as String,
      authorUsername: json['username'] as String,
      authorProfileImage: json['avatar_url'] as String?,
      postContent: json['content'] as String?,
      mediaUrls: List<String>.from(json['media_urls'] ?? []),
      postType: json['post_type'] as String? ?? 'text',
      isPublic: json['is_public'] as bool? ?? true,
      location: json['location'] as String?,
      tags: List<String>.from(json['tags'] ?? []),
      mentions: List<String>.from(json['mentions'] ?? []),
      hashtags: List<String>.from(json['hashtags'] ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      likesCount: json['likes_count'] as int? ?? 0,
      commentsCount: json['comments_count'] as int? ?? 0,
      sharesCount: json['shares_count'] as int? ?? 0,
      viewsCount: json['views_count'] as int? ?? 0,
      isLiked: json['is_liked'] as bool? ?? false,
      isBookmarked: false, // This would come from a separate bookmarks table
    );
  }

  Post copyWith({
    String? id,
    String? userId,
    String? productId,
    String? authorName,
    String? authorUsername,
    String? authorProfileImage,
    String? postContent,
    List<String>? mediaUrls,
    String? postType,
    bool? isPublic,
    String? location,
    List<String>? tags,
    List<String>? mentions,
    List<String>? hashtags,
    DateTime? createdAt,
    int? likesCount,
    int? commentsCount,
    int? sharesCount,
    int? viewsCount,
    bool? isLiked,
    bool? isBookmarked,
  }) {
    return Post(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      authorName: authorName ?? this.authorName,
      authorUsername: authorUsername ?? this.authorUsername,
      authorProfileImage: authorProfileImage ?? this.authorProfileImage,
      postContent: postContent ?? this.postContent,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      postType: postType ?? this.postType,
      isPublic: isPublic ?? this.isPublic,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      mentions: mentions ?? this.mentions,
      hashtags: hashtags ?? this.hashtags,
      createdAt: createdAt ?? this.createdAt,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      viewsCount: viewsCount ?? this.viewsCount,
      isLiked: isLiked ?? this.isLiked,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    productId,
    authorName,
    authorUsername,
    authorProfileImage,
    postContent,
    mediaUrls,
    postType,
    isPublic,
    location,
    tags,
    mentions,
    hashtags,
    createdAt,
    likesCount,
    commentsCount,
    sharesCount,
    viewsCount,
    isLiked,
    isBookmarked,
  ];
}

class PostsState extends Equatable {
  final PostsStatus status;
  final List<Post> posts;
  final int selectedTabIndex;
  final bool hasReachedMax;
  final String? errorMessage;

  const PostsState({
    this.status = PostsStatus.initial,
    this.posts = const [],
    this.selectedTabIndex = 0,
    this.hasReachedMax = false,
    this.errorMessage,
  });

  PostsState copyWith({
    PostsStatus? status,
    List<Post>? posts,
    int? selectedTabIndex,
    bool? hasReachedMax,
    String? errorMessage,
  }) {
    return PostsState(
      status: status ?? this.status,
      posts: posts ?? this.posts,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    status,
    posts,
    selectedTabIndex,
    hasReachedMax,
    errorMessage,
  ];
}
