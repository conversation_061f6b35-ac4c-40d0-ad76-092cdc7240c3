import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:google_fonts/google_fonts.dart';

import 'models/business_metrics.dart';
import 'widgets/metric_card.dart';
import 'widgets/charts/revenue_chart.dart';
import 'widgets/charts/sales_chart.dart';
import 'widgets/charts/engagement_chart.dart';
import 'widgets/insights_section.dart';
import 'widgets/performance_overview.dart';
import 'widgets/profit_loss_analysis.dart';

class BusinessStats extends StatefulWidget {
  const BusinessStats({super.key});

  @override
  State<BusinessStats> createState() => _BusinessStatsState();
}

class _BusinessStatsState extends State<BusinessStats>
    with TickerProviderStateMixin {
  late TabController _tabController;
  TimeRange _selectedRange = TimeRange.ranges[1]; // Default to 30 days
  final BusinessMetrics _metrics = BusinessMetrics.mockData;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: isDark ? Colors.grey[850] : Colors.white,
              foregroundColor: isDark ? Colors.white : Colors.black87,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'Business Analytics',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                  ),
                ),
                centerTitle: false,
                titlePadding: const EdgeInsets.only(left: 16, bottom: 56),
              ),
              actions: [
                Container(
                  margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<TimeRange>(
                      value: _selectedRange,
                      icon: const Icon(
                        LucideIcons.chevronDown,
                        color: Colors.blue,
                        size: 16,
                      ),
                      style: GoogleFonts.inter(
                        color: Colors.blue,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      onChanged: (TimeRange? newValue) {
                        if (newValue != null) {
                          setState(() {
                            _selectedRange = newValue;
                          });
                        }
                      },
                      items:
                          TimeRange.ranges.map((TimeRange range) {
                            return DropdownMenuItem<TimeRange>(
                              value: range,
                              child: Text(range.label),
                            );
                          }).toList(),
                    ),
                  ),
                ),
              ],
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: Colors.blue,
                indicatorWeight: 3,
                labelColor: Colors.blue,
                unselectedLabelColor:
                    isDark ? Colors.grey[400] : Colors.grey[600],
                labelStyle: GoogleFonts.inter(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                unselectedLabelStyle: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
                tabs: const [
                  Tab(text: 'Overview'),
                  Tab(text: 'Revenue'),
                  Tab(text: 'Sales'),
                  Tab(text: 'Insights'),
                ],
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _OverviewTab(metrics: _metrics, timeRange: _selectedRange),
            _RevenueTab(metrics: _metrics, timeRange: _selectedRange),
            _SalesTab(metrics: _metrics, timeRange: _selectedRange),
            _InsightsTab(metrics: _metrics, timeRange: _selectedRange),
          ],
        ),
      ),
    );
  }
}

// Overview Tab
class _OverviewTab extends StatelessWidget {
  final BusinessMetrics metrics;
  final TimeRange timeRange;

  const _OverviewTab({required this.metrics, required this.timeRange});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Key Metrics Grid
          _buildMetricsGrid(context),
          const SizedBox(height: 24),

          // Performance Overview
          _buildPerformanceOverview(context),
          const SizedBox(height: 24),

          // Quick Insights
          _buildQuickInsights(context),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = constraints.maxWidth > 600 ? 4 : 2;
        final childAspectRatio = constraints.maxWidth > 600 ? 1.2 : 1.1;

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: childAspectRatio,
          children: [
            MetricCard(
              title: 'Total Revenue',
              value: '\$${(metrics.totalRevenue / 1000).toStringAsFixed(1)}K',
              subtitle: 'This ${timeRange.label.toLowerCase()}',
              icon: LucideIcons.dollarSign,
              color: Colors.green,
              changePercentage: 12.5,
              isPositiveChange: true,
            ),
            MetricCard(
              title: 'Net Profit',
              value: '\$${(metrics.netProfit / 1000).toStringAsFixed(1)}K',
              subtitle:
                  'Profit margin ${metrics.profitMargin.toStringAsFixed(1)}%',
              icon: LucideIcons.trendingUp,
              color: Colors.blue,
              changePercentage: 8.3,
              isPositiveChange: true,
            ),
            MetricCard(
              title: 'Total Orders',
              value: '${metrics.totalOrders}',
              subtitle: 'Orders completed',
              icon: LucideIcons.shoppingCart,
              color: Colors.purple,
              changePercentage: 15.2,
              isPositiveChange: true,
            ),
            MetricCard(
              title: 'Conversion Rate',
              value: '${metrics.conversionRate.toStringAsFixed(1)}%',
              subtitle: 'Views to orders',
              icon: LucideIcons.target,
              color: Colors.orange,
              changePercentage: 2.1,
              isPositiveChange: true,
            ),
          ],
        );
      },
    );
  }

  Widget _buildPerformanceOverview(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance Overview',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildPerformanceMetric(
                  'Customer Satisfaction',
                  '${metrics.customerSatisfaction}/5.0',
                  Colors.green,
                  LucideIcons.star,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildPerformanceMetric(
                  'Growth Rate',
                  '${metrics.growthRate.toStringAsFixed(1)}%',
                  Colors.blue,
                  LucideIcons.trendingUp,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildPerformanceMetric(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickInsights(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Insights',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInsightItem(
            'Revenue is up 12.5% compared to last period',
            Colors.green,
            LucideIcons.trendingUp,
          ),
          const SizedBox(height: 12),
          _buildInsightItem(
            'Customer satisfaction remains high at 4.6/5.0',
            Colors.blue,
            LucideIcons.star,
          ),
          const SizedBox(height: 12),
          _buildInsightItem(
            'Conversion rate improved by 2.1%',
            Colors.purple,
            LucideIcons.target,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildInsightItem(String text, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }
}

// Revenue Tab
class _RevenueTab extends StatelessWidget {
  final BusinessMetrics metrics;
  final TimeRange timeRange;

  const _RevenueTab({required this.metrics, required this.timeRange});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Revenue Overview Cards
          LayoutBuilder(
            builder: (context, constraints) {
              final crossAxisCount = constraints.maxWidth > 600 ? 3 : 1;
              final childAspectRatio = constraints.maxWidth > 600 ? 1.5 : 2.5;

              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: childAspectRatio,
                children: [
                  LargeMetricCard(
                    title: 'Total Revenue',
                    value:
                        '\$${(metrics.totalRevenue / 1000).toStringAsFixed(1)}K',
                    subtitle: 'This ${timeRange.label.toLowerCase()}',
                    icon: LucideIcons.dollarSign,
                    color: Colors.green,
                  ),
                  LargeMetricCard(
                    title: 'Average Revenue',
                    value:
                        '\$${(metrics.totalRevenue / timeRange.days).toStringAsFixed(0)}',
                    subtitle: 'Per day',
                    icon: LucideIcons.calendar,
                    color: Colors.blue,
                  ),
                  LargeMetricCard(
                    title: 'Revenue Growth',
                    value: '+12.5%',
                    subtitle: 'vs last period',
                    icon: LucideIcons.trendingUp,
                    color: Colors.purple,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 24),

          // Revenue Chart
          RevenueChart(
            data: RevenueChartData.monthlyRevenue,
            title: 'Revenue Trend',
            primaryColor: Colors.green,
            height: 320,
          ),
          const SizedBox(height: 24),

          // Profit/Loss Analysis
          ProfitLossAnalysis(metrics: metrics, timeRange: timeRange),
        ],
      ),
    );
  }
}

// Sales Tab
class _SalesTab extends StatelessWidget {
  final BusinessMetrics metrics;
  final TimeRange timeRange;

  const _SalesTab({required this.metrics, required this.timeRange});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Sales Overview Cards
          LayoutBuilder(
            builder: (context, constraints) {
              final crossAxisCount = constraints.maxWidth > 600 ? 4 : 2;
              final childAspectRatio = constraints.maxWidth > 600 ? 1.2 : 1.1;

              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: childAspectRatio,
                children: [
                  MetricCard(
                    title: 'Total Sales',
                    value: '${metrics.totalOrders}',
                    subtitle: 'Orders completed',
                    icon: LucideIcons.shoppingCart,
                    color: Colors.blue,
                    changePercentage: 15.2,
                    isPositiveChange: true,
                  ),
                  MetricCard(
                    title: 'Avg Order Value',
                    value: '\$${metrics.averageOrderValue.toStringAsFixed(0)}',
                    subtitle: 'Per transaction',
                    icon: LucideIcons.dollarSign,
                    color: Colors.green,
                    changePercentage: 7.8,
                    isPositiveChange: true,
                  ),
                  MetricCard(
                    title: 'Conversion Rate',
                    value: '${metrics.conversionRate.toStringAsFixed(1)}%',
                    subtitle: 'Views to sales',
                    icon: LucideIcons.target,
                    color: Colors.orange,
                    changePercentage: 3.2,
                    isPositiveChange: true,
                  ),
                  MetricCard(
                    title: 'Sales Growth',
                    value: '+18.5%',
                    subtitle: 'vs last period',
                    icon: LucideIcons.trendingUp,
                    color: Colors.purple,
                    changePercentage: 18.5,
                    isPositiveChange: true,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 24),

          // Sales Charts
          Row(
            children: [
              Expanded(
                child: SalesChart(
                  data: SalesChartData.categorySales,
                  title: 'Sales by Category',
                  primaryColor: Colors.blue,
                  height: 320,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: SalesPieChart(
                  data: SalesChartData.salesDistribution,
                  title: 'Sales Distribution',
                  height: 320,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Engagement Analytics
          EngagementChart(
            data: EngagementChartData.engagementBreakdown,
            title: 'Customer Engagement',
            primaryColor: Colors.orange,
            height: 320,
          ),
          const SizedBox(height: 24),

          // Engagement Trends
          EngagementTrendChart(
            data: EngagementChartData.weeklyEngagement,
            title: 'Weekly Engagement Trends',
            primaryColor: Colors.purple,
            height: 280,
          ),
        ],
      ),
    );
  }
}

// Insights Tab
class _InsightsTab extends StatelessWidget {
  final BusinessMetrics metrics;
  final TimeRange timeRange;

  const _InsightsTab({required this.metrics, required this.timeRange});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI Insights Section
          InsightsSection(metrics: metrics, timeRange: timeRange),
          const SizedBox(height: 24),

          // Performance Overview
          PerformanceOverview(metrics: metrics, timeRange: timeRange),
        ],
      ),
    );
  }
}
