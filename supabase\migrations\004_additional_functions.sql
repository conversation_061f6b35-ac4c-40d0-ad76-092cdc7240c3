-- =====================================================
-- ADDITIONAL FUNCTIONS AND TRIGGERS
-- Advanced business logic and automation
-- =====================================================

-- =====================================================
-- NOTIFICATION FUNCTIONS
-- =====================================================

-- Function to create notifications
CREATE OR REPLACE FUNCTION public.create_notification(
    recipient_id UUID,
    notification_type VARCHAR(50),
    title VARCHAR(200),
    message TEXT,
    data JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        id,
        user_id,
        type,
        title,
        message,
        data,
        created_at
    ) VALUES (
        uuid_generate_v4(),
        recipient_id,
        notification_type,
        title,
        message,
        data,
        NOW()
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle follow notifications
CREATE OR REPLACE FUNCTION public.handle_follow_notification()
RETURNS TRIGGER AS $$
DECLARE
    follower_name TEXT;
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Get follower's name
        SELECT full_name INTO follower_name
        FROM public.profiles
        WHERE id = NEW.follower_id;
        
        -- Create notification for the followed user
        PERFORM public.create_notification(
            NEW.following_id,
            'follow',
            'New Follower',
            follower_name || ' started following you',
            jsonb_build_object('follower_id', NEW.follower_id, 'follower_name', follower_name)
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ANALYTICS FUNCTIONS
-- =====================================================

-- Function to track product views
CREATE OR REPLACE FUNCTION public.track_product_view(product_uuid UUID, viewer_id UUID DEFAULT NULL)
RETURNS VOID AS $$
BEGIN
    -- Update product view count
    UPDATE public.products 
    SET view_count = view_count + 1 
    WHERE id = product_uuid;
    
    -- TODO: Add detailed analytics tracking here
    -- INSERT INTO product_views (product_id, viewer_id, viewed_at) VALUES (product_uuid, viewer_id, NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trending products
CREATE OR REPLACE FUNCTION public.get_trending_products(
    time_period INTERVAL DEFAULT '7 days',
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    name VARCHAR(200),
    price DECIMAL(10,2),
    images TEXT[],
    shop_name VARCHAR(100),
    view_count INTEGER,
    rating DECIMAL(3,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.price,
        p.images,
        s.shop_name,
        p.view_count,
        p.rating
    FROM public.products p
    JOIN public.shops s ON p.shop_id = s.id
    WHERE 
        p.status = 'active' AND
        s.is_active = true AND
        p.created_at >= NOW() - time_period
    ORDER BY p.view_count DESC, p.rating DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VALIDATION FUNCTIONS
-- =====================================================

-- Function to validate product data
CREATE OR REPLACE FUNCTION public.validate_product_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate price
    IF NEW.price <= 0 THEN
        RAISE EXCEPTION 'Product price must be greater than 0';
    END IF;
    
    -- Validate discount price
    IF NEW.discount_price IS NOT NULL AND NEW.discount_price >= NEW.price THEN
        RAISE EXCEPTION 'Discount price must be less than regular price';
    END IF;
    
    -- Validate stock quantity
    IF NEW.stock_quantity < 0 THEN
        RAISE EXCEPTION 'Stock quantity cannot be negative';
    END IF;
    
    -- Validate images array
    IF array_length(NEW.images, 1) > 10 THEN
        RAISE EXCEPTION 'Maximum 10 images allowed per product';
    END IF;
    
    -- Set published_at when status changes to active
    IF TG_OP = 'UPDATE' AND OLD.status != 'active' AND NEW.status = 'active' THEN
        NEW.published_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for product validation
CREATE TRIGGER validate_product_trigger
    BEFORE INSERT OR UPDATE ON public.products
    FOR EACH ROW EXECUTE FUNCTION public.validate_product_data();

-- =====================================================
-- CLEANUP FUNCTIONS
-- =====================================================

-- Function to cleanup old notifications
CREATE OR REPLACE FUNCTION public.cleanup_old_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.notifications 
    WHERE created_at < NOW() - INTERVAL '30 days'
    AND is_read = true;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update category path (for hierarchical categories)
CREATE OR REPLACE FUNCTION public.update_category_path()
RETURNS TRIGGER AS $$
DECLARE
    parent_path TEXT;
BEGIN
    IF NEW.parent_id IS NULL THEN
        NEW.path = NEW.id::TEXT;
        NEW.level = 0;
    ELSE
        SELECT path, level INTO parent_path, NEW.level
        FROM public.categories
        WHERE id = NEW.parent_id;
        
        NEW.path = parent_path || '.' || NEW.id::TEXT;
        NEW.level = NEW.level + 1;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for category path updates
CREATE TRIGGER update_category_path_trigger
    BEFORE INSERT OR UPDATE ON public.categories
    FOR EACH ROW EXECUTE FUNCTION public.update_category_path();

-- =====================================================
-- BUSINESS LOGIC FUNCTIONS
-- =====================================================

-- Function to get user feed (posts from followed users)
CREATE OR REPLACE FUNCTION public.get_user_feed(
    user_uuid UUID,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    username VARCHAR(50),
    full_name VARCHAR(100),
    avatar_url TEXT,
    content TEXT,
    media_urls TEXT[],
    post_type VARCHAR(20),
    likes_count INTEGER,
    comments_count INTEGER,
    created_at TIMESTAMPTZ,
    is_liked BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.user_id,
        pr.username,
        pr.full_name,
        pr.avatar_url,
        p.content,
        p.media_urls,
        p.post_type,
        p.likes_count,
        p.comments_count,
        p.created_at,
        EXISTS(SELECT 1 FROM public.likes l WHERE l.post_id = p.id AND l.user_id = user_uuid) as is_liked
    FROM public.posts p
    JOIN public.profiles pr ON p.user_id = pr.id
    WHERE 
        p.status = 'published' AND
        p.is_public = true AND
        (
            p.user_id = user_uuid OR -- Own posts
            EXISTS (
                SELECT 1 FROM public.follows f 
                WHERE f.follower_id = user_uuid AND f.following_id = p.user_id
            ) -- Posts from followed users
        )
    ORDER BY p.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get shop products with filters
CREATE OR REPLACE FUNCTION public.get_shop_products(
    shop_uuid UUID,
    category_filter UUID DEFAULT NULL,
    status_filter VARCHAR(20) DEFAULT 'active',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    shop_id UUID,
    category_id UUID,
    name VARCHAR(200),
    description TEXT,
    price DECIMAL(10,2),
    currency VARCHAR(3),
    discount_price DECIMAL(10,2),
    images TEXT[],
    stock_quantity INTEGER,
    sku VARCHAR(100),
    condition VARCHAR(20),
    status VARCHAR(20),
    brand VARCHAR(100),
    weight DECIMAL(8,3),
    dimensions JSONB,
    tags TEXT[],
    specifications JSONB,
    view_count INTEGER,
    like_count INTEGER,
    rating DECIMAL(3,2),
    review_count INTEGER,
    is_featured BOOLEAN,
    is_promoted BOOLEAN,
    promoted_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    published_at TIMESTAMPTZ,
    shop_name VARCHAR(100),
    category_name VARCHAR(100)
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.shop_id,
        p.category_id,
        p.name,
        p.description,
        p.price,
        p.currency,
        p.discount_price,
        p.images,
        p.stock_quantity,
        p.sku,
        p.condition,
        p.status,
        p.brand,
        p.weight,
        p.dimensions,
        p.tags,
        p.specifications,
        p.view_count,
        p.like_count,
        p.rating,
        p.review_count,
        p.is_featured,
        p.is_promoted,
        p.promoted_until,
        p.created_at,
        p.updated_at,
        p.published_at,
        s.shop_name,
        c.name as category_name
    FROM public.products p
    LEFT JOIN public.shops s ON p.shop_id = s.id
    LEFT JOIN public.categories c ON p.category_id = c.id
    WHERE
        p.shop_id = shop_uuid AND
        (status_filter IS NULL OR p.status = status_filter) AND
        (category_filter IS NULL OR p.category_id = category_filter)
    ORDER BY p.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's liked posts
CREATE OR REPLACE FUNCTION public.get_user_liked_posts(
    user_uuid UUID,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    username VARCHAR(50),
    full_name VARCHAR(100),
    avatar_url TEXT,
    content TEXT,
    media_urls TEXT[],
    post_type VARCHAR(20),
    likes_count INTEGER,
    comments_count INTEGER,
    created_at TIMESTAMPTZ,
    liked_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.user_id,
        pr.username,
        pr.full_name,
        pr.avatar_url,
        p.content,
        p.media_urls,
        p.post_type,
        p.likes_count,
        p.comments_count,
        p.created_at,
        l.created_at as liked_at
    FROM public.posts p
    JOIN public.profiles pr ON p.user_id = pr.id
    JOIN public.likes l ON p.id = l.post_id
    WHERE
        l.user_id = user_uuid AND
        p.status = 'published'
    ORDER BY l.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- ADMIN MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to assign admin role to a user by email
CREATE OR REPLACE FUNCTION public.assign_admin_role(
    user_email TEXT,
    new_role VARCHAR(20) DEFAULT 'admin'
)
RETURNS BOOLEAN AS $$
DECLARE
    user_found BOOLEAN := FALSE;
    updated_count INTEGER;
BEGIN
    -- Validate role
    IF new_role NOT IN ('admin', 'super_admin', 'moderator') THEN
        RAISE EXCEPTION 'Invalid role. Must be admin, super_admin, or moderator';
    END IF;

    -- Check if user exists
    SELECT EXISTS(
        SELECT 1 FROM public.profiles
        WHERE email = user_email
    ) INTO user_found;

    IF NOT user_found THEN
        RAISE EXCEPTION 'User with email % not found', user_email;
    END IF;

    -- Update user role
    UPDATE public.profiles
    SET
        role = new_role,
        updated_at = NOW()
    WHERE email = user_email;

    GET DIAGNOSTICS updated_count = ROW_COUNT;

    -- Log the role assignment (you can extend this for audit trail)
    RAISE NOTICE 'Admin role % assigned to user %', new_role, user_email;

    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove admin role from a user
CREATE OR REPLACE FUNCTION public.remove_admin_role(
    user_email TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    user_found BOOLEAN := FALSE;
    updated_count INTEGER;
BEGIN
    -- Check if user exists
    SELECT EXISTS(
        SELECT 1 FROM public.profiles
        WHERE email = user_email
    ) INTO user_found;

    IF NOT user_found THEN
        RAISE EXCEPTION 'User with email % not found', user_email;
    END IF;

    -- Update user role back to user
    UPDATE public.profiles
    SET
        role = 'user',
        updated_at = NOW()
    WHERE email = user_email;

    GET DIAGNOSTICS updated_count = ROW_COUNT;

    -- Log the role removal
    RAISE NOTICE 'Admin role removed from user %', user_email;

    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all admin users
CREATE OR REPLACE FUNCTION public.get_admin_users()
RETURNS TABLE (
    id UUID,
    username VARCHAR(50),
    full_name VARCHAR(100),
    email VARCHAR(255),
    role VARCHAR(20),
    created_at TIMESTAMPTZ,
    last_seen_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.username,
        p.full_name,
        p.email,
        p.role,
        p.created_at,
        p.last_seen_at
    FROM public.profiles p
    WHERE p.role IN ('admin', 'super_admin', 'moderator')
    ORDER BY
        CASE p.role
            WHEN 'super_admin' THEN 1
            WHEN 'admin' THEN 2
            WHEN 'moderator' THEN 3
            ELSE 4
        END,
        p.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has admin privileges
CREATE OR REPLACE FUNCTION public.is_user_admin(
    user_email TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    user_role VARCHAR(20);
BEGIN
    SELECT role INTO user_role
    FROM public.profiles
    WHERE email = user_email;

    RETURN user_role IN ('admin', 'super_admin', 'moderator');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- USER ROLE MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to upgrade user to business role
CREATE OR REPLACE FUNCTION public.upgrade_user_to_business(
    user_uuid UUID,
    business_name VARCHAR(100),
    business_description TEXT,
    business_type VARCHAR(50) DEFAULT 'individual',
    business_location VARCHAR(100) DEFAULT NULL,
    business_phone VARCHAR(20) DEFAULT NULL,
    business_email VARCHAR(255) DEFAULT NULL,
    business_website VARCHAR(255) DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    user_exists BOOLEAN := FALSE;
    shop_exists BOOLEAN := FALSE;
BEGIN
    -- Check if user exists and is not already a business user
    SELECT EXISTS(
        SELECT 1 FROM public.profiles
        WHERE id = user_uuid AND role = 'user' AND (is_business = FALSE OR is_business IS NULL)
    ) INTO user_exists;

    IF NOT user_exists THEN
        RAISE EXCEPTION 'User not found or already a business user';
    END IF;

    -- Check if user already has a shop
    SELECT EXISTS(
        SELECT 1 FROM public.shops
        WHERE owner_id = user_uuid
    ) INTO shop_exists;

    -- Start transaction
    BEGIN
        -- Update user profile to business role
        UPDATE public.profiles
        SET
            role = 'business',
            is_business = TRUE,
            updated_at = NOW()
        WHERE id = user_uuid;

        -- Create or update shop profile
        IF shop_exists THEN
            -- Update existing shop
            UPDATE public.shops
            SET
                shop_name = business_name,
                shop_description = business_description,
                business_type = upgrade_user_to_business.business_type,
                location = business_location,
                phone = business_phone,
                email = business_email,
                website_url = business_website,
                is_active = TRUE,
                updated_at = NOW()
            WHERE owner_id = user_uuid;
        ELSE
            -- Create new shop
            INSERT INTO public.shops (
                owner_id,
                shop_name,
                shop_description,
                business_type,
                location,
                phone,
                email,
                website_url,
                is_active
            ) VALUES (
                user_uuid,
                business_name,
                business_description,
                upgrade_user_to_business.business_type,
                business_location,
                business_phone,
                business_email,
                business_website,
                TRUE
            );
        END IF;

        RETURN TRUE;
    EXCEPTION
        WHEN OTHERS THEN
            -- Rollback will happen automatically
            RAISE EXCEPTION 'Failed to upgrade user to business: %', SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can upgrade to business
CREATE OR REPLACE FUNCTION public.can_upgrade_to_business(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_role VARCHAR(20);
    is_business_user BOOLEAN;
BEGIN
    SELECT role, is_business
    INTO user_role, is_business_user
    FROM public.profiles
    WHERE id = user_uuid;

    -- User can upgrade if they exist, have 'user' role, and are not already a business
    RETURN user_role = 'user' AND (is_business_user = FALSE OR is_business_user IS NULL);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
