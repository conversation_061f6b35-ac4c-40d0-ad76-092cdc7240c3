import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'skeleton_config.dart';

/// Base skeleton widget that provides Spotify-style shimmer animation
class BaseSkeleton extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final Duration? duration;
  final BorderRadius? borderRadius;
  final Color? baseColor;
  final Color? highlightColor;

  const BaseSkeleton({
    super.key,
    required this.child,
    this.isLoading = true,
    this.duration,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
  });

  @override
  Widget build(BuildContext context) {
    if (!isLoading) {
      return child;
    }

    return Shimmer.fromColors(
      baseColor: baseColor ?? SkeletonConfig.getBaseColor(context),
      highlightColor:
          highlightColor ?? SkeletonConfig.getHighlightColor(context),
      period: duration ?? SkeletonConfig.animationDuration,
      child: Container(
        decoration: BoxDecoration(
          color: SkeletonConfig.getBaseColor(context),
          borderRadius:
              borderRadius ??
              BorderRadius.circular(SkeletonConfig.defaultBorderRadius),
        ),
        child: child,
      ),
    );
  }
}

/// Skeleton container with predefined dimensions
class SkeletonContainer extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final bool isLoading;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const SkeletonContainer({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.isLoading = true,
    this.margin,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      child: BaseSkeleton(
        isLoading: isLoading,
        borderRadius: borderRadius,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: SkeletonConfig.getBaseColor(context),
            borderRadius:
                borderRadius ??
                BorderRadius.circular(SkeletonConfig.defaultBorderRadius),
          ),
        ),
      ),
    );
  }
}

/// Skeleton text line
class SkeletonText extends StatelessWidget {
  final double? width;
  final double height;
  final bool isLoading;
  final EdgeInsetsGeometry? margin;

  const SkeletonText({
    super.key,
    this.width,
    this.height = 16.0,
    this.isLoading = true,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: SkeletonContainer(
        width: width,
        height: height,
        isLoading: isLoading,
        borderRadius: BorderRadius.circular(height / 2),
      ),
    );
  }
}

/// Skeleton circle (for avatars)
class SkeletonCircle extends StatelessWidget {
  final double size;
  final bool isLoading;
  final EdgeInsetsGeometry? margin;

  const SkeletonCircle({
    super.key,
    required this.size,
    this.isLoading = true,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: SkeletonContainer(
        width: size,
        height: size,
        isLoading: isLoading,
        borderRadius: BorderRadius.circular(size / 2),
      ),
    );
  }
}

/// Skeleton card
class SkeletonCard extends StatelessWidget {
  final double? width;
  final double? height;
  final bool isLoading;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const SkeletonCard({
    super.key,
    this.width,
    this.height,
    this.isLoading = true,
    this.margin,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding,
      child: SkeletonContainer(
        width: width,
        height: height,
        isLoading: isLoading,
        borderRadius: BorderRadius.circular(SkeletonConfig.cardBorderRadius),
      ),
    );
  }
}
