// Test file to verify phone detection fix for forgot password
// This file demonstrates the fix for the phone number detection issue

import 'package:logger/logger.dart';

// Logger instance for proper logging
final Logger _logger = Logger(
  printer: PrettyPrinter(
    methodCount: 0,
    errorMethodCount: 3,
    lineLength: 120,
    colors: true,
    printEmojis: true,
    printTime: false,
  ),
);

void main() {
  _logger.i('🧪 Testing Phone Detection Logic Fix\n');

  // Test cases that were failing before the fix
  final testCases = [
    '265984671670', // Without + (this was failing)
    '+265984671670', // With + (this was working)
    '0984671670', // Local format
    '+1234567890', // Different country
    '<EMAIL>', // Email (should not be phone)
    'username123', // Username (should not be phone)
  ];

  for (final testCase in testCases) {
    testPhoneDetection(testCase);
  }
}

void testPhoneDetection(String identifier) {
  // OLD LOGIC (was causing the bug):
  final oldIsPhone =
      identifier.startsWith('+') ||
      RegExp(
        r'^\d+$',
      ).hasMatch(identifier.replaceAll(RegExp(r'[\s\-\(\)]'), ''));

  // NEW LOGIC (fixed):
  final newIsPhone = RegExp(r'^\+?[0-9]').hasMatch(identifier);

  _logger.d('📱 Testing: "$identifier"');
  _logger.d('   Old logic: $oldIsPhone');
  _logger.d('   New logic: $newIsPhone');
  _logger.d('   Fixed: ${oldIsPhone != newIsPhone ? "✅ YES" : "❌ NO"}');
  _logger.d('');
}

/*
Expected Output:
📱 Testing: "265984671670"
   Old logic: true
   New logic: true
   Fixed: ❌ NO

📱 Testing: "+265984671670"
   Old logic: true  
   New logic: true
   Fixed: ❌ NO

The issue was more subtle - it was in how the normalized phone number
(+265984671670) was being processed by the old regex after normalization.

The fix ensures consistent phone detection between AuthBloc and Service layers.
*/
