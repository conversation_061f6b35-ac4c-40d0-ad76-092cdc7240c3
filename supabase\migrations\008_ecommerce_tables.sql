-- =====================================================
-- E-COMMERCE TABLES
-- Professional marketplace functionality
-- =====================================================

-- =====================================================
-- 9. ORDERS TABLE (Transaction Management)
-- =====================================================

CREATE TABLE public.orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    buyer_id UUID REFERENCES public.profiles(id) ON DELETE RESTRICT NOT NULL,
    shop_id UUID REFERENCES public.shops(id) ON DELETE RESTRICT NOT NULL,
    
    -- Order details
    order_number VARCHAR(50) UNIQUE NOT NULL, -- Human-readable order number
    total_amount DECIMAL(12,2) NOT NULL CHECK (total_amount >= 0),
    subtotal DECIMAL(12,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(12,2) DEFAULT 0.00 CHECK (tax_amount >= 0),
    shipping_amount DECIMAL(12,2) DEFAULT 0.00 CHECK (shipping_amount >= 0),
    discount_amount DECIMAL(12,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Order status and workflow
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
        'pending', 'confirmed', 'processing', 'shipped', 'delivered', 
        'cancelled', 'refunded', 'disputed'
    )),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN (
        'pending', 'processing', 'completed', 'failed', 'refunded', 'partially_refunded'
    )),
    fulfillment_status VARCHAR(20) DEFAULT 'unfulfilled' CHECK (fulfillment_status IN (
        'unfulfilled', 'partial', 'fulfilled', 'shipped', 'delivered', 'returned'
    )),
    
    -- Shipping information
    shipping_address JSONB NOT NULL,
    billing_address JSONB,
    shipping_method VARCHAR(50),
    tracking_number VARCHAR(100),
    estimated_delivery_date DATE,
    actual_delivery_date DATE,
    
    -- Payment information
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100), -- External payment ID
    payment_data JSONB DEFAULT '{}', -- Additional payment info
    
    -- Customer communication
    notes TEXT, -- Customer notes
    internal_notes TEXT, -- Shop owner notes
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    confirmed_at TIMESTAMPTZ,
    shipped_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    
    -- Note: Self-purchase prevention handled by trigger instead of CHECK constraint
    CONSTRAINT valid_currency CHECK (currency IN ('USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD')),
    CONSTRAINT valid_total CHECK (
        total_amount = subtotal + tax_amount + shipping_amount - discount_amount
    )
);

-- Indexes for performance
CREATE INDEX idx_orders_buyer_id ON public.orders(buyer_id);
CREATE INDEX idx_orders_shop_id ON public.orders(shop_id);
CREATE INDEX idx_orders_status ON public.orders(status);
CREATE INDEX idx_orders_payment_status ON public.orders(payment_status);
CREATE INDEX idx_orders_created_at ON public.orders(created_at DESC);
CREATE INDEX idx_orders_order_number ON public.orders(order_number);

-- Composite indexes for common queries
CREATE INDEX idx_orders_buyer_status ON public.orders(buyer_id, status, created_at DESC);
CREATE INDEX idx_orders_shop_status ON public.orders(shop_id, status, created_at DESC);

-- Function to generate order number
CREATE OR REPLACE FUNCTION public.generate_order_number()
RETURNS TEXT AS $$
DECLARE
    order_num TEXT;
    counter INTEGER;
BEGIN
    -- Generate order number: ORD-YYYYMMDD-XXXX
    SELECT 
        'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
        LPAD((COUNT(*) + 1)::TEXT, 4, '0')
    INTO order_num
    FROM public.orders 
    WHERE DATE(created_at) = CURRENT_DATE;
    
    RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate order number
CREATE OR REPLACE FUNCTION public.set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL THEN
        NEW.order_number := public.generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_order_number_trigger
    BEFORE INSERT ON public.orders
    FOR EACH ROW EXECUTE FUNCTION public.set_order_number();

-- Function to prevent self-purchases
CREATE OR REPLACE FUNCTION public.prevent_self_purchase()
RETURNS TRIGGER AS $$
DECLARE
    shop_owner_id UUID;
BEGIN
    -- Get the shop owner ID
    SELECT owner_id INTO shop_owner_id
    FROM public.shops
    WHERE id = NEW.shop_id;

    -- Check if buyer is trying to purchase from their own shop
    IF NEW.buyer_id = shop_owner_id THEN
        RAISE EXCEPTION 'Users cannot purchase from their own shop';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to prevent self-purchases
CREATE TRIGGER prevent_self_purchase_trigger
    BEFORE INSERT ON public.orders
    FOR EACH ROW
    EXECUTE FUNCTION public.prevent_self_purchase();

-- =====================================================
-- 10. ORDER_ITEMS TABLE (Order Line Items)
-- =====================================================

CREATE TABLE public.order_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES public.products(id) ON DELETE RESTRICT NOT NULL,
    
    -- Product snapshot (at time of order)
    product_name VARCHAR(200) NOT NULL,
    product_description TEXT,
    product_image_url TEXT,
    product_sku VARCHAR(100),
    
    -- Pricing and quantity
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(12,2) NOT NULL CHECK (total_price >= 0),
    discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    
    -- Product variants/options
    variant_options JSONB DEFAULT '{}', -- Size, color, etc.
    
    -- Fulfillment
    fulfillment_status VARCHAR(20) DEFAULT 'unfulfilled' CHECK (fulfillment_status IN (
        'unfulfilled', 'fulfilled', 'shipped', 'delivered', 'returned', 'cancelled'
    )),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_total_price CHECK (total_price = (unit_price * quantity) - discount_amount)
);

-- Indexes for performance
CREATE INDEX idx_order_items_order_id ON public.order_items(order_id);
CREATE INDEX idx_order_items_product_id ON public.order_items(product_id);
CREATE INDEX idx_order_items_created_at ON public.order_items(created_at DESC);

-- =====================================================
-- RLS POLICIES FOR E-COMMERCE TABLES
-- =====================================================

-- ORDERS TABLE SECURITY
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;

-- Users can view their own orders (as buyer) or orders for their shop
CREATE POLICY "orders_select_policy" ON public.orders
FOR SELECT USING (
    buyer_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.shops s 
        WHERE s.id = shop_id AND s.owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- Only authenticated users can create orders
CREATE POLICY "orders_insert_policy" ON public.orders
FOR INSERT WITH CHECK (
    auth.uid() = buyer_id AND
    auth.uid() IS NOT NULL
);

-- Buyers and shop owners can update orders (with restrictions)
CREATE POLICY "orders_update_policy" ON public.orders
FOR UPDATE USING (
    buyer_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.shops s 
        WHERE s.id = shop_id AND s.owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- Only admins can delete orders
CREATE POLICY "orders_delete_policy" ON public.orders
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- ORDER_ITEMS TABLE SECURITY
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;

-- Users can view order items for orders they can see
CREATE POLICY "order_items_select_policy" ON public.order_items
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.orders o 
        WHERE o.id = order_id AND (
            o.buyer_id = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.shops s 
                WHERE s.id = o.shop_id AND s.owner_id = auth.uid()
            )
        )
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- Order items are created automatically with orders
CREATE POLICY "order_items_insert_policy" ON public.order_items
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.orders o 
        WHERE o.id = order_id AND o.buyer_id = auth.uid()
    )
);

-- Shop owners can update fulfillment status
CREATE POLICY "order_items_update_policy" ON public.order_items
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM public.orders o
        JOIN public.shops s ON o.shop_id = s.id
        WHERE o.id = order_id AND s.owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- Only admins can delete order items
CREATE POLICY "order_items_delete_policy" ON public.order_items
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- =====================================================
-- E-COMMERCE BUSINESS LOGIC FUNCTIONS
-- =====================================================

-- Function to create an order with items
CREATE OR REPLACE FUNCTION public.create_order(
    p_buyer_id UUID,
    p_shop_id UUID,
    p_items JSONB, -- Array of {product_id, quantity, unit_price}
    p_shipping_address JSONB,
    p_billing_address JSONB DEFAULT NULL,
    p_payment_method VARCHAR(50) DEFAULT 'card',
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    order_id UUID;
    item JSONB;
    product_info RECORD;
    subtotal DECIMAL(12,2) := 0;
    total_amount DECIMAL(12,2);
BEGIN
    -- Create the order
    INSERT INTO public.orders (
        buyer_id,
        shop_id,
        subtotal,
        total_amount,
        shipping_address,
        billing_address,
        payment_method,
        notes,
        status
    ) VALUES (
        p_buyer_id,
        p_shop_id,
        0, -- Will be updated below
        0, -- Will be updated below
        p_shipping_address,
        COALESCE(p_billing_address, p_shipping_address),
        p_payment_method,
        p_notes,
        'pending'
    ) RETURNING id INTO order_id;
    
    -- Add order items
    FOR item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Get product info
        SELECT p.name, p.description, p.images[1], p.sku, p.price
        INTO product_info
        FROM public.products p
        WHERE p.id = (item->>'product_id')::UUID AND p.status = 'active';
        
        IF NOT FOUND THEN
            RAISE EXCEPTION 'Product not found or not available: %', item->>'product_id';
        END IF;
        
        -- Validate stock
        IF (SELECT stock_quantity FROM public.products WHERE id = (item->>'product_id')::UUID) < (item->>'quantity')::INTEGER THEN
            RAISE EXCEPTION 'Insufficient stock for product: %', product_info.name;
        END IF;
        
        -- Insert order item
        INSERT INTO public.order_items (
            order_id,
            product_id,
            product_name,
            product_description,
            product_image_url,
            product_sku,
            quantity,
            unit_price,
            total_price
        ) VALUES (
            order_id,
            (item->>'product_id')::UUID,
            product_info.name,
            product_info.description,
            product_info.images,
            product_info.sku,
            (item->>'quantity')::INTEGER,
            (item->>'unit_price')::DECIMAL,
            (item->>'quantity')::INTEGER * (item->>'unit_price')::DECIMAL
        );
        
        -- Update subtotal
        subtotal := subtotal + ((item->>'quantity')::INTEGER * (item->>'unit_price')::DECIMAL);
        
        -- Reserve stock
        UPDATE public.products 
        SET stock_quantity = stock_quantity - (item->>'quantity')::INTEGER
        WHERE id = (item->>'product_id')::UUID;
    END LOOP;
    
    -- Update order totals
    total_amount := subtotal; -- Add shipping, tax, etc. as needed
    
    UPDATE public.orders 
    SET 
        subtotal = subtotal,
        total_amount = total_amount,
        updated_at = NOW()
    WHERE id = order_id;
    
    RETURN order_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update order status
CREATE OR REPLACE FUNCTION public.update_order_status(
    p_order_id UUID,
    p_status VARCHAR(20),
    p_tracking_number VARCHAR(100) DEFAULT NULL,
    p_internal_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_status VARCHAR(20);
    shop_owner_id UUID;
BEGIN
    -- Get current status and verify permissions
    SELECT o.status, s.owner_id
    INTO current_status, shop_owner_id
    FROM public.orders o
    JOIN public.shops s ON o.shop_id = s.id
    WHERE o.id = p_order_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Order not found';
    END IF;

    -- Check permissions
    IF shop_owner_id != auth.uid() AND NOT EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions';
    END IF;

    -- Update order
    UPDATE public.orders
    SET
        status = p_status,
        tracking_number = COALESCE(p_tracking_number, tracking_number),
        internal_notes = COALESCE(p_internal_notes, internal_notes),
        updated_at = NOW(),
        confirmed_at = CASE WHEN p_status = 'confirmed' AND confirmed_at IS NULL THEN NOW() ELSE confirmed_at END,
        shipped_at = CASE WHEN p_status = 'shipped' AND shipped_at IS NULL THEN NOW() ELSE shipped_at END,
        delivered_at = CASE WHEN p_status = 'delivered' AND delivered_at IS NULL THEN NOW() ELSE delivered_at END,
        cancelled_at = CASE WHEN p_status = 'cancelled' AND cancelled_at IS NULL THEN NOW() ELSE cancelled_at END
    WHERE id = p_order_id;

    -- If order is cancelled, restore stock
    IF p_status = 'cancelled' AND current_status != 'cancelled' THEN
        UPDATE public.products
        SET stock_quantity = stock_quantity + oi.quantity
        FROM public.order_items oi
        WHERE products.id = oi.product_id AND oi.order_id = p_order_id;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get order details with items
CREATE OR REPLACE FUNCTION public.get_order_details(p_order_id UUID)
RETURNS TABLE (
    order_id UUID,
    order_number VARCHAR(50),
    buyer_name VARCHAR(100),
    buyer_email VARCHAR(255),
    shop_name VARCHAR(100),
    status VARCHAR(20),
    payment_status VARCHAR(20),
    total_amount DECIMAL(12,2),
    created_at TIMESTAMPTZ,
    items JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        o.id,
        o.order_number,
        bp.full_name,
        bp.email,
        s.shop_name,
        o.status,
        o.payment_status,
        o.total_amount,
        o.created_at,
        jsonb_agg(
            jsonb_build_object(
                'product_id', oi.product_id,
                'product_name', oi.product_name,
                'quantity', oi.quantity,
                'unit_price', oi.unit_price,
                'total_price', oi.total_price,
                'image_url', oi.product_image_url
            )
        ) as items
    FROM public.orders o
    JOIN public.profiles bp ON o.buyer_id = bp.id
    JOIN public.shops s ON o.shop_id = s.id
    JOIN public.order_items oi ON o.id = oi.order_id
    WHERE o.id = p_order_id
    GROUP BY o.id, o.order_number, bp.full_name, bp.email, s.shop_name,
             o.status, o.payment_status, o.total_amount, o.created_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's order history
CREATE OR REPLACE FUNCTION public.get_user_orders(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    order_number VARCHAR(50),
    shop_name VARCHAR(100),
    status VARCHAR(20),
    total_amount DECIMAL(12,2),
    created_at TIMESTAMPTZ,
    item_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        o.id,
        o.order_number,
        s.shop_name,
        o.status,
        o.total_amount,
        o.created_at,
        COUNT(oi.id) as item_count
    FROM public.orders o
    JOIN public.shops s ON o.shop_id = s.id
    LEFT JOIN public.order_items oi ON o.id = oi.order_id
    WHERE o.buyer_id = p_user_id
    GROUP BY o.id, o.order_number, s.shop_name, o.status, o.total_amount, o.created_at
    ORDER BY o.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get shop's order history
CREATE OR REPLACE FUNCTION public.get_shop_orders(
    p_shop_id UUID,
    p_status VARCHAR(20) DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    order_number VARCHAR(50),
    buyer_name VARCHAR(100),
    status VARCHAR(20),
    payment_status VARCHAR(20),
    total_amount DECIMAL(12,2),
    created_at TIMESTAMPTZ,
    item_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        o.id,
        o.order_number,
        bp.full_name,
        o.status,
        o.payment_status,
        o.total_amount,
        o.created_at,
        COUNT(oi.id) as item_count
    FROM public.orders o
    JOIN public.profiles bp ON o.buyer_id = bp.id
    LEFT JOIN public.order_items oi ON o.id = oi.order_id
    WHERE
        o.shop_id = p_shop_id AND
        (p_status IS NULL OR o.status = p_status)
    GROUP BY o.id, o.order_number, bp.full_name, o.status, o.payment_status, o.total_amount, o.created_at
    ORDER BY o.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- TRIGGERS AND AUTOMATION
-- =====================================================

-- Trigger to update order timestamps
CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON public.orders
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_order_items_updated_at
    BEFORE UPDATE ON public.order_items
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Trigger to update shop stats when orders are completed
CREATE TRIGGER orders_shop_stats_trigger
    AFTER INSERT OR UPDATE ON public.orders
    FOR EACH ROW EXECUTE FUNCTION public.update_shop_stats();

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions on e-commerce tables
GRANT SELECT, INSERT, UPDATE ON public.orders TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.order_items TO authenticated;
