-- =====================================================
-- FIX GET_SHOP_PRODUCTS FUNCTION
-- Fix the null casting error by returning all required fields
-- =====================================================

-- Function to get shop products with filters (FIXED VERSION)
CREATE OR REPLACE FUNCTION public.get_shop_products(
    shop_uuid UUID,
    category_filter UUID DEFAULT NULL,
    status_filter VARCHAR(20) DEFAULT 'active',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    shop_id UUID,
    category_id UUID,
    name VARCHAR(200),
    description TEXT,
    price DECIMAL(10,2),
    currency VARCHAR(3),
    discount_price DECIMAL(10,2),
    images TEXT[],
    stock_quantity INTEGER,
    sku VARCHAR(100),
    condition VARCHAR(20),
    status VARCHAR(20),
    brand VARCHAR(100),
    weight DECIMAL(8,3),
    dimensions JSONB,
    tags TEXT[],
    specifications JSONB,
    view_count INTEGER,
    like_count INTEGER,
    rating DECIMAL(3,2),
    review_count INTEGER,
    is_featured BOOLEAN,
    is_promoted BOOLEAN,
    promoted_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    published_at TIMESTAMPTZ,
    shop_name VARCHAR(100),
    category_name VARCHAR(100)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.shop_id,
        p.category_id,
        p.name,
        p.description,
        p.price,
        p.currency,
        p.discount_price,
        p.images,
        p.stock_quantity,
        p.sku,
        p.condition,
        p.status,
        p.brand,
        p.weight,
        p.dimensions,
        p.tags,
        p.specifications,
        p.view_count,
        p.like_count,
        p.rating,
        p.review_count,
        p.is_featured,
        p.is_promoted,
        p.promoted_until,
        p.created_at,
        p.updated_at,
        p.published_at,
        s.shop_name,
        c.name as category_name
    FROM public.products p
    LEFT JOIN public.shops s ON p.shop_id = s.id
    LEFT JOIN public.categories c ON p.category_id = c.id
    WHERE 
        p.shop_id = shop_uuid AND
        (status_filter IS NULL OR p.status = status_filter) AND
        (category_filter IS NULL OR p.category_id = category_filter)
    ORDER BY p.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
