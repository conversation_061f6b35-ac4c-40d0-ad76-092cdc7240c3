import 'dart:async';
import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart' as app_auth;
import 'package:business_app/services/supabase_service.dart';
import 'package:business_app/services/google_signin_service.dart';
import 'package:business_app/supabase/config.dart';
import 'package:business_app/models/auth/auth_result.dart';
import 'package:business_app/models/auth/user_model.dart';
import 'package:business_app/services/session_service.dart';
import 'package:business_app/utils/phone_utils.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

class AuthBloc extends Bloc<AuthEvent, app_auth.AuthState> {
  StreamSubscription<SessionState>? _sessionSubscription;

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  AuthBloc() : super(app_auth.AuthState.initial()) {
    // Register event handlers
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<LoginRequested>(_onLoginRequested);
    on<EmailOtpLoginRequested>(_onEmailOtpLoginRequested);
    on<LoginOtpRequested>(_onLoginOtpRequested);
    on<LoginOtpVerificationSubmitted>(_onLoginOtpVerificationSubmitted);
    on<SignupStarted>(_onSignupStarted);
    on<SignupBasicInfoSubmitted>(_onSignupBasicInfoSubmitted);
    on<SignupVerificationSubmitted>(_onSignupVerificationSubmitted);
    on<SignupPasswordCreated>(_onSignupPasswordCreated);
    on<SignupBirthdaySelected>(_onSignupBirthdaySelected);
    on<SignupUsernameSelected>(_onSignupUsernameSelected);
    on<SignupProfileSetup>(_onSignupProfileSetup);
    on<SignupCategorySelection>(_onSignupCategorySelection);
    on<SignupCompleted>(_onSignupCompleted);
    on<ForgotPasswordRequested>(_onForgotPasswordRequested);
    on<PasswordResetCodeSubmitted>(_onPasswordResetCodeSubmitted);
    on<PasswordResetOTPResendRequested>(_onPasswordResetOTPResendRequested);
    on<NewPasswordSubmitted>(_onNewPasswordSubmitted);
    on<GoogleSignInRequested>(_onGoogleSignInRequested);
    on<GoogleSignInCompleted>(_onGoogleSignInCompleted);
    on<GoogleUserNeedsOnboarding>(_onGoogleUserNeedsOnboarding);
    on<GoogleOnboardingContinueRequested>(_onGoogleOnboardingContinueRequested);
    on<AppleSignInRequested>(_onAppleSignInRequested);
    on<EmailVerificationRequested>(_onEmailVerificationRequested);
    on<EmailVerificationResendRequested>(_onEmailVerificationResendRequested);
    on<NewDeviceVerificationRequested>(_onNewDeviceVerificationRequested);
    on<NewDeviceOTPSubmitted>(_onNewDeviceOTPSubmitted);
    on<EmailChangeRequested>(_onEmailChangeRequested);
    on<EmailChangeOTPSubmitted>(_onEmailChangeOTPSubmitted);
    on<SecurityOperationRequested>(_onSecurityOperationRequested);
    on<SecurityOTPSubmitted>(_onSecurityOTPSubmitted);
    on<LogoutRequested>(_onLogoutRequested);
    on<AuthClearError>(_onAuthClearError);

    // Start monitoring session state changes
    _startSessionMonitoring();
  }

  @override
  Future<void> close() {
    _sessionSubscription?.cancel();
    return super.close();
  }

  /// Start monitoring session state changes for automatic session management
  void _startSessionMonitoring() {
    _sessionSubscription = SessionService.sessionStateStream.listen(
      (sessionState) {
        // Handle session state changes
        switch (sessionState.status) {
          case SessionStatus.authenticated:
            if (sessionState.user != null && sessionState.session != null) {
              // Session is valid, trigger auth check to refresh user profile
              // BUT: Don't interfere with password reset flow OR signup flow
              if (state.status != app_auth.AuthStatus.authenticated &&
                  state.resetCode == null &&
                  state.signupStep == null) {
                // Only trigger auth check if not in password reset or signup flow
                _logger.i('🔄 Session authenticated - triggering auth check');
                add(AuthCheckRequested());
              } else {
                _logger.i(
                  '🔄 Session authenticated but skipping auth check - status: ${state.status}, resetCode: ${state.resetCode != null}, signupStep: ${state.signupStep}',
                );
              }
            }
            break;

          case SessionStatus.unauthenticated:
            // Session expired or user signed out
            if (state.status == app_auth.AuthStatus.authenticated) {
              add(LogoutRequested());
            }
            break;

          case SessionStatus.error:
            // Session error occurred
            if (state.status == app_auth.AuthStatus.authenticated) {
              // Use event to trigger logout instead of direct emit
              add(LogoutRequested());
            }
            break;

          default:
            break;
        }
      },
      onError: (error) {
        _logger.e('🔐 AuthBloc: Session monitoring error: $error');
      },
    );
  }

  // Check if user is already authenticated
  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    try {
      // Don't auto-authenticate if we're in the middle of signup
      // Check this BEFORE emitting loading state to prevent state loss
      if (state.signupStep != null) {
        _logger.i(
          '🔄 Skipping auth check - signup in progress (step: ${state.signupStep})',
        );
        return;
      }

      emit(state.loading());

      // Check if user is already authenticated with Supabase
      final user = SupabaseConfig.currentUser;
      final session = SupabaseConfig.currentSession;
      if (user != null && session != null) {
        // Get user profile from database
        final userProfile = await AuthService.getCurrentUserProfile();
        if (userProfile != null) {
          emit(
            state.authenticated(
              userId: userProfile.id,
              userName: userProfile.username,
              userEmail: userProfile.email,
              userPhone: userProfile.phone,
              userProfileImage: userProfile.profileImageUrl,
              userBio: userProfile.bio,
              accessToken: session.accessToken,
              userRole: userProfile.role,
            ),
          );
          return;
        }
      }

      emit(state.unauthenticated());
    } catch (e) {
      emit(
        state.error('Failed to check authentication status: ${e.toString()}'),
      );
    }
  }

  // Handle login
  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate input
      if (event.identifier.isEmpty || event.password.isEmpty) {
        emit(state.error('Please enter both email and password'));
        return;
      }

      // Determine if identifier is email or phone
      final isEmail = event.identifier.contains('@');
      final isPhone = RegExp(r'^\+?[0-9]').hasMatch(event.identifier);

      AuthResult<AuthResponse> result;

      if (isEmail) {
        // Email + Password login (traditional authentication)
        result = await AuthService.signInWithEmail(
          email: event.identifier,
          password: event.password,
        );
      } else if (isPhone) {
        // Phone + Password login - normalize phone number first
        final normalizedPhone = PhoneUtils.normalizePhoneNumber(
          event.identifier,
        );
        _logger.i(
          '🔄 AuthBloc: Login with normalized phone: "$normalizedPhone" (from "${event.identifier}")',
        );

        // Convert phone to temp email format for signin (since that's how phone users are stored)
        final tempEmail = PhoneUtils.phoneToTempEmail(normalizedPhone);
        _logger.i(
          '🔄 AuthBloc: Converting phone to temp email for signin: "$tempEmail"',
        );
        _logger.d('🔍 AuthBloc: Phone signin debug:');
        _logger.d('   Original input: "${event.identifier}"');
        _logger.d('   Normalized phone: "$normalizedPhone"');
        _logger.d('   Temp email: "$tempEmail"');
        _logger.d('   Password length: ${event.password.length}');

        result = await AuthService.signInWithEmail(
          email: tempEmail,
          password: event.password,
        );
      } else {
        // Username + Password login - look up email from username
        result = await AuthService.signInWithUsername(
          username: event.identifier,
          password: event.password,
        );
      }

      if (result.isSuccess && result.data?.user != null) {
        // Get user profile from database
        final userProfile = await AuthService.getCurrentUserProfile();
        if (userProfile != null) {
          // Update last seen
          await AuthService.updateLastSeen();

          // Save session with SessionService for persistence
          if (result.data?.session != null) {
            await SessionService.saveSession(
              result.data!.session!,
              userProfile,
            );
          }

          emit(
            state.authenticated(
              userId: userProfile.id,
              userName: userProfile.username,
              userEmail: userProfile.email,
              userPhone: userProfile.phone,
              userProfileImage: userProfile.profileImageUrl,
              userBio: userProfile.bio,
              accessToken: result.data?.session?.accessToken ?? '',
              userRole: userProfile.role,
            ),
          );
        } else {
          emit(state.error('User profile not found'));
        }
      } else {
        // Check if the error is related to email verification
        final errorString = result.error?.toLowerCase() ?? '';
        if (errorString.contains('email not confirmed') ||
            errorString.contains('verify your email') ||
            errorString.contains('email confirmation')) {
          // Emit a special state for email verification needed
          emit(
            state.copyWith(
              status: app_auth.AuthStatus.unauthenticated,
              loginStep: app_auth.LoginStep.emailVerificationNeeded,
              isLoading: false,
              tempEmailOrPhone:
                  event.identifier.contains('@') ? event.identifier : null,
              errorMessage:
                  'Please verify your email address before signing in. Check your inbox for a verification link.',
            ),
          );
        } else {
          emit(
            state.error(
              result.error ?? 'Invalid credentials. Please try again.',
            ),
          );
        }
      }
    } catch (e) {
      // Enhanced error logging for debugging
      _logger.e('🚨 AuthBloc Login Error: $e');
      _logger.e('🚨 Error Type: ${e.runtimeType}');
      _logger.e('🚨 Error String: ${e.toString()}');

      String errorMessage = 'Login failed. Please try again.';
      final errorString = e.toString().toLowerCase();

      if (errorString.contains('invalid login credentials')) {
        errorMessage = 'Invalid email or password. Please try again.';
      } else if (errorString.contains('email not confirmed') ||
          errorString.contains('verify your email') ||
          errorString.contains('email confirmation')) {
        // Handle email verification needed
        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            loginStep: app_auth.LoginStep.emailVerificationNeeded,
            isLoading: false,
            tempEmailOrPhone:
                event.identifier.contains('@') ? event.identifier : null,
            errorMessage:
                'Please verify your email address before signing in. Check your inbox for a verification link.',
          ),
        );
        return;
      } else if (errorString.contains('user not found')) {
        errorMessage =
            'No account found with this email. Please sign up first.';
      } else if (errorString.contains('network')) {
        errorMessage =
            'Network error. Please check your connection and try again.';
      }

      emit(state.error(errorMessage));
    }
  }

  // Handle Email OTP login request (magic link)
  Future<void> _onEmailOtpLoginRequested(
    EmailOtpLoginRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate email format
      if (event.email.isEmpty || !event.email.contains('@')) {
        emit(state.error('Please enter a valid email address'));
        return;
      }

      // Send OTP via email (magic link)
      final otpResult = await AuthService.signInWithEmailOTP(
        email: event.email,
        emailRedirectTo: event.emailRedirectTo ?? 'myapp://login-callback',
      );

      if (otpResult.isSuccess) {
        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            loginStep: app_auth.LoginStep.otpVerification,
            isLoading: false,
            tempEmailOrPhone: event.email,
            tempIsEmail: true,
            errorMessage: null,
            successMessage: 'Magic link sent! Check your email to sign in.',
          ),
        );
      } else {
        emit(
          state.error(
            otpResult.error ?? 'Failed to send magic link. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Failed to send magic link. Please try again.'));
    }
  }

  // Handle OTP login request
  Future<void> _onLoginOtpRequested(
    LoginOtpRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Send OTP based on type
      AuthResult<void> otpResult;

      if (event.isEmail) {
        // Send email OTP (magic link or code) - this is the correct method for OTP login
        otpResult = await AuthService.signInWithEmailOTP(
          email: event.emailOrPhone,
          emailRedirectTo: 'myapp://login-callback',
        );
      } else {
        // Send phone OTP - normalize phone number first
        final normalizedPhone = PhoneUtils.normalizePhoneNumber(
          event.emailOrPhone,
        );
        _logger.i(
          '🔄 AuthBloc: Sending OTP to normalized phone: "$normalizedPhone" (from "${event.emailOrPhone}")',
        );

        otpResult = await AuthService.sendPhoneOTP(phone: normalizedPhone);
      }

      if (otpResult.isSuccess) {
        // Store normalized phone number for consistency
        final normalizedEmailOrPhone =
            event.isEmail
                ? event.emailOrPhone
                : PhoneUtils.normalizePhoneNumber(event.emailOrPhone);

        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            loginStep: app_auth.LoginStep.otpVerification,
            isLoading: false,
            tempEmailOrPhone: normalizedEmailOrPhone,
            tempIsEmail: event.isEmail,
            errorMessage: null,
          ),
        );
      } else {
        emit(
          state.error(
            otpResult.error ??
                'Failed to send verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Failed to send verification code. Please try again.'));
    }
  }

  // Handle OTP login verification
  Future<void> _onLoginOtpVerificationSubmitted(
    LoginOtpVerificationSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate code format
      if (event.code.length != 6) {
        emit(state.error('Please enter a 6-digit verification code'));
        return;
      }

      // Verify OTP based on type
      if (state.tempEmailOrPhone == null || state.tempIsEmail == null) {
        emit(state.error('Verification session expired. Please start again.'));
        return;
      }

      AuthResult<AuthResponse> verificationResult;

      if (state.tempIsEmail!) {
        // Verify email OTP
        verificationResult = await AuthService.verifyEmailOTP(
          email: state.tempEmailOrPhone!,
          token: event.code,
          type: OtpType.email,
        );
      } else {
        // Verify phone OTP
        verificationResult = await AuthService.verifyPhoneOTP(
          phone: state.tempEmailOrPhone!,
          token: event.code,
          type: OtpType.sms,
        );
      }

      if (verificationResult.isSuccess && verificationResult.data != null) {
        final user = verificationResult.data!.user;
        final session = verificationResult.data!.session;

        if (user != null && session != null) {
          // Save session
          await SessionService.saveSession(session);

          // Extract user data
          final userData = user.userMetadata ?? {};
          final fullName =
              userData['full_name'] ?? userData['display_name'] ?? 'User';

          emit(
            state.authenticated(
              userId: user.id,
              userName: fullName,
              userEmail: user.email,
              userPhone: user.phone,
              accessToken: session.accessToken,
            ),
          );
        } else {
          emit(state.error('Authentication failed. Please try again.'));
        }
      } else {
        emit(
          state.error(
            verificationResult.error ??
                'Invalid verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Verification failed. Please try again.'));
    }
  }

  // Start signup process
  Future<void> _onSignupStarted(
    SignupStarted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(
      state.copyWith(
        status: app_auth.AuthStatus.unauthenticated,
        signupStep: app_auth.SignupStep.basicInfo,
        isLoading: false,
        errorMessage: null,
      ),
    );
  }

  // Handle basic info submission - now checks user existence first
  Future<void> _onSignupBasicInfoSubmitted(
    SignupBasicInfoSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate basic info
      if (event.name.trim().isEmpty) {
        emit(state.error('Name is required'));
        return;
      }

      if (event.emailOrPhone.trim().isEmpty) {
        emit(state.error('Email or phone number is required'));
        return;
      }

      // Normalize phone number if it's a phone signup
      String normalizedEmailOrPhone = event.emailOrPhone;
      if (!event.isEmail) {
        normalizedEmailOrPhone = PhoneUtils.normalizePhoneNumber(
          event.emailOrPhone,
        );
        _logger.i(
          '🔄 AuthBloc: Normalized phone number from "${event.emailOrPhone}" to "$normalizedEmailOrPhone"',
        );
      }

      // Check if user already exists before proceeding to password step
      bool userExists = false;
      if (event.isEmail) {
        // Check email existence
        userExists = await AuthService.checkUserExistsByEmail(
          normalizedEmailOrPhone,
        );
        if (userExists) {
          emit(
            state.error(
              'An account with this email already exists. Please sign in instead.',
            ),
          );
          return;
        }
      } else {
        // Check phone existence
        userExists = await AuthService.checkUserExistsByPhone(
          normalizedEmailOrPhone,
        );
        if (userExists) {
          emit(
            state.error(
              'An account with this phone number already exists. Please sign in instead.',
            ),
          );
          return;
        }
      }

      // Store basic info and proceed to password step (no OTP yet)
      emit(
        state.signupBasicInfo(
          name: event.name,
          emailOrPhone: normalizedEmailOrPhone,
          dateOfBirth: event.dateOfBirth,
          isEmail: event.isEmail,
        ),
      );
    } catch (e) {
      emit(
        state.error('Failed to process basic information. Please try again.'),
      );
    }
  }

  // Handle verification code submission with real OTP verification
  Future<void> _onSignupVerificationSubmitted(
    SignupVerificationSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate code format
      if (event.code.length != 6) {
        emit(state.error('Please enter a 6-digit verification code'));
        return;
      }

      // Verify OTP based on type
      if (state.tempEmailOrPhone == null || state.tempIsEmail == null) {
        emit(state.error('Verification session expired. Please start again.'));
        return;
      }

      AuthResult<AuthResponse> verificationResult;

      if (state.tempIsEmail!) {
        // For email users, account is already created, just verify the email
        final emailVerificationResult = await AuthService.verifyEmailOTP(
          email: state.tempEmailOrPhone!,
          token: event.code,
          type: OtpType.email,
        );

        if (emailVerificationResult.isSuccess) {
          // Email verified successfully, continue with signup flow to username
          emit(
            state.copyWith(
              status: app_auth.AuthStatus.unauthenticated,
              signupStep: app_auth.SignupStep.username,
              isLoading: false,
              tempVerificationCode: event.code,
              errorMessage: null,
              successMessage: 'Email verified! Now choose your username.',
            ),
          );
          return; // Exit early for email verification
        } else {
          emit(
            state.error(
              emailVerificationResult.error ??
                  'Invalid verification code. Please try again.',
            ),
          );
          return; // Exit early for email verification error
        }
      } else {
        // Verify phone OTP (existing method)
        verificationResult = await AuthService.verifyPhoneOTP(
          phone: state.tempEmailOrPhone!,
          token: event.code,
          type: OtpType.sms,
        );
      }

      // Handle phone verification result (only reached if phone verification)
      if (verificationResult.isSuccess) {
        // Phone OTP verified - sign out immediately to prevent auto-login during signup
        await AuthService.signOut();

        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            signupStep: app_auth.SignupStep.username,
            isLoading: false,
            tempVerificationCode: event.code,
            errorMessage: null,
            successMessage: 'Phone verified! Now choose your username.',
          ),
        );
      } else {
        emit(
          state.error(
            verificationResult.error ??
                'Invalid verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Verification failed. Please try again.'));
    }
  }

  // Handle password creation - Spotify style (no OTP during signup)
  Future<void> _onSignupPasswordCreated(
    SignupPasswordCreated event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate password
      if (event.password.length < 8) {
        emit(state.error('Password must be at least 8 characters long'));
        return;
      }

      // Validate required data
      if (state.tempEmailOrPhone == null ||
          state.tempIsEmail == null ||
          state.tempName == null) {
        emit(state.error('Missing signup information. Please start again.'));
        return;
      }

      // Spotify-style: Create account immediately without OTP verification
      // Email/phone verification can be done later for security actions
      // Note: User existence already checked in Step 1, so we can proceed directly
      AuthResult<AuthResponse> signupResult;

      if (state.tempIsEmail!) {
        // Create account with email and password
        signupResult = await AuthService.signUpWithEmail(
          email: state.tempEmailOrPhone!,
          password: event.password,
          fullName: state.tempName!,
          additionalData: {
            'date_of_birth': state.tempDateOfBirth?.toIso8601String(),
          },
        );
      } else {
        // For phone signup, create account directly (no OTP during signup)
        // Use PhoneUtils to ensure consistent phone number formatting
        final normalizedPhone = PhoneUtils.normalizePhoneNumber(
          state.tempEmailOrPhone!,
        );
        final tempEmail = PhoneUtils.phoneToTempEmail(normalizedPhone);

        _logger.i(
          '🔄 AuthBloc: Creating phone account with normalized phone: "$normalizedPhone"',
        );
        _logger.i('🔄 AuthBloc: Using temp email: "$tempEmail"');

        signupResult = await AuthService.signUpWithEmail(
          email: tempEmail,
          password: event.password,
          fullName: state.tempName!,
          additionalData: {
            'phone': normalizedPhone,
            'date_of_birth': state.tempDateOfBirth?.toIso8601String(),
            'signup_method': 'phone',
          },
        );
      }

      if (signupResult.isSuccess) {
        // Account created successfully, sign out to continue signup flow
        _logger.i(
          '🔐 Account created (Spotify-style), signing out to continue signup flow...',
        );
        await AuthService.signOut();
        _logger.i('🔄 Signed out, proceeding to username step');

        // Proceed directly to username step (no verification)
        emit(state.signupPassword(password: event.password));
      } else {
        // Handle specific error cases
        String errorMessage = 'Failed to create account. Please try again.';
        if (signupResult.error?.contains('already registered') == true ||
            signupResult.error?.contains('already exists') == true) {
          errorMessage = 'This account already exists. Please sign in instead.';
        }
        emit(state.error(errorMessage));
      }
    } catch (e) {
      emit(state.error('Failed to create account. Please try again.'));
    }
  }

  // Handle birthday selection (for Google users)
  Future<void> _onSignupBirthdaySelected(
    SignupBirthdaySelected event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // For Google users, update their profile with birthday
      if (state.isGoogleSignup == true && state.userId != null) {
        _logger.i('🎯 Google user selected birthday: ${event.dateOfBirth}');

        await SupabaseConfig.client
            .from('profiles')
            .update({'date_of_birth': event.dateOfBirth.toIso8601String()})
            .eq('id', state.userId!);

        _logger.i('✅ Updated Google user profile with birthday');
      }

      // Move to username selection
      emit(state.signupBirthday(dateOfBirth: event.dateOfBirth));
    } catch (e) {
      _logger.e('❌ Failed to update birthday: $e');
      emit(state.error('Failed to save birthday. Please try again.'));
    }
  }

  // Handle username selection
  Future<void> _onSignupUsernameSelected(
    SignupUsernameSelected event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Note: Username validation is handled by the UI
      // The UI should only call this event if the username is valid and available
      // So we don't need to re-validate here

      // Check if this is a Google user completing onboarding
      if (state.isGoogleSignup == true && state.userId != null) {
        // Google user - update their profile with the chosen username
        _logger.i('🎯 Google user selected username: ${event.username}');

        try {
          await SupabaseConfig.client
              .from('profiles')
              .update({'username': event.username})
              .eq('id', state.userId!);

          _logger.i('✅ Updated Google user profile with new username');

          // Move to profile setup
          emit(
            state.copyWith(
              signupStep: app_auth.SignupStep.profileSetup,
              tempUsername: event.username,
              isLoading: false,
              errorMessage: null,
            ),
          );
        } catch (e) {
          _logger.e('❌ Failed to update username: $e');
          emit(state.error('Failed to update username. Please try again.'));
        }
      } else {
        // Regular signup flow
        emit(state.signupUsername(username: event.username));
      }
    } catch (e) {
      emit(
        state.error('Failed to check username availability. Please try again.'),
      );
    }
  }

  // Handle profile setup
  Future<void> _onSignupProfileSetup(
    SignupProfileSetup event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      _logger.i('🔄 Setting up user profile...');

      // Validate bio length if provided
      if (event.bio != null && event.bio!.length > 160) {
        emit(state.error('Bio must be 160 characters or less'));
        return;
      }

      // Validate profile image if provided
      if (event.profileImagePath != null) {
        final file = File(event.profileImagePath!);
        if (!await file.exists()) {
          _logger.w(
            '⚠️ Profile image file does not exist: ${event.profileImagePath}',
          );
          emit(state.error('Selected image file is not valid'));
          return;
        }

        // Check file size (max 5MB)
        final fileSize = await file.length();
        if (fileSize > 5 * 1024 * 1024) {
          emit(state.error('Profile image must be less than 5MB'));
          return;
        }
      }

      _logger.i('✅ Profile validation passed');

      // For Google users, update their profile immediately
      if (state.isGoogleSignup == true && state.userId != null) {
        _logger.i('🎯 Updating Google user profile in database...');

        final updateData = <String, dynamic>{};
        if (event.bio != null) updateData['bio'] = event.bio;

        // TODO: Handle profile image upload to Supabase storage
        if (event.profileImagePath != null) {
          _logger.i('📸 Profile image upload will be implemented later');
          // updateData['avatar_url'] = uploadedImageUrl;
        }

        if (updateData.isNotEmpty) {
          await SupabaseConfig.client
              .from('profiles')
              .update(updateData)
              .eq('id', state.userId!);

          _logger.i('✅ Updated Google user profile in database');
        }
      }

      // Store profile data temporarily and move to category selection
      final updatedState = state.signupProfileSetup(
        profileImagePath: event.profileImagePath,
        bio: event.bio,
      );
      emit(updatedState);
    } catch (e) {
      _logger.e('❌ Failed to set up profile: $e');
      emit(state.error('Failed to set up profile. Please try again.'));
    }
  }

  // Handle category selection
  Future<void> _onSignupCategorySelection(
    SignupCategorySelection event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      // Check if this is a Google user completing onboarding
      if (state.isGoogleSignup == true && state.userId != null) {
        // Google user completing onboarding
        _logger.i('🎯 Google user completing category selection...');
        _logger.i('📋 Selected categories: ${event.selectedCategories}');
        _logger.i('👤 User ID: ${state.userId}');

        // Store category preferences in database
        final storeResult = await AuthService.storeCategoryPreferences(
          userId: state.userId!,
          categoryIds: event.selectedCategories,
        );

        if (!storeResult.isSuccess) {
          emit(
            state.error(
              'Failed to save category preferences. Please try again.',
            ),
          );
          return;
        }

        _logger.i('✅ Category preferences stored for Google user');

        // Add a small delay to ensure database consistency
        await Future.delayed(const Duration(milliseconds: 500));

        // Get the updated user profile with retry logic
        UserModel? userProfile;
        int retryCount = 0;
        const maxRetries = 3;

        while (userProfile == null && retryCount < maxRetries) {
          userProfile = await AuthService.getCurrentUserProfile();
          if (userProfile == null) {
            retryCount++;
            _logger.w(
              '🔄 Retry $retryCount: User profile not found, retrying...',
            );
            await Future.delayed(Duration(milliseconds: 500 * retryCount));
          }
        }

        if (userProfile != null) {
          // Update last seen
          await AuthService.updateLastSeen();

          _logger.i('🎉 Google user onboarding completed successfully!');
          _logger.i('👤 User: ${userProfile.username} (${userProfile.email})');

          // Complete authentication
          emit(
            state.authenticated(
              userId: userProfile.id,
              userName: userProfile.username,
              userEmail: userProfile.email,
              userPhone: userProfile.phone,
              userProfileImage: userProfile.profileImageUrl,
              userBio: userProfile.bio,
              accessToken: state.accessToken ?? '',
              userRole: userProfile.role,
            ),
          );
        } else {
          _logger.e(
            '❌ Failed to retrieve user profile after $maxRetries retries',
          );
          emit(
            state.error(
              'Failed to complete signup. Please try signing in again.',
            ),
          );
        }
      } else {
        // Regular signup flow
        final updatedState = state.signupCategorySelection(
          selectedCategories: event.selectedCategories,
        );
        emit(updatedState);

        // Automatically complete signup after category selection
        await Future.delayed(const Duration(milliseconds: 100));

        // Trigger the actual signup completion
        add(SignupCompleted());
      }
    } catch (e) {
      emit(
        state.error('Failed to save category preferences. Please try again.'),
      );
    }
  }

  // Complete signup
  Future<void> _onSignupCompleted(
    SignupCompleted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate required fields
      if (state.tempEmailOrPhone == null ||
          state.tempPassword == null ||
          state.tempUsername == null ||
          state.tempName == null) {
        emit(state.error('Missing required signup information'));
        return;
      }

      // Account is already created, now sign in and update profile
      AuthResult<AuthResponse> signInResult;

      if (state.tempIsEmail!) {
        // Sign in with email and password (account already exists)
        signInResult = await AuthService.signInWithEmail(
          email: state.tempEmailOrPhone!,
          password: state.tempPassword!,
        );
      } else {
        // For phone accounts, sign in with the temporary email we created
        final normalizedPhone = PhoneUtils.normalizePhoneNumber(
          state.tempEmailOrPhone!,
        );
        final tempEmail = PhoneUtils.phoneToTempEmail(normalizedPhone);

        _logger.i(
          '🔄 AuthBloc: Signing in with normalized phone: "$normalizedPhone"',
        );
        _logger.i('🔄 AuthBloc: Using temp email: "$tempEmail"');

        signInResult = await AuthService.signInWithEmail(
          email: tempEmail,
          password: state.tempPassword!,
        );
      }

      if (!signInResult.isSuccess) {
        emit(
          state.error(
            signInResult.error ?? 'Failed to sign in. Please try again.',
          ),
        );
        return;
      }

      // Get the current user (either from signup or signin)
      final currentUser = AuthService.currentUser;
      if (currentUser == null) {
        emit(
          state.error(
            'Account created but authentication failed. Please sign in manually.',
          ),
        );
        return;
      }

      // Update user profile with collected information
      final profileData = {
        'id': currentUser.id,
        'username': state.tempUsername!,
        'full_name': state.tempName!,
        'email':
            state.tempIsEmail == true
                ? state.tempEmailOrPhone!
                : currentUser.email,
        'phone': state.tempIsEmail == false ? state.tempEmailOrPhone! : null,
        'bio': state.tempBio ?? '',
        'avatar_url': state.tempProfileImagePath,
        'date_of_birth': state.tempDateOfBirth?.toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_verified': false,
        'is_private': false,
        'followers_count': 0,
        'following_count': 0,
        'posts_count': 0,
        'role': 'user',
      };

      // Insert/update profile in database
      await SupabaseConfig.client.from('profiles').upsert(profileData);

      // Get the updated user profile
      final userProfile = await AuthService.getCurrentUserProfile();
      if (userProfile != null) {
        // Update last seen
        await AuthService.updateLastSeen();

        emit(
          state.authenticated(
            userId: userProfile.id,
            userName: userProfile.username,
            userEmail: userProfile.email,
            userPhone: userProfile.phone,
            userProfileImage: userProfile.profileImageUrl,
            userBio: userProfile.bio,
            accessToken: AuthService.currentSession?.accessToken ?? '',
            userRole: userProfile.role,
          ),
        );
      } else {
        emit(state.error('Failed to create user profile'));
      }
    } catch (e) {
      String errorMessage = 'Failed to complete signup. Please try again.';
      if (e.toString().contains('duplicate key value')) {
        errorMessage = 'Username already taken. Please choose another.';
      }
      emit(state.error(errorMessage));
    }
  }

  // Handle forgot password with enhanced validation
  Future<void> _onForgotPasswordRequested(
    ForgotPasswordRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate input is not empty
      if (event.emailOrPhone.trim().isEmpty) {
        emit(state.error('Please enter your email, phone, or username'));
        return;
      }

      // Normalize phone numbers like we do in signin
      String identifier = event.emailOrPhone.trim();
      final isPhone = RegExp(r'^\+?[0-9]').hasMatch(identifier);

      if (isPhone) {
        // Normalize phone number for consistency with signin flow
        identifier = PhoneUtils.normalizePhoneNumber(identifier);
        _logger.i(
          '🔄 AuthBloc: Forgot password with normalized phone: "$identifier" (from "${event.emailOrPhone}")',
        );
      }

      // Send password reset OTP (supports email, phone, username)
      final result = await AuthService.sendPasswordResetOTP(
        identifier: identifier,
      );

      if (result.isSuccess) {
        // Store both the original identifier and resolved email
        emit(
          state.copyWith(
            isLoading: false,
            resetEmailOrPhone: event.emailOrPhone,
            resolvedEmail:
                result.data, // The actual email to use for OTP verification
            tempEmailOrPhone: result.data, // For compatibility
            successMessage: 'Reset code sent to your email',
            errorMessage: null,
          ),
        );
      } else {
        // Provide specific error messages
        emit(state.error(result.error!));
      }
    } catch (e) {
      emit(state.error('Failed to send reset code. Please try again.'));
    }
  }

  // Handle password reset code verification with strict validation
  Future<void> _onPasswordResetCodeSubmitted(
    PasswordResetCodeSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate code format first
      if (event.code.trim().isEmpty) {
        emit(state.error('Please enter the reset code'));
        return;
      }

      if (event.code.length != 6) {
        emit(state.error('Please enter a 6-digit reset code'));
        return;
      }

      if (!RegExp(r'^\d{6}$').hasMatch(event.code)) {
        emit(state.error('Reset code must contain only numbers'));
        return;
      }

      // Get the resolved email from the previous step
      if (state.resolvedEmail == null) {
        emit(state.error('Reset session expired. Please start again.'));
        return;
      }

      // Verify password reset OTP with Supabase using resolved email
      final result = await AuthService.verifyPasswordResetOTP(
        email: state.resolvedEmail!,
        token: event.code,
      );

      if (result.isSuccess && result.data != null) {
        // Strict validation: Ensure we have both user and session
        if (result.data!.user != null && result.data!.session != null) {
          // User is now temporarily authenticated and can change password
          emit(state.passwordResetVerified(code: event.code));
        } else {
          emit(
            state.error('Invalid OTP. Please check your code and try again.'),
          );
        }
      } else {
        // Provide the specific error message from the service
        emit(
          state.error(
            result.error ??
                'Invalid OTP. Please check your code and try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Invalid OTP. Please check your code and try again.'));
    }
  }

  // Handle password reset OTP resend
  Future<void> _onPasswordResetOTPResendRequested(
    PasswordResetOTPResendRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Resend password reset OTP
      final result = await AuthService.resendPasswordResetOTP(
        email: event.email,
      );

      if (result.isSuccess) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: null,
            successMessage: 'Reset code sent again to your email',
          ),
        );
      } else {
        emit(
          state.error(
            result.error ?? 'Failed to resend reset code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Failed to resend reset code. Please try again.'));
    }
  }

  // Handle new password submission with real password update
  Future<void> _onNewPasswordSubmitted(
    NewPasswordSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate password strength
      if (event.newPassword.length < 8) {
        emit(state.error('Password must be at least 8 characters long'));
        return;
      }

      // Update password
      final result = await AuthService.updatePassword(
        newPassword: event.newPassword,
      );

      if (result.isSuccess) {
        emit(state.passwordResetCompleted());
      } else {
        emit(
          state.error(
            result.error ?? 'Failed to update password. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Failed to reset password. Please try again.'));
    }
  }

  // Handle Google sign in (Spotify-style - no context checking)
  Future<void> _onGoogleSignInRequested(
    GoogleSignInRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      _logger.i('🔐 Starting Google Sign-In process...');

      // Initialize Google Sign-In service
      await GoogleSignInService.initialize();

      // Sign in with Google
      final googleSignInResult = await GoogleSignInService.signIn();
      if (!googleSignInResult.isSuccess) {
        emit(state.error('Google Sign-In failed. Please try again.'));
        return;
      }

      final googleUser = googleSignInResult.data!;
      _logger.i('✅ Google Sign-In successful for: ${googleUser.email}');

      // Get Google ID token using the already authenticated user
      final idTokenResult = await GoogleSignInService.getIdToken(googleUser);
      if (!idTokenResult.isSuccess) {
        emit(state.error('Google Sign-In failed. Please try again.'));
        return;
      }

      final idToken = idTokenResult.data!;
      _logger.i('🎫 Google ID token retrieved successfully');

      // Get Google access token (optional) using the already authenticated user
      final accessTokenResult = await GoogleSignInService.getAccessToken(
        googleUser,
      );
      final accessToken =
          accessTokenResult.isSuccess ? accessTokenResult.data : null;

      // Sign in to Supabase with Google tokens
      final supabaseResult = await AuthService.signInWithGoogle(
        idToken: idToken,
        accessToken: accessToken,
      );

      if (!supabaseResult.isSuccess) {
        emit(state.error('Google Sign-In failed. Please try again.'));
        return;
      }

      final authResponse = supabaseResult.data!;
      final user = authResponse.user;

      if (user == null) {
        emit(state.error('Google Sign-In failed. Please try again.'));
        return;
      }

      _logger.i('🎉 Google Sign-In completed successfully!');
      _logger.i('👤 User ID: ${user.id}');
      _logger.i('📧 Email: ${user.email}');

      // Extract user information
      final userMetadata = user.userMetadata ?? {};
      final userName =
          userMetadata['full_name'] ??
          userMetadata['name'] ??
          user.email?.split('@').first ??
          'User';

      // Check if user has completed onboarding (Spotify-style approach)
      final onboardingResult = await AuthService.hasCompletedOnboarding(
        userId: user.id,
      );
      if (!onboardingResult.isSuccess) {
        _logger.e(
          '❌ Failed to check onboarding status: ${onboardingResult.error}',
        );
        emit(state.error('Google Sign-In failed. Please try again.'));
        return;
      }

      final hasCompletedOnboarding = onboardingResult.data ?? false;
      _logger.i(
        '🔍 User onboarding status: ${hasCompletedOnboarding ? 'COMPLETED' : 'NEEDS_ONBOARDING'}',
      );

      if (!hasCompletedOnboarding) {
        // New user - Spotify-style seamless onboarding
        _logger.i(
          '🆕 New Google user detected, starting seamless onboarding...',
        );

        // Generate a unique username
        final usernameResult = await AuthService.generateUniqueUsername(
          userName,
        );
        if (!usernameResult.isSuccess) {
          emit(state.error('Google Sign-In failed. Please try again.'));
          return;
        }

        final uniqueUsername = usernameResult.data!;
        _logger.i('✅ Generated unique username: $uniqueUsername');

        // Update the profile created by the database trigger
        // Note: The trigger already created a basic profile, we just need to update it
        final profileData = {
          'username': uniqueUsername,
          'full_name': userName,
          'email': user.email,
          'avatar_url': userMetadata['avatar_url'],
          'role': 'user',
          'is_verified': false,
          'is_business': false,
          'onboarding_completed': false, // Key: Mark as not completed
          'preferences': {}, // Will be updated after category selection
        };

        try {
          // Use update instead of insert since the trigger already created the profile
          // Add a small delay to ensure the trigger has completed
          await Future.delayed(const Duration(milliseconds: 100));

          final result =
              await SupabaseConfig.client
                  .from('profiles')
                  .update(profileData)
                  .eq('id', user.id)
                  .select();

          if (result.isEmpty) {
            // If update didn't affect any rows, the profile might not exist yet
            // Fall back to upsert to handle edge cases
            _logger.w('⚠️ Profile update affected 0 rows, trying upsert...');
            await SupabaseConfig.client.from('profiles').upsert({
              ...profileData,
              'id': user.id,
            });
          }

          _logger.i('✅ Updated profile for new Google user');
        } catch (e) {
          _logger.e('❌ Failed to update profile: $e');
          emit(state.error('Google Sign-In failed. Please try again.'));
          return;
        }

        // Route to birthday selection for Google users (Spotify-style flow)
        _logger.i('🎯 Routing Google user to birthday selection...');
        _logger.i(
          '📋 User details: ID=${user.id}, Name=$userName, Email=${user.email}',
        );

        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            signupStep: app_auth.SignupStep.birthday,
            isLoading: false,
            userId: user.id,
            userName: userName,
            userEmail: user.email,
            userProfileImage: userMetadata['avatar_url'],
            accessToken: authResponse.session?.accessToken,
            isGoogleSignup: true,
            tempUsername:
                uniqueUsername, // Store the generated username as suggestion
            errorMessage: null,
          ),
        );
      } else {
        // Existing user - Spotify-style seamless sign-in (no context checking)
        _logger.i('👋 Existing Google user, proceeding to home...');

        // Get the existing user profile
        final userProfile = await AuthService.getCurrentUserProfile();
        if (userProfile != null) {
          // Update last seen
          await AuthService.updateLastSeen();

          emit(
            state.authenticated(
              userId: userProfile.id,
              userName: userProfile.username,
              userEmail: userProfile.email,
              userPhone: userProfile.phone,
              userProfileImage: userProfile.profileImageUrl,
              userBio: userProfile.bio,
              accessToken: authResponse.session?.accessToken,
              userRole: userProfile.role,
            ),
          );
        } else {
          emit(state.error('User profile not found'));
        }
      }
    } catch (e) {
      _logger.e('❌ Google Sign-In error: $e');
      emit(state.error('Google Sign-In failed. Please try again.'));
    }
  }

  // Handle Google sign-in completed
  Future<void> _onGoogleSignInCompleted(
    GoogleSignInCompleted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    _logger.i('🎯 Google sign-in completed for: ${event.userName}');

    // Emit state to show confirmation popup
    emit(
      state.googleSignInCompleted(
        userId: event.userId,
        userName: event.userName,
        userEmail: event.userEmail,
        userProfileImage: event.userProfileImage,
        accessToken: event.accessToken,
      ),
    );
  }

  // Handle Google onboarding continue request
  Future<void> _onGoogleOnboardingContinueRequested(
    GoogleOnboardingContinueRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    _logger.i('🎯 User confirmed to continue with Google onboarding');

    // Transition to category selection
    emit(
      state.googleUserNeedsOnboarding(
        userId: state.userId!,
        userName: state.userName!,
        userEmail: state.userEmail,
        userProfileImage: state.userProfileImage,
        accessToken: state.accessToken,
      ),
    );
  }

  // Handle Google user needs onboarding
  Future<void> _onGoogleUserNeedsOnboarding(
    GoogleUserNeedsOnboarding event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    _logger.i('🎯 Routing Google user to onboarding: ${event.userName}');

    // Emit state to show category selection for Google user
    emit(
      state.googleUserNeedsOnboarding(
        userId: event.userId,
        userName: event.userName,
        userEmail: event.userEmail,
        userProfileImage: event.userProfileImage,
        accessToken: event.accessToken,
      ),
    );
  }

  // Handle Apple sign in
  Future<void> _onAppleSignInRequested(
    AppleSignInRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // TODO: Implement Apple Sign In
      await Future.delayed(const Duration(seconds: 2));
      emit(state.error('Apple Sign In not implemented yet'));
    } catch (e) {
      emit(state.error('Apple Sign In failed. Please try again.'));
    }
  }

  // Handle email verification request
  Future<void> _onEmailVerificationRequested(
    EmailVerificationRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Check if email is already verified
      final verificationResult = await AuthService.isEmailVerified(
        email: event.email,
      );

      if (verificationResult.isSuccess && verificationResult.data == true) {
        // Email is already verified, try to sign in again
        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            loginStep: app_auth.LoginStep.identifier,
            isLoading: false,
            errorMessage: null,
            successMessage: 'Email verified! Please try signing in again.',
          ),
        );
      } else {
        // Email not verified, stay in verification needed state
        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            loginStep: app_auth.LoginStep.emailVerificationNeeded,
            isLoading: false,
            tempEmailOrPhone: event.email,
            errorMessage:
                'Email not yet verified. Please check your inbox and click the verification link.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Failed to check email verification status.'));
    }
  }

  // Handle email verification resend request
  Future<void> _onEmailVerificationResendRequested(
    EmailVerificationResendRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      final result = await AuthService.resendEmailVerification(
        email: event.email,
      );

      if (result.isSuccess) {
        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            loginStep: app_auth.LoginStep.emailVerificationNeeded,
            isLoading: false,
            tempEmailOrPhone: event.email,
            errorMessage: null,
            successMessage: 'Verification email sent! Please check your inbox.',
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: app_auth.AuthStatus.unauthenticated,
            loginStep: app_auth.LoginStep.emailVerificationNeeded,
            isLoading: false,
            tempEmailOrPhone: event.email,
            errorMessage: result.error ?? 'Failed to send verification email.',
          ),
        );
      }
    } catch (e) {
      emit(
        state.error('Failed to resend verification email. Please try again.'),
      );
    }
  }

  // Handle logout with enhanced error handling
  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Sign out from Supabase with enhanced error handling
      final result = await AuthService.signOut();

      if (result.isSuccess) {
        emit(state.unauthenticated());
      } else {
        emit(state.error(result.error ?? 'Logout failed. Please try again.'));
      }
    } catch (e) {
      emit(state.error('Logout failed. Please try again.'));
    }
  }

  // Clear error
  Future<void> _onAuthClearError(
    AuthClearError event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.copyWith(errorMessage: null, successMessage: null));
  }

  // ==================== NEW SECURITY HANDLERS ====================

  // Handle new device verification request
  Future<void> _onNewDeviceVerificationRequested(
    NewDeviceVerificationRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Send new device login OTP
      final result = await AuthService.sendNewDeviceLoginOTP(
        email: event.email,
      );

      if (result.isSuccess) {
        emit(state.newDeviceVerificationSent(email: event.email));
      } else {
        emit(
          state.error(
            result.error ??
                'Failed to send verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Failed to send verification code. Please try again.'));
    }
  }

  // Handle new device OTP submission
  Future<void> _onNewDeviceOTPSubmitted(
    NewDeviceOTPSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate code format
      if (event.code.length != 6) {
        emit(state.error('Please enter a 6-digit verification code'));
        return;
      }

      // Get the email from the previous step
      if (state.tempEmailOrPhone == null) {
        emit(state.error('Verification session expired. Please start again.'));
        return;
      }

      // Verify new device OTP
      final result = await AuthService.verifyNewDeviceLoginOTP(
        email: state.tempEmailOrPhone!,
        token: event.code,
      );

      if (result.isSuccess) {
        emit(state.newDeviceVerified());
      } else {
        emit(
          state.error(
            result.error ??
                'Invalid or expired verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Verification failed. Please try again.'));
    }
  }

  // Handle email change request
  Future<void> _onEmailChangeRequested(
    EmailChangeRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate email format
      if (!event.newEmail.contains('@') || !event.newEmail.contains('.')) {
        emit(state.error('Please enter a valid email address'));
        return;
      }

      // Send email change OTP
      final result = await AuthService.sendEmailChangeOTP(
        newEmail: event.newEmail,
      );

      if (result.isSuccess) {
        emit(state.emailChangeRequested(newEmail: event.newEmail));
      } else {
        emit(
          state.error(
            result.error ??
                'Failed to send verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Failed to send verification code. Please try again.'));
    }
  }

  // Handle email change OTP submission
  Future<void> _onEmailChangeOTPSubmitted(
    EmailChangeOTPSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate code format
      if (event.code.length != 6) {
        emit(state.error('Please enter a 6-digit verification code'));
        return;
      }

      // Get the new email from the previous step
      if (state.newEmailPending == null) {
        emit(state.error('Email change session expired. Please start again.'));
        return;
      }

      // Verify email change OTP
      final result = await AuthService.verifyEmailChangeOTP(
        email: state.newEmailPending!,
        token: event.code,
      );

      if (result.isSuccess) {
        // Update user email in Supabase
        final updateResult = await AuthService.updateUserEmail(
          newEmail: state.newEmailPending!,
        );

        if (updateResult.isSuccess) {
          emit(state.emailChangeCompleted());
        } else {
          emit(
            state.error(
              updateResult.error ?? 'Failed to update email. Please try again.',
            ),
          );
        }
      } else {
        emit(
          state.error(
            result.error ??
                'Invalid or expired verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Email change failed. Please try again.'));
    }
  }

  // Handle security operation request
  Future<void> _onSecurityOperationRequested(
    SecurityOperationRequested event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Send security alert OTP
      final result = await AuthService.sendSecurityAlertOTP(
        email: event.email,
        operation: event.operation,
      );

      if (result.isSuccess) {
        emit(state.securityOperationRequested(operation: event.operation));
      } else {
        emit(
          state.error(
            result.error ??
                'Failed to send security verification. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(
        state.error('Failed to send security verification. Please try again.'),
      );
    }
  }

  // Handle security OTP submission
  Future<void> _onSecurityOTPSubmitted(
    SecurityOTPSubmitted event,
    Emitter<app_auth.AuthState> emit,
  ) async {
    emit(state.loading());

    try {
      // Validate code format
      if (event.code.length != 6) {
        emit(state.error('Please enter a 6-digit verification code'));
        return;
      }

      // Get the current user email
      final currentUser = SupabaseConfig.currentUser;
      if (currentUser?.email == null) {
        emit(state.error('User session expired. Please sign in again.'));
        return;
      }

      // Verify security OTP
      final result = await AuthService.verifySecurityOTP(
        email: currentUser!.email!,
        token: event.code,
      );

      if (result.isSuccess && result.data == true) {
        emit(state.securityOperationCompleted());
      } else {
        emit(
          state.error(
            'Invalid or expired verification code. Please try again.',
          ),
        );
      }
    } catch (e) {
      emit(state.error('Security verification failed. Please try again.'));
    }
  }
}
