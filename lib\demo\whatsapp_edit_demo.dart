import 'package:flutter/material.dart';
import 'package:business_app/components/whatsapp_edit_dialog.dart';

/// Demo page showing WhatsApp-style edit functionality
/// This demonstrates how the edit dialog works with different field types
class WhatsAppEditDemo extends StatefulWidget {
  const WhatsAppEditDemo({super.key});

  @override
  State<WhatsAppEditDemo> createState() => _WhatsAppEditDemoState();
}

class _WhatsAppEditDemoState extends State<WhatsAppEditDemo> {
  // Demo data
  final Map<String, String> _demoData = {
    'name': '<PERSON>',
    'bio':
        'Fashion designer and entrepreneur passionate about creating beautiful designs that inspire confidence.',
    'location': 'Lilongwe, Malawi',
    'website': 'www.chris-fashions.com',
    'phone': '+265 991 234 567',
    'email': '<EMAIL>',
  };

  // Character limits for different fields
  final Map<String, int> _limits = {
    'name': 50,
    'bio': 160,
    'location': 60,
    'website': 50,
    'phone': 20,
    'email': 50,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'WhatsApp Edit Demo',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF25D366),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF25D366).withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.edit_rounded,
                    size: 48,
                    color: const Color(0xFF25D366),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Tap-to-Edit Demo',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Tap any field below to edit with WhatsApp-style dialog',
                    style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Demo fields
            _buildDemoCard('Personal Information', [
              _buildEditableField('name', 'Name', Icons.person, Colors.blue),
              _buildEditableField(
                'bio',
                'Bio',
                Icons.info,
                Colors.purple,
                maxLines: 3,
              ),
              _buildEditableField(
                'location',
                'Location',
                Icons.location_on,
                Colors.red,
              ),
            ]),

            const SizedBox(height: 16),

            _buildDemoCard('Contact Information', [
              _buildEditableField(
                'website',
                'Website',
                Icons.link,
                Colors.orange,
              ),
              _buildEditableField('phone', 'Phone', Icons.phone, Colors.green),
              _buildEditableField('email', 'Email', Icons.email, Colors.indigo),
            ]),

            const SizedBox(height: 24),

            // Features list
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Features',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem('🎨', 'Stunning animations (scale + fade)'),
                  _buildFeatureItem(
                    '📝',
                    'Character limits with visual feedback',
                  ),
                  _buildFeatureItem('⚡', 'Auto-focus on text field'),
                  _buildFeatureItem('🔄', 'Change detection'),
                  _buildFeatureItem('⚠️', 'Discard confirmation'),
                  _buildFeatureItem('🌙', 'Dark/Light theme support'),
                  _buildFeatureItem('📱', 'WhatsApp-inspired design'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoCard(String title, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          ...children,
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildEditableField(
    String key,
    String label,
    IconData icon,
    Color color, {
    int maxLines = 1,
  }) {
    return InkWell(
      onTap: () => _showEditDialog(key, label, icon, color, maxLines),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _demoData[key] ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: maxLines,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Icon(Icons.edit, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(child: Text(text, style: const TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  void _showEditDialog(
    String key,
    String label,
    IconData icon,
    Color color,
    int maxLines,
  ) {
    WhatsAppEditDialog.show(
      context: context,
      title: label,
      initialValue: _demoData[key] ?? '',
      maxLength: _limits[key] ?? 50,
      icon: icon,
      accentColor: color,
      maxLines: maxLines,
      hintText: 'Enter your ${label.toLowerCase()}...',
      onSave: (String newValue) {
        setState(() {
          _demoData[key] = newValue;
        });

        // Show success feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$label updated successfully!'),
            backgroundColor: const Color(0xFF25D366),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(milliseconds: 800),
          ),
        );
      },
    );
  }
}
