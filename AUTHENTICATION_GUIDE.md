# Complete Authentication Guide

This guide explains the complete authentication system with proper separation of email+password and OTP (magic link) authentication methods.

## 🔧 Authentication Methods

### 1. Email + Password Authentication (Traditional)
- User provides email and password
- Uses `signInWithPassword()` 
- Requires email verification if enabled in Supabase

### 2. Email OTP Authentication (Magic Link)
- User provides only email
- Uses `signInWithOtp()`
- No password required
- Sends magic link to email

## 📋 Implementation

### AuthService Methods

```dart
// Method 1: Traditional Email + Password
static Future<AuthResult<AuthResponse>> signInWithEmail({
  required String email,
  required String password,
}) async {
  final response = await BaseSupabaseService.client.auth.signInWithPassword(
    email: email,
    password: password,
  );
  // Returns user session if successful
}

// Method 2: Email OTP (Magic Link)
static Future<AuthResult<void>> signInWithEmailOTP({
  required String email,
  String? emailRedirectTo,
}) async {
  await BaseSupabaseService.client.auth.signInWithOtp(
    email: email,
    emailRedirectTo: emailRedirectTo ?? 'myapp://login-callback',
    shouldCreateUser: false,
  );
  // Sends magic link to email
}

// Email Verification Helpers
static Future<AuthResult<void>> resendEmailVerification({
  required String email,
}) async {
  await BaseSupabaseService.client.auth.resend(
    type: OtpType.signup,
    email: email,
  );
}

static Future<AuthResult<bool>> isEmailVerified({
  required String email,
}) async {
  // Checks if user's email is verified
}
```

### Auth Events

```dart
// Traditional login with password
class LoginRequested extends AuthEvent {
  final String identifier; // email, phone, or username
  final String password;
}

// OTP login (magic link) - NO PASSWORD
class EmailOtpLoginRequested extends AuthEvent {
  final String email;
  final String? emailRedirectTo;
}

// Email verification events
class EmailVerificationRequested extends AuthEvent {
  final String email;
}

class EmailVerificationResendRequested extends AuthEvent {
  final String email;
}
```

### Auth States

```dart
enum LoginStep {
  identifier,
  password,
  otpVerification,
  emailVerificationNeeded, // For unverified emails
}
```

## 🚀 Usage Examples

### In Your Login UI

```dart
// Traditional Email + Password Login
context.read<AuthBloc>().add(
  LoginRequested(
    identifier: '<EMAIL>',
    password: 'userpassword123',
  ),
);

// Magic Link Login
context.read<AuthBloc>().add(
  EmailOtpLoginRequested(
    email: '<EMAIL>',
    emailRedirectTo: 'myapp://login-callback',
  ),
);

// Resend Email Verification
context.read<AuthBloc>().add(
  EmailVerificationResendRequested(
    email: '<EMAIL>',
  ),
);
```

### Handling Auth States

```dart
BlocBuilder<AuthBloc, AuthState>(
  builder: (context, state) {
    switch (state.loginStep) {
      case LoginStep.identifier:
        return LoginForm(); // Show email/password form
        
      case LoginStep.otpVerification:
        return OtpVerificationWidget(); // Show "check your email" message
        
      case LoginStep.emailVerificationNeeded:
        return EmailVerificationWidget(
          email: state.tempEmailOrPhone ?? '',
        ); // Show verification needed UI
        
      default:
        return LoginForm();
    }
  },
)
```

## 🔍 Error Handling

### Common Error Scenarios

1. **Email Not Confirmed** (Traditional Login)
   - Error: "Please verify your email before signing in"
   - Solution: Show EmailVerificationWidget
   - User can resend verification or check email

2. **Invalid Credentials** (Traditional Login)
   - Error: "Invalid email or password"
   - Solution: User corrects credentials

3. **Magic Link Sent** (OTP Login)
   - Success: "Magic link sent! Check your email"
   - User clicks link in email to complete login

4. **User Not Found**
   - Error: "No account found with this email"
   - Solution: Redirect to signup

## 📧 Deep Link Configuration

For magic links to work, configure deep linking:

### Android (android/app/src/main/AndroidManifest.xml)
```xml
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="myapp" />
</intent-filter>
```

### iOS (ios/Runner/Info.plist)
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>myapp</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>myapp</string>
        </array>
    </dict>
</array>
```

## 🧪 Testing

Run the test script to verify both authentication methods:

```bash
dart test_auth.dart
```

The test will:
- Test traditional email+password login
- Test magic link OTP login
- Test email verification status
- Test resending verification emails

## 📱 UI Components

### Available Widgets

1. **DualLoginWidget** - Offers both login methods
2. **EmailVerificationWidget** - Handles email verification flow
3. **OtpVerificationWidget** - Shows "check your email" message

### Integration Example

```dart
// In your login page
BlocBuilder<AuthBloc, AuthState>(
  builder: (context, state) {
    if (state.loginStep == LoginStep.emailVerificationNeeded) {
      return EmailVerificationWidget(
        email: state.tempEmailOrPhone ?? '',
      );
    }
    
    if (state.loginStep == LoginStep.otpVerification) {
      return OtpVerificationWidget(
        email: state.tempEmailOrPhone ?? '',
      );
    }
    
    return DualLoginWidget(); // Default login form
  },
)
```

## ✅ Best Practices

1. **Separate Methods**: Never mix password and OTP authentication
2. **Clear UI**: Make it obvious which method user is choosing
3. **Error Handling**: Provide specific, actionable error messages
4. **Deep Links**: Properly configure for magic links
5. **Testing**: Test both flows thoroughly
6. **Security**: Use PKCE flow for enhanced security

This complete system provides a professional, user-friendly authentication experience with proper error handling and multiple login options.
