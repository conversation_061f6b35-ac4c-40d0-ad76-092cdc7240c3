import 'dart:io';
import 'package:equatable/equatable.dart';

abstract class Profile<PERSON>vent extends Equatable {
  @override
  List<Object> get props => [];
}

class LoadProfileEvent extends ProfileEvent {}

class UpdateProfileEvent extends ProfileEvent {
  final String? name;
  final String? username;
  final String? bio;
  final String? location;
  final String? website;
  final String? profileImageUrl;
  final String? backgroundImageUrl;
  final File? profileImageFile;
  final File? backgroundImageFile;

  UpdateProfileEvent({
    this.name,
    this.username,
    this.bio,
    this.location,
    this.website,
    this.profileImageUrl,
    this.backgroundImageUrl,
    this.profileImageFile,
    this.backgroundImageFile,
  });

  @override
  List<Object> get props => [
    name ?? '',
    username ?? '',
    bio ?? '',
    location ?? '',
    website ?? '',
    profileImageUrl ?? '',
    backgroundImageUrl ?? '',
    profileImageFile?.path ?? '',
    backgroundImageFile?.path ?? '',
  ];
}

class CheckUsernameAvailabilityEvent extends ProfileEvent {
  final String username;

  CheckUsernameAvailabilityEvent({required this.username});

  @override
  List<Object> get props => [username];
}

class ClearUsernameValidationEvent extends ProfileEvent {}

class ToggleFollowEvent extends ProfileEvent {
  final String userId;

  ToggleFollowEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

class ChangeProfileTabEvent extends ProfileEvent {
  final int tabIndex;

  ChangeProfileTabEvent(this.tabIndex);

  @override
  List<Object> get props => [tabIndex];
}

class ShowProfileOptionsEvent extends ProfileEvent {}

class HideProfileOptionsEvent extends ProfileEvent {}

class UpdateFollowersCountEvent extends ProfileEvent {
  final int count;

  UpdateFollowersCountEvent(this.count);

  @override
  List<Object> get props => [count];
}

class UpdateFollowingCountEvent extends ProfileEvent {
  final int count;

  UpdateFollowingCountEvent(this.count);

  @override
  List<Object> get props => [count];
}
