import 'package:equatable/equatable.dart';

abstract class Posts<PERSON>vent extends Equatable {
  @override
  List<Object> get props => [];
}

class LoadPostsEvent extends PostsEvent {}

class RefreshPostsEvent extends PostsEvent {}

class LoadMorePostsEvent extends PostsEvent {}

class LikePostEvent extends PostsEvent {
  final String postId;

  LikePostEvent(this.postId);

  @override
  List<Object> get props => [postId];
}

class UnlikePostEvent extends PostsEvent {
  final String postId;

  UnlikePostEvent(this.postId);

  @override
  List<Object> get props => [postId];
}

class SharePostEvent extends PostsEvent {
  final String postId;

  SharePostEvent(this.postId);

  @override
  List<Object> get props => [postId];
}

class CommentOnPostEvent extends PostsEvent {
  final String postId;
  final String comment;

  CommentOnPostEvent(this.postId, this.comment);

  @override
  List<Object> get props => [postId, comment];
}

class DeletePostEvent extends PostsEvent {
  final String postId;

  DeletePostEvent(this.postId);

  @override
  List<Object> get props => [postId];
}

class ReportPostEvent extends PostsEvent {
  final String postId;
  final String reason;

  ReportPostEvent(this.postId, this.reason);

  @override
  List<Object> get props => [postId, reason];
}

class ChangePostTabEvent extends PostsEvent {
  final int tabIndex;

  ChangePostTabEvent(this.tabIndex);

  @override
  List<Object> get props => [tabIndex];
}

class ToggleLikeEvent extends PostsEvent {
  final String postId;

  ToggleLikeEvent(this.postId);

  @override
  List<Object> get props => [postId];
}

class ToggleBookmarkEvent extends PostsEvent {
  final String postId;

  ToggleBookmarkEvent(this.postId);

  @override
  List<Object> get props => [postId];
}
