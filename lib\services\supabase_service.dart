import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import '../supabase/config.dart';
import '../models/auth/user_model.dart';
import '../models/auth/auth_result.dart';
import '../utils/phone_utils.dart';
import 'error_handler_service.dart';

/// Base service class for all Supabase operations
/// Provides common error handling and utilities
abstract class BaseSupabaseService {
  static SupabaseClient get client => SupabaseConfig.client;

  /// Handle Supabase exceptions and convert to app-specific exceptions
  static Never handleException(dynamic error) {
    if (error is PostgrestException) {
      throw SupabaseException(
        'Database Error: ${error.message}',
        code: error.code,
        details:
            error.details is Map<String, dynamic>
                ? error.details as Map<String, dynamic>
                : null,
      );
    } else if (error is AuthException) {
      throw SupabaseException(
        'Authentication Error: ${error.message}',
        code: error.statusCode,
      );
    } else if (error is StorageException) {
      throw SupabaseException(
        'Storage Error: ${error.message}',
        code: error.statusCode,
      );
    } else {
      throw SupabaseException('Unexpected Error: $error');
    }
  }

  /// Execute query with error handling
  static Future<T> executeQuery<T>(Future<T> Function() query) async {
    try {
      return await query();
    } catch (e) {
      handleException(e);
    }
  }

  /// Execute RPC with error handling
  static Future<T?> executeRPC<T>(
    String functionName,
    Map<String, dynamic>? params,
  ) async {
    try {
      final response = await client.rpc(functionName, params: params);
      return response as T?;
    } catch (e) {
      handleException(e);
    }
  }
}

/// Professional Authentication service with comprehensive Supabase integration
class AuthService extends BaseSupabaseService {
  static const String _logTag = '🔐 AuthService';

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  /// Authentication result wrapper for better error handling
  static AuthResult<T> _createResult<T>(T? data, String? error) {
    return AuthResult<T>(
      data: data,
      error: error,
      isSuccess: error == null, // Success when no error, regardless of data
    );
  }

  /// Get current user session
  static Session? get currentSession =>
      BaseSupabaseService.client.auth.currentSession;

  /// Get current user
  static User? get currentUser => BaseSupabaseService.client.auth.currentUser;

  /// Check if user is authenticated
  static bool get isAuthenticated =>
      currentUser != null && currentSession != null;

  /// Listen to authentication state changes
  static Stream<AuthState> get authStateChanges =>
      BaseSupabaseService.client.auth.onAuthStateChange;

  // ==================== PRIVATE HELPER METHODS ====================

  /// Handle authentication errors with user-friendly messages and proper logging
  static String _handleAuthError(dynamic exception, {String? context}) {
    final authError = ErrorHandlerService.handleAuthError(
      exception,
      context: context,
    );
    return authError.message;
  }

  /// Check if user exists in the system by email
  static Future<bool> _checkUserExists(String email) async {
    try {
      // Try to get user from profiles table
      final response =
          await BaseSupabaseService.client
              .from('profiles')
              .select('id')
              .eq('email', email)
              .maybeSingle();

      return response != null;
    } catch (e) {
      // If we can't check, assume user exists to avoid blocking legitimate users
      _logger.w('$_logTag ⚠️ Could not verify user existence: $e');
      return true;
    }
  }

  /// Public method to check if user exists by email (for signup validation)
  static Future<bool> checkUserExistsByEmail(String email) async {
    _logger.i('$_logTag Checking if user exists with email: $email');
    try {
      // Check both auth.users and profiles tables for comprehensive check
      final profileResponse =
          await BaseSupabaseService.client
              .from('profiles')
              .select('id')
              .eq('email', email)
              .maybeSingle();

      if (profileResponse != null) {
        _logger.i('$_logTag ✅ User found in profiles table');
        return true;
      }

      // Also check auth.users table as backup
      try {
        final authResponse =
            await BaseSupabaseService.client
                .from('auth.users')
                .select('id')
                .eq('email', email)
                .maybeSingle();

        if (authResponse != null) {
          _logger.i('$_logTag ✅ User found in auth.users table');
          return true;
        }
      } catch (authError) {
        _logger.w('$_logTag ⚠️ Could not check auth.users table: $authError');
      }

      _logger.i('$_logTag ❌ No user found with email: $email');
      return false;
    } catch (e) {
      _logger.e('$_logTag ❌ Error checking user existence by email: $e');
      // Return false to allow signup attempt (fail open for better UX)
      return false;
    }
  }

  /// Public method to check if user exists by phone number (for signup validation)
  static Future<bool> checkUserExistsByPhone(String phone) async {
    _logger.i('$_logTag Checking if user exists with phone: $phone');
    try {
      // Check profiles table for phone number
      final response =
          await BaseSupabaseService.client
              .from('profiles')
              .select('id')
              .eq('phone', phone)
              .maybeSingle();

      if (response != null) {
        _logger.i('$_logTag ✅ User found with phone number');
        return true;
      }

      _logger.i('$_logTag ❌ No user found with phone: $phone');
      return false;
    } catch (e) {
      _logger.e('$_logTag ❌ Error checking user existence by phone: $e');
      // Return false to allow signup attempt (fail open for better UX)
      return false;
    }
  }

  /// Resolve identifier (email, phone, or username) to email address
  static Future<AuthResult<String>> _resolveIdentifierToEmail(
    String identifier,
  ) async {
    try {
      // If it's already an email, validate it exists
      if (identifier.contains('@')) {
        final exists = await _checkUserExists(identifier);
        if (!exists) {
          return _createResult<String>(
            null,
            'No account found with this email address',
          );
        }
        return _createResult<String>(identifier, null);
      }

      // Check if it's a phone number - use same logic as AuthBloc for consistency
      final isPhone = RegExp(r'^\+?[0-9]').hasMatch(identifier);
      _logger.i(
        '$_logTag 🔍 Identifier analysis: "$identifier" -> isPhone: $isPhone',
      );

      if (isPhone) {
        // Normalize phone number and convert to temp email format
        // This matches how phone users are stored during signup
        final normalizedPhone = PhoneUtils.normalizePhoneNumber(identifier);
        if (normalizedPhone.isEmpty) {
          return _createResult<String>(null, 'Invalid phone number format');
        }

        final tempEmail = PhoneUtils.phoneToTempEmail(normalizedPhone);
        _logger.i(
          '$_logTag 🔄 Converted phone "$identifier" -> "$normalizedPhone" -> "$tempEmail"',
        );

        // Check if user exists with this temp email format
        final exists = await _checkUserExists(tempEmail);
        if (!exists) {
          return _createResult<String>(
            null,
            'No account found with this phone number',
          );
        }

        return _createResult<String>(tempEmail, null);
      }

      // Assume it's a username
      final response =
          await BaseSupabaseService.client
              .from('profiles')
              .select('email')
              .eq('username', identifier)
              .maybeSingle();

      if (response == null || response['email'] == null) {
        return _createResult<String>(
          null,
          'No account found with this username',
        );
      }
      return _createResult<String>(response['email'] as String, null);
    } catch (e) {
      final error = _handleAuthError(e, context: 'Identifier Resolution');
      return _createResult<String>(null, error);
    }
  }

  // ==================== EMAIL AUTHENTICATION ====================

  /// Sign up with email and password
  static Future<AuthResult<AuthResponse>> signUpWithEmail({
    required String email,
    required String password,
    required String fullName,
    Map<String, dynamic>? additionalData,
  }) async {
    _logger.i('$_logTag Attempting email signup for: $email');

    try {
      final response = await BaseSupabaseService.client.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'display_name': fullName,
          'email': email,
          ...?additionalData,
        },
      );

      if (response.user != null) {
        _logger.i(
          '$_logTag ✅ Email signup successful! User ID: ${response.user!.id}',
        );
        _logger.i(
          '$_logTag 📧 Email confirmation required: ${response.user!.emailConfirmedAt == null}',
        );

        return _createResult(response, null);
      } else {
        const error = 'Signup failed: No user data received';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e, context: 'Email Signup');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Sign in with email and password (traditional login)
  static Future<AuthResult<AuthResponse>> signInWithEmail({
    required String email,
    required String password,
  }) async {
    _logger.i('$_logTag Attempting email+password signin for: $email');

    try {
      // First check if user exists to provide specific error message
      final userExists = await _checkUserExists(email);
      if (!userExists) {
        const error = 'No account found with this email address';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }

      _logger.i('$_logTag 🔍 Calling Supabase signInWithPassword...');
      _logger.i('$_logTag 📧 Email being used: "$email"');
      _logger.i('$_logTag 🔐 Password length: ${password.length}');

      final response = await BaseSupabaseService.client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      _logger.i('$_logTag 📋 Response received:');
      _logger.i('   User: ${response.user?.id}');
      _logger.i(
        '   Session: ${response.session?.accessToken != null ? 'Present' : 'Null'}',
      );

      if (response.user != null) {
        _logger.i(
          '$_logTag ✅ Email+password signin successful! User ID: ${response.user!.id}',
        );
        _logger.i(
          '$_logTag 🔑 Session expires: ${response.session?.expiresAt}',
        );

        return _createResult(response, null);
      } else {
        const error = 'Sign in failed: Invalid credentials';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      _logger.e('$_logTag 🚨 Exception caught in signInWithEmail:');
      _logger.e('   Exception Type: ${e.runtimeType}');
      _logger.e('   Exception String: ${e.toString()}');

      final error = _handleAuthError(e, context: 'Email+Password Signin');
      _logger.e('$_logTag 🔄 Processed error: $error');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Sign in with email using OTP (magic link or code)
  static Future<AuthResult<void>> signInWithEmailOTP({
    required String email,
    String? emailRedirectTo,
  }) async {
    _logger.i('$_logTag Attempting email OTP signin for: $email');

    try {
      _logger.i('$_logTag 🔍 Calling Supabase signInWithOtp...');
      await BaseSupabaseService.client.auth.signInWithOtp(
        email: email,
        emailRedirectTo: emailRedirectTo ?? 'myapp://login-callback',
        shouldCreateUser: false, // Don't create user if they don't exist
      );

      _logger.i('$_logTag ✅ Email OTP sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      _logger.e('$_logTag 🚨 Exception caught in signInWithEmailOTP:');
      _logger.e('   Exception Type: ${e.runtimeType}');
      _logger.e('   Exception String: ${e.toString()}');

      final error = _handleAuthError(e, context: 'Email OTP Signin');
      _logger.e('$_logTag 🔄 Processed error: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Send email OTP for verification
  static Future<AuthResult<void>> sendEmailOTP({
    required String email,
    OtpType type = OtpType.email,
  }) async {
    _logger.i('$_logTag Sending email OTP to: $email (type: $type)');

    try {
      await BaseSupabaseService.client.auth.signInWithOtp(
        email: email,
        shouldCreateUser: true, // Always allow user creation for OTP
      );

      _logger.i('$_logTag ✅ Email OTP sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Failed to send email OTP: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Verify email OTP
  static Future<AuthResult<AuthResponse>> verifyEmailOTP({
    required String email,
    required String token,
    OtpType type = OtpType.email,
  }) async {
    _logger.i('$_logTag Verifying email OTP for: $email');

    try {
      // Validate token format first
      if (token.length != 6 || !RegExp(r'^\d{6}$').hasMatch(token)) {
        const error = 'Invalid OTP format. Please enter a 6-digit code.';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }

      _logger.i('$_logTag 🔄 Calling Supabase verifyOTP...');

      // Add timeout to prevent infinite hanging
      final response = await BaseSupabaseService.client.auth
          .verifyOTP(email: email, token: token, type: type)
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              _logger.w('$_logTag ⏰ Email OTP verification timed out');
              throw Exception('Verification timed out. Please try again.');
            },
          );

      if (response.user != null) {
        _logger.i(
          '$_logTag ✅ Email OTP verified successfully! User ID: ${response.user!.id}',
        );
        return _createResult(response, null);
      } else {
        const error = 'OTP verification failed: Invalid or expired code';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Email OTP verification failed: $error');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Verify email OTP for signup (Twitter/X style - just validates email)
  static Future<AuthResult<bool>> verifyEmailOTPForSignup({
    required String email,
    required String token,
    OtpType type = OtpType.email,
  }) async {
    _logger.i(
      '$_logTag Verifying email OTP for signup (Twitter/X style): $email',
    );

    try {
      // For Twitter/X style signup, we just need to verify the OTP is valid
      // We don't want to create an account yet - that happens at the end
      final response = await BaseSupabaseService.client.auth.verifyOTP(
        email: email,
        token: token,
        type: type,
      );

      if (response.user != null) {
        _logger.i('$_logTag ✅ Email OTP verified for signup! Email is valid.');

        // Note: A temporary user account was created by OTP verification
        // We'll handle this in the signup completion by updating the existing user

        // Sign out to ensure clean state
        await BaseSupabaseService.client.auth.signOut();
        _logger.i('$_logTag 🔄 Signed out to continue signup flow');

        return _createResult<bool>(true, null);
      } else {
        const error = 'OTP verification failed: Invalid or expired code';
        _logger.e('$_logTag ❌ $error');
        return _createResult<bool>(false, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Email OTP verification for signup failed: $error');
      return _createResult<bool>(false, error);
    }
  }

  // ==================== PHONE AUTHENTICATION ====================

  /// Send phone OTP for verification
  static Future<AuthResult<void>> sendPhoneOTP({
    required String phone,
    bool shouldCreateUser = true,
  }) async {
    _logger.i(
      '$_logTag Sending phone OTP to: $phone (shouldCreateUser: $shouldCreateUser)',
    );

    try {
      await BaseSupabaseService.client.auth.signInWithOtp(
        phone: phone,
        shouldCreateUser: shouldCreateUser,
      );

      _logger.i('$_logTag ✅ Phone OTP sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Failed to send phone OTP: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Verify phone OTP
  static Future<AuthResult<AuthResponse>> verifyPhoneOTP({
    required String phone,
    required String token,
    OtpType type = OtpType.sms,
  }) async {
    _logger.i('$_logTag Verifying phone OTP for: $phone');

    try {
      // Validate token format first
      if (token.length != 6 || !RegExp(r'^\d{6}$').hasMatch(token)) {
        const error = 'Invalid OTP format. Please enter a 6-digit code.';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }

      _logger.i('$_logTag 🔄 Calling Supabase verifyOTP...');

      // Add timeout to prevent infinite hanging
      final response = await BaseSupabaseService.client.auth
          .verifyOTP(phone: phone, token: token, type: type)
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              _logger.w('$_logTag ⏰ Phone OTP verification timed out');
              throw Exception('Verification timed out. Please try again.');
            },
          );

      if (response.user != null) {
        _logger.i(
          '$_logTag ✅ Phone OTP verified successfully! User ID: ${response.user!.id}',
        );
        return _createResult(response, null);
      } else {
        const error = 'OTP verification failed: Invalid or expired code';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Phone OTP verification failed: $error');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Sign up with phone number (requires OTP verification)
  static Future<AuthResult<void>> signUpWithPhone({
    required String phone,
    required String password,
    required String fullName,
    Map<String, dynamic>? additionalData,
  }) async {
    _logger.i('$_logTag Attempting phone signup for: $phone');

    try {
      // First send OTP to phone
      final otpResult = await sendPhoneOTP(phone: phone);
      if (!otpResult.isSuccess) {
        return _createResult<void>(null, otpResult.error);
      }

      _logger.i('$_logTag ✅ Phone signup OTP sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Phone signup failed: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Sign in with phone and password
  static Future<AuthResult<AuthResponse>> signInWithPhone({
    required String phone,
    required String password,
  }) async {
    _logger.i('$_logTag Attempting phone signin for: $phone');

    try {
      final response = await BaseSupabaseService.client.auth.signInWithPassword(
        phone: phone,
        password: password,
      );

      if (response.user != null) {
        _logger.i(
          '$_logTag ✅ Phone signin successful! User ID: ${response.user!.id}',
        );
        _logger.i(
          '$_logTag 🔑 Session expires: ${response.session?.expiresAt}',
        );

        return _createResult(response, null);
      } else {
        const error = 'Sign in failed: Invalid credentials';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Phone signin failed: $error');
      return _createResult<AuthResponse>(null, error);
    }
  }

  // ==================== PASSWORD RESET ====================

  /// Send password reset OTP (supports email, phone, or username)
  static Future<AuthResult<String>> sendPasswordResetOTP({
    required String identifier,
  }) async {
    _logger.i(
      '$_logTag Sending password reset OTP for identifier: $identifier',
    );

    try {
      // Resolve identifier (email, phone, or username) to email address
      final emailResult = await _resolveIdentifierToEmail(identifier);
      if (!emailResult.isSuccess) {
        _logger.e('$_logTag ❌ ${emailResult.error}');
        return _createResult<String>(null, emailResult.error);
      }

      final email = emailResult.data!;
      _logger.i('$_logTag ✅ Resolved identifier to email: $email');

      // Check if this is a temp email (phone-based account)
      if (email.endsWith('@phone.temp')) {
        // For phone-based accounts, we need to use a different approach
        // Since Supabase doesn't have a direct phone password reset method,
        // we'll use the email method but with special handling
        final phoneNumber = PhoneUtils.tempEmailToPhone(email);
        _logger.i('$_logTag 📱 Phone-based account detected for: $phoneNumber');
        _logger.i(
          '$_logTag 🔄 Using alternative approach for phone password reset',
        );

        // For phone accounts, we'll send a regular email reset to the temp email
        // This should work because the user exists in Supabase Auth with this email
        await BaseSupabaseService.client.auth.resetPasswordForEmail(
          email, // Use the temp email format
          redirectTo: null,
        );
      } else {
        // Use resetPasswordForEmail for proper password reset flow
        await BaseSupabaseService.client.auth.resetPasswordForEmail(
          email,
          redirectTo: null, // We'll handle OTP verification manually
        );
      }

      _logger.i('$_logTag ✅ Password reset OTP sent successfully to: $email');
      // Return the resolved email so the caller knows which email to use for verification
      return _createResult<String>(email, null);
    } catch (e) {
      final error = _handleAuthError(e, context: 'Password Reset');
      _logger.e('$_logTag ❌ Failed to send password reset OTP: $error');
      return _createResult<String>(null, error);
    }
  }

  /// Verify password reset OTP with strict validation
  static Future<AuthResult<AuthResponse>> verifyPasswordResetOTP({
    required String email,
    required String token,
  }) async {
    _logger.i('$_logTag Verifying password reset OTP for: $email');
    _logger.i('$_logTag Token: $token');

    try {
      // Validate token format first
      if (token.length != 6 || !RegExp(r'^\d{6}$').hasMatch(token)) {
        const error = 'Invalid OTP format. Please enter a 6-digit code.';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }

      _logger.i('$_logTag 🔄 Calling Supabase verifyOTP...');

      // Add timeout to prevent hanging
      // Since we're now using resetPasswordForEmail for both email and phone accounts,
      // we can use the same verification method for both
      final response = await BaseSupabaseService.client.auth
          .verifyOTP(
            email: email, // Use the email (temp email for phone accounts)
            token: token,
            type: OtpType.recovery, // Use recovery type for password reset
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              _logger.w('$_logTag ⏰ OTP verification timed out');
              throw Exception('Verification timed out. Please try again.');
            },
          );

      _logger.i('$_logTag 🔄 Supabase verifyOTP completed');

      // Strict validation: Both user and session must be present
      if (response.user != null &&
          response.session != null &&
          response.session!.accessToken.isNotEmpty) {
        _logger.i(
          '$_logTag ✅ Password reset OTP verified! User temporarily authenticated',
        );
        return _createResult(response, null);
      } else {
        const error = 'Invalid OTP. Please check your code and try again.';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      // Enhanced error handling for OTP verification
      _logger.e('$_logTag 🚨 Exception caught in verifyPasswordResetOTP:');
      _logger.e('   Exception Type: ${e.runtimeType}');
      _logger.e('   Exception String: ${e.toString()}');

      String specificError;

      // Check if it's an AuthApiException with specific error codes
      if (e.toString().contains('AuthApiException')) {
        // Log the exact error for debugging
        _logger.e('$_logTag 🔍 AuthApiException details: ${e.toString()}');

        if (e.toString().contains('code: otp_expired') ||
            e.toString().contains('Token has expired or is invalid') ||
            e.toString().contains('Token has expired') ||
            e.toString().contains('expired')) {
          specificError = 'OTP expired';
        } else if (e.toString().contains('code: invalid_otp') ||
            e.toString().contains('invalid') ||
            e.toString().contains('Invalid token')) {
          specificError = 'Invalid OTP. Please check your code and try again.';
        } else if (e.toString().contains('too_many_requests') ||
            e.toString().contains('too many')) {
          specificError = 'Too many attempts. Please wait before trying again.';
        } else if (e.toString().contains('not_found') ||
            e.toString().contains('session_not_found')) {
          specificError =
              'Reset session not found. Please request a new reset code.';
        } else {
          specificError = 'Invalid OTP. Please check your code and try again.';
        }
      } else {
        // Fallback for other error types
        final errorString = e.toString().toLowerCase();
        if (errorString.contains('expired') ||
            errorString.contains('token_expired') ||
            errorString.contains('otp_expired')) {
          specificError = 'OTP expired';
        } else if (errorString.contains('signup_disabled')) {
          specificError = 'Password reset is temporarily disabled.';
        } else {
          specificError = 'Invalid OTP. Please check your code and try again.';
        }
      }

      _logger.e(
        '$_logTag ❌ Password reset OTP verification failed: $specificError',
      );
      return _createResult<AuthResponse>(null, specificError);
    }
  }

  /// Send login OTP for new device verification
  static Future<AuthResult<void>> sendNewDeviceLoginOTP({
    required String email,
  }) async {
    _logger.i('$_logTag Sending new device login OTP to: $email');

    try {
      await BaseSupabaseService.client.auth.signInWithOtp(
        email: email,
        shouldCreateUser: false, // Don't create account for device verification
      );

      _logger.i('$_logTag ✅ New device login OTP sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Failed to send new device login OTP: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Verify new device login OTP
  static Future<AuthResult<AuthResponse>> verifyNewDeviceLoginOTP({
    required String email,
    required String token,
  }) async {
    _logger.i('$_logTag Verifying new device login OTP for: $email');

    try {
      final response = await BaseSupabaseService.client.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.email, // Use email type for device verification
      );

      if (response.user != null) {
        _logger.i('$_logTag ✅ New device login OTP verified successfully');
        return _createResult(response, null);
      } else {
        const error = 'Invalid or expired verification code';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ New device login OTP verification failed: $error');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Send email change verification OTP
  static Future<AuthResult<void>> sendEmailChangeOTP({
    required String newEmail,
  }) async {
    _logger.i('$_logTag Sending email change OTP to: $newEmail');

    try {
      await BaseSupabaseService.client.auth.signInWithOtp(
        email: newEmail,
        shouldCreateUser: false, // Don't create account for email change
      );

      _logger.i('$_logTag ✅ Email change OTP sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Failed to send email change OTP: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Verify email change OTP
  static Future<AuthResult<AuthResponse>> verifyEmailChangeOTP({
    required String email,
    required String token,
  }) async {
    _logger.i('$_logTag Verifying email change OTP for: $email');

    try {
      final response = await BaseSupabaseService.client.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.email,
      );

      if (response.user != null) {
        _logger.i('$_logTag ✅ Email change OTP verified successfully');
        return _createResult(response, null);
      } else {
        const error = 'Invalid or expired verification code';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Email change OTP verification failed: $error');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Resend password reset OTP
  static Future<AuthResult<void>> resendPasswordResetOTP({
    required String email,
  }) async {
    _logger.i('$_logTag Resending password reset OTP to: $email');

    try {
      // For password reset, we need to use resetPasswordForEmail again
      // The resend() method doesn't support OtpType.recovery
      // This works for both email and phone accounts (using temp email)
      await BaseSupabaseService.client.auth.resetPasswordForEmail(
        email,
        redirectTo: null, // We'll handle OTP verification manually
      );

      _logger.i('$_logTag ✅ Password reset OTP resent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e, context: 'Password Reset Resend');
      _logger.e('$_logTag ❌ Failed to resend password reset OTP: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Send password reset email (legacy method - kept for compatibility)
  static Future<AuthResult<void>> sendPasswordResetEmail({
    required String email,
  }) async {
    _logger.i('$_logTag Sending password reset email to: $email');

    try {
      await BaseSupabaseService.client.auth.resetPasswordForEmail(email);

      _logger.i('$_logTag ✅ Password reset email sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Failed to send password reset email: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Resend email verification
  static Future<AuthResult<void>> resendEmailVerification({
    required String email,
  }) async {
    _logger.i('$_logTag Resending email verification to: $email');

    try {
      await BaseSupabaseService.client.auth.resend(
        type: OtpType.signup,
        email: email,
      );

      _logger.i('$_logTag ✅ Email verification sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e, context: 'Email Verification');
      _logger.e('$_logTag ❌ Failed to resend email verification: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Check if user's email is verified
  static Future<AuthResult<bool>> isEmailVerified({
    required String email,
  }) async {
    _logger.i('$_logTag Checking email verification status for: $email');

    try {
      // Try to get user info from auth.users table
      final response =
          await BaseSupabaseService.client
              .from('auth.users')
              .select('email_confirmed_at')
              .eq('email', email)
              .maybeSingle();

      final isVerified = response?['email_confirmed_at'] != null;
      _logger.i('$_logTag Email verification status: $isVerified');
      return _createResult<bool>(isVerified, null);
    } catch (e) {
      // If we can't check the auth table, try alternative method
      _logger.w(
        '$_logTag ⚠️ Could not check auth.users table, using alternative method',
      );

      // Alternative: Check current user if they're signed in
      final currentUser = BaseSupabaseService.client.auth.currentUser;
      if (currentUser != null && currentUser.email == email) {
        final isVerified = currentUser.emailConfirmedAt != null;
        _logger.i(
          '$_logTag Email verification status (current user): $isVerified',
        );
        return _createResult<bool>(isVerified, null);
      }

      // If we can't determine, assume not verified for safety
      _logger.e('$_logTag ❌ Could not determine email verification status');
      return _createResult<bool>(false, null);
    }
  }

  // ==================== SECURITY & DEVICE MANAGEMENT ====================

  /// Check if current device is new/unrecognized
  static Future<AuthResult<bool>> isNewDevice() async {
    _logger.i('$_logTag Checking if device is new/unrecognized');

    try {
      // Simple device check - in production, you'd use device fingerprinting
      // For now, we'll check if user has logged in recently
      final user = SupabaseConfig.currentUser;
      if (user == null) {
        return _createResult(true, null); // No user = new device
      }

      // Check last login time - if more than 30 days, consider it new device
      final lastSignInString = user.lastSignInAt;
      if (lastSignInString != null) {
        try {
          final lastSignIn = DateTime.parse(lastSignInString);
          final daysSinceLastLogin =
              DateTime.now().difference(lastSignIn).inDays;
          final isNewDevice = daysSinceLastLogin > 30;

          _logger.i(
            '$_logTag Days since last login: $daysSinceLastLogin, isNewDevice: $isNewDevice',
          );
          return _createResult(isNewDevice, null);
        } catch (e) {
          _logger.w(
            '$_logTag ⚠️ Could not parse last sign in date: $lastSignInString',
          );
          // If we can't parse the date, consider it new device for safety
          return _createResult(true, null);
        }
      }

      // If no last sign in data, consider it new
      return _createResult(true, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Failed to check device status: $error');
      return _createResult<bool>(null, error);
    }
  }

  /// Send security alert OTP for sensitive operations
  static Future<AuthResult<void>> sendSecurityAlertOTP({
    required String email,
    required String operation,
  }) async {
    _logger.i('$_logTag Sending security alert OTP for operation: $operation');

    try {
      await BaseSupabaseService.client.auth.signInWithOtp(
        email: email,
        shouldCreateUser: false,
      );

      _logger.i('$_logTag ✅ Security alert OTP sent successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Failed to send security alert OTP: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Verify security operation OTP
  static Future<AuthResult<bool>> verifySecurityOTP({
    required String email,
    required String token,
  }) async {
    _logger.i('$_logTag Verifying security operation OTP');

    try {
      final response = await BaseSupabaseService.client.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.email,
      );

      final isValid = response.user != null;
      _logger.i('$_logTag Security OTP verification result: $isValid');
      return _createResult(isValid, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Security OTP verification failed: $error');
      return _createResult<bool>(null, error);
    }
  }

  /// Update user password
  static Future<AuthResult<User>> updatePassword({
    required String newPassword,
  }) async {
    _logger.i('$_logTag Updating user password');

    try {
      final response = await BaseSupabaseService.client.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      if (response.user != null) {
        _logger.i('$_logTag ✅ Password updated successfully');
        return _createResult(response.user!, null);
      } else {
        const error = 'Failed to update password';
        _logger.e('$_logTag ❌ $error');
        return _createResult<User>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Password update failed: $error');
      return _createResult<User>(null, error);
    }
  }

  /// Update user email
  static Future<AuthResult<User>> updateUserEmail({
    required String newEmail,
  }) async {
    _logger.i('$_logTag Updating user email to: $newEmail');

    try {
      final response = await BaseSupabaseService.client.auth.updateUser(
        UserAttributes(email: newEmail),
      );

      if (response.user != null) {
        _logger.i('$_logTag ✅ Email updated successfully');
        return _createResult(response.user!, null);
      } else {
        const error = 'Failed to update email';
        _logger.e('$_logTag ❌ $error');
        return _createResult<User>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Email update failed: $error');
      return _createResult<User>(null, error);
    }
  }

  // ==================== SESSION MANAGEMENT ====================

  /// Sign out user
  static Future<AuthResult<void>> signOut() async {
    _logger.i('$_logTag Signing out user');

    try {
      await BaseSupabaseService.client.auth.signOut();
      _logger.i('$_logTag ✅ User signed out successfully');
      return _createResult<void>(null, null);
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Sign out failed: $error');
      return _createResult<void>(null, error);
    }
  }

  /// Refresh current session
  static Future<AuthResult<AuthResponse>> refreshSession() async {
    _logger.i('$_logTag Refreshing session');

    try {
      final response = await BaseSupabaseService.client.auth.refreshSession();

      if (response.session != null) {
        _logger.i('$_logTag ✅ Session refreshed successfully');
        return _createResult(response, null);
      } else {
        const error = 'Failed to refresh session';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e);
      _logger.e('$_logTag ❌ Session refresh failed: $error');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Get current user profile
  static Future<UserModel?> getCurrentUserProfile() async {
    final user = BaseSupabaseService.client.auth.currentUser;
    if (user == null) return null;

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.profiles)
              .select()
              .eq('id', user.id)
              .maybeSingle();

      return response != null ? UserModel.fromJson(response) : null;
    });
  }

  /// Update user profile
  static Future<UserModel> updateProfile(UserModel user) async {
    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.profiles)
              .update(user.toJson())
              .eq('id', user.id)
              .select()
              .single();

      return UserModel.fromJson(response);
    });
  }

  // ==================== UTILITY METHODS ====================

  /// Check if username is available
  static Future<bool> isUsernameAvailable(String username) async {
    try {
      final response =
          await BaseSupabaseService.client
              .from('profiles')
              .select('id')
              .eq('username', username)
              .maybeSingle();

      return response == null;
    } catch (e) {
      _logger.e('$_logTag ❌ Username availability check failed: $e');
      return false;
    }
  }

  /// Generate a unique username from a base name
  static Future<AuthResult<String>> generateUniqueUsername(
    String baseName,
  ) async {
    _logger.i('$_logTag Generating unique username from: $baseName');

    try {
      // Clean and process the base name
      String cleanBaseName = baseName
          .toLowerCase()
          .replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_')
          .replaceAll(RegExp(r'_+'), '_')
          .replaceAll(RegExp(r'^_|_$'), '');

      // Ensure minimum length
      if (cleanBaseName.length < 3) {
        cleanBaseName = '${cleanBaseName}user';
      }

      // Limit length to reasonable size
      if (cleanBaseName.length > 20) {
        cleanBaseName = cleanBaseName.substring(0, 20);
      }

      // Try the base name first
      if (await isUsernameAvailable(cleanBaseName)) {
        _logger.i('$_logTag ✅ Base username available: $cleanBaseName');
        return _createResult<String>(cleanBaseName, null);
      }

      // If base name is taken, try with numbers
      for (int i = 1; i <= 999; i++) {
        final candidateUsername = '${cleanBaseName}_$i';
        if (await isUsernameAvailable(candidateUsername)) {
          _logger.i('$_logTag ✅ Generated unique username: $candidateUsername');
          return _createResult<String>(candidateUsername, null);
        }
      }

      // If all numbered variants are taken, use timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch
          .toString()
          .substring(8);
      final timestampUsername = '${cleanBaseName}_$timestamp';

      _logger.i('$_logTag ✅ Generated timestamp username: $timestampUsername');
      return _createResult<String>(timestampUsername, null);
    } catch (e) {
      final error = 'Failed to generate unique username: $e';
      _logger.e('$_logTag ❌ $error');
      return _createResult<String>(null, error);
    }
  }

  /// Update user last seen timestamp
  static Future<void> updateLastSeen() async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return;

    try {
      await BaseSupabaseService.client.rpc(
        'update_user_last_seen',
        params: {'user_uuid': userId},
      );
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to update last seen: $e');
    }
  }

  /// Check if user is new (no profile exists in database)
  static Future<AuthResult<bool>> isNewUser({String? userId}) async {
    _logger.i('$_logTag Checking if user is new...');

    try {
      final targetUserId =
          userId ?? BaseSupabaseService.client.auth.currentUser?.id;
      if (targetUserId == null) {
        _logger.e('$_logTag ❌ No user ID provided for new user check');
        return _createResult<bool>(null, 'No user ID available');
      }

      // Check if profile exists in database
      final response =
          await BaseSupabaseService.client
              .from('profiles')
              .select('id')
              .eq('id', targetUserId)
              .maybeSingle();

      final isNewUser = response == null;
      _logger.i(
        '$_logTag User $targetUserId is ${isNewUser ? 'NEW' : 'EXISTING'}',
      );

      return _createResult<bool>(isNewUser, null);
    } catch (e) {
      final error = _handleAuthError(e, context: 'New User Check');
      _logger.e('$_logTag ❌ Failed to check if user is new: $error');
      return _createResult<bool>(null, error);
    }
  }

  /// Check if user has completed onboarding (Spotify-style approach)
  static Future<AuthResult<bool>> hasCompletedOnboarding({
    String? userId,
  }) async {
    _logger.i('$_logTag Checking if user has completed onboarding...');

    try {
      final targetUserId =
          userId ?? BaseSupabaseService.client.auth.currentUser?.id;
      if (targetUserId == null) {
        _logger.e('$_logTag ❌ No user ID provided for onboarding check');
        return _createResult<bool>(null, 'No user ID available');
      }

      // Check if user has completed onboarding
      final response =
          await BaseSupabaseService.client
              .from('profiles')
              .select(
                'onboarding_completed, date_of_birth, selected_categories',
              )
              .eq('id', targetUserId)
              .maybeSingle();

      if (response == null) {
        // No profile exists - definitely not completed onboarding
        _logger.i(
          '$_logTag User $targetUserId has NO profile - onboarding not completed',
        );
        return _createResult<bool>(false, null);
      }

      // Check if onboarding is marked as completed
      final onboardingCompleted =
          response['onboarding_completed'] as bool? ?? false;
      final hasDateOfBirth = response['date_of_birth'] != null;
      final hasSelectedCategories =
          response['selected_categories'] != null &&
          (response['selected_categories'] as List?)?.isNotEmpty == true;

      // User has completed onboarding if flag is true AND they have required data
      final isCompleted =
          onboardingCompleted && hasDateOfBirth && hasSelectedCategories;

      _logger.i(
        '$_logTag User $targetUserId onboarding status: ${isCompleted ? 'COMPLETED' : 'INCOMPLETE'}',
      );
      _logger.i('$_logTag - onboarding_completed: $onboardingCompleted');
      _logger.i('$_logTag - has_date_of_birth: $hasDateOfBirth');
      _logger.i('$_logTag - has_selected_categories: $hasSelectedCategories');

      return _createResult<bool>(isCompleted, null);
    } catch (e) {
      final error = _handleAuthError(e, context: 'Onboarding Check');
      _logger.e('$_logTag ❌ Failed to check onboarding status: $error');
      return _createResult<bool>(null, error);
    }
  }

  /// Store user category preferences and mark onboarding as complete
  static Future<AuthResult<bool>> storeCategoryPreferences({
    required String userId,
    required List<String> categoryIds,
  }) async {
    _logger.i('$_logTag Storing category preferences for user: $userId');

    try {
      // Update user preferences with selected categories AND mark onboarding complete
      final preferences = {
        'selected_categories': categoryIds,
        'onboarding_completed': true,
        'onboarding_completed_at': DateTime.now().toIso8601String(),
      };

      // Update both preferences AND onboarding_completed flag
      await BaseSupabaseService.client
          .from('profiles')
          .update({
            'preferences': preferences,
            'selected_categories': categoryIds, // Store in dedicated column too
            'onboarding_completed': true, // Mark onboarding as complete
            'onboarding_completed_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);

      _logger.i(
        '$_logTag ✅ Category preferences stored and onboarding marked complete',
      );
      return _createResult<bool>(true, null);
    } catch (e) {
      final error = _handleAuthError(e, context: 'Store Category Preferences');
      _logger.e('$_logTag ❌ Failed to store category preferences: $error');
      return _createResult<bool>(null, error);
    }
  }

  // ==================== ADMIN MANAGEMENT ====================

  /// Assign admin role to a user by email
  static Future<AuthResult<bool>> assignAdminRole({
    required String userEmail,
    String role = 'admin',
  }) async {
    _logger.i('$_logTag Assigning $role role to: $userEmail');

    try {
      final result = await BaseSupabaseService.executeRPC<bool>(
        'assign_admin_role',
        {'user_email': userEmail, 'new_role': role},
      );

      if (result == true) {
        _logger.i('$_logTag ✅ Successfully assigned $role role to $userEmail');
        return _createResult<bool>(true, null);
      } else {
        const error = 'Failed to assign admin role';
        _logger.e('$_logTag ❌ $error');
        return _createResult<bool>(false, error);
      }
    } catch (e) {
      final error = _handleAuthError(e, context: 'Admin Role Assignment');
      return _createResult<bool>(false, error);
    }
  }

  /// Remove admin role from a user
  static Future<AuthResult<bool>> removeAdminRole({
    required String userEmail,
  }) async {
    _logger.i('$_logTag Removing admin role from: $userEmail');

    try {
      final result = await BaseSupabaseService.executeRPC<bool>(
        'remove_admin_role',
        {'user_email': userEmail},
      );

      if (result == true) {
        _logger.i('$_logTag ✅ Successfully removed admin role from $userEmail');
        return _createResult<bool>(true, null);
      } else {
        const error = 'Failed to remove admin role';
        _logger.e('$_logTag ❌ $error');
        return _createResult<bool>(false, error);
      }
    } catch (e) {
      final error = _handleAuthError(e, context: 'Admin Role Removal');
      return _createResult<bool>(false, error);
    }
  }

  /// Get all admin users
  static Future<List<UserModel>> getAdminUsers() async {
    _logger.i('$_logTag Fetching all admin users');

    try {
      final response = await BaseSupabaseService.executeQuery(() async {
        return await BaseSupabaseService.client.rpc('get_admin_users');
      });

      if (response is List) {
        final adminUsers =
            response
                .map(
                  (userData) =>
                      UserModel.fromJson(userData as Map<String, dynamic>),
                )
                .toList();

        _logger.i('$_logTag ✅ Found ${adminUsers.length} admin users');
        return adminUsers;
      } else {
        _logger.e('$_logTag ❌ Invalid response format for admin users');
        return [];
      }
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to fetch admin users: $e');
      return [];
    }
  }

  /// Check if user has admin privileges
  static Future<bool> isUserAdmin({required String userEmail}) async {
    _logger.i('$_logTag Checking admin privileges for: $userEmail');

    try {
      final result = await BaseSupabaseService.executeRPC<bool>(
        'is_user_admin',
        {'user_email': userEmail},
      );

      final isAdmin = result == true;
      _logger.i('$_logTag User $userEmail admin status: $isAdmin');
      return isAdmin;
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to check admin status: $e');
      return false;
    }
  }

  /// Check if current user has admin privileges
  static Future<bool> isCurrentUserAdmin() async {
    final currentUser = BaseSupabaseService.client.auth.currentUser;
    if (currentUser?.email == null) {
      return false;
    }

    return await isUserAdmin(userEmail: currentUser!.email!);
  }

  // ==================== GOOGLE OAUTH AUTHENTICATION ====================

  /// Sign in with Google using OAuth
  static Future<AuthResult<AuthResponse>> signInWithGoogle({
    required String idToken,
    String? accessToken,
  }) async {
    _logger.i('$_logTag Attempting Google OAuth sign-in...');

    try {
      final response = await BaseSupabaseService.client.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: idToken,
        accessToken: accessToken,
      );

      if (response.user != null) {
        _logger.i(
          '$_logTag ✅ Google OAuth sign-in successful! User ID: ${response.user!.id}',
        );
        _logger.i('$_logTag 📧 Email: ${response.user!.email}');
        _logger.i(
          '$_logTag 👤 Name: ${response.user!.userMetadata?['full_name']}',
        );

        return _createResult(response, null);
      } else {
        const error = 'Google sign-in failed: No user data received';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e, context: 'Google OAuth Sign-In');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Sign in with Google using OAuth flow (alternative method)
  static Future<AuthResult<AuthResponse>> signInWithGoogleOAuth({
    String? redirectTo,
    Map<String, String>? queryParams,
  }) async {
    _logger.i('$_logTag Attempting Google OAuth flow sign-in...');

    try {
      final response = await BaseSupabaseService.client.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: redirectTo,
        authScreenLaunchMode: LaunchMode.externalApplication,
        queryParams:
            queryParams ?? {'access_type': 'offline', 'prompt': 'consent'},
      );

      if (response) {
        _logger.i('$_logTag ✅ Google OAuth flow initiated successfully');
        // Note: The actual authentication will be handled by the OAuth callback
        return _createResult<AuthResponse>(null, null);
      } else {
        const error = 'Failed to initiate Google OAuth flow';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e, context: 'Google OAuth Flow');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Handle Google OAuth callback and exchange code for session
  static Future<AuthResult<AuthResponse>> handleGoogleOAuthCallback({
    required String code,
  }) async {
    _logger.i('$_logTag Handling Google OAuth callback...');

    try {
      await BaseSupabaseService.client.auth.exchangeCodeForSession(code);

      // Get the current session after code exchange
      final session = BaseSupabaseService.client.auth.currentSession;
      if (session?.user != null) {
        _logger.i(
          '$_logTag ✅ Google OAuth callback handled successfully! User ID: ${session!.user.id}',
        );

        // Create AuthResponse from session
        final authResponse = AuthResponse(user: session.user, session: session);

        return _createResult(authResponse, null);
      } else {
        const error = 'Failed to exchange OAuth code for session';
        _logger.e('$_logTag ❌ $error');
        return _createResult<AuthResponse>(null, error);
      }
    } catch (e) {
      final error = _handleAuthError(e, context: 'Google OAuth Callback');
      return _createResult<AuthResponse>(null, error);
    }
  }

  // ==================== BACKWARD COMPATIBILITY ====================
  // These methods maintain compatibility with existing AuthBloc

  /// Legacy sign up method for backward compatibility
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
    String? phone,
  }) async {
    final result = await signUpWithEmail(
      email: email,
      password: password,
      fullName: fullName,
      additionalData: phone != null ? {'phone': phone} : null,
    );

    if (result.isSuccess) {
      return result.data!;
    } else {
      throw Exception(result.error);
    }
  }

  /// Sign in with username (looks up email first)
  static Future<AuthResult<AuthResponse>> signInWithUsername({
    required String username,
    required String password,
  }) async {
    _logger.i('$_logTag Attempting username signin for: $username');

    try {
      // First, look up the email associated with this username
      final emailResult = await _getUserEmailByUsername(username);
      if (!emailResult.isSuccess) {
        return _createResult<AuthResponse>(null, emailResult.error);
      }

      // Now sign in with the found email
      return await signInWithEmail(
        email: emailResult.data!,
        password: password,
      );
    } catch (e) {
      final error = _handleAuthError(e, context: 'Username Signin');
      return _createResult<AuthResponse>(null, error);
    }
  }

  /// Look up email by username from profiles table
  static Future<AuthResult<String>> _getUserEmailByUsername(
    String username,
  ) async {
    try {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.profiles)
              .select('email')
              .eq('username', username)
              .maybeSingle();

      if (response != null && response['email'] != null) {
        return _createResult<String>(response['email'] as String, null);
      } else {
        return _createResult<String>(
          null,
          'No account found with username "$username". Please check your username or use your email address.',
        );
      }
    } catch (e) {
      return _createResult<String>(
        null,
        'Error looking up username. Please try using your email address instead.',
      );
    }
  }

  /// Legacy sign in method for backward compatibility
  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    final result = await signInWithEmail(email: email, password: password);

    if (result.isSuccess) {
      return result.data!;
    } else {
      throw Exception(result.error);
    }
  }
}

/// Profile service for user management
class ProfileService extends BaseSupabaseService {
  /// Get user profile by ID
  static Future<UserModel?> getUserProfile(String userId) async {
    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.profiles)
              .select()
              .eq('id', userId)
              .maybeSingle();

      return response != null ? UserModel.fromJson(response) : null;
    });
  }

  /// Search users
  static Future<List<UserModel>> searchUsers({
    required String query,
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'search_users',
        {'search_query': query, 'limit_count': limit, 'offset_count': offset},
      );

      return response?.map((json) => UserModel.fromJson(json)).toList() ?? [];
    });
  }

  /// Get user recommendations
  static Future<List<UserModel>> getUserRecommendations({
    int limit = 10,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return [];

    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_user_recommendations',
        {'user_uuid': userId, 'limit_count': limit},
      );

      return response?.map((json) => UserModel.fromJson(json)).toList() ?? [];
    });
  }

  /// Follow user
  static Future<void> followUser(String userId) async {
    final currentUserId = BaseSupabaseService.client.auth.currentUser?.id;
    if (currentUserId == null) {
      throw const SupabaseException('Not authenticated');
    }

    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client.from(DatabaseTables.follows).insert({
        'follower_id': currentUserId,
        'following_id': userId,
      });
    });
  }

  /// Unfollow user
  static Future<void> unfollowUser(String userId) async {
    final currentUserId = BaseSupabaseService.client.auth.currentUser?.id;
    if (currentUserId == null) {
      throw const SupabaseException('Not authenticated');
    }

    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.follows)
          .delete()
          .eq('follower_id', currentUserId)
          .eq('following_id', userId);
    });
  }

  /// Get user's followers
  static Future<List<UserModel>> getUserFollowers({
    required String userId,
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.client
          .from(DatabaseTables.follows)
          .select('follower_id, profiles!follows_follower_id_fkey(*)')
          .eq('following_id', userId)
          .range(offset, offset + limit - 1);

      return response
          .map((item) => UserModel.fromJson(item['profiles']))
          .toList();
    });
  }

  /// Get user's following
  static Future<List<UserModel>> getUserFollowing({
    required String userId,
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.client
          .from(DatabaseTables.follows)
          .select('following_id, profiles!follows_following_id_fkey(*)')
          .eq('follower_id', userId)
          .range(offset, offset + limit - 1);

      return response
          .map((item) => UserModel.fromJson(item['profiles']))
          .toList();
    });
  }

  /// Check if current user is following another user
  static Future<bool> isFollowing(String userId) async {
    final currentUserId = BaseSupabaseService.client.auth.currentUser?.id;
    if (currentUserId == null) return false;

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.follows)
              .select('id')
              .eq('follower_id', currentUserId)
              .eq('following_id', userId)
              .maybeSingle();

      return response != null;
    });
  }

  /// Get user engagement stats
  static Future<Map<String, dynamic>?> getUserEngagementStats(
    String userId,
  ) async {
    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.executeRPC<Map<String, dynamic>>(
            'get_user_engagement_stats',
            {'user_uuid': userId},
          );

      return response;
    });
  }
}
