import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Simple Package Info Widget
/// This is a standalone implementation that can be used anywhere in the app
/// to display app information using package_info_plus package
class SimplePackageInfo extends StatefulWidget {
  const SimplePackageInfo({super.key});

  @override
  State<SimplePackageInfo> createState() => _SimplePackageInfoState();
}

class _SimplePackageInfoState extends State<SimplePackageInfo> {
  PackageInfo? _packageInfo;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  /// Load package information from platform
  Future<void> _loadPackageInfo() async {
    try {
      final info = await PackageInfo.fromPlatform();
      setState(() {
        _packageInfo = info;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Handle error if needed
      debugPrint('Error loading package info: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Information'),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _packageInfo == null
              ? const Center(
                  child: Text(
                    'Failed to load app information',
                    style: TextStyle(fontSize: 16),
                  ),
                )
              : _buildPackageInfoContent(),
    );
  }

  /// Build the main content showing package information
  Widget _buildPackageInfoContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App Icon and Name Section
          _buildAppHeaderSection(),
          const SizedBox(height: 24),
          
          // Package Details Card
          _buildPackageDetailsCard(),
          const SizedBox(height: 16),
          
          // Additional Info Card
          _buildAdditionalInfoCard(),
        ],
      ),
    );
  }

  /// Build app header with icon and name
  Widget _buildAppHeaderSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // App Icon (you can replace with your app icon)
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.business,
                color: Colors.white,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _packageInfo!.appName,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Version ${_packageInfo!.version}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build package details card
  Widget _buildPackageDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Package Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('App Name', _packageInfo!.appName),
            _buildInfoRow('Package Name', _packageInfo!.packageName),
            _buildInfoRow('Version', _packageInfo!.version),
            _buildInfoRow('Build Number', _packageInfo!.buildNumber),
          ],
        ),
      ),
    );
  }

  /// Build additional information card
  Widget _buildAdditionalInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Installer Store', _packageInfo!.installerStore ?? 'Unknown'),
            _buildInfoRow('Build Signature', _packageInfo!.buildSignature.isNotEmpty 
                ? _packageInfo!.buildSignature.substring(0, 20) + '...' 
                : 'Not available'),
          ],
        ),
      ),
    );
  }

  /// Helper method to build information rows
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.8),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}

/// Simple Package Info Demo Page
/// This demonstrates how to use package_info_plus in a simple way
class PackageInfoDemo extends StatelessWidget {
  const PackageInfoDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Package Info Demo'),
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.info_outline,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 24),
            const Text(
              'Package Info Plus Demo',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'This demo shows how to use package_info_plus to get app details like name, version, package name, and build number.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SimplePackageInfo(),
                  ),
                );
              },
              icon: const Icon(Icons.launch),
              label: const Text('View App Information'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
