import 'package:flutter/material.dart';

/// Spotify-style error popup that shows "Something went wrong" with an OK button
class SpotifyErrorPopup extends StatelessWidget {
  final String? customMessage;
  final VoidCallback? onOkPressed;

  const SpotifyErrorPopup({super.key, this.customMessage, this.onOkPressed});

  /// Static method to show the Spotify-style error popup
  static Future<void> show({
    required BuildContext context,
    String? customMessage,
    VoidCallback? onOkPressed,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // User must tap OK
      builder: (BuildContext context) {
        return SpotifyErrorPopup(
          customMessage: customMessage,
          onOkPressed: onOkPressed,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 8,
      backgroundColor: theme.dialogBackgroundColor,
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 320, minWidth: 280),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Error message
            Text(
              customMessage ?? 'Something went wrong. Please try again.',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: theme.textTheme.bodyLarge?.color,
                height: 1.3,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // OK button
            SizedBox(
              width: double.infinity,
              height: 18,
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onOkPressed?.call();
                },
                style: TextButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: const Color(0xFF1DA1F2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                ),
                child: const Text(
                  'OK',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Error popup specifically for Google sign-in errors
class GoogleSignInErrorPopup extends StatelessWidget {
  final VoidCallback? onOkPressed;

  const GoogleSignInErrorPopup({super.key, this.onOkPressed});

  /// Static method to show the Google sign-in error popup
  static Future<void> show({
    required BuildContext context,
    VoidCallback? onOkPressed,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // User must tap OK
      builder: (BuildContext context) {
        return GoogleSignInErrorPopup(onOkPressed: onOkPressed);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    // Calculate responsive dimensions
    final dialogWidth = screenSize.width * 0.85;
    final maxDialogWidth = isSmallScreen ? 300.0 : 350.0;
    final finalWidth =
        dialogWidth > maxDialogWidth ? maxDialogWidth : dialogWidth;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 12,
      backgroundColor: theme.dialogBackgroundColor,
      child: Container(
        width: finalWidth,
        height: finalWidth, // Square shape
        padding: EdgeInsets.all(isSmallScreen ? 20 : 28),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error message with increased height spacing
            Expanded(
              flex: 2,
              child: Center(
                child: Text(
                  'Something went wrong. Please try again',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.w500,
                    color: theme.textTheme.bodyLarge?.color,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

            // Spacer
            const SizedBox(height: 20),

            // OK button - Circular green button
            Expanded(
              flex: 1,
              child: Center(
                child: Container(
                  width: isSmallScreen ? 40 : 45, // 80 : 90,
                  height: isSmallScreen ? 20 : 22.5, // 80 : 90,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: () {
                        Navigator.of(context).pop();
                        onOkPressed?.call();
                      },
                      child: Center(
                        child: Text(
                          'OK',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 16 : 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
