import '../supabase/config.dart';
import '../bloc/posts_bloc/posts_state.dart';
import 'supabase_service.dart';

/// Service for managing posts and social interactions
class PostsService extends BaseSupabaseService {
  /// Get user feed (posts from followed users)
  static Future<List<Post>> getUserFeed({
    int limit = 20,
    int offset = 0,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return [];

    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_user_feed',
        {'user_uuid': userId, 'limit_count': limit, 'offset_count': offset},
      );

      return response?.map((json) => Post.fromJson(json)).toList() ?? [];
    });
  }

  /// Get trending posts
  static Future<List<Post>> getTrendingPosts({
    String timePeriod = '24 hours',
    int limit = 20,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_trending_posts',
        {'time_period': timePeriod, 'limit_count': limit},
      );

      return response?.map((json) => Post.fromJson(json)).toList() ?? [];
    });
  }

  /// Get user's posts
  static Future<List<Post>> getUserPosts({
    required String userId,
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.client
          .from(DatabaseTables.posts)
          .select('''
            id, user_id, content, media_urls, post_type, likes_count, 
            comments_count, shares_count, created_at,
            profiles!posts_user_id_fkey(username, full_name, avatar_url)
          ''')
          .eq('user_id', userId)
          .eq('status', 'published')
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return response.map((json) => Post.fromJson(json)).toList();
    });
  }

  /// Create a new post
  static Future<Post> createPost({
    required String content,
    List<String> mediaUrls = const [],
    String postType = 'text',
    String? productId,
    List<String> tags = const [],
    String? location,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.posts)
              .insert({
                'user_id': userId,
                'content': content,
                'media_urls': mediaUrls,
                'post_type': postType,
                'product_id': productId,
                'tags': tags,
                'location': location,
                'status': 'published',
              })
              .select('''
            id, user_id, content, media_urls, post_type, likes_count, 
            comments_count, shares_count, created_at,
            profiles!posts_user_id_fkey(username, full_name, avatar_url)
          ''')
              .single();

      return Post.fromJson(response);
    });
  }

  /// Update a post
  static Future<Post> updatePost({
    required String postId,
    String? content,
    List<String>? mediaUrls,
    List<String>? tags,
    String? location,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final updateData = <String, dynamic>{};
      if (content != null) updateData['content'] = content;
      if (mediaUrls != null) updateData['media_urls'] = mediaUrls;
      if (tags != null) updateData['tags'] = tags;
      if (location != null) updateData['location'] = location;
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.posts)
              .update(updateData)
              .eq('id', postId)
              .select('''
            id, user_id, content, media_urls, post_type, likes_count, 
            comments_count, shares_count, created_at,
            profiles!posts_user_id_fkey(username, full_name, avatar_url)
          ''')
              .single();

      return Post.fromJson(response);
    });
  }

  /// Delete a post
  static Future<void> deletePost(String postId) async {
    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.posts)
          .delete()
          .eq('id', postId);
    });
  }

  /// Like a post
  static Future<void> likePost(String postId) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client.from(DatabaseTables.likes).insert({
        'user_id': userId,
        'post_id': postId,
      });
    });
  }

  /// Unlike a post
  static Future<void> unlikePost(String postId) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.likes)
          .delete()
          .eq('user_id', userId)
          .eq('post_id', postId);
    });
  }

  /// Check if user has liked a post
  static Future<bool> hasLikedPost(String postId) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return false;

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.likes)
              .select('id')
              .eq('user_id', userId)
              .eq('post_id', postId)
              .maybeSingle();

      return response != null;
    });
  }

  /// Get user's liked posts
  static Future<List<Post>> getUserLikedPosts({
    int limit = 20,
    int offset = 0,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return [];

    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_user_liked_posts',
        {'user_uuid': userId, 'limit_count': limit, 'offset_count': offset},
      );

      return response?.map((json) => Post.fromJson(json)).toList() ?? [];
    });
  }

  /// Get post comments
  static Future<List<Comment>> getPostComments({
    required String postId,
    int limit = 50,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_post_comments',
        {'post_uuid': postId, 'limit_count': limit, 'offset_count': offset},
      );

      return response?.map((json) => Comment.fromJson(json)).toList() ?? [];
    });
  }

  /// Add comment to post
  static Future<Comment> addComment({
    required String postId,
    required String content,
    String? parentId,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.comments)
              .insert({
                'user_id': userId,
                'post_id': postId,
                'content': content,
                'parent_id': parentId,
              })
              .select('''
            id, user_id, post_id, parent_id, content, likes_count, 
            replies_count, created_at,
            profiles!comments_user_id_fkey(username, full_name, avatar_url)
          ''')
              .single();

      return Comment.fromJson(response);
    });
  }

  /// Update comment
  static Future<Comment> updateComment({
    required String commentId,
    required String content,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.comments)
              .update({
                'content': content,
                'is_edited': true,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', commentId)
              .select('''
            id, user_id, post_id, parent_id, content, likes_count, 
            replies_count, created_at,
            profiles!comments_user_id_fkey(username, full_name, avatar_url)
          ''')
              .single();

      return Comment.fromJson(response);
    });
  }

  /// Delete comment
  static Future<void> deleteComment(String commentId) async {
    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.comments)
          .delete()
          .eq('id', commentId);
    });
  }

  /// Share post
  static Future<void> sharePost(String postId) async {
    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.posts)
          .update({'shares_count': 'shares_count + 1'})
          .eq('id', postId);
    });
  }

  /// Report post
  static Future<void> reportPost({
    required String postId,
    required String reason,
    String? description,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      // This would typically go to a reports table
      // For now, we'll create a notification to moderators
      await BaseSupabaseService.executeRPC('create_notification', {
        'recipient_id': 'moderator', // This would be handled differently
        'notification_type': 'report',
        'title': 'Post Reported',
        'message': 'A post has been reported for: $reason',
        'data': {
          'post_id': postId,
          'reporter_id': userId,
          'reason': reason,
          'description': description,
        },
      });
    });
  }
}

/// Comment model for posts
class Comment {
  final String id;
  final String userId;
  final String postId;
  final String? parentId;
  final String content;
  final String username;
  final String fullName;
  final String? avatarUrl;
  final int likesCount;
  final int repliesCount;
  final DateTime createdAt;
  final bool isLiked;
  final int level;

  const Comment({
    required this.id,
    required this.userId,
    required this.postId,
    this.parentId,
    required this.content,
    required this.username,
    required this.fullName,
    this.avatarUrl,
    this.likesCount = 0,
    this.repliesCount = 0,
    required this.createdAt,
    this.isLiked = false,
    this.level = 0,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      postId: json['post_id'] as String,
      parentId: json['parent_id'] as String?,
      content: json['content'] as String,
      username: json['username'] as String,
      fullName: json['full_name'] as String,
      avatarUrl: json['avatar_url'] as String?,
      likesCount: json['likes_count'] as int? ?? 0,
      repliesCount: json['replies_count'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      isLiked: json['is_liked'] as bool? ?? false,
      level: json['level'] as int? ?? 0,
    );
  }
}
