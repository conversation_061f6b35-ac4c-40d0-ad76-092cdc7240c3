import 'package:equatable/equatable.dart';
import 'package:business_app/models/category/product_category.dart';

enum CategorySelectionStatus {
  initial,
  loading,
  loaded,
  error,
  validating,
  valid,
  invalid,
}

class CategorySelectionState extends Equatable {
  final CategorySelectionStatus status;
  final List<ProductCategory> categories;
  final List<String> selectedCategoryIds;
  final String? errorMessage;
  final bool isValid;
  final int minSelectionRequired;
  final bool showValidationError;

  const CategorySelectionState({
    this.status = CategorySelectionStatus.initial,
    this.categories = const [],
    this.selectedCategoryIds = const [],
    this.errorMessage,
    this.isValid = false,
    this.minSelectionRequired = 4,
    this.showValidationError = false,
  });

  // Getters
  List<ProductCategory> get selectedCategories {
    return categories
        .where((category) => selectedCategoryIds.contains(category.id))
        .toList();
  }

  int get selectedCount => selectedCategoryIds.length;

  bool get hasMinimumSelection => selectedCount >= minSelectionRequired;

  String get validationMessage {
    if (selectedCount == 0) {
      return 'Please select at least $minSelectionRequired categories to continue';
    } else if (selectedCount < minSelectionRequired) {
      final remaining = minSelectionRequired - selectedCount;
      return 'Select $remaining more ${remaining == 1 ? 'category' : 'categories'} to continue';
    }
    return '';
  }

  // Factory constructors
  factory CategorySelectionState.initial() {
    return const CategorySelectionState();
  }

  CategorySelectionState loading() {
    return copyWith(
      status: CategorySelectionStatus.loading,
      errorMessage: null,
    );
  }

  CategorySelectionState loaded({required List<ProductCategory> categories}) {
    return copyWith(
      status: CategorySelectionStatus.loaded,
      categories: categories,
      errorMessage: null,
    );
  }

  CategorySelectionState error(String message) {
    return copyWith(
      status: CategorySelectionStatus.error,
      errorMessage: message,
    );
  }

  CategorySelectionState categorySelected(String categoryId) {
    final updatedSelectedIds = List<String>.from(selectedCategoryIds);
    if (!updatedSelectedIds.contains(categoryId)) {
      updatedSelectedIds.add(categoryId);
    }

    return copyWith(
      selectedCategoryIds: updatedSelectedIds,
      isValid: updatedSelectedIds.length >= minSelectionRequired,
      showValidationError: false,
    );
  }

  CategorySelectionState categoryDeselected(String categoryId) {
    final updatedSelectedIds = List<String>.from(selectedCategoryIds);
    updatedSelectedIds.remove(categoryId);

    return copyWith(
      selectedCategoryIds: updatedSelectedIds,
      isValid: updatedSelectedIds.length >= minSelectionRequired,
    );
  }

  CategorySelectionState clearSelections() {
    return copyWith(
      selectedCategoryIds: [],
      isValid: false,
      showValidationError: false,
    );
  }

  CategorySelectionState validating() {
    return copyWith(
      status: CategorySelectionStatus.validating,
    );
  }

  CategorySelectionState validated({required bool isValid}) {
    return copyWith(
      status: isValid ? CategorySelectionStatus.valid : CategorySelectionStatus.invalid,
      isValid: isValid,
      showValidationError: !isValid,
    );
  }

  CategorySelectionState copyWith({
    CategorySelectionStatus? status,
    List<ProductCategory>? categories,
    List<String>? selectedCategoryIds,
    String? errorMessage,
    bool? isValid,
    int? minSelectionRequired,
    bool? showValidationError,
  }) {
    return CategorySelectionState(
      status: status ?? this.status,
      categories: categories ?? this.categories,
      selectedCategoryIds: selectedCategoryIds ?? this.selectedCategoryIds,
      errorMessage: errorMessage,
      isValid: isValid ?? this.isValid,
      minSelectionRequired: minSelectionRequired ?? this.minSelectionRequired,
      showValidationError: showValidationError ?? this.showValidationError,
    );
  }

  @override
  List<Object?> get props => [
        status,
        categories,
        selectedCategoryIds,
        errorMessage,
        isValid,
        minSelectionRequired,
        showValidationError,
      ];
}
