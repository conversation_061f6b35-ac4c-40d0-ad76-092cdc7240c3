import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:business_app/bloc/connectivity_bloc/connectivity_bloc.dart';
import 'package:business_app/widgets/connectivity/connectivity_snackbar.dart';

/// Utility class for connectivity-related operations
class ConnectivityUtils {
  /// Check if device is currently online
  static bool isOnline(BuildContext context) {
    final state = context.read<ConnectivityBloc>().state;
    return state.isConnected;
  }

  /// Check if device is currently offline
  static bool isOffline(BuildContext context) {
    final state = context.read<ConnectivityBloc>().state;
    return state.isDisconnected;
  }

  /// Check if device has limited connection (network but no internet)
  static bool hasLimitedConnection(BuildContext context) {
    final state = context.read<ConnectivityBloc>().state;
    return state.hasLimitedConnection;
  }

  /// Get current connectivity state
  static ConnectivityState getCurrentState(BuildContext context) {
    return context.read<ConnectivityBloc>().state;
  }

  /// Show offline message if not connected
  static void showOfflineMessageIfNeeded(BuildContext context) {
    final state = context.read<ConnectivityBloc>().state;
    if (!state.isConnected) {
      showOfflineMessage(context, state);
    }
  }

  /// Show offline message
  static void showOfflineMessage(
    BuildContext context,
    ConnectivityState state,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      ConnectivitySnackBar.offline(
        state: state,
        onRetry: () => _retryConnection(context),
        onOpenSettings: () => _openSettings(context, state),
        onDismiss: () {},
      ),
    );
  }

  /// Show connection restored message
  static void showConnectionRestoredMessage(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(ConnectivitySnackBar.connected(onDismiss: () {}));
  }

  /// Execute function only if online
  static Future<T?> executeIfOnline<T>(
    BuildContext context,
    Future<T> Function() function, {
    VoidCallback? onOffline,
    bool showOfflineMessage = true,
  }) async {
    if (isOnline(context)) {
      return await function();
    } else {
      if (showOfflineMessage) {
        showOfflineMessageIfNeeded(context);
      }
      onOffline?.call();
      return null;
    }
  }

  /// Execute function with connectivity check and retry
  static Future<T?> executeWithConnectivityCheck<T>(
    BuildContext context,
    Future<T> Function() function, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
    bool showMessages = true,
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      if (isOnline(context)) {
        try {
          return await function();
        } catch (e) {
          attempts++;
          if (attempts >= maxRetries) rethrow;
          await Future.delayed(retryDelay);
        }
      } else {
        if (showMessages) {
          showOfflineMessageIfNeeded(context);
        }
        return null;
      }
    }

    return null;
  }

  /// Wait for connection to be restored
  static Future<void> waitForConnection(
    BuildContext context, {
    Duration timeout = const Duration(minutes: 5),
  }) async {
    if (isOnline(context)) return;

    final completer = Completer<void>();
    late StreamSubscription subscription;

    subscription = context.read<ConnectivityBloc>().stream.listen((state) {
      if (state.isConnected && !completer.isCompleted) {
        completer.complete();
        subscription.cancel();
      }
    });

    // Set timeout
    Timer(timeout, () {
      if (!completer.isCompleted) {
        completer.completeError(
          TimeoutException('Connection timeout', timeout),
        );
        subscription.cancel();
      }
    });

    return completer.future;
  }

  /// Retry connection
  static void _retryConnection(BuildContext context) {
    context.read<ConnectivityBloc>().add(const ConnectivityRetryRequested());
  }

  /// Open device settings
  static void _openSettings(BuildContext context, ConnectivityState state) {
    ConnectivitySettingsType settingsType;

    switch (state.connectivityResult) {
      case ConnectivityResult.wifi:
        settingsType = ConnectivitySettingsType.wifi;
        break;
      case ConnectivityResult.mobile:
        settingsType = ConnectivitySettingsType.mobileData;
        break;
      default:
        settingsType = ConnectivitySettingsType.general;
    }

    context.read<ConnectivityBloc>().add(
      ConnectivityOpenSettingsRequested(settingsType: settingsType),
    );
  }
}

/// Mixin for widgets that need connectivity awareness
mixin ConnectivityAware<T extends StatefulWidget> on State<T> {
  late StreamSubscription<ConnectivityState> _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _connectivitySubscription = context.read<ConnectivityBloc>().stream.listen(
      onConnectivityChanged,
    );
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    super.dispose();
  }

  /// Override this method to handle connectivity changes
  void onConnectivityChanged(ConnectivityState state) {
    // Default implementation - override in your widget
  }

  /// Check if currently online
  bool get isOnline => ConnectivityUtils.isOnline(context);

  /// Check if currently offline
  bool get isOffline => ConnectivityUtils.isOffline(context);

  /// Execute function only if online
  Future<R?> executeIfOnline<R>(
    Future<R> Function() function, {
    VoidCallback? onOffline,
    bool showOfflineMessage = true,
  }) {
    return ConnectivityUtils.executeIfOnline(
      context,
      function,
      onOffline: onOffline,
      showOfflineMessage: showOfflineMessage,
    );
  }
}

/// Extension on BuildContext for easy connectivity access
extension ConnectivityContext on BuildContext {
  /// Check if device is currently online
  bool get isOnline => ConnectivityUtils.isOnline(this);

  /// Check if device is currently offline
  bool get isOffline => ConnectivityUtils.isOffline(this);

  /// Check if device has limited connection
  bool get hasLimitedConnection => ConnectivityUtils.hasLimitedConnection(this);

  /// Get current connectivity state
  ConnectivityState get connectivityState =>
      ConnectivityUtils.getCurrentState(this);

  /// Show offline message if needed
  void showOfflineMessageIfNeeded() =>
      ConnectivityUtils.showOfflineMessageIfNeeded(this);

  /// Execute function only if online
  Future<T?> executeIfOnline<T>(
    Future<T> Function() function, {
    VoidCallback? onOffline,
    bool showOfflineMessage = true,
  }) {
    return ConnectivityUtils.executeIfOnline(
      this,
      function,
      onOffline: onOffline,
      showOfflineMessage: showOfflineMessage,
    );
  }
}
