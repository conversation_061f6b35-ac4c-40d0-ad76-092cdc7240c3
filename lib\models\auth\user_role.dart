import 'package:equatable/equatable.dart';

/// Enum representing different user roles in the application
enum UserRole {
  user('user'),
  admin('admin'),
  moderator('moderator'),
  superAdmin('super_admin');

  const UserRole(this.value);
  final String value;

  /// Convert string to UserRole enum
  static UserRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'user':
        return UserRole.user;
      case 'admin':
        return UserRole.admin;
      case 'moderator':
        return UserRole.moderator;
      case 'super_admin':
      case 'superadmin':
        return UserRole.superAdmin;
      default:
        return UserRole.user; // Default to user role
    }
  }

  /// Get display name for the role
  String get displayName {
    switch (this) {
      case UserRole.user:
        return 'User';
      case UserRole.admin:
        return 'Admin';
      case UserRole.moderator:
        return 'Moderator';
      case UserRole.superAdmin:
        return 'Super Admin';
    }
  }

  /// Get role description
  String get description {
    switch (this) {
      case UserRole.user:
        return 'Regular user with basic access';
      case UserRole.admin:
        return 'Administrator with limited management access';
      case UserRole.moderator:
        return 'Content moderator with review permissions';
      case UserRole.superAdmin:
        return 'Super Administrator with full system access';
    }
  }

  /// Check if role has admin privileges
  bool get isAdmin => this == UserRole.admin || this == UserRole.superAdmin;

  /// Check if role has moderator privileges
  bool get isModerator => this == UserRole.moderator || isAdmin;

  /// Check if role has super admin privileges
  bool get isSuperAdmin => this == UserRole.superAdmin;

  /// Get role hierarchy level (higher number = more privileges)
  int get hierarchyLevel {
    switch (this) {
      case UserRole.user:
        return 1;
      case UserRole.moderator:
        return 2;
      case UserRole.admin:
        return 3;
      case UserRole.superAdmin:
        return 4;
    }
  }
}

/// Class representing user permissions based on role
class UserPermissions extends Equatable {
  final UserRole role;

  const UserPermissions({required this.role});

  // User Management Permissions
  bool get canViewUsers => role.isModerator;
  bool get canEditUsers => role.isAdmin;
  bool get canDeleteUsers => role.isSuperAdmin;
  bool get canAssignRoles => role.isSuperAdmin;

  // Content Management Permissions
  bool get canModerateContent => role.isModerator;
  bool get canDeleteContent => role.isAdmin;
  bool get canViewReports => role.isModerator;
  bool get canHandleReports => role.isModerator;

  // System Management Permissions
  bool get canAccessAnalytics => role.isAdmin;
  bool get canManageSystem => role.isSuperAdmin;
  bool get canViewFinancials => role.isAdmin;
  bool get canManageFinancials => role.isSuperAdmin;

  // Customer Support Permissions
  bool get canProvideSupport => role.isModerator;
  bool get canAccessSupportTools => role.isAdmin;

  // Admin Panel Access
  bool get canAccessAdminPanel => role.isModerator;
  bool get canAccessFullAdminPanel => role.isAdmin;

  /// Get all permissions as a map for easy checking
  Map<String, bool> get allPermissions => {
    'canViewUsers': canViewUsers,
    'canEditUsers': canEditUsers,
    'canDeleteUsers': canDeleteUsers,
    'canAssignRoles': canAssignRoles,
    'canModerateContent': canModerateContent,
    'canDeleteContent': canDeleteContent,
    'canViewReports': canViewReports,
    'canHandleReports': canHandleReports,
    'canAccessAnalytics': canAccessAnalytics,
    'canManageSystem': canManageSystem,
    'canViewFinancials': canViewFinancials,
    'canManageFinancials': canManageFinancials,
    'canProvideSupport': canProvideSupport,
    'canAccessSupportTools': canAccessSupportTools,
    'canAccessAdminPanel': canAccessAdminPanel,
    'canAccessFullAdminPanel': canAccessFullAdminPanel,
  };

  @override
  List<Object?> get props => [role];
}

/// Extension to add role-based functionality to UserRole
extension UserRoleExtension on UserRole {
  /// Get permissions for this role
  UserPermissions get permissions => UserPermissions(role: this);

  /// Check if this role can access a specific feature
  bool canAccess(String feature) {
    final permissions = this.permissions.allPermissions;
    return permissions[feature] ?? false;
  }

  /// Get role color for UI display
  String get colorHex {
    switch (this) {
      case UserRole.user:
        return '#6B7280'; // Gray
      case UserRole.moderator:
        return '#F59E0B'; // Amber
      case UserRole.admin:
        return '#3B82F6'; // Blue
      case UserRole.superAdmin:
        return '#EF4444'; // Red
    }
  }

  /// Get role icon for UI display
  String get iconName {
    switch (this) {
      case UserRole.user:
        return 'person';
      case UserRole.moderator:
        return 'shield';
      case UserRole.admin:
        return 'admin_panel_settings';
      case UserRole.superAdmin:
        return 'security';
    }
  }
}
