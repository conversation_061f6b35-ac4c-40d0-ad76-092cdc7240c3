import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

/// Professional connectivity service for handling network operations
/// Used by the ConnectivityBloc and other parts of the app
class ConnectivityService {
  static ConnectivityService? _instance;
  static ConnectivityService get instance =>
      _instance ??= ConnectivityService._();

  ConnectivityService._();

  final Connectivity _connectivity = Connectivity();
  late final InternetConnection _internetConnection;

  bool _isInitialized = false;

  /// Initialize the connectivity service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize internet connection checker with optimized settings
    _internetConnection = InternetConnection.createInstance(
      customCheckOptions: [
        // Primary check - fast and reliable
        InternetCheckOption(
          uri: Uri.parse('https://icanhazip.com/'),
          timeout: const Duration(seconds: 3),
        ),
        // Secondary check - fallback
        InternetCheckOption(
          uri: Uri.parse('https://jsonplaceholder.typicode.com/posts/1'),
          timeout: const Duration(seconds: 3),
        ),
        // Tertiary check - Google DNS
        InternetCheckOption(
          uri: Uri.parse('https://dns.google/resolve?name=google.com&type=A'),
          timeout: const Duration(seconds: 3),
        ),
      ],
      useDefaultOptions: false,
    );

    _isInitialized = true;
  }

  /// Get current connectivity status
  Future<ConnectivityResult> getCurrentConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      return results.isNotEmpty ? results.first : ConnectivityResult.none;
    } catch (e) {
      return ConnectivityResult.none;
    }
  }

  /// Check if device has internet access
  Future<bool> hasInternetAccess() async {
    if (!_isInitialized) await initialize();

    try {
      return await _internetConnection.hasInternetAccess;
    } catch (e) {
      return false;
    }
  }

  /// Get connectivity stream
  Stream<ConnectivityResult> get connectivityStream {
    return _connectivity.onConnectivityChanged.map(
      (results) => results.isNotEmpty ? results.first : ConnectivityResult.none,
    );
  }

  /// Get internet status stream
  Stream<InternetStatus> get internetStatusStream {
    if (!_isInitialized) {
      throw StateError(
        'ConnectivityService not initialized. Call initialize() first.',
      );
    }
    return _internetConnection.onStatusChange;
  }

  /// Check connectivity with detailed information
  Future<ConnectivityInfo> getDetailedConnectivityInfo() async {
    final connectivityResult = await getCurrentConnectivity();
    final hasInternet = await hasInternetAccess();

    return ConnectivityInfo(
      connectivityResult: connectivityResult,
      hasInternetAccess: hasInternet,
      timestamp: DateTime.now(),
    );
  }

  /// Ping a specific host to check connectivity
  Future<bool> pingHost(
    String host, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    try {
      final result = await InternetAddress.lookup(host).timeout(timeout);
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Check if specific service is reachable
  Future<bool> isServiceReachable(
    String url, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    try {
      final uri = Uri.parse(url);
      final request = await HttpClient().getUrl(uri).timeout(timeout);
      final response = await request.close().timeout(timeout);
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Get network interface information
  Future<List<NetworkInterface>> getNetworkInterfaces() async {
    try {
      return await NetworkInterface.list();
    } catch (e) {
      return [];
    }
  }

  /// Check if connected to WiFi
  Future<bool> isConnectedToWiFi() async {
    final connectivity = await getCurrentConnectivity();
    return connectivity == ConnectivityResult.wifi;
  }

  /// Check if connected to mobile data
  Future<bool> isConnectedToMobileData() async {
    final connectivity = await getCurrentConnectivity();
    return connectivity == ConnectivityResult.mobile;
  }

  /// Get connection quality estimate (rough estimate based on ping)
  Future<ConnectionQuality> getConnectionQuality() async {
    if (!await hasInternetAccess()) {
      return ConnectionQuality.none;
    }

    try {
      final stopwatch = Stopwatch()..start();
      final isReachable = await pingHost(
        '*******',
        timeout: const Duration(seconds: 2),
      );
      stopwatch.stop();

      if (!isReachable) return ConnectionQuality.poor;

      final latency = stopwatch.elapsedMilliseconds;

      if (latency < 100) return ConnectionQuality.excellent;
      if (latency < 300) return ConnectionQuality.good;
      if (latency < 600) return ConnectionQuality.fair;
      return ConnectionQuality.poor;
    } catch (e) {
      return ConnectionQuality.poor;
    }
  }

  /// Dispose resources
  void dispose() {
    // Clean up any resources if needed
    _isInitialized = false;
  }
}

/// Detailed connectivity information
class ConnectivityInfo {
  final ConnectivityResult connectivityResult;
  final bool hasInternetAccess;
  final DateTime timestamp;

  const ConnectivityInfo({
    required this.connectivityResult,
    required this.hasInternetAccess,
    required this.timestamp,
  });

  bool get isConnected =>
      connectivityResult != ConnectivityResult.none && hasInternetAccess;
  bool get hasLimitedConnection =>
      connectivityResult != ConnectivityResult.none && !hasInternetAccess;
  bool get isDisconnected => connectivityResult == ConnectivityResult.none;

  String get connectionTypeString {
    switch (connectivityResult) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
        return 'No Connection';
    }
  }

  @override
  String toString() {
    return 'ConnectivityInfo(type: $connectionTypeString, hasInternet: $hasInternetAccess, timestamp: $timestamp)';
  }
}

/// Connection quality levels
enum ConnectionQuality { none, poor, fair, good, excellent }

extension ConnectionQualityExtension on ConnectionQuality {
  String get displayName {
    switch (this) {
      case ConnectionQuality.none:
        return 'No Connection';
      case ConnectionQuality.poor:
        return 'Poor';
      case ConnectionQuality.fair:
        return 'Fair';
      case ConnectionQuality.good:
        return 'Good';
      case ConnectionQuality.excellent:
        return 'Excellent';
    }
  }

  Color get color {
    switch (this) {
      case ConnectionQuality.none:
        return const Color(0xFFE53E3E);
      case ConnectionQuality.poor:
        return const Color(0xFFED8936);
      case ConnectionQuality.fair:
        return const Color(0xFFECC94B);
      case ConnectionQuality.good:
        return const Color(0xFF48BB78);
      case ConnectionQuality.excellent:
        return const Color(0xFF38A169);
    }
  }
}
