import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/auth/auth_result.dart';

/// Professional error handling service for authentication and app-wide errors
class ErrorHandlerService {
  static const String _logTag = '🚨 ErrorHandler';

  // Logger instance for production logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: false, // Disable colors for production logs
      printEmojis: false,
      printTime: true,
    ),
  );

  /// Handle authentication errors with user-friendly messages and proper logging
  static AuthError handleAuthError(dynamic error, {String? context}) {
    final authError = AuthError.fromException(error);

    // Log the error for debugging
    _logError(
      error: error,
      context: context ?? 'Authentication',
      userMessage: authError.message,
      errorType: authError.type.toString(),
    );

    return authError;
  }

  /// Handle general app errors
  static AppError handleAppError(
    dynamic error, {
    String? context,
    String? userMessage,
    ErrorSeverity severity = ErrorSeverity.medium,
  }) {
    final appError = AppError.fromException(
      error,
      context: context,
      userMessage: userMessage,
      severity: severity,
    );

    // Log the error
    _logError(
      error: error,
      context: context ?? 'Application',
      userMessage: appError.userMessage,
      errorType: appError.type.toString(),
      severity: severity,
    );

    return appError;
  }

  /// Handle network errors specifically
  static NetworkError handleNetworkError(dynamic error, {String? context}) {
    final networkError = NetworkError.fromException(error);

    _logError(
      error: error,
      context: context ?? 'Network',
      userMessage: networkError.userMessage,
      errorType: 'NetworkError',
      severity: ErrorSeverity.high,
    );

    return networkError;
  }

  /// Log errors with proper formatting and context
  static void _logError({
    required dynamic error,
    required String context,
    required String userMessage,
    required String errorType,
    ErrorSeverity severity = ErrorSeverity.medium,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    final severityIcon = _getSeverityIcon(severity);

    final logMessage = '''
$_logTag $severityIcon [$context] $errorType
├─ Time: $timestamp
├─ User Message: $userMessage
├─ Technical Error: ${error.toString()}
└─ Severity: ${severity.name.toUpperCase()}
''';

    if (kDebugMode) {
      // In debug mode, use developer.log for better formatting
      developer.log(
        logMessage,
        name: _logTag,
        error: error,
        level: _getSeverityLevel(severity),
      );
    } else {
      // In release mode, use logger with appropriate level
      switch (severity) {
        case ErrorSeverity.low:
          _logger.i(logMessage);
          break;
        case ErrorSeverity.medium:
          _logger.w(logMessage);
          break;
        case ErrorSeverity.high:
          _logger.e(logMessage, error: error);
          break;
        case ErrorSeverity.critical:
          _logger.f(logMessage, error: error);
          break;
      }

      // TODO: Send to crash reporting service (Firebase Crashlytics, Sentry, etc.)
      // _sendToCrashReporting(error, context, severity);
    }
  }

  /// Get severity icon for logging
  static String _getSeverityIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return '⚠️';
      case ErrorSeverity.medium:
        return '🔥';
      case ErrorSeverity.high:
        return '💥';
      case ErrorSeverity.critical:
        return '🚨';
    }
  }

  /// Get severity level for developer.log
  static int _getSeverityLevel(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return 500; // INFO
      case ErrorSeverity.medium:
        return 900; // WARNING
      case ErrorSeverity.high:
        return 1000; // SEVERE
      case ErrorSeverity.critical:
        return 1200; // SHOUT
    }
  }

  /// Show user-friendly error dialog or snackbar
  static String getDisplayMessage(dynamic error) {
    if (error is AuthError) {
      return error.message;
    } else if (error is AppError) {
      return error.userMessage;
    } else if (error is NetworkError) {
      return error.userMessage;
    } else if (error is PostgrestException) {
      return _handlePostgrestError(error);
    } else if (error is AuthException) {
      return _handleAuthException(error);
    } else if (error is AuthApiException) {
      return _handleAuthApiException(error);
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Handle Supabase Postgrest errors
  static String _handlePostgrestError(PostgrestException error) {
    switch (error.code) {
      case '23505': // Unique violation
        return 'This information is already in use. Please try different details.';
      case '23503': // Foreign key violation
        return 'Invalid reference data. Please check your input.';
      case '42501': // Insufficient privilege
        return 'You don\'t have permission to perform this action.';
      case '42P01': // Undefined table
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return 'Database error occurred. Please try again.';
    }
  }

  /// Handle Supabase Auth errors
  static String _handleAuthException(AuthException error) {
    // Log the full error for debugging
    if (kDebugMode) {
      developer.log(
        'AuthException Details:\n'
        '├─ Status Code: ${error.statusCode}\n'
        '└─ Message: ${error.message}',
        name: _logTag,
        error: error,
        level: 1000, // SEVERE
      );
    }

    // Check for specific error messages first
    final message = error.message.toLowerCase();

    // OTP-specific errors
    if (message.contains('invalid') && message.contains('otp')) {
      return 'Invalid verification code. Please check and try again.';
    }
    if (message.contains('expired') && message.contains('otp')) {
      return 'Verification code has expired. Please request a new one.';
    }
    if (message.contains('otp') && message.contains('not found')) {
      return 'Verification session not found. Please start again.';
    }

    // User existence errors
    if (message.contains('user not found') ||
        message.contains('no user found') ||
        message.contains('email not found')) {
      return 'No account found with this email address.';
    }

    // Credential errors
    if (message.contains('invalid login credentials')) {
      return 'Invalid email or password. Please try again.';
    }

    // Email verification errors
    if (message.contains('email not confirmed') ||
        message.contains('verify your email') ||
        message.contains('email confirmation')) {
      return 'Please verify your email before signing in.';
    }

    switch (error.statusCode) {
      case '400':
        if (message.contains('invalid login credentials')) {
          return 'Invalid email or password. Please try again.';
        }
        if (message.contains('signup disabled')) {
          return 'Account registration is currently disabled.';
        }
        return 'Invalid request. Please check your input.';
      case '401':
        return 'Authentication failed. Please check your credentials.';
      case '403':
        return 'Access denied. You don\'t have permission.';
      case '404':
        return 'No account found with this email address.';
      case '422':
        if (message.contains('email')) {
          return 'Please enter a valid email address.';
        }
        if (message.contains('password')) {
          return 'Password does not meet requirements.';
        }
        return 'Invalid data provided. Please correct and try again.';
      case '429':
        return 'Too many attempts. Please wait before trying again.';
      case '500':
        return 'Server error. Please try again later.';
      default:
        // Network errors
        if (message.contains('network') || message.contains('connection')) {
          return 'Network error. Please check your connection and try again.';
        }

        return error.message.isNotEmpty
            ? error.message
            : 'Authentication error occurred. Please try again.';
    }
  }

  /// Handle Supabase AuthApiException errors
  static String _handleAuthApiException(AuthApiException error) {
    // Log the full error for debugging
    if (kDebugMode) {
      developer.log(
        'AuthApiException Details:\n'
        '├─ Status Code: ${error.statusCode}\n'
        '├─ Message: ${error.message}\n'
        '└─ Code: ${error.code}',
        name: _logTag,
        error: error,
        level: 1000, // SEVERE
      );
    }

    // Handle specific error codes
    switch (error.code) {
      case 'same_password':
        // Keep the technical error in console (already logged above)
        return 'New password can not be the same as old password';
      case 'weak_password':
        return 'Password is too weak. Please use at least 8 characters with letters and numbers.';
      case 'invalid_credentials':
        return 'Invalid email or password. Please try again.';
      case 'email_not_confirmed':
        return 'Please verify your email address before signing in.';
      case 'phone_not_confirmed':
        return 'Please verify your phone number before signing in.';
      case 'signup_disabled':
        return 'Account registration is currently disabled. Please try again later.';
      case 'over_email_send_rate_limit':
        return 'Please wait a few seconds before requesting another verification code.';
      default:
        // Check message content for additional patterns
        final message = error.message.toLowerCase();

        if (message.contains('password') && message.contains('different')) {
          return 'New password can not be the same as old password';
        }
        if (message.contains('invalid') && message.contains('credentials')) {
          return 'Invalid email or password. Please try again.';
        }
        if (message.contains('too many requests')) {
          return 'Please wait a few seconds before trying again.';
        }

        // Return the original message if it's user-friendly, otherwise a generic message
        return error.message.isNotEmpty && error.message.length < 100
            ? error.message
            : 'Authentication error occurred. Please try again.';
    }
  }
}

/// General application error class
class AppError {
  final AppErrorType type;
  final String userMessage;
  final String? technicalMessage;
  final String? context;
  final ErrorSeverity severity;
  final DateTime timestamp;

  AppError({
    required this.type,
    required this.userMessage,
    this.technicalMessage,
    this.context,
    this.severity = ErrorSeverity.medium,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory AppError.fromException(
    dynamic exception, {
    String? context,
    String? userMessage,
    ErrorSeverity severity = ErrorSeverity.medium,
  }) {
    final String message = exception.toString().toLowerCase();

    AppErrorType type;
    String defaultUserMessage;

    if (message.contains('network') || message.contains('connection')) {
      type = AppErrorType.network;
      defaultUserMessage =
          'Network connection error. Please check your internet.';
    } else if (message.contains('timeout')) {
      type = AppErrorType.timeout;
      defaultUserMessage = 'Request timed out. Please try again.';
    } else if (message.contains('permission') ||
        message.contains('unauthorized')) {
      type = AppErrorType.permission;
      defaultUserMessage = 'You don\'t have permission to perform this action.';
    } else if (message.contains('validation') || message.contains('invalid')) {
      type = AppErrorType.validation;
      defaultUserMessage = 'Invalid data provided. Please check your input.';
    } else {
      type = AppErrorType.unknown;
      defaultUserMessage = 'An unexpected error occurred. Please try again.';
    }

    return AppError(
      type: type,
      userMessage: userMessage ?? defaultUserMessage,
      technicalMessage: exception.toString(),
      context: context,
      severity: severity,
    );
  }

  @override
  String toString() {
    return 'AppError(type: $type, message: $userMessage, context: $context)';
  }
}

/// Network-specific error class
class NetworkError {
  final NetworkErrorType type;
  final String userMessage;
  final String? technicalMessage;

  NetworkError({
    required this.type,
    required this.userMessage,
    this.technicalMessage,
  });

  factory NetworkError.fromException(dynamic exception) {
    final String message = exception.toString().toLowerCase();

    NetworkErrorType type;
    String userMessage;

    if (message.contains('no internet') ||
        message.contains('network unreachable')) {
      type = NetworkErrorType.noConnection;
      userMessage =
          'No internet connection. Please check your network settings.';
    } else if (message.contains('timeout')) {
      type = NetworkErrorType.timeout;
      userMessage = 'Connection timed out. Please try again.';
    } else if (message.contains('dns') || message.contains('host')) {
      type = NetworkErrorType.dnsError;
      userMessage = 'Unable to reach server. Please try again later.';
    } else if (message.contains('ssl') || message.contains('certificate')) {
      type = NetworkErrorType.sslError;
      userMessage = 'Secure connection failed. Please try again.';
    } else {
      type = NetworkErrorType.unknown;
      userMessage = 'Network error occurred. Please check your connection.';
    }

    return NetworkError(
      type: type,
      userMessage: userMessage,
      technicalMessage: exception.toString(),
    );
  }
}

/// Error severity levels
enum ErrorSeverity {
  low, // Minor issues, user can continue
  medium, // Moderate issues, some functionality affected
  high, // Major issues, significant functionality affected
  critical, // Critical issues, app may crash or be unusable
}

/// Application error types
enum AppErrorType { network, timeout, permission, validation, storage, unknown }

/// Network error types
enum NetworkErrorType {
  noConnection,
  timeout,
  dnsError,
  sslError,
  serverError,
  unknown,
}
