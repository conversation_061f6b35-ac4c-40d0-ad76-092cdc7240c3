// import 'package:flutter/material.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// // Renamed to avoid conflict with <PERSON><PERSON><PERSON>'s ThemeMode
// enum AppThemeMode { system, light, dark }

// class ThemeProvider with ChangeNotifier {
//   AppThemeMode _themeMode = AppThemeMode.system;
//   static const String _prefKey = 'theme_mode';

//   // Make themes public so they can be accessed from MaterialApp
//   static final ThemeData lightTheme = _createLightTheme();
//   static final ThemeData darkTheme = _createDarkTheme();

//   AppThemeMode get themeMode => _themeMode;

//   ThemeProvider() {
//     _loadTheme();
//   }

//   Future<void> _loadTheme() async {
//     final prefs = await SharedPreferences.getInstance();
//     final modeIndex = prefs.getInt(_prefKey) ?? 0;
//     _themeMode = AppThemeMode.values[modeIndex];
//     notifyListeners();
//   }

//   Future<void> setThemeMode(AppThemeMode mode) async {
//     _themeMode = mode;
//     notifyListeners();
//     final prefs = await SharedPreferences.getInstance();
//     await prefs.setInt(_prefKey, mode.index);
//   }

//   ThemeData getTheme(BuildContext context) {
//     switch (_themeMode) {
//       case AppThemeMode.system:
//         return MediaQuery.platformBrightnessOf(context) == Brightness.dark
//             ? darkTheme
//             : lightTheme;
//       case AppThemeMode.light:
//         return lightTheme;
//       case AppThemeMode.dark:
//         return darkTheme;
//     }
//   }

//   static ThemeData _createLightTheme() {
//     return ThemeData(
//       brightness: Brightness.light,
//       colorScheme: const ColorScheme.light(
//         primary: Colors.blue,
//         secondary: Colors.blueAccent,
//         surface: Colors.white,
//         background: Colors.white,
//       ),
//       appBarTheme: const AppBarTheme(
//         backgroundColor: Colors.white,
//         foregroundColor: Colors.black,
//         elevation: 0,
//       ),
//       cardTheme: CardTheme(
//         elevation: 1,
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//       ),
//     );
//   }

//   //normal dark theme commented out for now
//   static ThemeData _createDarkTheme() {
//     return ThemeData(
//       brightness: Brightness.dark,
//       colorScheme: const ColorScheme.dark(
//         primary: Colors.blue,
//         secondary: Colors.blueAccent,
//         surface: Color(0xFF1E1E1E),
//         background: Color(0xFF121212),
//       ),
//       appBarTheme: const AppBarTheme(
//         backgroundColor: Color(0xFF1E1E1E),
//         elevation: 0,
//       ),
//       cardTheme: CardTheme(
//         elevation: 1,
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//         color: const Color(0xFF1E1E1E),
//       ),
//     );
//   }
// }

//with bloc
