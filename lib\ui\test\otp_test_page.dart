import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/auth_bloc/auth_bloc.dart';
import '../../bloc/auth_bloc/auth_event.dart';
import '../../bloc/auth_bloc/auth_state.dart';
import '../auth/components/auth_text_field.dart';

class OTPTestPage extends StatefulWidget {
  const OTPTestPage({super.key});

  @override
  State<OTPTestPage> createState() => _OTPTestPageState();
}

class _OTPTestPageState extends State<OTPTestPage> {
  final _emailController = TextEditingController();
  final _otpController = TextEditingController();

  bool _showOtpField = false;
  String? _emailError;
  String? _otpError;

  @override
  void dispose() {
    _emailController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  void _onSendOTP() {
    final email = _emailController.text.trim();

    if (email.isEmpty) {
      setState(() {
        _emailError = 'Please enter your email address';
      });
      return;
    }

    if (!email.contains('@') || !email.contains('.')) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      return;
    }

    context.read<AuthBloc>().add(ForgotPasswordRequested(emailOrPhone: email));
  }

  void _onVerifyOTP() {
    final code = _otpController.text.trim();

    if (code.length != 6) {
      setState(() {
        _otpError = 'Please enter the 6-digit code';
      });
      return;
    }

    context.read<AuthBloc>().add(PasswordResetCodeSubmitted(code: code));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('OTP Validation Test'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state.successMessage != null) {
            if (state.successMessage!.contains('Reset code sent')) {
              setState(() {
                _showOtpField = true;
                _emailError = null;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.successMessage!),
                  backgroundColor: Colors.blue,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            } else if (state.resetCode != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    '✅ OTP Verified Successfully! You can now reset your password.',
                  ),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          }

          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'OTP Validation Test',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Test the password reset OTP validation to ensure wrong codes are rejected.',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 32),

              // Test Instructions
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Instructions:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '1. Test "user not found" errors:\n'
                      '   • Enter <EMAIL>\n'
                      '   • Enter fake username/phone\n'
                      '2. Test valid identifier:\n'
                      '   • Enter your real email/phone/username\n'
                      '3. Test invalid OTP:\n'
                      '   • Try 123456, 000000, abcdef\n'
                      '   • Verify all are rejected\n'
                      '4. Test valid OTP:\n'
                      '   • Enter correct code from email',
                      style: TextStyle(fontSize: 14, color: Colors.blue[600]),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Identifier Input (supports email, phone, username)
              AuthTextField(
                controller: _emailController,
                hintText: 'Email, phone, or username',
                keyboardType: TextInputType.text,
                errorText: _emailError,
                enabled: !_showOtpField,
                onChanged: (value) {
                  if (_emailError != null) {
                    setState(() {
                      _emailError = null;
                    });
                  }
                },
              ),

              const SizedBox(height: 16),

              // OTP Input (shown after email is sent)
              if (_showOtpField) ...[
                AuthTextField(
                  controller: _otpController,
                  hintText: 'Enter 6-digit reset code from email',
                  keyboardType: TextInputType.number,
                  errorText: _otpError,
                  maxLength: 6,
                  onChanged: (value) {
                    if (_otpError != null) {
                      setState(() {
                        _otpError = null;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Action Button
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  return AuthPrimaryButton(
                    text: _showOtpField ? 'Verify OTP' : 'Send OTP',
                    isLoading: state.isLoading,
                    onPressed: _showOtpField ? _onVerifyOTP : _onSendOTP,
                  );
                },
              ),

              const SizedBox(height: 24),

              // Test Cases
              if (_showOtpField) ...[
                Text(
                  'Quick Test Cases:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
                const SizedBox(height: 12),

                _buildTestCaseButton('123456', 'Test Invalid Code'),
                const SizedBox(height: 8),
                _buildTestCaseButton('000000', 'Test Zero Code'),
                const SizedBox(height: 8),
                _buildTestCaseButton('abcdef', 'Test Letter Code'),
                const SizedBox(height: 8),
                _buildTestCaseButton('12345', 'Test Short Code'),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestCaseButton(String code, String label) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () {
          _otpController.text = code;
          _onVerifyOTP();
        },
        child: Text('$label ($code)'),
      ),
    );
  }
}
