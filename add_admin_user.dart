import 'dart:developer' as developer;
import 'package:business_app/services/supabase_service.dart';
import 'package:business_app/supabase/config.dart';

/// Script to add admin user to the system
/// Run this script to assign admin role to your email
void main() async {
  const String logTag = 'AdminSetup';

  developer.log('🔧 Admin User Setup Script', name: logTag);
  developer.log('==========================', name: logTag);

  try {
    // Initialize Supabase
    developer.log('📡 Initializing Supabase...', name: logTag);
    await SupabaseConfig.initialize();
    developer.log('✅ Supabase initialized successfully', name: logTag);

    // Your email address
    const String adminEmail = '<EMAIL>';

    developer.log('👤 Adding admin user: $adminEmail', name: logTag);

    // Method 1: Try using the admin function
    developer.log('🔄 Attempting to assign super_admin role...', name: logTag);

    final result = await AuthService.assignAdminRole(
      userEmail: adminEmail,
      role: 'super_admin',
    );

    if (result.isSuccess) {
      developer.log(
        '✅ Successfully assigned super_admin role to $adminEmail',
        name: logTag,
      );

      // Verify the assignment
      developer.log('🔍 Verifying admin assignment...', name: logTag);
      final isAdmin = await AuthService.isUserAdmin(userEmail: adminEmail);

      if (isAdmin) {
        developer.log(
          '✅ Verification successful! $adminEmail now has admin privileges',
          name: logTag,
        );

        // Get all admin users to confirm
        developer.log('📋 Current admin users:', name: logTag);
        final adminUsers = await AuthService.getAdminUsers();

        for (final user in adminUsers) {
          developer.log(
            '  - ${user.email} (${user.role}) - ${user.displayName}',
            name: logTag,
          );
        }
      } else {
        developer.log(
          '❌ Verification failed. Admin role may not have been assigned properly.',
          name: logTag,
        );
      }
    } else {
      developer.log(
        '❌ Failed to assign admin role: ${result.error}',
        name: logTag,
      );
      developer.log('💡 This might happen if:', name: logTag);
      developer.log('   1. The user account doesn\'t exist yet', name: logTag);
      developer.log(
        '   2. The database functions haven\'t been deployed',
        name: logTag,
      );
      developer.log('   3. There are permission issues', name: logTag);

      developer.log('🔧 Alternative solutions:', name: logTag);
      developer.log(
        '   1. Create an account with email $adminEmail first',
        name: logTag,
      );
      developer.log(
        '   2. Run the SQL migration: supabase/migrations/011_add_admin_user.sql',
        name: logTag,
      );
      developer.log(
        '   3. Use the Supabase dashboard to update the role manually',
        name: logTag,
      );
    }
  } catch (e) {
    developer.log('❌ Error occurred: $e', name: logTag, error: e);
    developer.log('🔧 Manual steps to add admin user:', name: logTag);
    developer.log('   1. Go to your Supabase dashboard', name: logTag);
    developer.log('   2. Navigate to Table Editor > profiles', name: logTag);
    developer.log(
      '   3. Find the row with email: <EMAIL>',
      name: logTag,
    );
    developer.log(
      '   4. Update the "role" column to "super_admin"',
      name: logTag,
    );
    developer.log('   5. Save the changes', name: logTag);
  }

  developer.log('🎉 Admin setup script completed!', name: logTag);
}
