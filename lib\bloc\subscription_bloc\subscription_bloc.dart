import 'package:flutter_bloc/flutter_bloc.dart';
import 'subscription_event.dart';
import 'subscription_state.dart';

class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  SubscriptionBloc() : super(const SubscriptionState()) {
    on<LoadSubscriptionPlansEvent>(_onLoadSubscriptionPlans);
    on<SelectPlanEvent>(_onSelectPlan);
    on<SubscribeToPlanEvent>(_onSubscribeToPlan);
    on<CancelSubscriptionEvent>(_onCancelSubscription);
    on<LoadCurrentSubscriptionEvent>(_onLoadCurrentSubscription);
    on<ChangePaymentMethodEvent>(_onChangePaymentMethod);
    on<TogglePlanFeatureEvent>(_onTogglePlanFeature);
    on<ApplyPromoCodeEvent>(_onApplyPromoCode);
    on<RemovePromoCodeEvent>(_onRemovePromoCode);
    on<RestoreSubscriptionEvent>(_onRestoreSubscription);
    on<UpgradePlanEvent>(_onUpgradePlan);
    on<DowngradePlanEvent>(_onDowngradePlan);
  }

  Future<void> _onLoadSubscriptionPlans(
    LoadSubscriptionPlansEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionStatus.loading));

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final plans = _generateMockPlans();

      emit(state.copyWith(status: SubscriptionStatus.loaded, plans: plans));
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to load subscription plans: ${e.toString()}',
        ),
      );
    }
  }

  void _onSelectPlan(SelectPlanEvent event, Emitter<SubscriptionState> emit) {
    emit(state.copyWith(selectedPlanIndex: event.planIndex));
  }

  Future<void> _onSubscribeToPlan(
    SubscribeToPlanEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    if (event.planIndex < 0 || event.planIndex >= state.plans.length) return;

    emit(state.copyWith(status: SubscriptionStatus.subscribing));

    try {
      await Future.delayed(const Duration(seconds: 2));

      final selectedPlan = state.plans[event.planIndex];
      final updatedPlans =
          state.plans.map((plan) {
            return plan.copyWith(isCurrentPlan: plan.id == selectedPlan.id);
          }).toList();

      emit(
        state.copyWith(
          status: SubscriptionStatus.loaded,
          plans: updatedPlans,
          currentSubscription: selectedPlan.copyWith(isCurrentPlan: true),
          hasActiveSubscription: true,
          subscriptionEndDate: DateTime.now().add(const Duration(days: 30)),
          selectedPaymentMethod: event.paymentMethod,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to subscribe: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onCancelSubscription(
    CancelSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionStatus.cancelling));

    try {
      await Future.delayed(const Duration(seconds: 1));

      final updatedPlans =
          state.plans.map((plan) {
            return plan.copyWith(isCurrentPlan: false);
          }).toList();

      emit(
        state.copyWith(
          status: SubscriptionStatus.loaded,
          plans: updatedPlans,
          currentSubscription: null,
          hasActiveSubscription: false,
          subscriptionEndDate: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to cancel subscription: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadCurrentSubscription(
    LoadCurrentSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));

      // Check if user has an active subscription
      // This would typically check with your backend/Supabase
      final hasSubscription =
          state.hasActiveSubscription ||
          (state.currentSubscription != null &&
              state.subscriptionEndDate != null &&
              state.subscriptionEndDate!.isAfter(DateTime.now()));

      if (hasSubscription) {
        final currentPlan = state.plans.isNotEmpty ? state.plans[1] : null;
        emit(
          state.copyWith(
            currentSubscription: currentPlan?.copyWith(isCurrentPlan: true),
            hasActiveSubscription: true,
            subscriptionEndDate: DateTime.now().add(const Duration(days: 15)),
          ),
        );
      } else {
        // No active subscription found
        emit(
          state.copyWith(
            currentSubscription: null,
            hasActiveSubscription: false,
            subscriptionEndDate: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to load current subscription: ${e.toString()}',
        ),
      );
    }
  }

  void _onChangePaymentMethod(
    ChangePaymentMethodEvent event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(state.copyWith(selectedPaymentMethod: event.paymentMethod));
  }

  void _onTogglePlanFeature(
    TogglePlanFeatureEvent event,
    Emitter<SubscriptionState> emit,
  ) {
    // This could be used for customizable plan features
    // For now, it's a placeholder for future functionality
  }

  Future<void> _onApplyPromoCode(
    ApplyPromoCodeEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      // Mock promo code validation
      final promoCode = _validatePromoCode(event.promoCode);

      if (promoCode.isValid) {
        emit(state.copyWith(appliedPromoCode: promoCode));
      } else {
        emit(
          state.copyWith(
            status: SubscriptionStatus.error,
            errorMessage: 'Invalid promo code',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to apply promo code: ${e.toString()}',
        ),
      );
    }
  }

  void _onRemovePromoCode(
    RemovePromoCodeEvent event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(state.copyWith(appliedPromoCode: null));
  }

  Future<void> _onRestoreSubscription(
    RestoreSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionStatus.loading));

    try {
      await Future.delayed(const Duration(seconds: 1));

      // Mock restore subscription logic
      emit(
        state.copyWith(
          status: SubscriptionStatus.loaded,
          hasActiveSubscription: true,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to restore subscription: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUpgradePlan(
    UpgradePlanEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionStatus.subscribing));

    try {
      await Future.delayed(const Duration(seconds: 1));

      final newPlan = state.plans[event.newPlanIndex];
      final updatedPlans =
          state.plans.map((plan) {
            return plan.copyWith(isCurrentPlan: plan.id == newPlan.id);
          }).toList();

      emit(
        state.copyWith(
          status: SubscriptionStatus.loaded,
          plans: updatedPlans,
          currentSubscription: newPlan.copyWith(isCurrentPlan: true),
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to upgrade plan: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onDowngradePlan(
    DowngradePlanEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionStatus.subscribing));

    try {
      await Future.delayed(const Duration(seconds: 1));

      final newPlan = state.plans[event.newPlanIndex];
      final updatedPlans =
          state.plans.map((plan) {
            return plan.copyWith(isCurrentPlan: plan.id == newPlan.id);
          }).toList();

      emit(
        state.copyWith(
          status: SubscriptionStatus.loaded,
          plans: updatedPlans,
          currentSubscription: newPlan.copyWith(isCurrentPlan: true),
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: SubscriptionStatus.error,
          errorMessage: 'Failed to downgrade plan: ${e.toString()}',
        ),
      );
    }
  }

  List<SubscriptionPlan> _generateMockPlans() {
    return [
      const SubscriptionPlan(
        id: 'basic',
        title: 'Basic',
        price: '\$9.99',
        duration: '/month',
        benefits: [
          'Basic analytics',
          'Up to 100 products',
          'Email support',
          'Standard features',
        ],
        color: 'blue',
      ),
      const SubscriptionPlan(
        id: 'premium',
        title: 'Premium',
        price: '\$19.99',
        duration: '/month',
        benefits: [
          'Advanced analytics',
          'Unlimited products',
          'Priority support',
          'Premium features',
          'Custom branding',
        ],
        color: 'gold',
        isPopular: true,
      ),
      const SubscriptionPlan(
        id: 'enterprise',
        title: 'Enterprise',
        price: '\$49.99',
        duration: '/month',
        benefits: [
          'Enterprise analytics',
          'Unlimited everything',
          '24/7 phone support',
          'All features',
          'Custom integrations',
          'Dedicated manager',
        ],
        color: 'purple',
      ),
    ];
  }

  PromoCode _validatePromoCode(String code) {
    // Mock promo code validation
    switch (code.toUpperCase()) {
      case 'SAVE20':
        return const PromoCode(
          code: 'SAVE20',
          description: '20% off your first month',
          discountPercentage: 20.0,
          isValid: true,
        );
      case 'WELCOME10':
        return const PromoCode(
          code: 'WELCOME10',
          description: '10% off any plan',
          discountPercentage: 10.0,
          isValid: true,
        );
      default:
        return PromoCode(
          code: code,
          description: 'Invalid promo code',
          discountPercentage: 0.0,
          isValid: false,
        );
    }
  }
}
