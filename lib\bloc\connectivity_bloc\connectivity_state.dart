part of 'connectivity_bloc.dart';

/// States for connectivity management
@immutable
class ConnectivityState extends Equatable {
  final ConnectivityStatus status;
  final ConnectivityResult connectivityResult;
  final InternetStatus internetStatus;
  final String? errorMessage;
  final DateTime lastChecked;
  final bool isRetrying;

  const ConnectivityState({
    required this.status,
    required this.connectivityResult,
    required this.internetStatus,
    this.errorMessage,
    required this.lastChecked,
    this.isRetrying = false,
  });

  /// Initial state
  factory ConnectivityState.initial() {
    return ConnectivityState(
      status: ConnectivityStatus.checking,
      connectivityResult: ConnectivityResult.none,
      internetStatus: InternetStatus.disconnected,
      lastChecked: DateTime.now(),
    );
  }

  /// Connected state (both network and internet available)
  ConnectivityState connected({
    required ConnectivityResult connectivityResult,
  }) {
    return copyWith(
      status: ConnectivityStatus.connected,
      connectivityResult: connectivityResult,
      internetStatus: InternetStatus.connected,
      errorMessage: null,
      lastChecked: DateTime.now(),
      isRetrying: false,
    );
  }

  /// Disconnected state (no network connection)
  ConnectivityState disconnected() {
    return copyWith(
      status: ConnectivityStatus.disconnected,
      connectivityResult: ConnectivityResult.none,
      internetStatus: InternetStatus.disconnected,
      lastChecked: DateTime.now(),
      isRetrying: false,
    );
  }

  /// Limited connection (network available but no internet)
  ConnectivityState limitedConnection({
    required ConnectivityResult connectivityResult,
  }) {
    return copyWith(
      status: ConnectivityStatus.limitedConnection,
      connectivityResult: connectivityResult,
      internetStatus: InternetStatus.disconnected,
      lastChecked: DateTime.now(),
      isRetrying: false,
    );
  }

  /// Error state
  ConnectivityState error(String message) {
    return copyWith(
      status: ConnectivityStatus.error,
      errorMessage: message,
      lastChecked: DateTime.now(),
      isRetrying: false,
    );
  }

  /// Retrying state
  ConnectivityState retrying() {
    return copyWith(
      status: ConnectivityStatus.retrying,
      isRetrying: true,
      errorMessage: null,
    );
  }

  /// Copy with method
  ConnectivityState copyWith({
    ConnectivityStatus? status,
    ConnectivityResult? connectivityResult,
    InternetStatus? internetStatus,
    String? errorMessage,
    DateTime? lastChecked,
    bool? isRetrying,
  }) {
    return ConnectivityState(
      status: status ?? this.status,
      connectivityResult: connectivityResult ?? this.connectivityResult,
      internetStatus: internetStatus ?? this.internetStatus,
      errorMessage: errorMessage ?? this.errorMessage,
      lastChecked: lastChecked ?? this.lastChecked,
      isRetrying: isRetrying ?? this.isRetrying,
    );
  }

  /// Convenience getters
  bool get isConnected => status == ConnectivityStatus.connected;
  bool get isDisconnected => status == ConnectivityStatus.disconnected;
  bool get hasLimitedConnection => status == ConnectivityStatus.limitedConnection;
  bool get hasError => status == ConnectivityStatus.error;
  bool get isChecking => status == ConnectivityStatus.checking;
  
  /// Get connection type as string
  String get connectionTypeString {
    switch (connectivityResult) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
        return 'No Connection';
    }
  }

  @override
  List<Object?> get props => [
        status,
        connectivityResult,
        internetStatus,
        errorMessage,
        lastChecked,
        isRetrying,
      ];
}

/// Connectivity status enum
enum ConnectivityStatus {
  checking,
  connected,
  disconnected,
  limitedConnection,
  retrying,
  error,
}
