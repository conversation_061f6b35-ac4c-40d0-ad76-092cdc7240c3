-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- Enterprise-grade security following Big App patterns
-- =====================================================

-- =====================================================
-- 1. PROFILES TABLE SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Users can view public profiles or their own profile
CREATE POLICY "profiles_select_policy" ON public.profiles
FOR SELECT USING (
    -- Own profile
    auth.uid() = id OR
    -- Public profiles (non-private)
    is_private = false OR
    -- Following relationship exists
    EXISTS (
        SELECT 1 FROM public.follows 
        WHERE follower_id = auth.uid() AND following_id = id
    )
);

-- Users can only insert their own profile
CREATE POLICY "profiles_insert_policy" ON public.profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- Users can only update their own profile
CREATE POLICY "profiles_update_policy" ON public.profiles
FOR UPDATE USING (auth.uid() = id);

-- Profile deletion disabled to prevent infinite recursion
-- Most apps don't allow profile deletion anyway - use soft delete instead
-- CREATE POLICY "profiles_delete_policy" ON public.profiles
-- FOR DELETE USING (false); -- No one can delete profiles

-- =====================================================
-- 2. SHOPS TABLE SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.shops ENABLE ROW LEVEL SECURITY;

-- Anyone can view active shops
CREATE POLICY "shops_select_policy" ON public.shops
FOR SELECT USING (
    is_active = true OR
    owner_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only authenticated users can create shops
CREATE POLICY "shops_insert_policy" ON public.shops
FOR INSERT WITH CHECK (
    auth.uid() = owner_id AND
    auth.uid() IS NOT NULL
);

-- Only shop owners can update their shops
CREATE POLICY "shops_update_policy" ON public.shops
FOR UPDATE USING (
    owner_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- Only shop owners and admins can delete shops
CREATE POLICY "shops_delete_policy" ON public.shops
FOR DELETE USING (
    owner_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- =====================================================
-- 3. CATEGORIES TABLE SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- Anyone can view active categories
CREATE POLICY "categories_select_policy" ON public.categories
FOR SELECT USING (is_active = true);

-- Only admins can manage categories
CREATE POLICY "categories_insert_policy" ON public.categories
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

CREATE POLICY "categories_update_policy" ON public.categories
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

CREATE POLICY "categories_delete_policy" ON public.categories
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- =====================================================
-- 4. PRODUCTS TABLE SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- Users can view active products or their own products
CREATE POLICY "products_select_policy" ON public.products
FOR SELECT USING (
    status = 'active' OR
    EXISTS (
        SELECT 1 FROM public.shops 
        WHERE id = products.shop_id AND owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only shop owners can create products
CREATE POLICY "products_insert_policy" ON public.products
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.shops 
        WHERE id = shop_id AND owner_id = auth.uid()
    )
);

-- Only shop owners can update their products
CREATE POLICY "products_update_policy" ON public.products
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM public.shops 
        WHERE id = products.shop_id AND owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- Only shop owners and admins can delete products
CREATE POLICY "products_delete_policy" ON public.products
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM public.shops 
        WHERE id = products.shop_id AND owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- =====================================================
-- 5. FOLLOWS TABLE SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.follows ENABLE ROW LEVEL SECURITY;

-- Users can view follows where they are involved (follower or being followed)
CREATE POLICY "follows_select_policy" ON public.follows
FOR SELECT USING (
    -- Can see follows where they are the follower
    follower_id = auth.uid() OR
    -- Can see follows where they are being followed
    following_id = auth.uid() OR
    -- Admin access
    EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only authenticated users can create follows (follow someone)
CREATE POLICY "follows_insert_policy" ON public.follows
FOR INSERT WITH CHECK (
    -- Must be authenticated
    auth.uid() IS NOT NULL AND
    -- Can only create follows where they are the follower
    follower_id = auth.uid() AND
    -- Cannot follow themselves (additional check)
    follower_id != following_id
);

-- Users can only delete their own follows (unfollow)
CREATE POLICY "follows_delete_policy" ON public.follows
FOR DELETE USING (
    -- Can only delete follows where they are the follower
    follower_id = auth.uid() OR
    -- Admin access
    EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- No updates allowed on follows table (only insert/delete)
-- This prevents tampering with follow relationships

-- =====================================================
-- 6. POSTS TABLE SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;

-- Users can view public posts, their own posts, or posts from followed users
CREATE POLICY "posts_select_policy" ON public.posts
FOR SELECT USING (
    -- Public posts
    (is_public = true AND status = 'published') OR
    -- Own posts
    user_id = auth.uid() OR
    -- Posts from followed users
    EXISTS (
        SELECT 1 FROM public.follows 
        WHERE follower_id = auth.uid() AND following_id = posts.user_id
    ) OR
    -- Admin access
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only authenticated users can create posts
CREATE POLICY "posts_insert_policy" ON public.posts
FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
);

-- Only post authors can update their posts
CREATE POLICY "posts_update_policy" ON public.posts
FOR UPDATE USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only post authors and moderators can delete posts
CREATE POLICY "posts_delete_policy" ON public.posts
FOR DELETE USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- =====================================================
-- SECURITY FUNCTIONS
-- =====================================================

-- Function to check if user has specific role
CREATE OR REPLACE FUNCTION public.has_role(required_role TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = required_role
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has any of the specified roles
CREATE OR REPLACE FUNCTION public.has_any_role(required_roles TEXT[])
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = ANY(required_roles)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user owns a shop
CREATE OR REPLACE FUNCTION public.owns_shop(shop_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.shops 
        WHERE id = shop_uuid AND owner_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if users are following each other
CREATE OR REPLACE FUNCTION public.is_following(target_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.follows 
        WHERE follower_id = auth.uid() AND following_id = target_user_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO anon, authenticated;

-- Grant permissions on tables
GRANT SELECT, INSERT, UPDATE, DELETE ON public.profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.shops TO authenticated;
GRANT SELECT ON public.categories TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON public.categories TO authenticated;
GRANT SELECT ON public.products TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON public.products TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.posts TO authenticated;

-- Grant permissions on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
