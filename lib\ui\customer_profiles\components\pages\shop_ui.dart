import 'package:business_app/bloc/search_bloc/search_bloc.dart';
import 'package:business_app/bloc/search_bloc/search_event.dart';
import 'package:business_app/bloc/search_bloc/search_state.dart';
import 'package:business_app/ui/customer_profiles/components/pages/products_ui.dart';
import 'package:business_app/ui/customer_profiles/customer_dashboard.dart';
import 'package:business_app/ui/customer_profiles/components/pages/shoes/shoes_page.dart';
import 'package:business_app/const/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Purchase Method Enum
enum PurchaseMethod { buyOnline, buyDirect }

// Shop UI Page

//greeting message function
String getGreeting(String username) {
  final hour = DateTime.now().hour;
  if (hour < 12) {
    return "Good morning $username";
  } else if (hour < 17) {
    return "Good afternoon $username";
  } else {
    return "Good evening $username";
  }
}

class ShopUi extends StatefulWidget {
  const ShopUi({Key? key}) : super(key: key);

  @override
  State<ShopUi> createState() => _ShopUiState();
}

class _ShopUiState extends State<ShopUi> {
  final String username = "";

  //greeting components
  late String greeting;
  late final Ticker _ticker;

  @override
  void initState() {
    super.initState();
    greeting = getGreeting(username);
    _ticker = Ticker((_) {
      final newGreeting = getGreeting(username);
      if (newGreeting != greeting) {
        setState(() {
          greeting = newGreeting;
        });
      }
    })..start();
  }

  @override
  void dispose() {
    _ticker.dispose();
    super.dispose();
  }
  //greeting components end

  // Purchase Method Dialog
  void _showPurchaseMethodDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(32),
                      ),
                      child: const Icon(
                        Icons.shopping_cart,
                        color: Colors.blue,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Choose Purchase Method',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'How would you like to buy?',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Buy Online Option
                _buildPurchaseOption(
                  context,
                  icon: Icons.local_shipping,
                  title: 'Buy Online',
                  subtitle: 'Includes shipping if business owner sets it',
                  color: Colors.blue,
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => const CartScreen(
                              purchaseMethod: PurchaseMethod.buyOnline,
                            ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 16),

                // Buy Direct Option
                _buildPurchaseOption(
                  context,
                  icon: Icons.store,
                  title: 'Buy Direct',
                  subtitle: 'No shipping - direct from business',
                  color: Colors.green,
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => const CartScreen(
                              purchaseMethod: PurchaseMethod.buyDirect,
                            ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPurchaseOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(12),
          color: color.withValues(alpha: 0.05),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(140.0),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors:
                  isDark
                      ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
                      : [const Color(0xFF667eea), const Color(0xFF764ba2)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              child: Column(
                children: [
                  // Top Row: Profile, Shop Name, and Actions
                  Row(
                    children: [
                      // Profile Avatar with Premium Ring
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CustomerProfilePage(),
                            ),
                          );
                        },
                        child: Container(
                          width: 45,
                          height: 45,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: const LinearGradient(
                              colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(2),
                          child: Container(
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                            ),
                            padding: const EdgeInsets.all(2),
                            child: ClipOval(
                              child: Image.asset(
                                Assets.newProfile,
                                width: 40,
                                height: 40,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.grey[300],
                                    ),
                                    child: const Icon(
                                      Icons.person,
                                      color: Colors.grey,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Shop Info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Christina Fashions & Cosmetics",
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    offset: const Offset(0, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              greeting,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withValues(alpha: 0.9),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Action Buttons
                      Row(
                        children: [
                          /*  // Notifications
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.notifications_outlined,
                                color: Colors.white,
                                size: 22,
                              ),
                              onPressed: () {
                                // Handle notifications
                              },
                            ),
                          ),*/
                          const SizedBox(width: 8),

                          // More Menu
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(32),
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.more_vert,
                                color: Colors.white,
                                size: 22,
                              ),
                              onPressed: () {
                                moreMenusBottomSheet(context);
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Search Bar
                  BlocBuilder<SearchBloc, SearchState>(
                    builder: (context, state) {
                      return Container(
                        height:
                            MediaQuery.of(context).size.width < 600
                                ? 44
                                : 48, // Responsive height
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: TextField(
                          onChanged: (query) {
                            if (query.isNotEmpty) {
                              context.read<SearchBloc>().add(
                                SearchQueryChangedEvent(query),
                              );
                            } else {
                              context.read<SearchBloc>().add(
                                ClearSearchEvent(),
                              );
                            }
                          },
                          onSubmitted: (query) {
                            if (query.isNotEmpty) {
                              context.read<SearchBloc>().add(
                                SearchSubmittedEvent(query),
                              );
                              // Navigate to search results page
                              Navigator.pushNamed(context, '/search_results');
                            }
                          },
                          decoration: InputDecoration(
                            hintText: 'Search products, brands, categories...',
                            hintStyle: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 14,
                            ),
                            prefixIcon: Container(
                              padding: const EdgeInsets.all(12),
                              child: Icon(
                                Icons.search,
                                color: Colors.grey[600],
                                size: 20,
                              ),
                            ),
                            suffixIcon:
                                state.query.isNotEmpty
                                    ? IconButton(
                                      icon: Icon(
                                        Icons.clear,
                                        color: Colors.grey[600],
                                        size: 20,
                                      ),
                                      onPressed: () {
                                        context.read<SearchBloc>().add(
                                          ClearSearchEvent(),
                                        );
                                      },
                                    )
                                    : Container(
                                      padding: const EdgeInsets.all(8),
                                      child: Icon(
                                        Icons.tune,
                                        color: Colors.grey[600],
                                        size: 20,
                                      ),
                                    ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: ListView(
        physics: const BouncingScrollPhysics(),
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Banner with Discount
                Container(
                  height: 150,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.0),
                    image: const DecorationImage(
                      image: AssetImage(Assets.product1),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Stack(
                    children: [
                      const Positioned(
                        top: 16,
                        left: 8,
                        child: Text(
                          '60% OFF',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 16,
                        right: 8,
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                          ),
                          child: const Text(
                            'Shop now',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8.0),
                // Brands Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Popular brands',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    /*TextButton(
                      onPressed: () {},
                      child: const Text(
                        'See all',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ),*/
                  ],
                ),
                const AutoScrollingBrandList(),
                /* SizedBox(
                  height: 60,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: const [
                      _BrandLogo(assetPath: Assets.trademateLogo),
                      _BrandLogo(assetPath: Assets.product2),
                      _BrandLogo(assetPath: Assets.trademateLogo),
                      _BrandLogo(assetPath: Assets.product4),
                      _BrandLogo(assetPath: Assets.product1),
                    ],
                  ),
                ),*/
                const SizedBox(height: 8.0),
                // Men's Shoes Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "Men's Shoes",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ShoesPage(),
                          ),
                        );
                      },
                      child: const Text(
                        'See all',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 280,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    children: const [
                      ShoeCard(
                        image: Assets.product1,
                        title: 'Pure Decent',
                        rating: 5,
                        colors: 5,
                        price: 250000,
                      ),
                      SizedBox(width: 8),
                      ShoeCard(
                        image: Assets.product2,
                        title: 'Dr. Martens',
                        rating: 5,
                        colors: 8,
                        price: 130500,
                      ),
                      SizedBox(width: 8),
                      ShoeCard(
                        image: Assets.product3,
                        title: 'Sneakers',
                        rating: 3,
                        colors: 4,
                        price: 50000,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                // Women's Shoes Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "Women's Shoes",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ShoesPage(),
                          ),
                        );
                      },
                      child: const Text(
                        'See all',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 280,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    children: const [
                      ShoeCard(
                        image: Assets.product1,
                        title: 'Pure Decent',
                        rating: 2,
                        colors: 6,
                        price: 10000,
                      ),
                      SizedBox(width: 8),
                      ShoeCard(
                        image: Assets.product2,
                        title: 'Dr. Martens',
                        rating: 4,
                        colors: 8,
                        price: 30000,
                      ),
                      SizedBox(width: 8),
                      ShoeCard(
                        image: Assets.product3,
                        title: 'Sneakers',
                        rating: 3,
                        colors: 4,
                        price: 11000,
                      ),
                    ],
                  ),
                ),
                // Kitchen's Utils Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "Kitchen Utils",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {},
                      child: const Text(
                        'See all',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 280,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    children: const [
                      ShoeCard(
                        image: Assets.product1,
                        title: 'Pure Decent',
                        rating: 5,
                        colors: 5,
                        price: 250000,
                      ),
                      SizedBox(width: 8),
                      ShoeCard(
                        image: Assets.product2,
                        title: 'Dr. Martens',
                        rating: 5,
                        colors: 8,
                        price: 130500,
                      ),
                      SizedBox(width: 8),
                      ShoeCard(
                        image: Assets.product3,
                        title: 'Sneakers',
                        rating: 3,
                        colors: 4,
                        price: 50000,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 80),
                Center(child: StyleCaughtUpWidget(onVisitShop: () {})),
              ],
            ),
          ),
        ],
      ),
      // ...starts code...
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: GestureDetector(
        onTap: () {
          _showPurchaseMethodDialog(context);
        },
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(Icons.shopping_cart, color: Colors.blue, size: 28),
            ),
            Positioned(
              right: 4,
              top: -8,
              // ...existing code...
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ), // More horizontal padding
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.rectangle, // Use rectangle for adaptive width
                  borderRadius: BorderRadius.circular(12), // Rounded corners
                  border: Border.all(color: Colors.white, width: 2),
                ),

                child: const Center(
                  child: Text(
                    '3', // Make this dynamic
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis, // Prevent overflow
                    maxLines: 1,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      // ...ends code...
    );
  }
}
//more about bottom sheet in the app bar

//message bottom sheet
// Reusable function to show the custom bottom sheet
void moreMenusBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    //backgroundColor: Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
    ),
    builder: (BuildContext context) {
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),
            ),
            const SizedBox(height: 20),
            GestureDetector(
              onTap: () {},
              child: const Text(
                'Copy Shop URL',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 20),
            GestureDetector(
              onTap: () {},
              child: const Text('Share', style: TextStyle(fontSize: 16)),
            ),
            const SizedBox(height: 20),
            const Text(
              'Report Shop',
              style: TextStyle(fontSize: 16, color: Colors.red),
            ),

            const SizedBox(height: 20),
          ],
        ),
      );
    },
  );
}

//more about bottom sheet ends here
//class for auto scroll of brans list
class AutoScrollingBrandList extends StatefulWidget {
  const AutoScrollingBrandList({super.key});

  @override
  State<AutoScrollingBrandList> createState() => _AutoScrollingBrandListState();
}

class _AutoScrollingBrandListState extends State<AutoScrollingBrandList>
    with SingleTickerProviderStateMixin {
  late final ScrollController _controller;
  late final Ticker _ticker;
  double _scrollPosition = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = ScrollController();
    _ticker = createTicker(_onTick)..start();
  }

  void _onTick(Duration elapsed) {
    if (!_controller.hasClients) return;
    _scrollPosition += 0.3; // Adjust speed here
    if (_scrollPosition > _controller.position.maxScrollExtent) {
      _scrollPosition = 0;
    }
    _controller.jumpTo(_scrollPosition);
  }

  @override
  void dispose() {
    _ticker.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 60,
      child: ListView(
        controller: _controller,
        scrollDirection: Axis.horizontal,
        children: const [
          _BrandLogo(assetPath: Assets.product4),
          _BrandLogo(assetPath: Assets.trademateLogo),
          _BrandLogo(assetPath: Assets.product2),
          _BrandLogo(assetPath: Assets.trademateLogo),
          _BrandLogo(assetPath: Assets.product4),
          _BrandLogo(assetPath: Assets.product1),
          _BrandLogo(assetPath: Assets.product3),
        ],
      ),
    );
  }
}

//////
class _BrandLogo extends StatelessWidget {
  final String assetPath;

  const _BrandLogo({Key? key, required this.assetPath}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: CircleAvatar(
        radius: 30,
        backgroundImage: AssetImage(assetPath),
        backgroundColor: Colors.grey[200],
      ),
    );
  }
}

// Brand Circle Widget
class BrandCircle extends StatelessWidget {
  final String brandImage;

  const BrandCircle({super.key, required this.brandImage});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[200],
      ),
      child: Center(
        child: Image.asset(
          brandImage,
          width: 40,
          height: 40,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}

// Shoe Card Widget
class ShoeCard extends StatelessWidget {
  final String image;
  final String title;
  final int rating;
  final int colors;
  final double price;

  const ShoeCard({
    super.key,
    required this.image,
    required this.title,
    required this.rating,
    required this.colors,
    required this.price,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 160,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                child: Image.asset(
                  image,
                  height: 140,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 8,
                right: 8,
                child: Icon(
                  Icons.favorite_border,
                  color: Colors.grey[600],
                  size: 20,
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: List.generate(
                    5,
                    (index) => Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                      size: 16,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$colors Colors',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '\MWK$price',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    Icon(Icons.shopping_cart, color: Colors.red[400], size: 20),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// floating action button

class ClassFloatingActionButton extends StatefulWidget {
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final VoidCallback onPressed;
  final double size;
  final String? tooltip;

  const ClassFloatingActionButton({
    Key? key,
    required this.icon,
    this.backgroundColor = const Color(0xFF1DA1F2),
    this.iconColor = Colors.white,
    required this.onPressed,
    this.size = 56.0,
    this.tooltip,
  }) : super(key: key);

  @override
  _ClassFloatingActionButtonState createState() =>
      _ClassFloatingActionButtonState();
}

class _ClassFloatingActionButtonState extends State<ClassFloatingActionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onPressed();
      },
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Material(
          elevation: 6.0,
          shape: const CircleBorder(),
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(
                widget.icon,
                color: widget.iconColor,
                size: widget.size * 0.5,
              ),
              tooltip: widget.tooltip,
              onPressed: () {},
            ),
          ),
        ),
      ),
    );
  }
}

//cart page UI -----------------------------------------------------------------------------------------------------------------------
// Cart Page UI
class CartScreen extends StatefulWidget {
  final PurchaseMethod purchaseMethod;

  const CartScreen({super.key, this.purchaseMethod = PurchaseMethod.buyOnline});

  @override
  _CartScreenState createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  // Business owner shipping settings (simulate from backend)
  final bool businessOwnerHasShipping = true; // This would come from backend
  final double shippingCost = 10.0; // This would come from backend
  final double freeShippingThreshold = 100.0; // This would come from backend

  // Sample cart items
  final List<CartItem> cartItems = [
    CartItem(
      id: 1,
      name: 'Wireless Headphones',
      price: 79.99,
      imageUrl: Assets.product3, //'https://via.placeholder.com/100',
      quantity: 1,
    ),
    CartItem(
      id: 2,
      name: 'Smartwatch',
      price: 199.99,
      imageUrl: Assets.product3, //'https://via.placeholder.com/100',
      quantity: 2,
    ),
    CartItem(
      id: 3,
      name: 'Dr Martens Boots',
      price: 80.66,
      imageUrl: Assets.product3, //'https://via.placeholder.com/100',
      quantity: 3,
    ),
  ];

  void updateQuantity(int id, int newQuantity) {
    setState(() {
      if (newQuantity <= 0) {
        cartItems.removeWhere((item) => item.id == id);
      } else {
        final item = cartItems.firstWhere((item) => item.id == id);
        item.quantity = newQuantity;
      }
    });
  }

  double get subtotal =>
      cartItems.fold(0, (sum, item) => sum + item.price * item.quantity);

  bool get shouldShowShipping =>
      widget.purchaseMethod == PurchaseMethod.buyOnline &&
      businessOwnerHasShipping;

  double get calculatedShipping {
    if (!shouldShowShipping) return 0.0;
    if (subtotal >= freeShippingThreshold) return 0.0;
    return shippingCost;
  }

  String get purchaseMethodTitle {
    switch (widget.purchaseMethod) {
      case PurchaseMethod.buyOnline:
        return 'Your Cart - Online Purchase';
      case PurchaseMethod.buyDirect:
        return 'Your Cart - Direct Purchase';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(purchaseMethodTitle),
        centerTitle: true,
        elevation: 0,
        actions: [
          // Purchase method indicator
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color:
                  widget.purchaseMethod == PurchaseMethod.buyOnline
                      ? Colors.blue.withValues(alpha: 0.1)
                      : Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.purchaseMethod == PurchaseMethod.buyOnline
                      ? Icons.local_shipping
                      : Icons.store,
                  size: 16,
                  color:
                      widget.purchaseMethod == PurchaseMethod.buyOnline
                          ? Colors.blue
                          : Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  widget.purchaseMethod == PurchaseMethod.buyOnline
                      ? 'Online'
                      : 'Direct',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color:
                        widget.purchaseMethod == PurchaseMethod.buyOnline
                            ? Colors.blue
                            : Colors.green,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body:
          cartItems.isEmpty
              ? const Center(
                child: Text(
                  'Your cart is empty!',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              )
              : Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: cartItems.length,
                      itemBuilder: (context, index) {
                        final item = cartItems[index];
                        return CartItemWidget(
                          item: item,
                          onQuantityChanged:
                              (newQuantity) =>
                                  updateQuantity(item.id, newQuantity),
                        );
                      },
                    ),
                  ),
                  _buildSummarySection(context),
                ],
              ),
    );
  }

  Widget _buildSummarySection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(),
      child: Column(
        children: [
          // Purchase Method Info Banner
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color:
                  widget.purchaseMethod == PurchaseMethod.buyOnline
                      ? Colors.blue.withValues(alpha: 0.1)
                      : Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color:
                    widget.purchaseMethod == PurchaseMethod.buyOnline
                        ? Colors.blue.withValues(alpha: 0.3)
                        : Colors.green.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  widget.purchaseMethod == PurchaseMethod.buyOnline
                      ? Icons.local_shipping
                      : Icons.store,
                  color:
                      widget.purchaseMethod == PurchaseMethod.buyOnline
                          ? Colors.blue
                          : Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.purchaseMethod == PurchaseMethod.buyOnline
                        ? 'Online Purchase - Shipping included if set by business'
                        : 'Direct Purchase - No shipping fees',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color:
                          widget.purchaseMethod == PurchaseMethod.buyOnline
                              ? Colors.blue[700]
                              : Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Subtotal
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Subtotal',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              Text(
                '\$${subtotal.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          // Conditional Shipping Row
          if (shouldShowShipping) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Text(
                      'Shipping',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (calculatedShipping == 0.0 &&
                        subtotal >= freeShippingThreshold)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          'FREE',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                Text(
                  '\$${calculatedShipping.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: calculatedShipping == 0.0 ? Colors.green : null,
                  ),
                ),
              ],
            ),
            if (subtotal < freeShippingThreshold && businessOwnerHasShipping)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Free shipping on orders over \$${freeShippingThreshold.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],

          const Divider(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Est. Total',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Text(
                '\$${(subtotal + calculatedShipping).toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color:
                      widget.purchaseMethod == PurchaseMethod.buyOnline
                          ? Colors.blue
                          : Colors.green,
                ),
              ),
            ],
          ),
          Text(
            widget.purchaseMethod == PurchaseMethod.buyOnline
                ? "Business will confirm your order and shipping details, including any tax, fees and discounts."
                : "Business will confirm your order for direct pickup/delivery. No shipping fees apply.",
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed:
                  cartItems.isEmpty
                      ? null
                      : () {
                        // Handle checkout action
                        final checkoutMessage =
                            widget.purchaseMethod == PurchaseMethod.buyOnline
                                ? 'Proceeding to online checkout...'
                                : 'Proceeding to direct purchase...';

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(checkoutMessage),
                            backgroundColor:
                                widget.purchaseMethod ==
                                        PurchaseMethod.buyOnline
                                    ? Colors.blue
                                    : Colors.green,
                            duration: const Duration(milliseconds: 800),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor:
                    widget.purchaseMethod == PurchaseMethod.buyOnline
                        ? Colors.blue
                        : Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    widget.purchaseMethod == PurchaseMethod.buyOnline
                        ? Icons.local_shipping
                        : Icons.store,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.purchaseMethod == PurchaseMethod.buyOnline
                        ? 'Checkout Online'
                        : 'Checkout Direct',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CartItem {
  final int id;
  final String name;
  final double price;
  final String imageUrl;
  int quantity;

  CartItem({
    required this.id,
    required this.name,
    required this.price,
    required this.imageUrl,
    required this.quantity,
  });
}

class CartItemWidget extends StatelessWidget {
  final CartItem item;
  final Function(int) onQuantityChanged;

  const CartItemWidget({
    super.key,
    required this.item,
    required this.onQuantityChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(item.id.toString()),
      direction: DismissDirection.endToStart,
      onDismissed: (_) => onQuantityChanged(0),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  item.imageUrl,
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => const Icon(Icons.error),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '\$${item.price.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              QuantitySelector(
                quantity: item.quantity,
                onChanged: (newQuantity) => onQuantityChanged(newQuantity),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class QuantitySelector extends StatelessWidget {
  final int quantity;
  final Function(int) onChanged;

  const QuantitySelector({
    super.key,
    required this.quantity,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: () => onChanged(quantity - 1),
          icon: const Icon(Icons.remove_circle_outline),
          color: Colors.grey,
        ),
        Text(
          quantity.toString(),
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        IconButton(
          onPressed: () => onChanged(quantity + 1),
          icon: const Icon(Icons.add_circle_outline),
          color: Colors.grey,
        ),
      ],
    );
  }
}

//cart page UI ends here

//----------------------------------------------------------------------------------
/**
 * 
   appBar: PreferredSize(
        preferredSize: const Size.fromHeight(140.0),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors:
                  isDark
                      ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
                      : [const Color(0xFF667eea), const Color(0xFF764ba2)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              child: Column(
                children: [
                  // Top Row: Profile, Shop Name, and Actions
                  Row(
                    children: [
                      // Profile Avatar with Premium Ring
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CustomerProfilePage(),
                            ),
                          );
                        },
                        child: Container(
                          width: 45,
                          height: 45,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: const LinearGradient(
                              colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(2),
                          child: Container(
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                            ),
                            padding: const EdgeInsets.all(2),
                            child: ClipOval(
                              child: Image.asset(
                                Assets.newProfile,
                                width: 40,
                                height: 40,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.grey[300],
                                    ),
                                    child: const Icon(
                                      Icons.person,
                                      color: Colors.grey,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Shop Info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Christina Fashions & Cosmetics",
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    offset: const Offset(0, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              greeting,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withValues(alpha: 0.9),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Action Buttons
                      Row(
                        children: [
                          // Notifications
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.notifications_outlined,
                                color: Colors.white,
                                size: 22,
                              ),
                              onPressed: () {
                                // Handle notifications
                              },
                            ),
                          ),
                          const SizedBox(width: 8),

                          // More Menu
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.more_vert,
                                color: Colors.white,
                                size: 22,
                              ),
                              onPressed: () {
                                moreMenusBottomSheet(context);
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Search Bar
                  BlocBuilder<SearchBloc, SearchState>(
                    builder: (context, state) {
                      return Container(
                        height:
                            MediaQuery.of(context).size.width < 600
                                ? 44
                                : 48, // Responsive height
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: TextField(
                          onChanged: (query) {
                            if (query.isNotEmpty) {
                              context.read<SearchBloc>().add(
                                SearchQueryChangedEvent(query),
                              );
                            } else {
                              context.read<SearchBloc>().add(
                                ClearSearchEvent(),
                              );
                            }
                          },
                          onSubmitted: (query) {
                            if (query.isNotEmpty) {
                              context.read<SearchBloc>().add(
                                SearchSubmittedEvent(query),
                              );
                              // Navigate to search results page
                              Navigator.pushNamed(context, '/search_results');
                            }
                          },
                          decoration: InputDecoration(
                            hintText: 'Search products, brands, categories...',
                            hintStyle: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 14,
                            ),
                            prefixIcon: Container(
                              padding: const EdgeInsets.all(12),
                              child: Icon(
                                Icons.search,
                                color: Colors.grey[600],
                                size: 20,
                              ),
                            ),
                            suffixIcon:
                                state.query.isNotEmpty
                                    ? IconButton(
                                      icon: Icon(
                                        Icons.clear,
                                        color: Colors.grey[600],
                                        size: 20,
                                      ),
                                      onPressed: () {
                                        context.read<SearchBloc>().add(
                                          ClearSearchEvent(),
                                        );
                                      },
                                    )
                                    : Container(
                                      padding: const EdgeInsets.all(8),
                                      child: Icon(
                                        Icons.tune,
                                        color: Colors.grey[600],
                                        size: 20,
                                      ),
                                    ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
 * 
 */
