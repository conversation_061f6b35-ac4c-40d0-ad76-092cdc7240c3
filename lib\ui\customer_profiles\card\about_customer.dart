import 'package:business_app/ui/chat_interface/new_chat_ui.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class AboutCustomer extends StatefulWidget {
  const AboutCustomer({super.key});

  @override
  State<AboutCustomer> createState() => _AboutCustomerState();
}

// Reusable UseBioCard
class UseBioCard extends StatelessWidget {
  const UseBioCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.symmetric(vertical: 12),
      //child property
    );
  }
}

class _AboutCustomerState extends State<AboutCustomer> {
  final ScrollController _scrollController = ScrollController();
  bool _showUserName = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      setState(() {
        _showUserName = _scrollController.offset > 50;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          // Dynamic Sliver AppBar
          SliverAppBar(
            expandedHeight: 56.0,
            floating: true,
            pinned: true,
            snap: false,

            // backgroundColor: Colors.black.withOpacity(0.9),
            flexibleSpace: FlexibleSpaceBar(
              title: AnimatedSwitcher(
                duration: const Duration(milliseconds: 0),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(opacity: animation, child: child);
                },
                child: Text(
                  _showUserName ? 'Christina Lungu' : 'About',
                  key: ValueKey<bool>(_showUserName),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    //color: Colors.white,
                  ),
                ),
              ),

              /*background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.green.withOpacity(0.5), Colors.black],
                  ),
                ),
              ),*/
            ),
          ),

          // Bio Cards in a Column
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 0,
              ), // Reduced horizontal padding
              child: Column(
                children: const [BusinessBio(), LocationCard(), PersonalBio()],
              ),
            ),
          ),

          // Playlist Grid (unchanged)
        ],
      ),
    );
  }
}

//demo card info

//business bio
class BusinessBio extends StatefulWidget {
  const BusinessBio({Key? key}) : super(key: key);

  @override
  _BusinessBioState createState() => _BusinessBioState();
}

class _BusinessBioState extends State<BusinessBio> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      //padding: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: 10.0,
      ), // Reduced padding
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        // color: const Color(0xFF242526),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Business Info",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  //  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                "About Business Information",
                style: TextStyle(fontSize: 14 /*color: Colors.grey*/),
              ),
              const SizedBox(height: 20),
              _buildCardRow("Beauty, Fashions and Cosmetics ", Icons.work),
              const SizedBox(height: 14),
              _buildCardRow("Mitundu", Icons.location_on),
              const SizedBox(height: 14),
              _buildCardRow("Business has no ads", Icons.ads_click),
              const SizedBox(height: 14),
              _buildCardRow("3 Shops", Icons.shop),
              const SizedBox(height: 14),
              _buildCardRow("Joined July 2025", Icons.date_range),
              const SizedBox(height: 14),
              _buildCardRow("16,861,000 Views", Icons.timeline),
              const SizedBox(height: 14),
              _buildCardRow("Unverified Business", Icons.verified_outlined),
              const SizedBox(height: 14),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardRow(String title, IconData icon) {
    return InkWell(
      onTap: () {
        // TODO: Add navigation or action
        print('$title tapped');
      },
      child: Row(
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                //color: Colors.white,
                fontSize: 16,
              ),
            ),
          ),
          // forward arrow
          // const Icon(
          //   Icons.arrow_forward_ios, //color: Colors.grey,
          //   size: 16,
          // ),
        ],
      ),
    );
  }
}

//personal info

//business bio
class PersonalBio extends StatefulWidget {
  const PersonalBio({Key? key}) : super(key: key);

  @override
  _PersonalBioState createState() => _PersonalBioState();
}

class _PersonalBioState extends State<PersonalBio> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      //padding: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: 10.0,
      ), // Reduced padding
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        // color: const Color(0xFF242526),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Personal Info",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  //  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                "About Christina Lungu",
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 20),
              _buildCardRow("Lives in Lilongwe, Malawi ", Icons.home),
              const SizedBox(height: 14),
              _buildCardRow("Student at Catholic University", Icons.school),
              const SizedBox(height: 14),
              _buildCardRow("www.chris-fashions.com", Icons.link),
              const SizedBox(height: 14),
              _buildCardRow("134 Posts • 13.6M Customers", Icons.info_rounded),
              const SizedBox(height: 14),
              _buildCardRow("Birthday, 16 September", Icons.cake),
              const SizedBox(height: 14),
              _buildCardRow("Chris Fashions", Icons.store),
              const SizedBox(height: 14),
              const Text(
                "Social Links",
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 14),
              _buildCardRow("Facebook", Icons.facebook),
              const SizedBox(height: 14),
              _buildCardRow("TickTock", Icons.tiktok),
              // _buildCardRow("WhatsApp", Icons.chat),
              const SizedBox(height: 14),
              _buildCardRow("Instagram", FontAwesomeIcons.instagram),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardRow(String title, IconData icon) {
    return InkWell(
      onTap: () {
        // TODO: Add navigation or action
        print('$title tapped');
      },
      child: Row(
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                //color: Colors.white,
                fontSize: 16,
              ),
            ),
          ),
          // forward arrow
          // const Icon(
          //   Icons.arrow_forward_ios, //color: Colors.grey,
          //   size: 16,
          // ),
        ],
      ),
    );
  }
}

//business location card

class LocationCard extends StatelessWidget {
  const LocationCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      //color: Colors.grey[900],
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Quick Access",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Row(
              children: [
                // Icon(Icons.location_on, color: Colors.blue[300], size: 40),
                // Place this where you want the card to appear
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.location_on,
                      //color: Colors.blue[300],
                      size: 40,
                    ),
                  ),
                ),

                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'linktr.ee/chris-fashions',
                        style: TextStyle(
                          color: Colors.blue[300],
                          //decoration: TextDecoration.underline,
                        ),
                      ),
                      const SizedBox(height: 1),
                      const Text(
                        'Christina Lungu Rever',
                        style: TextStyle(
                          //color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 1),
                      const Text(
                        "Open 8AM - 4PM • Mon to Fri",
                        style: TextStyle(
                          color: Colors.green,

                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            // ...existing code...
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle directions
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        'Get directions',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ChatInterfaceUI(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text('Contact', textAlign: TextAlign.center),
                    ),
                  ),
                ),
              ],
            ),
            // ...existing code...
          ],
        ),
      ),
    );
  }
}

//business location card ends here
