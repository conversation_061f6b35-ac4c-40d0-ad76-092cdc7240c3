import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:google_fonts/google_fonts.dart';

import '../models/business_metrics.dart';

class InsightsSection extends StatelessWidget {
  final BusinessMetrics metrics;
  final TimeRange timeRange;

  const InsightsSection({
    super.key,
    required this.metrics,
    required this.timeRange,
  });

  @override
  Widget build(BuildContext context) {
    final insights = _generateInsights();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(context),
        const SizedBox(height: 16),
        ...insights.asMap().entries.map((entry) {
          final index = entry.key;
          final insight = entry.value;
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: InsightCard(
              insight: insight,
              delay: Duration(milliseconds: 100 * index),
            ),
          );
        }),
        const SizedBox(height: 24),
        _buildAIRecommendations(context),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.purple, Colors.purple.shade300],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            LucideIcons.brain,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'AI-Powered Insights',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              Text(
                'Smart recommendations for your business',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.green.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                'Live',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAIRecommendations(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.withOpacity(0.1),
            Colors.purple.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                LucideIcons.sparkles,
                color: Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'AI Recommendations',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._getAIRecommendations().map((recommendation) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    width: 4,
                    height: 4,
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      recommendation,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        height: 1.4,
                        color: isDark ? Colors.grey[300] : Colors.grey[700],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.2, end: 0);
  }

  List<BusinessInsight> _generateInsights() {
    final insights = <BusinessInsight>[];

    // Revenue insights
    if (metrics.totalRevenue > 100000) {
      insights.add(const BusinessInsight(
        title: 'Strong Revenue Performance',
        description: 'Your revenue is performing exceptionally well. Consider expanding your product line or entering new markets.',
        category: 'Revenue',
        priority: InsightPriority.high,
        type: InsightType.opportunity,
        actionText: 'Explore expansion opportunities',
        impactScore: 8.5,
      ));
    }

    // Profit margin insights
    if (metrics.profitMargin < 20) {
      insights.add(const BusinessInsight(
        title: 'Optimize Profit Margins',
        description: 'Your profit margin could be improved. Review your pricing strategy and operational costs.',
        category: 'Profitability',
        priority: InsightPriority.medium,
        type: InsightType.warning,
        actionText: 'Review pricing strategy',
        impactScore: 7.2,
      ));
    }

    // Conversion rate insights
    if (metrics.conversionRate > 4.0) {
      insights.add(const BusinessInsight(
        title: 'Excellent Conversion Rate',
        description: 'Your conversion rate is above industry average. Focus on increasing traffic to maximize revenue.',
        category: 'Marketing',
        priority: InsightPriority.high,
        type: InsightType.opportunity,
        actionText: 'Increase marketing efforts',
        impactScore: 8.0,
      ));
    }

    // Customer satisfaction insights
    if (metrics.customerSatisfaction >= 4.5) {
      insights.add(const BusinessInsight(
        title: 'High Customer Satisfaction',
        description: 'Customers love your service! Leverage this with referral programs and testimonials.',
        category: 'Customer Experience',
        priority: InsightPriority.medium,
        type: InsightType.opportunity,
        actionText: 'Launch referral program',
        impactScore: 7.8,
      ));
    }

    return insights;
  }

  List<String> _getAIRecommendations() {
    return [
      'Based on your high conversion rate, consider increasing your marketing budget by 20% to drive more traffic.',
      'Your customer satisfaction score suggests implementing a premium service tier could increase revenue.',
      'Peak sales occur on weekends - consider running targeted promotions during these periods.',
      'Your electronics category shows strong growth - expanding inventory in this area could boost profits.',
      'Customer retention is strong - a loyalty program could further increase lifetime value.',
    ];
  }
}

class InsightCard extends StatelessWidget {
  final BusinessInsight insight;
  final Duration delay;

  const InsightCard({
    super.key,
    required this.insight,
    this.delay = Duration.zero,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getPriorityColor().withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: _getPriorityColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  _getInsightIcon(),
                  color: _getPriorityColor(),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      insight.title,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    Text(
                      insight.category,
                      style: GoogleFonts.inter(
                        fontSize: 11,
                        color: _getPriorityColor(),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getPriorityColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${insight.impactScore}/10',
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: _getPriorityColor(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            insight.description,
            style: GoogleFonts.inter(
              fontSize: 13,
              height: 1.4,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Text(
                  insight.actionText,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _getPriorityColor(),
                  ),
                ),
              ),
              Icon(
                LucideIcons.arrowRight,
                color: _getPriorityColor(),
                size: 14,
              ),
            ],
          ),
        ],
      ),
    ).animate(delay: delay).fadeIn(duration: 400.ms).slideX(begin: 0.2, end: 0);
  }

  Color _getPriorityColor() {
    switch (insight.priority) {
      case InsightPriority.critical:
        return Colors.red;
      case InsightPriority.high:
        return Colors.orange;
      case InsightPriority.medium:
        return Colors.blue;
      case InsightPriority.low:
        return Colors.green;
    }
  }

  IconData _getInsightIcon() {
    switch (insight.type) {
      case InsightType.opportunity:
        return LucideIcons.trendingUp;
      case InsightType.warning:
        return LucideIcons.alertTriangle;
      case InsightType.recommendation:
        return LucideIcons.lightbulb;
      case InsightType.trend:
        return LucideIcons.activity;
    }
  }
}
