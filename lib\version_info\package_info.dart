import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class AppInfo extends StatefulWidget {
  const AppInfo({super.key});

  @override
  State<AppInfo> createState() => _AppInfoState();
}

class _AppInfoState extends State<AppInfo> {
  String _appVersion = 'Loading...';

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
  }

  Future<void> _loadAppVersion() async {
    final info = await PackageInfo.fromPlatform();
    setState(() {
      //_appVersion = 'v${info.version} (build ${info.buildNumber}'; // Display version only (build ${info.buildNumber})
      _appVersion = '${info.version}+${info.buildNumber}';
    });
  }

  void _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  Widget _buildRow(String title, {String? url}) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.normal, fontSize: 16.0),
      ),
      trailing:
          url != null
              ? const Icon(Icons.open_in_new, color: Colors.grey)
              : Text(
                _appVersion,
                style: const TextStyle(color: Colors.grey, fontSize: 16.0),
              ),
      onTap: url != null ? () => _launchUrl(url) : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //backgroundColor: Colors.black,
      appBar: AppBar(
        centerTitle: true,
        title: const Text(
          'About',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18.0),
        ),
        //backgroundColor: Colors.black,
        iconTheme: const IconThemeData(),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Optional: implement search functionality
            },
          ),
        ],
      ),
      body: ListView(
        children: [
          const SizedBox(height: 16),
          _buildRow('Version'),
          _buildRow('Privacy Policy', url: 'https://example.com/privacy'),
          _buildRow(
            'Third-party Licenses',
            url: 'https://example.com/licenses',
          ),
          //_buildRow('Terms of Use', url: 'https://example.com/terms'),
          _buildRow('Platform Rules', url: 'https://example.com/rules'),
          _buildRow('Support', url: 'https://example.com/support'),
          _buildRow('Developer    _____rever', url: 'https://wa.link/smirhm'),
        ],
      ),
    );
  }
}
// Note: Replace 'https://example.com/privacy', 'https://example.com/licenses', etc. with actual URLs.
// The URLs are placeholders and should be replaced with the actual links to your privacy policy, licenses, terms of use, platform rules, support, and developer contact information.
// The app version is displayed in the 'Version' row, and the other rows are clickable links that open in the default web browser.
// The app bar includes a search icon, which can be implemented later if needed.