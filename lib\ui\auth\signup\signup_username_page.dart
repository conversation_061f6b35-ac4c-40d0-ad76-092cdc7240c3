import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';
import 'package:business_app/services/supabase_service.dart';

class SignupUsernamePage extends StatefulWidget {
  const SignupUsernamePage({super.key});

  @override
  State<SignupUsernamePage> createState() => _SignupUsernamePageState();
}

class _SignupUsernamePageState extends State<SignupUsernamePage> {
  final _usernameController = TextEditingController();
  String? _usernameError;
  bool _isCheckingAvailability = false;
  bool? _isAvailable;
  List<String> _suggestions = [];

  @override
  void initState() {
    super.initState();
    _generateSuggestions();
    _prefillUsernameForGoogleUsers();
  }

  void _prefillUsernameForGoogleUsers() {
    final state = context.read<AuthBloc>().state;

    // If this is a Google user with a generated username, pre-fill it
    if (state.isGoogleSignup == true && state.tempUsername != null) {
      _usernameController.text = state.tempUsername!;
      _onUsernameChanged(state.tempUsername!);
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  void _generateSuggestions() {
    final state = context.read<AuthBloc>().state;
    final name = state.tempName ?? 'user';

    // Generate username suggestions based on name
    final baseName = name.toLowerCase().replaceAll(' ', '');
    _suggestions = [
      baseName,
      '${baseName}123',
      '${baseName}_official',
      '${baseName}2024',
      'the_$baseName',
    ];
  }

  void _onUsernameChanged(String value) {
    setState(() {
      _usernameError = null;
      _isAvailable = null;
    });

    if (value.length >= 3) {
      _checkAvailability(value);
    }
  }

  void _checkAvailability(String username) async {
    setState(() {
      _isCheckingAvailability = true;
    });

    try {
      // Use real username availability check
      final isAvailable = await AuthService.isUsernameAvailable(username);

      if (mounted) {
        setState(() {
          _isCheckingAvailability = false;
          _isAvailable = isAvailable;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCheckingAvailability = false;
          _isAvailable = false;
          _usernameError = 'Failed to check availability. Please try again.';
        });
      }
    }
  }

  void _onNext() {
    final username = _usernameController.text.trim();

    if (username.length < 3) {
      setState(() {
        _usernameError = 'Username must be at least 3 characters';
      });
      return;
    }

    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username)) {
      setState(() {
        _usernameError =
            'Username can only contain letters, numbers, and underscores';
      });
      return;
    }

    if (_isAvailable == false) {
      setState(() {
        _usernameError = 'Username is not available';
      });
      return;
    }

    context.read<AuthBloc>().add(SignupUsernameSelected(username: username));
  }

  void _selectSuggestion(String suggestion) {
    _usernameController.text = suggestion;
    _onUsernameChanged(suggestion);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final username = _usernameController.text;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: IntrinsicHeight(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),

                    // Header with progress
                    BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        final isGoogleUser = state.isGoogleSignup == true;

                        return Column(
                          children: [
                            // Show progress only for regular signup
                            if (!isGoogleUser) ...[
                              const AuthProgressIndicator(
                                currentStep: 3,
                                totalSteps: 5,
                              ),
                              const SizedBox(height: 20),
                            ],

                            // Dynamic header based on user type
                            if (isGoogleUser)
                              _buildGoogleUserHeader(state)
                            else
                              const AuthUsernameHeader(),
                          ],
                        );
                      },
                    ),

                    const SizedBox(height: 40),

                    // Username field
                    AuthTextField(
                      controller: _usernameController,
                      label: 'Username',
                      hintText: 'Choose a username',
                      errorText: _usernameError,
                      autofocus: true,
                      prefixIcon: const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '@',
                          style: TextStyle(fontSize: 17, color: Colors.grey),
                        ),
                      ),
                      suffixIcon:
                          _isCheckingAvailability
                              ? const Padding(
                                padding: EdgeInsets.all(12),
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              )
                              : _isAvailable != null
                              ? Icon(
                                _isAvailable!
                                    ? Icons.check_circle
                                    : Icons.cancel,
                                color:
                                    _isAvailable! ? Colors.green : Colors.red,
                              )
                              : null,
                      onChanged: _onUsernameChanged,
                    ),

                    const SizedBox(height: 16),

                    // Availability status
                    if (username.isNotEmpty &&
                        !_isCheckingAvailability &&
                        _isAvailable != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color:
                              _isAvailable!
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color:
                                _isAvailable!
                                    ? Colors.green.withOpacity(0.3)
                                    : Colors.red.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _isAvailable! ? Icons.check_circle : Icons.cancel,
                              size: 16,
                              color: _isAvailable! ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _isAvailable!
                                  ? '@$username is available'
                                  : '@$username is not available',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color:
                                    _isAvailable! ? Colors.green : Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),

                    const SizedBox(height: 24),

                    // Username suggestions
                    if (_suggestions.isNotEmpty) ...[
                      Text(
                        'Suggestions:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.textTheme.bodyLarge?.color,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children:
                            _suggestions.map((suggestion) {
                              return GestureDetector(
                                onTap: () => _selectSuggestion(suggestion),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(
                                      0xFF1DA1F2,
                                    ).withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: const Color(
                                        0xFF1DA1F2,
                                      ).withOpacity(0.3),
                                    ),
                                  ),
                                  child: Text(
                                    '@$suggestion',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF1DA1F2),
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                      ),
                    ],

                    const Spacer(),

                    // Next button
                    BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        return AuthPrimaryButton(
                          text: 'Next',
                          isLoading: state.isLoading,
                          onPressed: _onNext,
                        );
                      },
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGoogleUserHeader(AuthState state) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Welcome back message
        Text(
          'Almost there, ${state.userName ?? 'User'}!',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: theme.textTheme.headlineLarge?.color,
          ),
        ),

        const SizedBox(height: 12),

        Text(
          'Choose your username to complete your profile',
          style: TextStyle(fontSize: 16, color: Colors.grey[600], height: 1.4),
        ),

        const SizedBox(height: 20),

        // Show suggested username info
        if (state.tempUsername != null)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'We\'ve suggested a username based on your Google profile. Feel free to customize it!',
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
