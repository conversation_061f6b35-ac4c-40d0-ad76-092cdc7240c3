import 'package:equatable/equatable.dart';

abstract class ProductEvent extends Equatable {
  const ProductEvent();

  @override
  List<Object?> get props => [];
}

// Load products events
class LoadProductsEvent extends ProductEvent {
  final String? category;
  final String? searchQuery;
  final bool refresh;

  const LoadProductsEvent({
    this.category,
    this.searchQuery,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [category, searchQuery, refresh];
}

class LoadMoreProductsEvent extends ProductEvent {
  const LoadMoreProductsEvent();
}

class RefreshProductsEvent extends ProductEvent {
  const RefreshProductsEvent();
}

// Product CRUD events
class AddProductEvent extends ProductEvent {
  final String name;
  final String description;
  final double price;
  final String category;
  final List<String> images;
  final int stockQuantity;
  final double? discountPrice;
  final String? brand;
  final double? weight;
  final String? dimensions;
  final List<String> tags;
  final Map<String, dynamic>? specifications;

  const AddProductEvent({
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    this.images = const [],
    this.stockQuantity = 0,
    this.discountPrice,
    this.brand,
    this.weight,
    this.dimensions,
    this.tags = const [],
    this.specifications,
  });

  @override
  List<Object?> get props => [
    name,
    description,
    price,
    category,
    images,
    stockQuantity,
    discountPrice,
    brand,
    weight,
    dimensions,
    tags,
    specifications,
  ];
}

class UpdateProductEvent extends ProductEvent {
  final String productId;
  final String? name;
  final String? description;
  final double? price;
  final String? category;
  final List<String>? images;
  final int? stockQuantity;
  final bool? isAvailable;
  final double? discountPrice;
  final String? brand;
  final double? weight;
  final String? dimensions;
  final List<String>? tags;
  final Map<String, dynamic>? specifications;

  const UpdateProductEvent({
    required this.productId,
    this.name,
    this.description,
    this.price,
    this.category,
    this.images,
    this.stockQuantity,
    this.isAvailable,
    this.discountPrice,
    this.brand,
    this.weight,
    this.dimensions,
    this.tags,
    this.specifications,
  });

  @override
  List<Object?> get props => [
    productId,
    name,
    description,
    price,
    category,
    images,
    stockQuantity,
    isAvailable,
    discountPrice,
    brand,
    weight,
    dimensions,
    tags,
    specifications,
  ];
}

class DeleteProductEvent extends ProductEvent {
  final String productId;

  const DeleteProductEvent(this.productId);

  @override
  List<Object?> get props => [productId];
}

// Product interaction events
class ToggleProductAvailabilityEvent extends ProductEvent {
  final String productId;

  const ToggleProductAvailabilityEvent(this.productId);

  @override
  List<Object?> get props => [productId];
}

class UpdateProductStockEvent extends ProductEvent {
  final String productId;
  final int newStock;

  const UpdateProductStockEvent({
    required this.productId,
    required this.newStock,
  });

  @override
  List<Object?> get props => [productId, newStock];
}

// Filter and search events
class FilterProductsByCategoryEvent extends ProductEvent {
  final String? category;

  const FilterProductsByCategoryEvent(this.category);

  @override
  List<Object?> get props => [category];
}

class SearchProductsEvent extends ProductEvent {
  final String query;

  const SearchProductsEvent(this.query);

  @override
  List<Object?> get props => [query];
}

class ClearProductSearchEvent extends ProductEvent {
  const ClearProductSearchEvent();
}

// Sort events
class SortProductsEvent extends ProductEvent {
  final ProductSortType sortType;

  const SortProductsEvent(this.sortType);

  @override
  List<Object?> get props => [sortType];
}

enum ProductSortType {
  nameAsc,
  nameDesc,
  priceAsc,
  priceDesc,
  dateAsc,
  dateDesc,
  stockAsc,
  stockDesc,
  ratingAsc,
  ratingDesc,
}

// Bulk operations
class BulkDeleteProductsEvent extends ProductEvent {
  final List<String> productIds;

  const BulkDeleteProductsEvent(this.productIds);

  @override
  List<Object?> get props => [productIds];
}

class BulkUpdateProductAvailabilityEvent extends ProductEvent {
  final List<String> productIds;
  final bool isAvailable;

  const BulkUpdateProductAvailabilityEvent({
    required this.productIds,
    required this.isAvailable,
  });

  @override
  List<Object?> get props => [productIds, isAvailable];
}

// Analytics events
class IncrementProductViewEvent extends ProductEvent {
  final String productId;

  const IncrementProductViewEvent(this.productId);

  @override
  List<Object?> get props => [productId];
}

class LoadProductAnalyticsEvent extends ProductEvent {
  final String productId;

  const LoadProductAnalyticsEvent(this.productId);

  @override
  List<Object?> get props => [productId];
}
