-- =====================================================
-- SOCIAL INTERACTION TABLES
-- Following Big App patterns for social features
-- =====================================================

-- =====================================================
-- NOTE: FOLLOWS TABLE ALREADY CREATED IN 001_core_tables.sql
-- Skipping follows table creation to avoid duplicate table error
-- =====================================================

-- =====================================================
-- 7. LIKES TABLE (Engagement Tracking)
-- =====================================================

CREATE TABLE public.likes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, post_id) -- Users can only like a post once
);

-- Indexes for performance
CREATE INDEX idx_likes_user_id ON public.likes(user_id);
CREATE INDEX idx_likes_post_id ON public.likes(post_id);
CREATE INDEX idx_likes_created_at ON public.likes(created_at DESC);

-- Composite index for user's liked posts
CREATE INDEX idx_likes_user_created ON public.likes(user_id, created_at DESC);

-- =====================================================
-- 8. COMMENTS TABLE (User Engagement)
-- =====================================================

CREATE TABLE public.comments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE NOT NULL,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE, -- For nested comments/replies
    
    -- Content
    content TEXT NOT NULL CHECK (length(content) > 0 AND length(content) <= 2000),
    
    -- Engagement metrics (denormalized)
    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),
    replies_count INTEGER DEFAULT 0 CHECK (replies_count >= 0),
    
    -- Status and moderation
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (parent_id IS NULL OR parent_id != id) -- Comments cannot be parent of themselves
);

-- Indexes for performance
CREATE INDEX idx_comments_user_id ON public.comments(user_id);
CREATE INDEX idx_comments_post_id ON public.comments(post_id);
CREATE INDEX idx_comments_parent_id ON public.comments(parent_id);
CREATE INDEX idx_comments_created_at ON public.comments(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX idx_comments_post_created ON public.comments(post_id, created_at DESC) WHERE is_deleted = false;
CREATE INDEX idx_comments_user_created ON public.comments(user_id, created_at DESC) WHERE is_deleted = false;

-- =====================================================
-- COMMENT LIKES TABLE (Nested Engagement)
-- =====================================================

CREATE TABLE public.comment_likes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    comment_id UUID REFERENCES public.comments(id) ON DELETE CASCADE NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, comment_id) -- Users can only like a comment once
);

-- Indexes for performance
CREATE INDEX idx_comment_likes_user_id ON public.comment_likes(user_id);
CREATE INDEX idx_comment_likes_comment_id ON public.comment_likes(comment_id);
CREATE INDEX idx_comment_likes_created_at ON public.comment_likes(created_at DESC);

-- =====================================================
-- RLS POLICIES FOR SOCIAL TABLES
-- =====================================================

-- FOLLOWS TABLE SECURITY - HANDLED IN 002_rls_policies.sql
-- Skipping follows RLS policies to avoid conflicts

-- LIKES TABLE SECURITY
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;

-- Users can view likes on posts they can see
CREATE POLICY "likes_select_policy" ON public.likes
FOR SELECT USING (
    -- Can see likes on posts they can view
    EXISTS (
        SELECT 1 FROM public.posts p 
        WHERE p.id = post_id AND (
            p.is_public = true OR
            p.user_id = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.follows f 
                WHERE f.follower_id = auth.uid() AND f.following_id = p.user_id
            )
        )
    )
);

-- Only authenticated users can like posts
CREATE POLICY "likes_insert_policy" ON public.likes
FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
);

-- Users can only unlike their own likes
CREATE POLICY "likes_delete_policy" ON public.likes
FOR DELETE USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- COMMENTS TABLE SECURITY
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Users can view comments on posts they can see
CREATE POLICY "comments_select_policy" ON public.comments
FOR SELECT USING (
    is_deleted = false AND
    EXISTS (
        SELECT 1 FROM public.posts p 
        WHERE p.id = post_id AND (
            p.is_public = true OR
            p.user_id = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.follows f 
                WHERE f.follower_id = auth.uid() AND f.following_id = p.user_id
            )
        )
    )
);

-- Only authenticated users can comment
CREATE POLICY "comments_insert_policy" ON public.comments
FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
);

-- Users can only update their own comments
CREATE POLICY "comments_update_policy" ON public.comments
FOR UPDATE USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Users can delete their own comments, moderators can delete any
CREATE POLICY "comments_delete_policy" ON public.comments
FOR DELETE USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- COMMENT LIKES TABLE SECURITY
ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;

-- Users can view comment likes on comments they can see
CREATE POLICY "comment_likes_select_policy" ON public.comment_likes
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.comments c
        JOIN public.posts p ON c.post_id = p.id
        WHERE c.id = comment_id AND c.is_deleted = false AND (
            p.is_public = true OR
            p.user_id = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.follows f 
                WHERE f.follower_id = auth.uid() AND f.following_id = p.user_id
            )
        )
    )
);

-- Only authenticated users can like comments
CREATE POLICY "comment_likes_insert_policy" ON public.comment_likes
FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
);

-- Users can only unlike their own comment likes
CREATE POLICY "comment_likes_delete_policy" ON public.comment_likes
FOR DELETE USING (
    user_id = auth.uid()
);

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions on social tables
GRANT SELECT, INSERT, DELETE ON public.follows TO authenticated;
GRANT SELECT, INSERT, DELETE ON public.likes TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.comments TO authenticated;
GRANT SELECT, INSERT, DELETE ON public.comment_likes TO authenticated;
