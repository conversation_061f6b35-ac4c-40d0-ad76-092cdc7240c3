# Avatar Implementation Guide

This guide shows how to integrate the new avatar system into your existing Flutter business app with Supabase storage.

## Overview

The avatar system provides:
- ✅ **Professional image handling** for profile and background images
- ✅ **Supabase Storage integration** with optimized uploads
- ✅ **Row Level Security (RLS)** for secure access control
- ✅ **Image transformations** for optimized delivery
- ✅ **Comprehensive error handling** and validation
- ✅ **Flutter best practices** with BLoC state management
- ✅ **Reusable widgets** for consistent UI

## File Structure

```
lib/avatar/
├── models/
│   ├── image_config.dart          # Image configuration settings
│   └── image_upload_result.dart   # Upload result model
├── services/
│   ├── avatar_service.dart        # Main avatar service
│   ├── image_picker_service.dart  # Image picking with permissions
│   └── supabase_storage_service.dart # Enhanced Supabase storage
├── widgets/
│   ├── profile_image_widget.dart  # Profile image widget
│   └── background_image_widget.dart # Background image widget
├── examples/
│   └── enhanced_edit_profile_example.dart # Usage example
└── avatar.dart                    # Main export file
```

## Step-by-Step Implementation

### Step 1: Supabase Setup

1. **Follow the Supabase setup guide**: `SUPABASE_AVATAR_SETUP_GUIDE.md`
2. **Create storage buckets** with proper RLS policies
3. **Configure file size limits** and MIME types
4. **Test the setup** with SQL queries

### Step 2: Update Your Edit Profile Page

Replace your existing image handling in `lib/ui/user_profiles/edit_profile_page.dart`:

```dart
import 'package:business_app/avatar/avatar.dart';

class EditProfilePage extends StatefulWidget {
  // ... existing code
}

class _EditProfilePageState extends State<EditProfilePage> {
  // Replace existing image handling with:
  String? _currentProfileImageUrl;
  String? _currentBackgroundImageUrl;
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    // Initialize with current user data
    final profileState = context.read<ProfileBloc>().state;
    if (profileState.profile != null) {
      _currentProfileImageUrl = profileState.profile!.profileImageUrl;
      _currentBackgroundImageUrl = profileState.profile!.backgroundImageUrl;
      _currentUserId = profileState.profile!.id;
    }
  }

  // Replace your existing image widgets with:
  Widget _buildProfileImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Profile Image',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Center(
          child: ProfileImageWidget(
            currentImageUrl: _currentProfileImageUrl,
            onImageChanged: (url) {
              setState(() {
                _currentProfileImageUrl = url;
                _hasChanges = true;
              });
            },
            onError: _handleImageError,
            radius: 60,
            isEditable: true,
            userId: _currentUserId,
          ),
        ),
      ],
    );
  }

  Widget _buildBackgroundImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Background Image',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        BackgroundImageWidget(
          currentImageUrl: _currentBackgroundImageUrl,
          onImageChanged: (url) {
            setState(() {
              _currentBackgroundImageUrl = url;
              _hasChanges = true;
            });
          },
          onError: _handleImageError,
          height: 200,
          isEditable: true,
          userId: _currentUserId,
        ),
      ],
    );
  }

  void _handleImageError(String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(error)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        duration: const Duration(milliseconds: 3000),
      ),
    );
  }
}
```

### Step 3: Update Your Profile BLoC

Your existing ProfileBloc already handles image uploads correctly. The avatar system integrates seamlessly with your current implementation.

### Step 4: Update Profile Display Pages

In `lib/ui/user_profiles/profile_page.dart`, use optimized image URLs:

```dart
import 'package:business_app/avatar/avatar.dart';

// Replace existing image displays with optimized URLs:
Widget _buildProfileImage(String? imageUrl) {
  final optimizedUrl = AvatarService.getOptimizedProfileImageUrl(imageUrl);
  
  return CircleAvatar(
    radius: 50,
    backgroundImage: optimizedUrl != null 
        ? CachedNetworkImageProvider(optimizedUrl)
        : null,
    child: optimizedUrl == null 
        ? const Icon(Icons.person, size: 50)
        : null,
  );
}

Widget _buildBackgroundImage(String? imageUrl) {
  final optimizedUrl = AvatarService.getOptimizedBackgroundImageUrl(imageUrl);
  
  return Container(
    height: 200,
    width: double.infinity,
    decoration: BoxDecoration(
      image: optimizedUrl != null
          ? DecorationImage(
              image: CachedNetworkImageProvider(optimizedUrl),
              fit: BoxFit.cover,
            )
          : null,
      color: optimizedUrl == null ? Colors.grey[200] : null,
    ),
    child: optimizedUrl == null
        ? const Center(
            child: Icon(Icons.image, size: 48, color: Colors.grey),
          )
        : null,
  );
}
```

### Step 5: Update Signup Profile Page

In `lib/ui/auth/signup/signup_profile_page.dart`:

```dart
import 'package:business_app/avatar/avatar.dart';

class _SignupProfilePageState extends State<SignupProfilePage> {
  String? _selectedImageUrl;
  String? _currentUserId;

  // Replace existing image picker with:
  Widget _buildProfileImagePicker() {
    return ProfileImageWidget(
      currentImageUrl: _selectedImageUrl,
      onImageChanged: (url) {
        setState(() {
          _selectedImageUrl = url;
        });
      },
      onError: (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
      },
      radius: 60,
      isEditable: true,
      userId: _currentUserId,
    );
  }
}
```

## Configuration Options

### Image Configurations

The system provides pre-configured settings for different image types:

```dart
// Profile images: 512x512, 80% quality, 5MB max
ImageConfig.profile

// Background images: 1920x1080, 85% quality, 10MB max  
ImageConfig.background

// Product images: 1024x1024, 85% quality, 8MB max
ImageConfig.product
```

### Custom Configuration

```dart
const customConfig = ImageConfig(
  maxWidth: 800,
  maxHeight: 600,
  imageQuality: 90,
  maxFileSizeBytes: 3 * 1024 * 1024, // 3MB
  allowedExtensions: ['.jpg', '.png', '.webp'],
  enableTransformation: true,
  transformWidth: 400,
  transformHeight: 300,
  transformQuality: 85,
);
```

## Error Handling

The system provides comprehensive error handling:

```dart
// Service-level error handling
final result = await AvatarService.uploadProfileImage(
  userId: userId,
  imageFile: imageFile,
);

if (result.isSuccess) {
  // Handle success
  final imageUrl = result.data!;
} else {
  // Handle error
  final error = result.error!;
}

// Widget-level error handling
ProfileImageWidget(
  onError: (error) {
    // Show user-friendly error message
    showErrorSnackBar(context, error);
  },
  // ... other properties
)
```

## Performance Optimizations

1. **Image Transformations**: Automatic resizing and optimization
2. **Caching**: Uses CachedNetworkImage for efficient caching
3. **Compression**: Configurable quality settings
4. **CDN Delivery**: Supabase CDN for global delivery
5. **Lazy Loading**: Images load only when needed

## Security Features

1. **RLS Policies**: User-specific folder access
2. **File Validation**: Type and size validation
3. **Permission Handling**: Proper camera/gallery permissions
4. **Secure URLs**: Signed URLs for private content
5. **User Isolation**: Each user has their own folder

## Testing

Test the implementation with:

```dart
// Test image upload
final result = await AvatarService.uploadProfileImage(
  userId: 'test-user-id',
  imageFile: File('path/to/test/image.jpg'),
);

// Test image optimization
final optimizedUrl = AvatarService.getOptimizedProfileImageUrl(
  'https://your-project.supabase.co/storage/v1/object/public/profile-images/user-id/profile_123.jpg'
);

// Test image validation
final validationError = await AvatarService.validateImageFile(
  File('path/to/image.jpg'),
  ImageConfig.profile,
);
```

## Migration from Existing Code

1. **Keep existing BLoC logic** - no changes needed
2. **Replace image widgets** with new avatar widgets
3. **Update image URLs** to use optimized versions
4. **Add error handling** for better user experience
5. **Test thoroughly** with different image types and sizes

## Best Practices

1. **Always validate images** before upload
2. **Use optimized URLs** for display
3. **Handle errors gracefully** with user feedback
4. **Implement loading states** for better UX
5. **Test on different devices** and network conditions
6. **Monitor storage usage** in Supabase dashboard
7. **Use appropriate image sizes** for different contexts

This implementation provides a production-ready, secure, and performant image handling system that integrates seamlessly with your existing Flutter business app architecture.
