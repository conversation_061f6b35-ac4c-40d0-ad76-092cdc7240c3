-- =====================================================
-- DATABASE FUNCTIONS AND TRIGGERS
-- Business logic automation following Big App patterns
-- =====================================================

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate username from email
CREATE OR REPLACE FUNCTION public.generate_username(email_input TEXT)
RETURNS TEXT AS $$
DECLARE
    base_username TEXT;
    final_username TEXT;
    counter INTEGER := 0;
BEGIN
    -- Extract username part from email
    base_username := split_part(email_input, '@', 1);
    
    -- Clean up username (remove special characters, convert to lowercase)
    base_username := lower(regexp_replace(base_username, '[^a-zA-Z0-9]', '', 'g'));
    
    -- Ensure minimum length
    IF length(base_username) < 3 THEN
        base_username := base_username || 'user';
    END IF;
    
    -- Find available username
    final_username := base_username;
    WHILE EXISTS (SELECT 1 FROM public.profiles WHERE username = final_username) LOOP
        counter := counter + 1;
        final_username := base_username || counter::TEXT;
    END LOOP;
    
    RETURN final_username;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- USER PROFILE FUNCTIONS
-- =====================================================

-- Function to create user profile after signup
CREATE OR REPLACE FUNCTION public.create_user_profile()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_phone TEXT;
    generated_username TEXT;
    display_name TEXT;
BEGIN
    -- Extract email and phone from the user record
    user_email := NEW.email;
    user_phone := NEW.phone;

    -- Generate username based on available information
    IF user_email IS NOT NULL THEN
        generated_username := public.generate_username(user_email);
        display_name := COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(user_email, '@', 1));
    ELSIF user_phone IS NOT NULL THEN
        generated_username := 'user_' || substring(replace(user_phone, '+', ''), 1, 8) || '_' || extract(epoch from now())::text;
        display_name := COALESCE(NEW.raw_user_meta_data->>'full_name', 'User');
    ELSE
        -- Fallback for edge cases
        generated_username := 'user_' || NEW.id::text;
        display_name := COALESCE(NEW.raw_user_meta_data->>'full_name', 'User');
    END IF;

    INSERT INTO public.profiles (
        id,
        username,
        full_name,
        email,
        phone,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        generated_username,
        display_name,
        user_email,
        user_phone,
        NOW(),
        NOW()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user last seen
CREATE OR REPLACE FUNCTION public.update_user_last_seen(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.profiles 
    SET last_seen_at = NOW() 
    WHERE id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COUNTER UPDATE FUNCTIONS
-- =====================================================

-- Function to update profile counters
CREATE OR REPLACE FUNCTION public.update_profile_counters()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle follows count
    IF TG_TABLE_NAME = 'follows' THEN
        IF TG_OP = 'INSERT' THEN
            -- Increment following count for follower
            UPDATE public.profiles 
            SET following_count = following_count + 1 
            WHERE id = NEW.follower_id;
            
            -- Increment followers count for followed user
            UPDATE public.profiles 
            SET followers_count = followers_count + 1 
            WHERE id = NEW.following_id;
            
            RETURN NEW;
        ELSIF TG_OP = 'DELETE' THEN
            -- Decrement following count for follower
            UPDATE public.profiles 
            SET following_count = GREATEST(following_count - 1, 0) 
            WHERE id = OLD.follower_id;
            
            -- Decrement followers count for followed user
            UPDATE public.profiles 
            SET followers_count = GREATEST(followers_count - 1, 0) 
            WHERE id = OLD.following_id;
            
            RETURN OLD;
        END IF;
    END IF;
    
    -- Handle posts count
    IF TG_TABLE_NAME = 'posts' THEN
        IF TG_OP = 'INSERT' AND NEW.status = 'published' THEN
            UPDATE public.profiles 
            SET posts_count = posts_count + 1 
            WHERE id = NEW.user_id;
            RETURN NEW;
        ELSIF TG_OP = 'DELETE' AND OLD.status = 'published' THEN
            UPDATE public.profiles 
            SET posts_count = GREATEST(posts_count - 1, 0) 
            WHERE id = OLD.user_id;
            RETURN OLD;
        ELSIF TG_OP = 'UPDATE' THEN
            -- Handle status changes
            IF OLD.status = 'published' AND NEW.status != 'published' THEN
                UPDATE public.profiles 
                SET posts_count = GREATEST(posts_count - 1, 0) 
                WHERE id = NEW.user_id;
            ELSIF OLD.status != 'published' AND NEW.status = 'published' THEN
                UPDATE public.profiles 
                SET posts_count = posts_count + 1 
                WHERE id = NEW.user_id;
            END IF;
            RETURN NEW;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to update post engagement counters
CREATE OR REPLACE FUNCTION public.update_post_counters()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle likes
    IF TG_TABLE_NAME = 'likes' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE public.posts 
            SET likes_count = likes_count + 1 
            WHERE id = NEW.post_id;
            RETURN NEW;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE public.posts 
            SET likes_count = GREATEST(likes_count - 1, 0) 
            WHERE id = OLD.post_id;
            RETURN OLD;
        END IF;
    END IF;
    
    -- Handle comments
    IF TG_TABLE_NAME = 'comments' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE public.posts 
            SET comments_count = comments_count + 1 
            WHERE id = NEW.post_id;
            RETURN NEW;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE public.posts 
            SET comments_count = GREATEST(comments_count - 1, 0) 
            WHERE id = OLD.post_id;
            RETURN OLD;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SHOP FUNCTIONS
-- =====================================================

-- Function to calculate shop rating
CREATE OR REPLACE FUNCTION public.calculate_shop_rating(shop_uuid UUID)
RETURNS VOID AS $$
DECLARE
    avg_rating DECIMAL(3,2);
    review_count INTEGER;
BEGIN
    SELECT 
        COALESCE(AVG(rating), 0.00),
        COUNT(*)
    INTO avg_rating, review_count
    FROM public.reviews 
    WHERE shop_id = shop_uuid;
    
    UPDATE public.shops 
    SET 
        rating = avg_rating,
        total_reviews = review_count,
        updated_at = NOW()
    WHERE id = shop_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to update shop stats
CREATE OR REPLACE FUNCTION public.update_shop_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle reviews
    IF TG_TABLE_NAME = 'reviews' THEN
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
            PERFORM public.calculate_shop_rating(COALESCE(NEW.shop_id, OLD.shop_id));
        END IF;
    END IF;
    
    -- Handle orders (sales count)
    IF TG_TABLE_NAME = 'orders' THEN
        IF TG_OP = 'INSERT' AND NEW.status = 'delivered' THEN
            UPDATE public.shops 
            SET 
                total_sales = total_sales + 1,
                total_revenue = total_revenue + NEW.total_amount,
                updated_at = NOW()
            WHERE id = NEW.shop_id;
        ELSIF TG_OP = 'UPDATE' THEN
            -- Handle status changes
            IF OLD.status != 'delivered' AND NEW.status = 'delivered' THEN
                UPDATE public.shops 
                SET 
                    total_sales = total_sales + 1,
                    total_revenue = total_revenue + NEW.total_amount,
                    updated_at = NOW()
                WHERE id = NEW.shop_id;
            ELSIF OLD.status = 'delivered' AND NEW.status != 'delivered' THEN
                UPDATE public.shops 
                SET 
                    total_sales = GREATEST(total_sales - 1, 0),
                    total_revenue = GREATEST(total_revenue - OLD.total_amount, 0),
                    updated_at = NOW()
                WHERE id = NEW.shop_id;
            END IF;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SEARCH FUNCTIONS
-- =====================================================

-- Function for advanced product search
CREATE OR REPLACE FUNCTION public.search_products(
    search_query TEXT DEFAULT '',
    category_filter UUID DEFAULT NULL,
    min_price DECIMAL DEFAULT NULL,
    max_price DECIMAL DEFAULT NULL,
    shop_filter UUID DEFAULT NULL,
    sort_by TEXT DEFAULT 'relevance',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name VARCHAR(200),
    description TEXT,
    price DECIMAL(10,2),
    images TEXT[],
    shop_name VARCHAR(100),
    rating DECIMAL(3,2),
    view_count INTEGER,
    relevance_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.description,
        p.price,
        p.images,
        s.shop_name,
        p.rating,
        p.view_count,
        CASE 
            WHEN search_query = '' THEN 0::REAL
            ELSE ts_rank(to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, '')), plainto_tsquery('english', search_query))
        END as relevance_score
    FROM public.products p
    JOIN public.shops s ON p.shop_id = s.id
    WHERE 
        p.status = 'active' AND
        s.is_active = true AND
        (search_query = '' OR to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, '')) @@ plainto_tsquery('english', search_query)) AND
        (category_filter IS NULL OR p.category_id = category_filter) AND
        (min_price IS NULL OR p.price >= min_price) AND
        (max_price IS NULL OR p.price <= max_price) AND
        (shop_filter IS NULL OR p.shop_id = shop_filter)
    ORDER BY 
        CASE 
            WHEN sort_by = 'price_asc' THEN p.price
            ELSE NULL
        END ASC,
        CASE 
            WHEN sort_by = 'price_desc' THEN p.price
            ELSE NULL
        END DESC,
        CASE 
            WHEN sort_by = 'rating' THEN p.rating
            ELSE NULL
        END DESC,
        CASE 
            WHEN sort_by = 'newest' THEN p.created_at
            ELSE NULL
        END DESC,
        CASE 
            WHEN sort_by = 'popular' THEN p.view_count
            ELSE NULL
        END DESC,
        CASE 
            WHEN sort_by = 'relevance' OR search_query != '' THEN ts_rank(to_tsvector('english', p.name || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, '')), plainto_tsquery('english', search_query))
            ELSE NULL
        END DESC,
        p.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREATE TRIGGERS
-- =====================================================

-- Trigger to create user profile after signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.create_user_profile();

-- Triggers to update updated_at timestamps
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_shops_updated_at
    BEFORE UPDATE ON public.shops
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON public.categories
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON public.products
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_posts_updated_at
    BEFORE UPDATE ON public.posts
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
