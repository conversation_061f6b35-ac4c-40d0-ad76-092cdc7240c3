import 'package:logger/logger.dart';
import '../supabase/config.dart';
import '../models/auth/user_role.dart';
import '../models/auth/auth_result.dart';
import 'supabase_service.dart';

/// Service for managing user roles and business upgrades
class UserRoleService extends BaseSupabaseService {
  static final Logger _logger = Logger();
  static const String _logTag = '[UserRoleService]';

  /// Upgrade user to business role
  static Future<AuthResult<bool>> upgradeToBusinessUser({
    required String userId,
    required String businessName,
    required String businessDescription,
    String? businessType,
    String? location,
    String? phone,
    String? email,
    String? websiteUrl,
  }) async {
    _logger.i('$_logTag Upgrading user $userId to business role...');

    try {
      // Start a transaction to update both profile and create shop
      await BaseSupabaseService.client.rpc(
        'upgrade_user_to_business',
        params: {
          'user_uuid': userId,
          'business_name': businessName,
          'business_description': businessDescription,
          'business_type': businessType ?? 'individual',
          'business_location': location,
          'business_phone': phone,
          'business_email': email,
          'business_website': websiteUrl,
        },
      );

      _logger.i('$_logTag ✅ Successfully upgraded user to business role');
      return AuthResult.success(true);
    } catch (e) {
      final error = 'Failed to upgrade to business user: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Check if user can upgrade to business
  static Future<AuthResult<bool>> canUpgradeToBusiness(String userId) async {
    _logger.i('$_logTag Checking if user $userId can upgrade to business...');

    try {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.profiles)
              .select('role, is_business')
              .eq('id', userId)
              .maybeSingle();

      if (response == null) {
        return AuthResult.error('User profile not found');
      }

      final role = response['role'] as String?;
      final isBusiness = response['is_business'] as bool?;

      // User can upgrade if they're not already a business user
      final canUpgrade =
          role == 'user' && (isBusiness == false || isBusiness == null);

      _logger.i(
        '$_logTag User can upgrade: $canUpgrade (role: $role, is_business: $isBusiness)',
      );
      return AuthResult.success(canUpgrade);
    } catch (e) {
      final error = 'Failed to check upgrade eligibility: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Change user role (admin only)
  static Future<AuthResult<bool>> changeUserRole({
    required String targetUserId,
    required UserRole newRole,
    required String adminUserId,
  }) async {
    _logger.i(
      '$_logTag Admin $adminUserId changing role for user $targetUserId to ${newRole.name}',
    );

    try {
      // Verify admin has permission to change roles
      final adminCheck = await _verifyAdminPermissions(adminUserId);
      if (!adminCheck.isSuccess) {
        return AuthResult.error(adminCheck.error!);
      }

      // Update user role
      await BaseSupabaseService.client
          .from(DatabaseTables.profiles)
          .update({
            'role': newRole.name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', targetUserId);

      _logger.i('$_logTag ✅ Successfully changed user role');
      return AuthResult.success(true);
    } catch (e) {
      final error = 'Failed to change user role: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Get user role
  static Future<AuthResult<UserRole>> getUserRole(String userId) async {
    try {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.profiles)
              .select('role')
              .eq('id', userId)
              .maybeSingle();

      if (response == null) {
        return AuthResult.error('User profile not found');
      }

      final roleString = response['role'] as String? ?? 'user';
      final role = UserRole.fromString(roleString);

      return AuthResult.success(role);
    } catch (e) {
      final error = 'Failed to get user role: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Verify admin permissions
  static Future<AuthResult<bool>> _verifyAdminPermissions(String userId) async {
    try {
      final roleResult = await getUserRole(userId);
      if (!roleResult.isSuccess) {
        return AuthResult.error('Failed to verify admin permissions');
      }

      final role = roleResult.data!;
      final hasPermission = role.isAdmin || role.isSuperAdmin;

      if (!hasPermission) {
        return AuthResult.error(
          'Insufficient permissions to change user roles',
        );
      }

      return AuthResult.success(true);
    } catch (e) {
      return AuthResult.error(
        'Failed to verify admin permissions: ${e.toString()}',
      );
    }
  }

  /// Get business profile for user
  static Future<AuthResult<Map<String, dynamic>?>> getBusinessProfile(
    String userId,
  ) async {
    _logger.i('$_logTag Getting business profile for user $userId...');

    try {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.shops)
              .select()
              .eq('owner_id', userId)
              .maybeSingle();

      _logger.i('$_logTag Business profile found: ${response != null}');
      return AuthResult.success(response);
    } catch (e) {
      final error = 'Failed to get business profile: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Check if user has business profile
  static Future<AuthResult<bool>> hasBusinessProfile(String userId) async {
    final result = await getBusinessProfile(userId);
    if (!result.isSuccess) {
      return AuthResult.error(result.error!);
    }

    return AuthResult.success(result.data != null);
  }

  /// Downgrade from business to regular user
  static Future<AuthResult<bool>> downgradeFromBusiness({
    required String userId,
    required String adminUserId,
  }) async {
    _logger.i('$_logTag Downgrading user $userId from business role...');

    try {
      // Verify admin permissions
      final adminCheck = await _verifyAdminPermissions(adminUserId);
      if (!adminCheck.isSuccess) {
        return AuthResult.error(adminCheck.error!);
      }

      // Update user profile
      await BaseSupabaseService.client
          .from(DatabaseTables.profiles)
          .update({
            'role': 'user',
            'is_business': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);

      // Deactivate shop (don't delete to preserve data)
      await BaseSupabaseService.client
          .from(DatabaseTables.shops)
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('owner_id', userId);

      _logger.i('$_logTag ✅ Successfully downgraded user from business role');
      return AuthResult.success(true);
    } catch (e) {
      final error = 'Failed to downgrade from business: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Get user permissions
  static Future<AuthResult<Map<String, bool>>> getUserPermissions(
    String userId,
  ) async {
    try {
      final roleResult = await getUserRole(userId);
      if (!roleResult.isSuccess) {
        return AuthResult.error(roleResult.error!);
      }

      final role = roleResult.data!;
      final permissions = role.permissions.allPermissions;

      return AuthResult.success(permissions);
    } catch (e) {
      final error = 'Failed to get user permissions: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }
}
