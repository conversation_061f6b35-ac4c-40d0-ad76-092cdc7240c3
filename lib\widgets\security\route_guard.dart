import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/models/auth/user_role.dart';

/// A page wrapper that protects routes based on user roles
class ProtectedRoute extends StatelessWidget {
  final Widget child;
  final UserRole? requiredRole;
  final String? requiredPermission;
  final Widget? unauthorizedPage;
  final VoidCallback? onUnauthorized;

  const ProtectedRoute({
    super.key,
    required this.child,
    this.requiredRole,
    this.requiredPermission,
    this.unauthorizedPage,
    this.onUnauthorized,
  }) : assert(
          requiredRole != null || requiredPermission != null,
          'Either requiredRole or requiredPermission must be provided',
        );

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // Check if user is authenticated
        if (state.status != AuthStatus.authenticated) {
          return _buildUnauthorizedPage(context);
        }

        final userRole = state.userRole ?? UserRole.user;

        // Check role-based access
        if (requiredRole != null) {
          if (!_hasRequiredRole(userRole, requiredRole!)) {
            return _buildUnauthorizedPage(context);
          }
        }

        // Check permission-based access
        if (requiredPermission != null) {
          if (!userRole.canAccess(requiredPermission!)) {
            return _buildUnauthorizedPage(context);
          }
        }

        // User has required access, show the protected page
        return child;
      },
    );
  }

  bool _hasRequiredRole(UserRole userRole, UserRole requiredRole) {
    return userRole.hierarchyLevel >= requiredRole.hierarchyLevel;
  }

  Widget _buildUnauthorizedPage(BuildContext context) {
    // Call the unauthorized callback if provided
    if (onUnauthorized != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onUnauthorized!();
      });
    }

    // Return custom unauthorized page or default
    if (unauthorizedPage != null) {
      return unauthorizedPage!;
    }

    return _buildDefaultUnauthorizedPage(context);
  }

  Widget _buildDefaultUnauthorizedPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Access Denied'),
        backgroundColor: Colors.red.shade600,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.red.shade600,
              Colors.red.shade50,
            ],
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.security,
                        size: 64,
                        color: Colors.red.shade600,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Access Denied',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'You don\'t have permission to access this page.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        requiredRole != null
                            ? 'Required role: ${requiredRole!.displayName}'
                            : 'Required permission: $requiredPermission',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade500,
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.arrow_back),
                        label: const Text('Go Back'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Helper class for creating protected routes
class RouteGuard {
  /// Creates a route that requires admin access
  static Route<T> adminRoute<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
    Widget? unauthorizedPage,
    VoidCallback? onUnauthorized,
  }) {
    return MaterialPageRoute<T>(
      settings: settings,
      builder: (context) => ProtectedRoute(
        requiredRole: UserRole.admin,
        unauthorizedPage: unauthorizedPage,
        onUnauthorized: onUnauthorized,
        child: page,
      ),
    );
  }

  /// Creates a route that requires moderator access
  static Route<T> moderatorRoute<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
    Widget? unauthorizedPage,
    VoidCallback? onUnauthorized,
  }) {
    return MaterialPageRoute<T>(
      settings: settings,
      builder: (context) => ProtectedRoute(
        requiredRole: UserRole.moderator,
        unauthorizedPage: unauthorizedPage,
        onUnauthorized: onUnauthorized,
        child: page,
      ),
    );
  }

  /// Creates a route that requires super admin access
  static Route<T> superAdminRoute<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
    Widget? unauthorizedPage,
    VoidCallback? onUnauthorized,
  }) {
    return MaterialPageRoute<T>(
      settings: settings,
      builder: (context) => ProtectedRoute(
        requiredRole: UserRole.superAdmin,
        unauthorizedPage: unauthorizedPage,
        onUnauthorized: onUnauthorized,
        child: page,
      ),
    );
  }

  /// Creates a route that requires specific permission
  static Route<T> permissionRoute<T extends Object?>(
    Widget page,
    String permission, {
    RouteSettings? settings,
    Widget? unauthorizedPage,
    VoidCallback? onUnauthorized,
  }) {
    return MaterialPageRoute<T>(
      settings: settings,
      builder: (context) => ProtectedRoute(
        requiredPermission: permission,
        unauthorizedPage: unauthorizedPage,
        onUnauthorized: onUnauthorized,
        child: page,
      ),
    );
  }

  /// Checks if navigation to a route should be allowed
  static bool canNavigate(BuildContext context, UserRole requiredRole) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.hierarchyLevel >= requiredRole.hierarchyLevel;
  }

  /// Checks if navigation with specific permission should be allowed
  static bool canNavigateWithPermission(BuildContext context, String permission) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.canAccess(permission);
  }
}
