import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'voice_call_interface.dart';
import 'video_call_interface.dart';

class CallDemoHelper {
  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  /// Simulates an incoming voice call
  static void simulateIncomingVoiceCall(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => VoiceCallInterface(
              contactName: 'Aubrey Tanaka',
              contactImage: 'https://i.pravatar.cc/150?img=8',
              isIncoming: true,
              onAccept: () {
                // Handle call acceptance
                _logger.i('📞 Voice call accepted');
              },
              onDecline: () {
                // Handle call decline
                _logger.i('📞 Voice call declined');
              },
            ),
      ),
    );
  }

  /// Simulates an incoming video call
  static void simulateIncomingVideoCall(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => VideoCallInterface(
              contactName: '<PERSON> Tanaka',
              contactImage: 'https://i.pravatar.cc/150?img=8',
              isIncoming: true,
              onAccept: () {
                // Handle call acceptance
                _logger.i('📹 Video call accepted');
              },
              onDecline: () {
                // Handle call decline
                _logger.i('📹 Video call declined');
              },
            ),
      ),
    );
  }

  /// Starts an outgoing voice call
  static void startVoiceCall(
    BuildContext context, {
    required String contactName,
    required String contactImage,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => VoiceCallInterface(
              contactName: contactName,
              contactImage: contactImage,
              isIncoming: false,
              onEndCall: () {
                // Handle call end
                _logger.i('📞 Voice call ended');
              },
            ),
      ),
    );
  }

  /// Starts an outgoing video call
  static void startVideoCall(
    BuildContext context, {
    required String contactName,
    required String contactImage,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => VideoCallInterface(
              contactName: contactName,
              contactImage: contactImage,
              isIncoming: false,
              onEndCall: () {
                // Handle call end
                _logger.i('📹 Video call ended');
              },
            ),
      ),
    );
  }

  /// Demo widget to test call interfaces
  static Widget buildCallTestWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Call Interface Demo',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // Outgoing Calls
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed:
                      () => startVoiceCall(
                        context,
                        contactName: 'Aubrey Tanaka',
                        contactImage: 'https://i.pravatar.cc/150?img=8',
                      ),
                  icon: const Icon(Icons.call),
                  label: const Text('Voice Call'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed:
                      () => startVideoCall(
                        context,
                        contactName: 'Aubrey Tanaka',
                        contactImage: 'https://i.pravatar.cc/150?img=8',
                      ),
                  icon: const Icon(Icons.videocam),
                  label: const Text('Video Call'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),

          // Incoming Calls
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => simulateIncomingVoiceCall(context),
                  icon: const Icon(Icons.call_received),
                  label: const Text('Incoming Voice'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => simulateIncomingVideoCall(context),
                  icon: const Icon(Icons.video_call),
                  label: const Text('Incoming Video'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Extension to add call functionality to any widget
extension CallExtension on BuildContext {
  void makeVoiceCall(String contactName, String contactImage) {
    CallDemoHelper.startVoiceCall(
      this,
      contactName: contactName,
      contactImage: contactImage,
    );
  }

  void makeVideoCall(String contactName, String contactImage) {
    CallDemoHelper.startVideoCall(
      this,
      contactName: contactName,
      contactImage: contactImage,
    );
  }

  void simulateIncomingVoice() {
    CallDemoHelper.simulateIncomingVoiceCall(this);
  }

  void simulateIncomingVideo() {
    CallDemoHelper.simulateIncomingVideoCall(this);
  }
}
