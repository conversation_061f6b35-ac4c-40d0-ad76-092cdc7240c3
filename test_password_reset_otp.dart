import 'dart:developer' as developer;
import 'package:business_app/services/supabase_service.dart';

/// Test script to verify password reset OTP functionality
/// This script tests the complete password reset flow including:
/// 1. Sending password reset OTP
/// 2. Verifying OTP (with proper error handling)
/// 3. Resending OTP
/// 4. Handling expired OTP errors
void main() async {
  developer.log('🔄 Starting Password Reset OTP Tests...', name: 'TestPasswordReset');

  // Test email - replace with a real email for testing
  const testEmail = '<EMAIL>';

  try {
    // Test 1: Send password reset OTP
    developer.log('📤 Test 1: Sending password reset OTP...', name: 'TestPasswordReset');
    final sendResult = await AuthService.sendPasswordResetOTP(
      identifier: testEmail,
    );

    if (sendResult.isSuccess) {
      developer.log('✅ Password reset OTP sent successfully', name: 'TestPasswordReset');
      developer.log('📧 Resolved email: ${sendResult.data}', name: 'TestPasswordReset');

      // Test 2: Test invalid OTP format
      developer.log('🔍 Test 2: Testing invalid OTP format...', name: 'TestPasswordReset');
      final invalidFormatResult = await AuthService.verifyPasswordResetOTP(
        email: sendResult.data!,
        token: '12345', // Invalid: only 5 digits
      );

      if (!invalidFormatResult.isSuccess) {
        developer.log('✅ Invalid OTP format correctly rejected: ${invalidFormatResult.error}', name: 'TestPasswordReset');
      } else {
        developer.log('❌ Invalid OTP format should have been rejected', name: 'TestPasswordReset');
      }

      // Test 3: Test invalid OTP code
      developer.log('🔍 Test 3: Testing invalid OTP code...', name: 'TestPasswordReset');
      final invalidCodeResult = await AuthService.verifyPasswordResetOTP(
        email: sendResult.data!,
        token: '000000', // Invalid code
      );

      if (!invalidCodeResult.isSuccess) {
        developer.log('✅ Invalid OTP code correctly rejected: ${invalidCodeResult.error}', name: 'TestPasswordReset');
        
        // Check if error message is user-friendly
        if (invalidCodeResult.error!.contains('Invalid OTP') || 
            invalidCodeResult.error!.contains('OTP expired')) {
          developer.log('✅ Error message is user-friendly', name: 'TestPasswordReset');
        } else {
          developer.log('⚠️ Error message could be more user-friendly: ${invalidCodeResult.error}', name: 'TestPasswordReset');
        }
      } else {
        developer.log('❌ Invalid OTP code should have been rejected', name: 'TestPasswordReset');
      }

      // Test 4: Test resend password reset OTP
      developer.log('📤 Test 4: Testing resend password reset OTP...', name: 'TestPasswordReset');
      final resendResult = await AuthService.resendPasswordResetOTP(
        email: sendResult.data!,
      );

      if (resendResult.isSuccess) {
        developer.log('✅ Password reset OTP resent successfully', name: 'TestPasswordReset');
      } else {
        developer.log('❌ Failed to resend password reset OTP: ${resendResult.error}', name: 'TestPasswordReset');
      }

    } else {
      developer.log('❌ Failed to send password reset OTP: ${sendResult.error}', name: 'TestPasswordReset');
    }

    // Test 5: Test with non-existent email
    developer.log('🔍 Test 5: Testing with non-existent email...', name: 'TestPasswordReset');
    final nonExistentResult = await AuthService.sendPasswordResetOTP(
      identifier: '<EMAIL>',
    );

    if (!nonExistentResult.isSuccess) {
      developer.log('✅ Non-existent email correctly handled: ${nonExistentResult.error}', name: 'TestPasswordReset');
    } else {
      developer.log('⚠️ Non-existent email was accepted (this might be expected behavior)', name: 'TestPasswordReset');
    }

  } catch (e) {
    developer.log('❌ Test failed with exception: $e', name: 'TestPasswordReset');
  }

  developer.log('🏁 Password Reset OTP Tests completed!', name: 'TestPasswordReset');
}
