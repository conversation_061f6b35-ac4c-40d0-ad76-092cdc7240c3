import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/notification_bloc/notification_bloc.dart';
import 'package:business_app/bloc/notification_bloc/notification_event.dart';
import 'package:business_app/bloc/notification_bloc/notification_state.dart';
import 'notification_settings_widgets.dart';
import 'notification_analytics_page.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() =>
      _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text(
          'Notification Settings',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationAnalyticsPage(),
                ),
              );
            },
            tooltip: 'Analytics',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(),
            tooltip: 'Help',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          tabs: const [Tab(text: 'Push'), Tab(text: 'Email'), Tab(text: 'SMS')],
        ),
      ),
      body: Column(
        children: [
          _buildQuickActions(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPushNotificationsTab(),
                _buildEmailNotificationsTab(),
                _buildSMSNotificationsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: BlocBuilder<NotificationBloc, NotificationState>(
              builder: (context, state) {
                final allEnabled = state.notificationSettings.values.every(
                  (enabled) => enabled,
                );
                return NotificationQuickActionCard(
                  icon:
                      allEnabled
                          ? Icons.notifications_off
                          : Icons.notifications_active,
                  title: allEnabled ? 'Turn Off All' : 'Turn On All',
                  subtitle:
                      allEnabled
                          ? 'Disable all notifications'
                          : 'Enable all notifications',
                  onTap: () => _toggleAllNotifications(!allEnabled),
                  color: allEnabled ? Colors.red : Colors.green,
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: NotificationQuickActionCard(
              icon: Icons.schedule,
              title: 'Quiet Hours',
              subtitle: 'Set do not disturb times',
              onTap: () => _showQuietHoursDialog(),
              color: Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPushNotificationsTab() {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, state) {
        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildSectionHeader('Social Activity'),
            NotificationSettingsTile(
              icon: Icons.favorite,
              title: 'Likes',
              subtitle: 'When someone likes your posts or comments',
              value: state.notificationSettings['likes'] ?? true,
              onChanged: (value) => _updateSetting('likes', value),
              color: Colors.red,
            ),
            NotificationSettingsTile(
              icon: Icons.comment,
              title: 'Comments',
              subtitle: 'When someone comments on your posts',
              value: state.notificationSettings['comments'] ?? true,
              onChanged: (value) => _updateSetting('comments', value),
              color: Colors.blue,
            ),
            NotificationSettingsTile(
              icon: Icons.person_add,
              title: 'New Followers',
              subtitle: 'When someone starts following you',
              value: state.notificationSettings['follows'] ?? true,
              onChanged: (value) => _updateSetting('follows', value),
              color: Colors.green,
            ),
            NotificationSettingsTile(
              icon: Icons.alternate_email,
              title: 'Mentions',
              subtitle: 'When someone mentions you in posts or comments',
              value: state.notificationSettings['mentions'] ?? true,
              onChanged: (value) => _updateSetting('mentions', value),
              color: Colors.orange,
            ),

            const SizedBox(height: 24),
            _buildSectionHeader('Messages & Communication'),
            NotificationSettingsTile(
              icon: Icons.message,
              title: 'Direct Messages',
              subtitle: 'When you receive new messages',
              value: state.notificationSettings['messages'] ?? true,
              onChanged: (value) => _updateSetting('messages', value),
              color: Colors.purple,
            ),
            NotificationSettingsTile(
              icon: Icons.article,
              title: 'New Posts',
              subtitle: 'When people you follow share new posts',
              value: state.notificationSettings['posts'] ?? true,
              onChanged: (value) => _updateSetting('posts', value),
              color: Colors.teal,
            ),

            const SizedBox(height: 24),
            _buildSectionHeader('Business & Updates'),
            NotificationSettingsTile(
              icon: Icons.business,
              title: 'Business Updates',
              subtitle: 'Important business-related notifications',
              value: state.notificationSettings['business'] ?? true,
              onChanged: (value) => _updateSetting('business', value),
              color: Colors.indigo,
            ),
            NotificationSettingsTile(
              icon: Icons.local_offer,
              title: 'Promotions & Offers',
              subtitle: 'Special deals and promotional content',
              value: state.notificationSettings['promotions'] ?? false,
              onChanged: (value) => _updateSetting('promotions', value),
              color: Colors.amber,
            ),
            NotificationSettingsTile(
              icon: Icons.schedule,
              title: 'Reminders',
              subtitle: 'Important reminders and deadlines',
              value: state.notificationSettings['reminders'] ?? true,
              onChanged: (value) => _updateSetting('reminders', value),
              color: Colors.cyan,
            ),

            const SizedBox(height: 24),
            _buildAdvancedSettings(),
          ],
        );
      },
    );
  }

  Widget _buildEmailNotificationsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Email Frequency'),
        NotificationFrequencyCard(
          title: 'Daily Digest',
          subtitle: 'Get a summary of your daily activity',
          frequency: 'Daily at 8:00 AM',
          isEnabled: true,
          onToggle:
              (value) => _showSnackBar(
                'Email digest ${value ? 'enabled' : 'disabled'}',
              ),
        ),
        NotificationFrequencyCard(
          title: 'Weekly Summary',
          subtitle: 'Weekly overview of your account activity',
          frequency: 'Sundays at 9:00 AM',
          isEnabled: true,
          onToggle:
              (value) => _showSnackBar(
                'Weekly summary ${value ? 'enabled' : 'disabled'}',
              ),
        ),
        NotificationFrequencyCard(
          title: 'Monthly Report',
          subtitle: 'Comprehensive monthly analytics',
          frequency: '1st of each month',
          isEnabled: false,
          onToggle:
              (value) => _showSnackBar(
                'Monthly report ${value ? 'enabled' : 'disabled'}',
              ),
        ),

        const SizedBox(height: 24),
        _buildSectionHeader('Email Types'),
        NotificationSettingsTile(
          icon: Icons.security,
          title: 'Security Alerts',
          subtitle: 'Login attempts and security notifications',
          value: true,
          onChanged:
              (value) => _showSnackBar('Security alerts cannot be disabled'),
          color: Colors.red,
          isLocked: true,
        ),
        NotificationSettingsTile(
          icon: Icons.campaign,
          title: 'Product Updates',
          subtitle: 'New features and app updates',
          value: true,
          onChanged:
              (value) => _showSnackBar(
                'Product updates ${value ? 'enabled' : 'disabled'}',
              ),
          color: Colors.blue,
        ),
        NotificationSettingsTile(
          icon: Icons.tips_and_updates,
          title: 'Tips & Tutorials',
          subtitle: 'Helpful tips to get the most out of the app',
          value: false,
          onChanged:
              (value) => _showSnackBar(
                'Tips & tutorials ${value ? 'enabled' : 'disabled'}',
              ),
          color: Colors.green,
        ),
      ],
    );
  }

  Widget _buildSMSNotificationsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'SMS Notifications',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'SMS notifications are only sent for critical security alerts and account verification.',
                      style: TextStyle(
                        color: Colors.blue.withValues(alpha: 0.8),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),
        _buildSectionHeader('Security & Verification'),
        NotificationSettingsTile(
          icon: Icons.verified_user,
          title: 'Two-Factor Authentication',
          subtitle: 'SMS codes for account verification',
          value: true,
          onChanged: (value) => _showSnackBar('2FA SMS cannot be disabled'),
          color: Colors.green,
          isLocked: true,
        ),
        NotificationSettingsTile(
          icon: Icons.warning,
          title: 'Security Alerts',
          subtitle: 'Suspicious activity and login alerts',
          value: true,
          onChanged:
              (value) => _showSnackBar('Security SMS cannot be disabled'),
          color: Colors.red,
          isLocked: true,
        ),
        NotificationSettingsTile(
          icon: Icons.password,
          title: 'Password Reset',
          subtitle: 'Password reset verification codes',
          value: true,
          onChanged:
              (value) => _showSnackBar('Password reset SMS cannot be disabled'),
          color: Colors.orange,
          isLocked: true,
        ),

        const SizedBox(height: 24),
        _buildPhoneNumberSection(),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12, top: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.tune, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'Advanced Settings',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildAdvancedOption(
            'Sound & Vibration',
            'Customize notification sounds',
            Icons.volume_up,
            () => _showSoundSettings(),
          ),
          _buildAdvancedOption(
            'Notification Preview',
            'Show content in lock screen notifications',
            Icons.preview,
            () => _showPreviewSettings(),
          ),
          _buildAdvancedOption(
            'Grouping',
            'How notifications are grouped',
            Icons.group_work,
            () => _showGroupingSettings(),
          ),
          _buildAdvancedOption(
            'Badge Count',
            'Show unread count on app icon',
            Icons.circle_notifications,
            () => _showBadgeSettings(),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedOption(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneNumberSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.phone, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'Phone Number',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.phone_android, color: Colors.green),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '+****************',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        'Verified',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () => _showChangePhoneDialog(),
                  child: const Text('Change'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _updateSetting(String key, bool value) {
    context.read<NotificationBloc>().add(
      ToggleNotificationSettingsEvent(key, value),
    );
    _showSnackBar(
      '${_getSettingDisplayName(key)} ${value ? 'enabled' : 'disabled'}',
    );
  }

  void _toggleAllNotifications(bool enable) {
    final settings =
        context.read<NotificationBloc>().state.notificationSettings;
    for (String key in settings.keys) {
      context.read<NotificationBloc>().add(
        ToggleNotificationSettingsEvent(key, enable),
      );
    }
    _showSnackBar('All notifications ${enable ? 'enabled' : 'disabled'}');
  }

  String _getSettingDisplayName(String key) {
    switch (key) {
      case 'likes':
        return 'Likes';
      case 'comments':
        return 'Comments';
      case 'follows':
        return 'New Followers';
      case 'mentions':
        return 'Mentions';
      case 'messages':
        return 'Direct Messages';
      case 'posts':
        return 'New Posts';
      case 'business':
        return 'Business Updates';
      case 'promotions':
        return 'Promotions';
      case 'reminders':
        return 'Reminders';
      default:
        return key;
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Notification Help'),
            content: const Text(
              'Customize your notification preferences to stay informed about what matters most to you.\n\n'
              '• Push notifications appear on your device\n'
              '• Email notifications are sent to your email\n'
              '• SMS notifications are for security only\n\n'
              'You can change these settings anytime.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Got it'),
              ),
            ],
          ),
    );
  }

  void _showQuietHoursDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Quiet Hours'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Set times when you don\'t want to receive notifications.',
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.bedtime),
                  title: const Text('10:00 PM - 8:00 AM'),
                  subtitle: const Text('Default quiet hours'),
                  trailing: Switch(
                    value: true,
                    onChanged: (value) => Navigator.pop(context),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showSnackBar('Quiet hours updated');
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _showSoundSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.6,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      const Text(
                        'Sound & Vibration',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    children: [
                      ListTile(
                        leading: const Icon(Icons.music_note),
                        title: const Text('Notification Sound'),
                        subtitle: const Text('Default'),
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () => _showSnackBar('Sound picker coming soon'),
                      ),
                      SwitchListTile(
                        secondary: const Icon(Icons.vibration),
                        title: const Text('Vibration'),
                        subtitle: const Text(
                          'Vibrate when notifications arrive',
                        ),
                        value: true,
                        onChanged:
                            (value) => _showSnackBar(
                              'Vibration ${value ? 'enabled' : 'disabled'}',
                            ),
                      ),
                      SwitchListTile(
                        secondary: const Icon(Icons.flash_on),
                        title: const Text('LED Flash'),
                        subtitle: const Text('Flash LED for notifications'),
                        value: false,
                        onChanged:
                            (value) => _showSnackBar(
                              'LED flash ${value ? 'enabled' : 'disabled'}',
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _showPreviewSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Notification Preview'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('Show All Content'),
                  subtitle: const Text('Show sender and message'),
                  value: 'all',
                  groupValue: 'all',
                  onChanged: (value) => Navigator.pop(context),
                ),
                RadioListTile<String>(
                  title: const Text('Hide Sensitive Content'),
                  subtitle: const Text('Show sender only'),
                  value: 'sender',
                  groupValue: 'all',
                  onChanged: (value) => Navigator.pop(context),
                ),
                RadioListTile<String>(
                  title: const Text('Hide All Content'),
                  subtitle: const Text('Show app name only'),
                  value: 'none',
                  groupValue: 'all',
                  onChanged: (value) => Navigator.pop(context),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showSnackBar('Preview settings updated');
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _showGroupingSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Notification Grouping'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('Group by App'),
                  subtitle: const Text('Group all notifications together'),
                  value: 'app',
                  groupValue: 'app',
                  onChanged: (value) => Navigator.pop(context),
                ),
                RadioListTile<String>(
                  title: const Text('Group by Type'),
                  subtitle: const Text('Group by notification type'),
                  value: 'type',
                  groupValue: 'app',
                  onChanged: (value) => Navigator.pop(context),
                ),
                RadioListTile<String>(
                  title: const Text('No Grouping'),
                  subtitle: const Text('Show each notification separately'),
                  value: 'none',
                  groupValue: 'app',
                  onChanged: (value) => Navigator.pop(context),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showSnackBar('Grouping settings updated');
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _showBadgeSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('App Badge'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SwitchListTile(
                  title: const Text('Show Badge Count'),
                  subtitle: const Text('Display unread count on app icon'),
                  value: true,
                  onChanged:
                      (value) => _showSnackBar(
                        'Badge count ${value ? 'enabled' : 'disabled'}',
                      ),
                ),
                SwitchListTile(
                  title: const Text('Include Read Notifications'),
                  subtitle: const Text('Count read notifications in badge'),
                  value: false,
                  onChanged:
                      (value) => _showSnackBar(
                        'Read notifications ${value ? 'included' : 'excluded'}',
                      ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _showChangePhoneDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Change Phone Number'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Enter your new phone number for SMS notifications.',
                ),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Phone Number',
                    prefixText: '+1 ',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.phone,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showSnackBar('Verification code sent');
                },
                child: const Text('Verify'),
              ),
            ],
          ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(milliseconds: 800),
      ),
    );
  }
}
