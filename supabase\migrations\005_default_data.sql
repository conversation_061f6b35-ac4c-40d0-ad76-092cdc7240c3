-- =====================================================
-- DEFAULT DATA INSERTION
-- Pre-populate essential data for the business app
-- =====================================================

-- =====================================================
-- INSERT DEFAULT CATEGORIES
-- Based on the 12 standardized business categories
-- =====================================================

INSERT INTO public.categories (id, name, description, icon_url, image_url, color_hex, sort_order, is_active, is_featured) VALUES
-- 1. Electronics & Technology
(
    uuid_generate_v4(),
    'Electronics & Technology',
    'Phones, laptops, gadgets, tech accessories, software',
    'assets/icons/electronics_technology.png',
    'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=400&fit=crop',
    '#3B82F6',
    1,
    true,
    true
),

-- 2. Vehicles & Automotive
(
    uuid_generate_v4(),
    'Vehicles & Automotive',
    'Cars, motorcycles, bicycles, parts, automotive services',
    'assets/icons/vehicles_automotive.png',
    'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=400&h=400&fit=crop',
    '#EF4444',
    2,
    true,
    true
),

-- 3. Home & Garden
(
    uuid_generate_v4(),
    'Home & Garden',
    'Furniture, appliances, tools, decor, home improvement',
    'assets/icons/home_garden.png',
    'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
    '#10B981',
    3,
    true,
    true
),

-- 4. Fashion & Clothing
(
    uuid_generate_v4(),
    'Fashion & Clothing',
    'Men''s, women''s, kids'' clothing, shoes, accessories',
    'assets/icons/fashion_clothing.png',
    'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
    '#F59E0B',
    4,
    true,
    true
),

-- 5. Health & Beauty
(
    uuid_generate_v4(),
    'Health & Beauty',
    'Skincare, cosmetics, health products, wellness services',
    'assets/icons/health_beauty.png',
    'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop',
    '#EC4899',
    5,
    true,
    false
),

-- 6. Food & Beverages
(
    uuid_generate_v4(),
    'Food & Beverages',
    'Restaurants, catering, food delivery, beverages, groceries',
    'assets/icons/food_beverages.png',
    'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=400&fit=crop',
    '#F97316',
    6,
    true,
    false
),

-- 7. Sports & Fitness
(
    uuid_generate_v4(),
    'Sports & Fitness',
    'Exercise equipment, sportswear, outdoor gear, fitness services',
    'assets/icons/sports_fitness.png',
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
    '#8B5CF6',
    7,
    true,
    false
),

-- 8. Business & Professional
(
    uuid_generate_v4(),
    'Business & Professional',
    'Office supplies, equipment, consulting, legal services',
    'assets/icons/business_professional.png',
    'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=400&fit=crop',
    '#6366F1',
    8,
    true,
    false
),

-- 9. Creative & Design
(
    uuid_generate_v4(),
    'Creative & Design',
    'Art supplies, graphic design, photography, marketing services',
    'assets/icons/creative_design.png',
    'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=400&fit=crop',
    '#06B6D4',
    9,
    true,
    false
),

-- 10. Maintenance & Repair
(
    uuid_generate_v4(),
    'Maintenance & Repair',
    'Technical services, home repairs, equipment maintenance, installation',
    'assets/icons/maintenance_repair.png',
    'https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=400&h=400&fit=crop',
    '#84CC16',
    10,
    true,
    false
),

-- 11. Education & Training
(
    uuid_generate_v4(),
    'Education & Training',
    'Courses, tutoring, books, educational materials, training services',
    'assets/icons/education_training.png',
    'https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?w=400&h=400&fit=crop',
    '#A855F7',
    11,
    true,
    false
),

-- 12. Other
(
    uuid_generate_v4(),
    'Other',
    'Miscellaneous products and services not covered by other categories',
    'assets/icons/other.png',
    'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
    '#6B7280',
    12,
    true,
    false
);

-- =====================================================
-- CREATE ADMIN USER FUNCTION
-- Function to promote a user to admin role
-- =====================================================

CREATE OR REPLACE FUNCTION public.promote_to_admin(user_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    rows_affected INTEGER;
BEGIN
    UPDATE public.profiles
    SET role = 'admin', updated_at = NOW()
    WHERE email = user_email;

    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    RETURN rows_affected > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- SAMPLE DATA FOR DEVELOPMENT (Optional)
-- Uncomment for development/testing purposes
-- =====================================================

/*
-- Sample shop data (for testing)
INSERT INTO public.shops (id, owner_id, shop_name, shop_description, location, is_verified, is_active) VALUES
(
    uuid_generate_v4(),
    (SELECT id FROM public.profiles LIMIT 1), -- Replace with actual user ID
    'TechHub Electronics',
    'Your one-stop shop for all electronic needs. Quality products at competitive prices.',
    'New York, NY',
    true,
    true
),
(
    uuid_generate_v4(),
    (SELECT id FROM public.profiles LIMIT 1), -- Replace with actual user ID
    'Fashion Forward',
    'Trendy clothing and accessories for the modern lifestyle.',
    'Los Angeles, CA',
    false,
    true
);

-- Sample product data (for testing)
INSERT INTO public.products (
    shop_id, 
    category_id, 
    name, 
    description, 
    price, 
    stock_quantity, 
    status,
    images,
    tags
) VALUES
(
    (SELECT id FROM public.shops WHERE shop_name = 'TechHub Electronics' LIMIT 1),
    (SELECT id FROM public.categories WHERE name = 'Electronics & Technology' LIMIT 1),
    'iPhone 15 Pro Max',
    'Latest iPhone with advanced camera system and A17 Pro chip.',
    1199.99,
    50,
    'active',
    ARRAY['https://example.com/iphone1.jpg', 'https://example.com/iphone2.jpg'],
    ARRAY['iphone', 'apple', 'smartphone', 'mobile']
),
(
    (SELECT id FROM public.shops WHERE shop_name = 'Fashion Forward' LIMIT 1),
    (SELECT id FROM public.categories WHERE name = 'Fashion & Clothing' LIMIT 1),
    'Designer Leather Jacket',
    'Premium quality leather jacket perfect for any season.',
    299.99,
    25,
    'active',
    ARRAY['https://example.com/jacket1.jpg', 'https://example.com/jacket2.jpg'],
    ARRAY['leather', 'jacket', 'fashion', 'clothing']
);
*/

-- =====================================================
-- UTILITY FUNCTIONS FOR DATA MANAGEMENT
-- =====================================================

-- Function to get category statistics
CREATE OR REPLACE FUNCTION public.get_category_stats()
RETURNS TABLE (
    category_id UUID,
    category_name VARCHAR(100),
    product_count BIGINT,
    active_product_count BIGINT,
    total_shops BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as category_id,
        c.name as category_name,
        COUNT(p.id) as product_count,
        COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_product_count,
        COUNT(DISTINCT p.shop_id) as total_shops
    FROM public.categories c
    LEFT JOIN public.products p ON c.id = p.category_id
    WHERE c.is_active = true
    GROUP BY c.id, c.name
    ORDER BY c.sort_order;
END;
$$ LANGUAGE plpgsql;

-- Function to get platform statistics
CREATE OR REPLACE FUNCTION public.get_platform_stats()
RETURNS TABLE (
    total_users BIGINT,
    total_shops BIGINT,
    active_shops BIGINT,
    verified_shops BIGINT,
    total_products BIGINT,
    active_products BIGINT,
    total_posts BIGINT,
    total_orders BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM public.profiles) as total_users,
        (SELECT COUNT(*) FROM public.shops) as total_shops,
        (SELECT COUNT(*) FROM public.shops WHERE is_active = true) as active_shops,
        (SELECT COUNT(*) FROM public.shops WHERE is_verified = true) as verified_shops,
        (SELECT COUNT(*) FROM public.products) as total_products,
        (SELECT COUNT(*) FROM public.products WHERE status = 'active') as active_products,
        (SELECT COUNT(*) FROM public.posts WHERE status = 'published') as total_posts,
        (SELECT COUNT(*) FROM public.orders) as total_orders;
END;
$$ LANGUAGE plpgsql;

-- Function to search users
CREATE OR REPLACE FUNCTION public.search_users(
    search_query TEXT,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    username VARCHAR(50),
    full_name VARCHAR(100),
    avatar_url TEXT,
    bio TEXT,
    is_verified BOOLEAN,
    followers_count INTEGER,
    is_following BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.username,
        p.full_name,
        p.avatar_url,
        p.bio,
        p.is_verified,
        p.followers_count,
        EXISTS(
            SELECT 1 FROM public.follows f 
            WHERE f.follower_id = auth.uid() AND f.following_id = p.id
        ) as is_following
    FROM public.profiles p
    WHERE 
        (p.is_private = false OR p.id = auth.uid()) AND
        (
            to_tsvector('english', p.username || ' ' || p.full_name || ' ' || COALESCE(p.bio, '')) 
            @@ plainto_tsquery('english', search_query)
        )
    ORDER BY 
        ts_rank(
            to_tsvector('english', p.username || ' ' || p.full_name || ' ' || COALESCE(p.bio, '')), 
            plainto_tsquery('english', search_query)
        ) DESC,
        p.followers_count DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user recommendations (users to follow)
CREATE OR REPLACE FUNCTION public.get_user_recommendations(
    user_uuid UUID,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    username VARCHAR(50),
    full_name VARCHAR(100),
    avatar_url TEXT,
    bio TEXT,
    followers_count INTEGER,
    mutual_followers INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.username,
        p.full_name,
        p.avatar_url,
        p.bio,
        p.followers_count,
        (
            SELECT COUNT(*)::INTEGER
            FROM public.follows f1
            JOIN public.follows f2 ON f1.following_id = f2.following_id
            WHERE f1.follower_id = user_uuid AND f2.follower_id = p.id
        ) as mutual_followers
    FROM public.profiles p
    WHERE 
        p.id != user_uuid AND
        p.is_private = false AND
        NOT EXISTS (
            SELECT 1 FROM public.follows f 
            WHERE f.follower_id = user_uuid AND f.following_id = p.id
        )
    ORDER BY 
        mutual_followers DESC,
        p.followers_count DESC,
        p.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
