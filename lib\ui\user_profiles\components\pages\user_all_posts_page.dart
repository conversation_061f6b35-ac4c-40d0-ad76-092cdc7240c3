import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/posts_bloc/posts_bloc.dart';
import 'package:business_app/bloc/posts_bloc/posts_event.dart';
import 'package:business_app/bloc/posts_bloc/posts_state.dart';
import 'package:business_app/ui/user_profiles/components/pages/widgets/user_post_card.dart';
import 'package:business_app/ui/user_profiles/components/pages/widgets/post_actions_bottom_sheet.dart';

class UserAllPostsPage extends StatefulWidget {
  final List<Post> posts;

  const UserAllPostsPage({super.key, required this.posts});

  @override
  State<UserAllPostsPage> createState() => _UserAllPostsPageState();
}

class _UserAllPostsPageState extends State<UserAllPostsPage> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore) {
      _loadMorePosts();
    }
  }

  void _loadMorePosts() {
    if (!_isLoadingMore) {
      setState(() {
        _isLoadingMore = true;
      });
      context.read<PostsBloc>().add(LoadMorePostsEvent());
    }
  }

  Future<void> _onRefresh() async {
    context.read<PostsBloc>().add(RefreshPostsEvent());
  }

  void _showPostActions(Post post) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (context) => PostActionsBottomSheet(
            post: post,
            onEdit: () => _editPost(post),
            onDelete: () => _deletePost(post),
            onShare: () => _sharePost(post),
          ),
    );
  }

  void _editPost(Post post) {
    Navigator.pop(context); // Close bottom sheet
    // TODO: Navigate to edit post page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          'Edit post feature coming soon!',
          style: TextStyle(fontWeight: FontWeight.w500, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1DA1F2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        duration: const Duration(milliseconds: 800),
      ),
    );
  }

  void _deletePost(Post post) {
    Navigator.pop(context); // Close bottom sheet

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text(
              'Delete Post',
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 20),
            ),
            content: const Text(
              'Are you sure you want to delete this post? This action cannot be undone.',
              style: TextStyle(fontSize: 16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  context.read<PostsBloc>().add(DeletePostEvent(post.id));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text(
                        'Post deleted successfully',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                      backgroundColor: Colors.red,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5),
                      ),
                      duration: const Duration(milliseconds: 800),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Delete',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
    );
  }

  void _sharePost(Post post) {
    Navigator.pop(context); // Close bottom sheet
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          'Share feature coming soon!',
          style: TextStyle(fontWeight: FontWeight.w500, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1DA1F2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        duration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PostsBloc, PostsState>(
      listener: (context, state) {
        if (state.status == PostsStatus.loaded ||
            state.status == PostsStatus.error) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      },
      child: RefreshIndicator(
        onRefresh: _onRefresh,
        color: const Color(0xFF1DA1F2),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        child: CustomScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(),
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.only(top: 8),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  if (index < widget.posts.length) {
                    final post = widget.posts[index];
                    return UserPostCard(
                      post: post,
                      onMoreActions: () => _showPostActions(post),
                    );
                  } else if (_isLoadingMore) {
                    return const Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(
                        child: CircularProgressIndicator(
                          color: Color(0xFF1DA1F2),
                        ),
                      ),
                    );
                  }
                  return null;
                }, childCount: widget.posts.length + (_isLoadingMore ? 1 : 0)),
              ),
            ),
            // Add some bottom padding
            const SliverPadding(padding: EdgeInsets.only(bottom: 100)),
          ],
        ),
      ),
    );
  }
}
