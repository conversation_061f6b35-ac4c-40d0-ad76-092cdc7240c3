import 'package:flutter/material.dart';

class ExpandablePostText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final int maxLines;
  final String expandText;
  final String collapseText;

  const ExpandablePostText({
    super.key,
    required this.text,
    this.style,
    this.maxLines = 3,
    this.expandText = 'Show more',
    this.collapseText = 'Show less',
  });

  @override
  State<ExpandablePostText> createState() => _ExpandablePostTextState();
}

class _ExpandablePostTextState extends State<ExpandablePostText> {
  bool _isExpanded = false;
  bool _isTextOverflowing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkTextOverflow();
    });
  }

  void _checkTextOverflow() {
    final textPainter = TextPainter(
      text: TextSpan(
        text: widget.text,
        style: widget.style,
      ),
      maxLines: widget.maxLines,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: MediaQuery.of(context).size.width - 32);

    if (textPainter.didExceedMaxLines) {
      setState(() {
        _isTextOverflowing = true;
      });
    }
  }

  String _getDisplayText() {
    if (!_isTextOverflowing || _isExpanded) {
      return widget.text;
    }

    // Calculate how much text can fit in the available lines
    final textPainter = TextPainter(
      text: TextSpan(
        text: widget.text,
        style: widget.style,
      ),
      maxLines: widget.maxLines,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: MediaQuery.of(context).size.width - 32);

    if (textPainter.didExceedMaxLines) {
      final endIndex = textPainter
          .getPositionForOffset(Offset(
            textPainter.width,
            textPainter.height,
          ))
          .offset;

      // Leave space for "... Show more"
      final truncatedText = widget.text.substring(0, endIndex - 20);
      return '$truncatedText...';
    }

    return widget.text;
  }

  List<TextSpan> _buildTextSpans() {
    final displayText = _getDisplayText();
    final spans = <TextSpan>[];

    // Handle hashtags and mentions
    final words = displayText.split(' ');
    
    for (int i = 0; i < words.length; i++) {
      final word = words[i];
      
      if (word.startsWith('#')) {
        // Hashtag
        spans.add(TextSpan(
          text: word,
          style: widget.style?.copyWith(
            color: const Color(0xFF1DA1F2),
            fontWeight: FontWeight.w600,
          ),
        ));
      } else if (word.startsWith('@')) {
        // Mention
        spans.add(TextSpan(
          text: word,
          style: widget.style?.copyWith(
            color: const Color(0xFF1DA1F2),
            fontWeight: FontWeight.w600,
          ),
        ));
      } else {
        // Regular text
        spans.add(TextSpan(
          text: word,
          style: widget.style,
        ));
      }
      
      // Add space between words (except for the last word)
      if (i < words.length - 1) {
        spans.add(TextSpan(
          text: ' ',
          style: widget.style,
        ));
      }
    }

    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: _buildTextSpans(),
          ),
          maxLines: _isExpanded ? null : widget.maxLines,
          overflow: _isExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
        ),
        
        if (_isTextOverflowing)
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                _isExpanded ? widget.collapseText : widget.expandText,
                style: TextStyle(
                  color: const Color(0xFF1DA1F2),
                  fontWeight: FontWeight.w600,
                  fontSize: widget.style?.fontSize ?? 16,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

// Alternative simpler version if the above is too complex
class SimpleExpandableText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final int maxLines;

  const SimpleExpandableText({
    super.key,
    required this.text,
    this.style,
    this.maxLines = 3,
  });

  @override
  State<SimpleExpandableText> createState() => _SimpleExpandableTextState();
}

class _SimpleExpandableTextState extends State<SimpleExpandableText> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.text,
          style: widget.style,
          maxLines: _isExpanded ? null : widget.maxLines,
          overflow: _isExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
        ),
        
        if (widget.text.length > 100) // Show expand/collapse for longer texts
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                _isExpanded ? 'Show less' : 'Show more',
                style: TextStyle(
                  color: const Color(0xFF1DA1F2),
                  fontWeight: FontWeight.w600,
                  fontSize: widget.style?.fontSize ?? 16,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
