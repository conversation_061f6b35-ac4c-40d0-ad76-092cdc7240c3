import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:business_app/models/category/product_category.dart';
import 'package:business_app/services/shop_service.dart' as shop_service;
import 'category_selection_event.dart';
import 'category_selection_state.dart';

class CategorySelectionBloc
    extends Bloc<CategorySelectionEvent, CategorySelectionState> {
  static final Logger _logger = Logger();

  CategorySelectionBloc() : super(CategorySelectionState.initial()) {
    on<LoadCategories>(_onLoadCategories);
    on<ToggleCategorySelection>(_onToggleCategorySelection);
    on<SelectCategory>(_onSelectCategory);
    on<DeselectCategory>(_onDeselectCategory);
    on<ClearAllSelections>(_onClearAllSelections);
    on<ValidateSelection>(_onValidateSelection);
    on<ResetCategorySelection>(_onResetCategorySelection);
  }

  Future<void> _onLoadCategories(
    LoadCategories event,
    Emitter<CategorySelectionState> emit,
  ) async {
    emit(state.loading());

    try {
      _logger.i('🔄 Loading categories from Supabase...');

      // Try to load categories from Supabase first
      List<ProductCategory> categories;
      try {
        final supabaseCategories =
            await shop_service.ShopService.getCategories();
        if (supabaseCategories.isNotEmpty) {
          // Convert from Supabase model to UI model with default images
          categories =
              supabaseCategories.map((supabaseCategory) {
                // Get default image if database doesn't have one
                String imageUrl = supabaseCategory.imageUrl ?? '';
                String iconPath = supabaseCategory.iconUrl ?? '';

                if (imageUrl.isEmpty) {
                  imageUrl = _getDefaultImageForCategory(supabaseCategory.name);
                }

                if (iconPath.isEmpty) {
                  iconPath = _getDefaultIconForCategory(supabaseCategory.name);
                }

                return ProductCategory(
                  id: supabaseCategory.id,
                  name: supabaseCategory.name,
                  description: supabaseCategory.description ?? '',
                  imageUrl: imageUrl,
                  iconPath: iconPath,
                );
              }).toList();
          _logger.i(
            '✅ Loaded ${categories.length} categories from Supabase with default images',
          );
        } else {
          // Fallback to default categories if none in database
          categories = CategoryData.getDefaultCategories();
          _logger.i('⚠️ No categories in database, using default categories');
        }
      } catch (e) {
        // Fallback to default categories on error
        _logger.w(
          '⚠️ Failed to load from Supabase, using default categories: $e',
        );
        categories = CategoryData.getDefaultCategories();
      }

      emit(state.loaded(categories: categories));
    } catch (e) {
      _logger.e('❌ Failed to load categories: $e');
      emit(state.error('Failed to load categories. Please try again.'));
    }
  }

  Future<void> _onToggleCategorySelection(
    ToggleCategorySelection event,
    Emitter<CategorySelectionState> emit,
  ) async {
    if (state.selectedCategoryIds.contains(event.categoryId)) {
      // Deselect category
      emit(state.categoryDeselected(event.categoryId));
    } else {
      // Select category
      emit(state.categorySelected(event.categoryId));
    }
  }

  Future<void> _onSelectCategory(
    SelectCategory event,
    Emitter<CategorySelectionState> emit,
  ) async {
    if (!state.selectedCategoryIds.contains(event.categoryId)) {
      emit(state.categorySelected(event.categoryId));
    }
  }

  Future<void> _onDeselectCategory(
    DeselectCategory event,
    Emitter<CategorySelectionState> emit,
  ) async {
    if (state.selectedCategoryIds.contains(event.categoryId)) {
      emit(state.categoryDeselected(event.categoryId));
    }
  }

  Future<void> _onClearAllSelections(
    ClearAllSelections event,
    Emitter<CategorySelectionState> emit,
  ) async {
    emit(state.clearSelections());
  }

  Future<void> _onValidateSelection(
    ValidateSelection event,
    Emitter<CategorySelectionState> emit,
  ) async {
    emit(state.validating());

    // Add small delay for validation animation
    await Future.delayed(const Duration(milliseconds: 300));

    final isValid = state.hasMinimumSelection;
    emit(state.validated(isValid: isValid));
  }

  Future<void> _onResetCategorySelection(
    ResetCategorySelection event,
    Emitter<CategorySelectionState> emit,
  ) async {
    emit(CategorySelectionState.initial());
  }

  /// Get default image for a category name
  String _getDefaultImageForCategory(String categoryName) {
    // Map of category names to default images
    const categoryImages = {
      'Electronics & Technology':
          'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=400&fit=crop',
      'Vehicles & Automotive':
          'https://images.unsplash.com/photo-**********-da3b142c6e3d?w=400&h=400&fit=crop', // Red sports car
      'Home & Garden':
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
      'Fashion & Clothing':
          'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
      'Health & Beauty':
          'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop',
      'Sports & Fitness':
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
      'Books & Media':
          'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop',
      'Food & Beverages':
          'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=400&fit=crop',
      'Toys & Games':
          'https://images.unsplash.com/photo-**********-d644479cb6f7?w=400&h=400&fit=crop',
      'Business & Professional':
          'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=400&fit=crop', // Business meeting
      'Creative & Design':
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=400&fit=crop', // Art supplies/palette
      'Maintenance & Repair':
          'https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=400&h=400&fit=crop', // Tools and repair
      'Education & Training':
          'https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?w=400&h=400&fit=crop', // Books and learning
      'Other':
          'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop', // Shopping bags/general commerce
    };

    return categoryImages[categoryName] ?? categoryImages['Other']!;
  }

  /// Get default icon path for a category name
  String _getDefaultIconForCategory(String categoryName) {
    // Map of category names to icon paths
    const categoryIcons = {
      'Electronics & Technology': 'assets/icons/electronics_technology.png',
      'Vehicles & Automotive': 'assets/icons/vehicles_automotive.png',
      'Home & Garden': 'assets/icons/home_garden.png',
      'Fashion & Clothing': 'assets/icons/fashion_clothing.png',
      'Health & Beauty': 'assets/icons/health_beauty.png',
      'Sports & Fitness': 'assets/icons/sports_fitness.png',
      'Books & Media': 'assets/icons/books_media.png',
      'Food & Beverages': 'assets/icons/food_beverages.png',
      'Toys & Games': 'assets/icons/toys_games.png',
      'Business Services': 'assets/icons/business_services.png',
      'Art & Crafts': 'assets/icons/art_crafts.png',
      'Other': 'assets/icons/other.png',
    };

    return categoryIcons[categoryName] ?? categoryIcons['Other']!;
  }
}
