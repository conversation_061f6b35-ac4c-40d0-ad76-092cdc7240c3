import 'package:flutter/material.dart';

// Shop Model for backend integration
class Shop {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category;
  final int totalProducts;
  final double rating;
  final bool isVerified;
  final String location;
  final DateTime createdAt;

  Shop({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.totalProducts,
    required this.rating,
    required this.isVerified,
    required this.location,
    required this.createdAt,
  });

  // Factory constructor for creating Shop from JSON (for backend integration)
  factory Shop.fromJson(Map<String, dynamic> json) {
    return Shop(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['image_url'] ?? '',
      category: json['category'] ?? '',
      totalProducts: json['total_products'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      isVerified: json['is_verified'] ?? false,
      location: json['location'] ?? '',
      createdAt: DateTime.parse(
        json['created_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  // Convert Shop to JSON (for backend integration)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'category': category,
      'total_products': totalProducts,
      'rating': rating,
      'is_verified': isVerified,
      'location': location,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class UserShopsPage extends StatelessWidget {
  // Mock data - will be replaced with backend data
  final List<Shop> shops;

  const UserShopsPage({super.key, this.shops = const []});

  @override
  Widget build(BuildContext context) {
    // Show empty state when no shops are available
    if (shops.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      itemCount: shops.length + 1,
      separatorBuilder:
          (_, index) =>
              index < shops.length
                  ? const Divider(indent: 70, endIndent: 20)
                  : const SizedBox(),
      itemBuilder: (context, index) {
        if (index < shops.length) {
          return ShopTile(shop: shops[index]);
        } else {
          // Last item: Explore more button
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 30),
            child: Center(
              child: OutlinedButton(
                onPressed: () {
                  // TODO: Implement explore more functionality
                  // This will navigate to a full shops listing page
                },
                style: OutlinedButton.styleFrom(
                  shape: const StadiumBorder(),
                  side: BorderSide(color: Colors.green.shade600),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 40,
                    vertical: 14,
                  ),
                ),
                child: const Text(
                  "Explore more",
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          );
        }
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.store_outlined, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No Shops Available',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Shops will appear here when they become available.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class ShopTile extends StatelessWidget {
  final Shop shop;

  const ShopTile({super.key, required this.shop});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Stack(
          children: [
            CircleAvatar(
              backgroundImage: NetworkImage(shop.imageUrl),
              radius: 30,
              backgroundColor: Colors.grey[300],
              onBackgroundImageError: (_, __) {},
              child:
                  shop.imageUrl.isEmpty
                      ? Icon(Icons.store, size: 30, color: Colors.grey[600])
                      : null,
            ),
            if (shop.isVerified)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                  child: const Icon(Icons.check, color: Colors.white, size: 12),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                shop.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (shop.rating > 0) ...[
              const SizedBox(width: 8),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.star, color: Colors.amber[600], size: 16),
                  const SizedBox(width: 2),
                  Text(
                    shop.rating.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              shop.description,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    shop.category,
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${shop.totalProducts} products',
                  style: TextStyle(color: Colors.grey[500], fontSize: 12),
                ),
                const Spacer(),
                Text(
                  shop.location,
                  style: TextStyle(color: Colors.grey[500], fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          // TODO: Navigate to shop details page
          // Navigator.push(context, MaterialPageRoute(
          //   builder: (context) => ShopDetailsPage(shop: shop),
          // ));
        },
      ),
    );
  }
}
