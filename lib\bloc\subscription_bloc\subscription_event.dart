import 'package:equatable/equatable.dart';

abstract class SubscriptionEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class LoadSubscriptionPlansEvent extends SubscriptionEvent {}

class SelectPlanEvent extends SubscriptionEvent {
  final int planIndex;

  SelectPlanEvent(this.planIndex);

  @override
  List<Object> get props => [planIndex];
}

class SubscribeToPlanEvent extends SubscriptionEvent {
  final int planIndex;
  final String paymentMethod;

  SubscribeToPlanEvent(this.planIndex, this.paymentMethod);

  @override
  List<Object> get props => [planIndex, paymentMethod];
}

class CancelSubscriptionEvent extends SubscriptionEvent {}

class LoadCurrentSubscriptionEvent extends SubscriptionEvent {}

class ChangePaymentMethodEvent extends SubscriptionEvent {
  final String paymentMethod;

  ChangePaymentMethodEvent(this.paymentMethod);

  @override
  List<Object> get props => [paymentMethod];
}

class TogglePlanFeatureEvent extends SubscriptionEvent {
  final int planIndex;
  final String featureId;

  TogglePlanFeatureEvent(this.planIndex, this.featureId);

  @override
  List<Object> get props => [planIndex, featureId];
}

class ApplyPromoCodeEvent extends SubscriptionEvent {
  final String promoCode;

  ApplyPromoCodeEvent(this.promoCode);

  @override
  List<Object> get props => [promoCode];
}

class RemovePromoCodeEvent extends SubscriptionEvent {}

class RestoreSubscriptionEvent extends SubscriptionEvent {}

class UpgradePlanEvent extends SubscriptionEvent {
  final int newPlanIndex;

  UpgradePlanEvent(this.newPlanIndex);

  @override
  List<Object> get props => [newPlanIndex];
}

class DowngradePlanEvent extends SubscriptionEvent {
  final int newPlanIndex;

  DowngradePlanEvent(this.newPlanIndex);

  @override
  List<Object> get props => [newPlanIndex];
}
