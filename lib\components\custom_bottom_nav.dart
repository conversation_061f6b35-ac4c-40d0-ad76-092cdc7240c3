import 'package:flutter/material.dart';

//themes ends here

class BottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  const BottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}

class CustomBottomBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<BottomNavItem> items;
  final double height;
  final double elevation;
  final double iconSize;
  final BorderRadius borderRadius;

  const CustomBottomBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.items = const [
      BottomNavItem(
        icon: Icons.home_outlined,
        activeIcon: Icons.home,
        label: '',
      ),
      BottomNavItem(
        icon: Icons.search_outlined,
        activeIcon: Icons.search,
        label: '',
      ),
      BottomNavItem(
        icon: Icons.add_circle_outline,
        activeIcon: Icons.add,
        label: '',
      ),

      BottomNavItem(
        icon: Icons.mail_outline,
        activeIcon: Icons.mail,
        label: '',
      ),
      BottomNavItem(
        icon: Icons.diamond_outlined,
        activeIcon: Icons.diamond,
        label: '',
      ),
    ],
    this.height = 50.0,
    this.elevation = 6.0,
    this.iconSize = 28.0,
    this.borderRadius = const BorderRadius.vertical(top: Radius.circular(16.0)),
  });

  //had a bug of pixel overflow on the bottom navigation bar
  // @override
  // Widget build(BuildContext context) {
  //   final theme = Theme.of(context);

  //   return Container(
  //     height: height + MediaQuery.of(context).padding.bottom,
  //     decoration: BoxDecoration(
  //       color: Colors.transparent, // Make background transparent
  //       //borderRadius: borderRadius,
  //       boxShadow: [
  //         BoxShadow(
  //           color:
  //               theme.brightness == Brightness.dark
  //                   ? Colors.black.withOpacity(0.9)
  //                   : Colors.white.withOpacity(0.9),
  //           // blurRadius: elevation,
  //           // offset: const Offset(0, -2),
  //         ),
  //       ],
  //     ),
  //     child: ClipRRect(
  //       borderRadius: borderRadius,
  //       child: BottomNavigationBar(
  //         currentIndex: currentIndex,
  //         onTap: onTap,
  //         type: BottomNavigationBarType.fixed,
  //         backgroundColor: Colors.transparent,
  //         elevation: 0,
  //         selectedItemColor:
  //             theme.bottomNavigationBarTheme.selectedItemColor ??
  //             theme.primaryColor,
  //         unselectedItemColor:
  //             theme.bottomNavigationBarTheme.unselectedItemColor ?? Colors.grey,
  //         selectedFontSize: 0,
  //         unselectedFontSize: 0,
  //         showSelectedLabels: false,
  //         showUnselectedLabels: false,
  //         items:
  //             items.asMap().entries.map((entry) {
  //               final index = entry.key;
  //               final item = entry.value;
  //               final isSelected = index == currentIndex;

  //               return BottomNavigationBarItem(
  //                 label: '',
  //                 icon: Column(
  //                   mainAxisAlignment: MainAxisAlignment.end,
  //                   children: [
  //                     Icon(
  //                       isSelected ? item.activeIcon : item.icon,
  //                       size: iconSize,
  //                     ),
  //                   ],
  //                 ),
  //               );
  //             }).toList(),
  //       ),
  //     ),
  //   );
  // }

  //working version of the custom bottom navigation bar
  // This widget creates a custom bottom navigation bar with a transparent background and shadow.
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    //for pure dark & white👇

    return Container(
      height: height + bottomPadding,
      decoration: BoxDecoration(
        //color: Colors.transparent,
        color: Theme.of(context).colorScheme.surface.withOpacity(0.94),
        //for pure dark & white👇
        // color: isDark? Colors.black.withOpacity(0.94): Colors.white.withOpacity(0.94),
        // boxShadow: [
        //   BoxShadow(
        //     color:
        //         theme.brightness == Brightness.dark
        //             ? Colors.black.withOpacity(0.9)
        //             : Colors.white.withOpacity(0.9),
        //   ),
        // ],
      ),
      child: ClipRRect(
        borderRadius: borderRadius,
        child: BottomNavigationBar(
          currentIndex: currentIndex,
          onTap: onTap,
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor:
              theme.bottomNavigationBarTheme.selectedItemColor ??
              theme.primaryColor,
          unselectedItemColor:
              theme.bottomNavigationBarTheme.unselectedItemColor ?? Colors.grey,
          selectedFontSize: 0,
          unselectedFontSize: 0,
          showSelectedLabels: false,
          showUnselectedLabels: false,
          items:
              items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isSelected = index == currentIndex;

                return BottomNavigationBarItem(
                  label: '',
                  icon: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(
                        isSelected ? item.activeIcon : item.icon,
                        size: iconSize,
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }
}

// CustomBottomBar is a custom bottom navigation bar widget that uses the google_nav_bar package.
// It allows for easy navigation between different sections of the app.
// class CustomBottomBar extends StatelessWidget {
//   final int currentIndex;
//   final ValueChanged<int> onTap;
//   final bool hasNewAlerts;

//   const CustomBottomBar({
//     Key? key,
//     required this.currentIndex,
//     required this.onTap,
//     this.hasNewAlerts = true,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     //return SafeArea() if you want to avoid the bottom notch on devices with paddings
//     return Container(
//       width: double.infinity,
//       //margin: const EdgeInsets.all(8),
//       //height: 0.09 * MediaQuery.of(context).size.height,
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
//       decoration: BoxDecoration(
//         color:
//             Theme.of(context).brightness == Brightness.dark
//                 ? Colors.black.withOpacity(0.88)
//                 : Colors.black.withOpacity(0.9),
//         //borderRadius: BorderRadius.circular(5),
//       ),
//       child: GNav(
//         iconSize: 24,
//         //color: Colors.white70,
//         color: Colors.grey,
//         textStyle: const TextStyle(
//           color: Colors.white,
//           fontSize: 1,
//           fontWeight: FontWeight.w500,
//           overflow: TextOverflow.ellipsis,
//         ),
//         activeColor: Colors.white,
//         /*padding: EdgeInsets.symmetric(
//             horizontal: MediaQuery.of(context).size.width * 0.02,
//             vertical: 14,
//           ),*/
//         curve: Curves.easeOutExpo,
//         duration: const Duration(milliseconds: 300),
//         tabBackgroundColor: Colors.transparent,
//         selectedIndex: currentIndex,
//         onTabChange: onTap,
//         tabs: [
//           const GButton(icon: Icons.home_filled, text: ''),
//           const GButton(icon: Icons.search, text: ''),
//           const GButton(icon: Icons.add, text: ''),
//           // const GButton(icon: Icons.message_outlined, text: ''),
//           GButton(
//             icon: Icons.message_outlined,
//             text: '',
//             leading: Builder(
//               builder: (context) {
//                 final isSelected =
//                     currentIndex == 3; // 3 is the index of this tab
//                 return Stack(
//                   clipBehavior: Clip.none,
//                   children: [
//                     Icon(
//                       Icons.message_outlined,
//                       size: 24,
//                       color: isSelected ? Colors.white : Colors.grey,
//                     ),
//                     Positioned(
//                       right: -8,
//                       top: -10,
//                       child: Container(
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 2,
//                           vertical: 1,
//                         ),
//                         decoration: BoxDecoration(
//                           color: Colors.blue,
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                         child: const Text(
//                           '99+',
//                           style: TextStyle(
//                             color: Colors.white,
//                             fontSize: 12,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),
//           GButton(
//             icon: Icons.star,
//             text: '',
//             gap: 4,
//             leading: ClipOval(
//               child: Image.asset(
//                 Assets.trademateLogo,
//                 height: 20,
//                 fit: BoxFit.cover,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
// Note: The above code uses the google_nav_bar package for the bottom navigation bar.
// Make sure to add it to your pubspec.yaml file:
