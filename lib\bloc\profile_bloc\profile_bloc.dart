import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../../services/supabase_service.dart';
import '../../services/storage_service.dart';
import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  static final Logger _logger = Logger();
  static const String _logTag = '[ProfileBloc]';

  Timer? _usernameDebounceTimer;

  ProfileBloc() : super(const ProfileState()) {
    on<LoadProfileEvent>(_onLoadProfile);
    on<UpdateProfileEvent>(_onUpdateProfile);
    on<CheckUsernameAvailabilityEvent>(_onCheckUsernameAvailability);
    on<ClearUsernameValidationEvent>(_onClearUsernameValidation);
    on<ToggleFollowEvent>(_onToggleFollow);
    on<ChangeProfileTabEvent>(_onChangeProfileTab);
    on<ShowProfileOptionsEvent>(_onShowProfileOptions);
    on<HideProfileOptionsEvent>(_onHideProfileOptions);
    on<UpdateFollowersCountEvent>(_onUpdateFollowersCount);
    on<UpdateFollowingCountEvent>(_onUpdateFollowingCount);
  }

  @override
  Future<void> close() {
    _usernameDebounceTimer?.cancel();
    return super.close();
  }

  Future<void> _onLoadProfile(
    LoadProfileEvent event,
    Emitter<ProfileState> emit,
  ) async {
    emit(state.copyWith(status: ProfileStatus.loading));

    try {
      // Get current user profile from database
      final userProfile = await AuthService.getCurrentUserProfile();

      if (userProfile != null) {
        emit(
          state.copyWith(
            status: ProfileStatus.loaded,
            name: userProfile.name,
            username: userProfile.username,
            bio: userProfile.bio ?? '',
            location: '', // Add location to UserModel if needed
            website: '', // Add website to UserModel if needed
            profileImageUrl: userProfile.profileImageUrl ?? '',
            backgroundImageUrl: userProfile.backgroundImageUrl ?? '',
            followersCount: userProfile.followersCount,
            followingCount: userProfile.followingCount,
            postsCount: userProfile.postsCount,
            isOwnProfile: true,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: ProfileStatus.error,
            errorMessage: 'User profile not found',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ProfileStatus.error,
          errorMessage: 'Failed to load profile: ${e.toString()}',
        ),
      );
    }
  }

  void _onUpdateProfile(
    UpdateProfileEvent event,
    Emitter<ProfileState> emit,
  ) async {
    emit(state.copyWith(status: ProfileStatus.updating));

    try {
      _logger.i('$_logTag Updating user profile...');

      // Get current user
      final currentUser = await AuthService.getCurrentUserProfile();
      if (currentUser == null) {
        emit(
          state.copyWith(
            status: ProfileStatus.error,
            errorMessage: 'User not found',
          ),
        );
        return;
      }

      // Handle image uploads
      String? profileImageUrl = event.profileImageUrl ?? state.profileImageUrl;
      String? backgroundImageUrl =
          event.backgroundImageUrl ?? state.backgroundImageUrl;

      // Upload profile image if provided
      if (event.profileImageFile != null) {
        _logger.i('$_logTag Uploading profile image...');
        final uploadResult = await StorageService.uploadProfileImage(
          userId: currentUser.id,
          imageFile: event.profileImageFile!,
        );

        if (uploadResult.isSuccess) {
          profileImageUrl = uploadResult.data!;
          _logger.i('$_logTag ✅ Profile image uploaded: $profileImageUrl');
        } else {
          emit(
            state.copyWith(
              status: ProfileStatus.error,
              errorMessage:
                  'Failed to upload profile image: ${uploadResult.error}',
            ),
          );
          return;
        }
      }

      // Upload background image if provided
      if (event.backgroundImageFile != null) {
        _logger.i('$_logTag Uploading background image...');
        final uploadResult = await StorageService.uploadBackgroundImage(
          userId: currentUser.id,
          imageFile: event.backgroundImageFile!,
        );

        if (uploadResult.isSuccess) {
          backgroundImageUrl = uploadResult.data!;
          _logger.i(
            '$_logTag ✅ Background image uploaded: $backgroundImageUrl',
          );
        } else {
          emit(
            state.copyWith(
              status: ProfileStatus.error,
              errorMessage:
                  'Failed to upload background image: ${uploadResult.error}',
            ),
          );
          return;
        }
      }

      // Create updated user model
      final updatedUser = currentUser.copyWith(
        name: event.name ?? state.name,
        username: event.username ?? state.username,
        bio: event.bio ?? state.bio,
        profileImageUrl: profileImageUrl,
        backgroundImageUrl: backgroundImageUrl,
        updatedAt: DateTime.now(),
      );

      // Update profile in database
      _logger.i('$_logTag Updating profile in database...');
      final updatedProfile = await AuthService.updateProfile(updatedUser);

      emit(
        state.copyWith(
          status: ProfileStatus.loaded,
          name: updatedProfile.name,
          username: updatedProfile.username,
          bio: updatedProfile.bio ?? '',
          profileImageUrl: updatedProfile.profileImageUrl ?? '',
          backgroundImageUrl: updatedProfile.backgroundImageUrl ?? '',
          errorMessage: null,
        ),
      );

      _logger.i('$_logTag ✅ Profile updated successfully');
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to update profile: $e');
      emit(
        state.copyWith(
          status: ProfileStatus.error,
          errorMessage: 'Failed to update profile: ${e.toString()}',
        ),
      );
    }
  }

  void _onCheckUsernameAvailability(
    CheckUsernameAvailabilityEvent event,
    Emitter<ProfileState> emit,
  ) async {
    // Cancel previous timer
    _usernameDebounceTimer?.cancel();

    // Set checking status immediately
    emit(
      state.copyWith(
        usernameValidationStatus: UsernameValidationStatus.checking,
        usernameValidationError: null,
      ),
    );

    // Debounce the actual check
    _usernameDebounceTimer = Timer(const Duration(milliseconds: 500), () async {
      // Check if emit is still valid before proceeding
      if (emit.isDone) return;

      try {
        _logger.i('$_logTag Checking username availability: ${event.username}');

        // Skip validation if username is the same as current
        if (event.username == state.username) {
          if (!emit.isDone) {
            emit(
              state.copyWith(
                usernameValidationStatus: UsernameValidationStatus.available,
                usernameValidationError: null,
              ),
            );
          }
          return;
        }

        // Validate username format
        if (event.username.length < 3) {
          if (!emit.isDone) {
            emit(
              state.copyWith(
                usernameValidationStatus: UsernameValidationStatus.unavailable,
                usernameValidationError:
                    'Username must be at least 3 characters',
              ),
            );
          }
          return;
        }

        if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(event.username)) {
          if (!emit.isDone) {
            emit(
              state.copyWith(
                usernameValidationStatus: UsernameValidationStatus.unavailable,
                usernameValidationError:
                    'Username can only contain letters, numbers, and underscores',
              ),
            );
          }
          return;
        }

        // Check availability in database
        final isAvailable = await AuthService.isUsernameAvailable(
          event.username,
        );

        // Check again if emit is still valid after async operation
        if (emit.isDone) return;

        if (isAvailable) {
          emit(
            state.copyWith(
              usernameValidationStatus: UsernameValidationStatus.available,
              usernameValidationError: null,
            ),
          );
          _logger.i('$_logTag ✅ Username available: ${event.username}');
        } else {
          emit(
            state.copyWith(
              usernameValidationStatus: UsernameValidationStatus.unavailable,
              usernameValidationError: 'Username not available',
            ),
          );
          _logger.i('$_logTag ❌ Username not available: ${event.username}');
        }
      } catch (e) {
        _logger.e('$_logTag ❌ Username validation error: $e');
        if (!emit.isDone) {
          emit(
            state.copyWith(
              usernameValidationStatus: UsernameValidationStatus.error,
              usernameValidationError: 'Failed to check username availability',
            ),
          );
        }
      }
    });
  }

  void _onClearUsernameValidation(
    ClearUsernameValidationEvent event,
    Emitter<ProfileState> emit,
  ) {
    _usernameDebounceTimer?.cancel();
    emit(
      state.copyWith(
        usernameValidationStatus: UsernameValidationStatus.initial,
        usernameValidationError: null,
      ),
    );
  }

  void _onToggleFollow(ToggleFollowEvent event, Emitter<ProfileState> emit) {
    final newFollowingStatus = !state.isFollowing;
    final newFollowersCount =
        newFollowingStatus
            ? state.followersCount + 1
            : state.followersCount - 1;

    emit(
      state.copyWith(
        isFollowing: newFollowingStatus,
        followersCount: newFollowersCount,
      ),
    );
  }

  void _onChangeProfileTab(
    ChangeProfileTabEvent event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedTabIndex: event.tabIndex));
  }

  void _onShowProfileOptions(
    ShowProfileOptionsEvent event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(showOptions: true));
  }

  void _onHideProfileOptions(
    HideProfileOptionsEvent event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(showOptions: false));
  }

  void _onUpdateFollowersCount(
    UpdateFollowersCountEvent event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(followersCount: event.count));
  }

  void _onUpdateFollowingCount(
    UpdateFollowingCountEvent event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(followingCount: event.count));
  }
}
