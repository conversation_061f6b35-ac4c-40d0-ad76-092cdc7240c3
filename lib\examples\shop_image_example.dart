import 'package:flutter/material.dart';
import '../avatar/widgets/shop_logo_widget.dart';
import '../avatar/widgets/shop_banner_widget.dart';
import '../services/shop_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Example page showing how to use shop logo and banner widgets
class ShopImageExample extends StatefulWidget {
  final String shopId;

  const ShopImageExample({super.key, required this.shopId});

  @override
  State<ShopImageExample> createState() => _ShopImageExampleState();
}

class _ShopImageExampleState extends State<ShopImageExample> {
  Shop? _shop;
  String? _currentUserId;
  bool _isLoading = true;
  String? _currentLogoUrl;
  String? _currentBannerUrl;

  @override
  void initState() {
    super.initState();
    _loadShopData();
  }

  Future<void> _loadShopData() async {
    try {
      // Get current user
      _currentUserId = Supabase.instance.client.auth.currentUser?.id;

      // Get shop data
      final shop = await ShopService.getShop(widget.shopId);

      setState(() {
        _shop = shop;
        _currentLogoUrl = shop?.shopLogoUrl;
        _currentBannerUrl = shop?.shopBannerUrl;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('Failed to load shop data: $e');
    }
  }

  void _handleLogoChanged(String? logoUrl) {
    setState(() {
      _currentLogoUrl = logoUrl;
    });

    // Update shop in database
    if (logoUrl != null) {
      _updateShopLogo(logoUrl);
    } else {
      // Handle delete - clear logo URL in database
      _deleteShopLogo();
    }
  }

  void _handleBannerChanged(String? bannerUrl) {
    setState(() {
      _currentBannerUrl = bannerUrl;
    });

    // Update shop in database
    if (bannerUrl != null) {
      _updateShopBanner(bannerUrl);
    } else {
      // Handle delete - clear banner URL in database
      _deleteShopBanner();
    }
  }

  Future<void> _updateShopLogo(String logoUrl) async {
    try {
      await ShopService.updateShopLogo(shopId: widget.shopId, logoUrl: logoUrl);
      print('✅ Shop logo updated in database');
    } catch (e) {
      _showError('Failed to update shop logo: $e');
    }
  }

  Future<void> _updateShopBanner(String bannerUrl) async {
    try {
      await ShopService.updateShopBanner(
        shopId: widget.shopId,
        bannerUrl: bannerUrl,
      );
      print('✅ Shop banner updated in database');
    } catch (e) {
      _showError('Failed to update shop banner: $e');
    }
  }

  Future<void> _deleteShopLogo() async {
    try {
      if (_currentLogoUrl != null && _currentLogoUrl!.isNotEmpty) {
        await ShopService.deleteAndClearShopLogo(
          shopId: widget.shopId,
          logoUrl: _currentLogoUrl!,
        );
        print('✅ Shop logo deleted from storage and database');
      } else {
        // Just clear the URL in database
        await ShopService.updateShopLogo(shopId: widget.shopId, logoUrl: '');
        print('✅ Shop logo URL cleared in database');
      }
    } catch (e) {
      _showError('Failed to delete shop logo: $e');
    }
  }

  Future<void> _deleteShopBanner() async {
    try {
      if (_currentBannerUrl != null && _currentBannerUrl!.isNotEmpty) {
        await ShopService.deleteAndClearShopBanner(
          shopId: widget.shopId,
          bannerUrl: _currentBannerUrl!,
        );
        print('✅ Shop banner deleted from storage and database');
      } else {
        // Just clear the URL in database
        await ShopService.updateShopBanner(
          shopId: widget.shopId,
          bannerUrl: '',
        );
        print('✅ Shop banner URL cleared in database');
      }
    } catch (e) {
      _showError('Failed to delete shop banner: $e');
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (_shop == null) {
      return const Scaffold(body: Center(child: Text('Shop not found')));
    }

    final isOwner = _currentUserId == _shop!.ownerId;

    return Scaffold(
      appBar: AppBar(
        title: Text(_shop!.shopName),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Shop Banner
            const Text(
              'Shop Banner',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ShopBannerWidget(
              userId: _currentUserId,
              shopId: widget.shopId,
              currentBannerUrl: _currentBannerUrl,
              width: double.infinity,
              height: 200,
              isEditable: isOwner,
              onImageChanged: _handleBannerChanged,
              onError: _showError,
            ),

            const SizedBox(height: 32),

            // Shop Logo
            const Text(
              'Shop Logo',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Center(
              child: ShopLogoWidget(
                userId: _currentUserId,
                shopId: widget.shopId,
                currentLogoUrl: _currentLogoUrl,
                size: 150,
                isEditable: isOwner,
                onImageChanged: _handleLogoChanged,
                onError: _showError,
                borderColor: Theme.of(context).primaryColor,
              ),
            ),

            const SizedBox(height: 32),

            // Shop Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _shop!.shopName,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_shop!.shopDescription != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        _shop!.shopDescription!,
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(Icons.star, color: Colors.amber, size: 20),
                        const SizedBox(width: 4),
                        Text('${_shop!.rating.toStringAsFixed(1)}'),
                        const SizedBox(width: 16),
                        Icon(Icons.reviews, color: Colors.grey[600], size: 20),
                        const SizedBox(width: 4),
                        Text('${_shop!.totalReviews} reviews'),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            if (!isOwner) ...[
              const SizedBox(height: 16),
              const Text(
                'Note: Only shop owners can edit images',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
