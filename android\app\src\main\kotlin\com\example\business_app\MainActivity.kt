package com.example.business_app

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity : FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Register the connectivity settings plugin
        flutterEngine.plugins.add(ConnectivitySettingsPlugin())
    }
}
