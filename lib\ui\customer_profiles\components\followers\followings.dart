import 'package:flutter/material.dart';
//import 'package:google_fonts/google_fonts.dart';

class Followings extends StatefulWidget {
  const Followings({super.key});

  @override
  State<Followings> createState() => _FollowingsState();
}

class _FollowingsState extends State<Followings>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _searchController;

  final List<Map<String, String>> Customers = [
    {
      'username': 'sa_yed3966',
      'name': 'Sayed <PERSON>...',
      'image': 'assets/1.png',
    },
    {'username': 'megadeth_453', 'name': 'vick', 'image': 'assets/2.png'},
    {
      'username': 'julian<PERSON><PERSON>tre<PERSON>',
      'name': '<PERSON>',
      'image': 'assets/3.png',
    },
    {
      'username': 'julian<PERSON><PERSON><PERSON><PERSON>',
      'name': '<PERSON>',
      'image': 'assets/3.png',
    },
    {
      'username': 'julian<PERSON><PERSON><PERSON><PERSON>',
      'name': '<PERSON>',
      'image': 'assets/3.png',
    },
    {
      'username': 'juli<PERSON><PERSON>',
      'name': '<PERSON>ytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
  ];

  final List<Map<String, String>> following = [
    {'username': 'ba_nki_mo.on_', 'name': 'peter', 'image': 'assets/4.png'},
    {
      'username': '_myrhhh01',
      'name': 'gadieslngkrara01👑',
      'image': 'assets/5.png',
    },
    {
      'username': 'yash_aphys',
      'name': 'Yaduvendra singh sol...',
      'image': 'assets/6.png',
    },
  ];

  final List<Map<String, String>> suggested = [
    {
      'username': 'humayunrajpoot_',
      'name': 'Humayun Rajpoot',
      'image': 'assets/7.png',
    },
    {'username': 'lorrenzo2025', 'name': 'Law Rence', 'image': 'assets/8.png'},
    {'username': 'whoes_darsh', 'name': 'here we go', 'image': 'assets/9.png'},
  ];

  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _searchController = TextEditingController();
    _searchController.addListener(() {
      setState(() {
        searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  List<Map<String, String>> filterList(List<Map<String, String>> list) {
    return list
        .where(
          (user) =>
              user['username']!.toLowerCase().contains(searchQuery) ||
              user['name']!.toLowerCase().contains(searchQuery),
        )
        .toList();
  }

  Widget buildUserTile(Map<String, String> user) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 4),
      leading: CircleAvatar(
        radius: 25,
        backgroundImage: AssetImage(user['image']!),
      ),
      title: Text(
        user['username']!,
        style: TextStyle(
          //  color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        user['name']!,
        style: TextStyle(color: Colors.white60, fontSize: 12),
      ),
      trailing: ElevatedButton(
        onPressed: () {},
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: const Text("Follow", style: TextStyle(color: Colors.amber)),
      ),
    );
  }

  Widget buildTabContent(List<Map<String, String>> dataList) {
    final filtered = filterList(dataList);
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filtered.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(10),
              ),
              child: TextField(
                controller: _searchController,
                //style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: TextStyle(
                    color: Colors.white54,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  prefixIcon: const Icon(Icons.search, color: Colors.white54),
                  border: InputBorder.none,
                ),
              ),
            ),
          );
        }
        return buildUserTile(filtered[index - 1]);
      },
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.black,
      appBar: AppBar(
        // backgroundColor: Colors.black,
        title: Text(
          'Christina Lungu',
          style: TextStyle(
            // color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        //leading: const BackButton(color: Colors.white),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: TabBar(
            physics: ScrollPhysics(
              parent: BouncingScrollPhysics(),
            ), // Add bouncing effect
            controller: _tabController,
            indicatorColor: Colors.blue,
            labelStyle: TextStyle(fontWeight: FontWeight.w500),
            labelColor: Colors.blue,
            // unselectedLabelColor: Colors.white54,
            tabs: const [
              Tab(text: 'Customers'),
              Tab(text: 'Following'),
              //Tab(text: 'Suggested'),
            ],
          ),
        ),
      ),
      body: SafeArea(
        child: TabBarView(
          controller: _tabController,
          children: [
            buildTabContent(Customers),
            buildTabContent(following),
            // buildTabContent(suggested),
          ],
        ),
      ),
    );
  }
}

//---------------------------------------------------------------------------------------------------------------------
/*
sample 2
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class Followings extends StatefulWidget {
  const Followings({super.key});

  @override
  State<Followings> createState() => _FollowingsState();
}

class _FollowingsState extends State<Followings>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _searchController;

  final List<Map<String, String>> Customers = [
    {
      'username': 'sa_yed3966',
      'name': 'Sayed Amier H Hashi...',
      'image': 'assets/1.png',
    },
    {'username': 'megadeth_453', 'name': 'vick', 'image': 'assets/2.png'},
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
  ];

  final List<Map<String, String>> following = [
    {'username': 'ba_nki_mo.on_', 'name': 'peter', 'image': 'assets/4.png'},
    {
      'username': '_myrhhh01',
      'name': 'gadieslngkrara01👑',
      'image': 'assets/5.png',
    },
    {
      'username': 'yash_aphys',
      'name': 'Yaduvendra singh sol...',
      'image': 'assets/6.png',
    },
  ];

  final List<Map<String, String>> suggested = [
    {
      'username': 'humayunrajpoot_',
      'name': 'Humayun Rajpoot',
      'image': 'assets/7.png',
    },
    {'username': 'lorrenzo2025', 'name': 'Law Rence', 'image': 'assets/8.png'},
    {'username': 'whoes_darsh', 'name': 'here we go', 'image': 'assets/9.png'},
  ];

  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _searchController = TextEditingController();
    _searchController.addListener(() {
      setState(() {
        searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  List<Map<String, String>> filterList(List<Map<String, String>> list) {
    return list
        .where(
          (user) =>
              user['username']!.toLowerCase().contains(searchQuery) ||
              user['name']!.toLowerCase().contains(searchQuery),
        )
        .toList();
  }

  Widget buildUserTile(Map<String, String> user) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 4),
      leading: CircleAvatar(
        radius: 25,
        backgroundImage: AssetImage(user['image']!),
      ),
      title: Text(
        user['username']!,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        user['name']!,
        style: TextStyle(color: Colors.white60, fontSize: 12),
      ),
      trailing: ElevatedButton(
        onPressed: () {},
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: const Text("Follow", style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget buildTabContent(List<Map<String, String>> dataList) {
    final filtered = filterList(dataList);
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filtered.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(10),
              ),
              child: TextField(
                controller: _searchController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: TextStyle(color: Colors.white54),
                  prefixIcon: const Icon(Icons.search, color: Colors.white54),
                  border: InputBorder.none,
                ),
              ),
            ),
          );
        }
        return buildUserTile(filtered[index - 1]);
      },
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'whoes_roshn',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: const BackButton(color: Colors.white),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelStyle: TextStyle(fontWeight: FontWeight.w500),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white54,
            tabs: const [
              Tab(text: 'Customers'),
              Tab(text: 'Following'),
              Tab(text: 'Suggested'),
            ],
          ),
        ),
      ),
      body: SafeArea(
        child: TabBarView(
          controller: _tabController,
          children: [
            buildTabContent(Customers),
            buildTabContent(following),
            buildTabContent(suggested),
          ],
        ),
      ),
    );
  }
}*/

//-----------------------------------------------------------------------------------------------------------------------
/*
sample 1
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class Followings extends StatefulWidget {
  const Followings({super.key});

  @override
  State<Followings> createState() => _FollowingsState();
}

class _FollowingsState extends State<Followings>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final Customers = [
    {
      'username': 'sa_yed3966',
      'name': 'Sayed Amier H Hashi...',
      'image': 'assets/1.png',
    },
    {'username': 'megadeth_453', 'name': 'vick', 'image': 'assets/2.png'},
    {
      'username': 'julianvanuytrecht',
      'name': 'Julian Van Uytrecht',
      'image': 'assets/3.png',
    },
    {'username': 'ba_nki_mo.on_', 'name': 'peter', 'image': 'assets/4.png'},
    {
      'username': '_myrhhh01',
      'name': 'gadieslngkrara01👑',
      'image': 'assets/5.png',
    },
    {
      'username': 'yash_aphys',
      'name': 'Yaduvendra singh sol...',
      'image': 'assets/6.png',
    },
    {
      'username': 'humayunrajpoot_',
      'name': 'Humayun Rajpoot',
      'image': 'assets/7.png',
    },
    {'username': 'lorrenzo2025', 'name': 'Law Rence', 'image': 'assets/8.png'},
    {'username': 'whoes_darsh', 'name': 'here we go', 'image': 'assets/9.png'},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  Widget buildUserTile(Map<String, String> user) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 4),
      leading: CircleAvatar(
        radius: 25,
        backgroundImage: AssetImage(user['image']!),
      ),
      title: Text(
        user['username']!,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        user['name']!,
        style: TextStyle(color: Colors.white60, fontSize: 12),
      ),
      trailing: ElevatedButton(
        onPressed: () {},
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: const Text("Follow", style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget buildTabContent() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: Customers.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(10),
              ),
              child: TextField(
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: TextStyle(color: Colors.white54),
                  prefixIcon: const Icon(Icons.search, color: Colors.white54),
                  border: InputBorder.none,
                ),
              ),
            ),
          );
        }
        return buildUserTile(Customers[index - 1]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'whoes_roshn',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: const BackButton(color: Colors.white),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelStyle: TextStyle(fontWeight: FontWeight.w500),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white54,
            tabs: const [
              Tab(text: 'Customers'),
              Tab(text: 'Following'),
              Tab(text: 'Suggested'),
            ],
          ),
        ),
      ),
      body: SafeArea(
        child: TabBarView(
          controller: _tabController,
          children: [buildTabContent(), buildTabContent(), buildTabContent()],
        ),
      ),
    );
  }
}
*/
