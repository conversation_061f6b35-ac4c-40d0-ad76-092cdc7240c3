import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/widgets/security/role_guard.dart';
import 'package:business_app/widgets/security/route_guard.dart';
import 'package:business_app/utils/security_utils.dart';
import 'package:business_app/models/auth/user_role.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';

/// Example 1: Enhanced Home Page with Role-Based Features
class SecureHomePage extends StatelessWidget {
  const SecureHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trademate'),
        actions: [
          // Show different actions based on user role
          RoleBasedWidget(
            userWidget: IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: () => _showNotifications(context),
            ),
            moderatorWidget: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.flag),
                  onPressed: () => _showReports(context),
                ),
                IconButton(
                  icon: const Icon(Icons.notifications),
                  onPressed: () => _showNotifications(context),
                ),
              ],
            ),
            adminWidget: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.analytics),
                  onPressed: () => _showAnalytics(context),
                ),
                IconButton(
                  icon: const Icon(Icons.flag),
                  onPressed: () => _showReports(context),
                ),
                IconButton(
                  icon: const Icon(Icons.notifications),
                  onPressed: () => _showNotifications(context),
                ),
              ],
            ),
            superAdminWidget: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.security),
                  onPressed: () => _showSystemSettings(context),
                ),
                IconButton(
                  icon: const Icon(Icons.analytics),
                  onPressed: () => _showAnalytics(context),
                ),
                IconButton(
                  icon: const Icon(Icons.flag),
                  onPressed: () => _showReports(context),
                ),
                IconButton(
                  icon: const Icon(Icons.notifications),
                  onPressed: () => _showNotifications(context),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Admin Banner - Only visible to admins
          AdminOnlyWidget(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              color: Colors.blue.shade100,
              child: Row(
                children: [
                  Icon(Icons.admin_panel_settings, color: Colors.blue.shade700),
                  const SizedBox(width: 8),
                  Text(
                    'Admin Mode Active',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => _navigateToAdminPanel(context),
                    child: const Text('Admin Panel'),
                  ),
                ],
              ),
            ),
          ),

          // Main content
          Expanded(
            child: ListView(
              children: [
                // Regular user content
                const ListTile(
                  title: Text('Welcome to Trademate'),
                  subtitle: Text('Your business marketplace'),
                ),

                // Moderator tools section
                ModeratorOnlyWidget(
                  child: Card(
                    margin: const EdgeInsets.all(16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.shield, color: Colors.amber.shade600),
                              const SizedBox(width: 8),
                              const Text(
                                'Moderator Tools',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              ElevatedButton.icon(
                                onPressed: () => _reviewContent(context),
                                icon: const Icon(Icons.rate_review),
                                label: const Text('Review Content'),
                              ),
                              const SizedBox(width: 8),
                              ElevatedButton.icon(
                                onPressed: () => _handleReports(context),
                                icon: const Icon(Icons.report),
                                label: const Text('Handle Reports'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Admin analytics section
                AdminOnlyWidget(
                  child: Card(
                    margin: const EdgeInsets.all(16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.analytics, color: Colors.blue.shade600),
                              const SizedBox(width: 8),
                              const Text(
                                'Admin Analytics',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text('Quick stats and insights for administrators'),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: () => _viewDetailedAnalytics(context),
                            child: const Text('View Detailed Analytics'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildRoleBasedFAB(context),
    );
  }

  Widget _buildRoleBasedFAB(BuildContext context) {
    return RoleBasedWidget(
      userWidget: FloatingActionButton(
        onPressed: () => _createPost(context),
        child: const Icon(Icons.add),
      ),
      moderatorWidget: FloatingActionButton.extended(
        onPressed: () => _moderatorActions(context),
        icon: const Icon(Icons.shield),
        label: const Text('Moderate'),
      ),
      adminWidget: FloatingActionButton.extended(
        onPressed: () => _adminActions(context),
        icon: const Icon(Icons.admin_panel_settings),
        label: const Text('Admin'),
      ),
      superAdminWidget: FloatingActionButton.extended(
        onPressed: () => _superAdminActions(context),
        icon: const Icon(Icons.security),
        label: const Text('System'),
      ),
    );
  }

  // Action methods
  void _showNotifications(BuildContext context) {
    // Regular notification logic
  }

  void _showReports(BuildContext context) {
    SecurityUtils.executeWithPermissionCheck(
      context,
      'canViewReports',
      () {
        // Navigate to reports page
      },
    );
  }

  void _showAnalytics(BuildContext context) {
    SecurityUtils.executeWithPermissionCheck(
      context,
      'canAccessAnalytics',
      () {
        // Navigate to analytics page
      },
    );
  }

  void _showSystemSettings(BuildContext context) {
    SecurityUtils.executeWithRoleCheck(
      context,
      UserRole.superAdmin,
      () {
        // Navigate to system settings
      },
    );
  }

  void _navigateToAdminPanel(BuildContext context) {
    if (context.isSuperAdmin) {
      Navigator.push(context, RouteGuard.superAdminRoute(Container()));
    } else if (context.isAdmin) {
      Navigator.push(context, RouteGuard.adminRoute(Container()));
    } else if (context.isModerator) {
      Navigator.push(context, RouteGuard.moderatorRoute(Container()));
    }
  }

  void _reviewContent(BuildContext context) {
    // Content review logic
  }

  void _handleReports(BuildContext context) {
    // Report handling logic
  }

  void _viewDetailedAnalytics(BuildContext context) {
    // Analytics logic
  }

  void _createPost(BuildContext context) {
    // Post creation logic
  }

  void _moderatorActions(BuildContext context) {
    // Moderator action menu
  }

  void _adminActions(BuildContext context) {
    // Admin action menu
  }

  void _superAdminActions(BuildContext context) {
    // Super admin action menu
  }
}

/// Example 2: Enhanced Profile Page with Role-Based Actions
class SecureProfilePage extends StatelessWidget with RoleCheckMixin {
  final String userId;
  final bool isOwnProfile;

  const SecureProfilePage({
    super.key,
    required this.userId,
    this.isOwnProfile = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          // Show admin actions for other users' profiles
          if (!isOwnProfile)
            ModeratorOnlyWidget(
              child: PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert),
                onSelected: (value) => _handleAdminAction(context, value),
                itemBuilder: (context) => [
                  if (hasPermission(context, 'canModerateContent'))
                    const PopupMenuItem(
                      value: 'moderate',
                      child: ListTile(
                        leading: Icon(Icons.flag),
                        title: Text('Moderate User'),
                      ),
                    ),
                  if (hasPermission(context, 'canEditUsers'))
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('Edit Profile'),
                      ),
                    ),
                  if (hasPermission(context, 'canDeleteUsers'))
                    const PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete),
                        title: Text('Delete User'),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // User role badge
          BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              final userRole = state.userRole ?? UserRole.user;
              return Padding(
                padding: const EdgeInsets.all(16),
                child: SecurityUtils.getRoleChip(userRole),
              );
            },
          ),

          // Profile content
          Expanded(
            child: ListView(
              children: [
                // Regular profile info
                const ListTile(
                  title: Text('Profile Information'),
                ),

                // Admin-only user management section
                AdminOnlyWidget(
                  child: Card(
                    margin: const EdgeInsets.all(16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'User Management',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            children: [
                              ElevatedButton(
                                onPressed: () => _viewUserActivity(context),
                                child: const Text('View Activity'),
                              ),
                              ElevatedButton(
                                onPressed: () => _manageUserPermissions(context),
                                child: const Text('Manage Permissions'),
                              ),
                              SuperAdminOnlyWidget(
                                child: ElevatedButton(
                                  onPressed: () => _changeUserRole(context),
                                  child: const Text('Change Role'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleAdminAction(BuildContext context, String action) {
    switch (action) {
      case 'moderate':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canModerateContent',
          () => _moderateUser(context),
        );
        break;
      case 'edit':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canEditUsers',
          () => _editUser(context),
        );
        break;
      case 'delete':
        SecurityUtils.executeWithPermissionCheck(
          context,
          'canDeleteUsers',
          () => _deleteUser(context),
        );
        break;
    }
  }

  void _moderateUser(BuildContext context) {
    // Moderation logic
  }

  void _editUser(BuildContext context) {
    // Edit user logic
  }

  void _deleteUser(BuildContext context) {
    // Delete user logic with confirmation
  }

  void _viewUserActivity(BuildContext context) {
    // View user activity logic
  }

  void _manageUserPermissions(BuildContext context) {
    // Manage permissions logic
  }

  void _changeUserRole(BuildContext context) {
    // Change role logic (Super Admin only)
  }
}
