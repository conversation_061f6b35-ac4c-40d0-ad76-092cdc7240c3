
BLoC in my app

MultiBlocProvider(
  providers: [
    BlocProvider<ThemeBloc>(),           // 🎨 Theme management
    BlocProvider<NavigationBloc>(),      // 🧭 Navigation & tabs
    BlocProvider<ProfileBloc>(),         // 👤 User profiles
    BlocProvider<PostsBloc>(),           // 📝 Posts & content
    BlocProvider<SearchBloc>(),          // 🔍 Search functionality
    BlocProvider<ChatBloc>(),            // 💬 Chat/messaging
    BlocProvider<BusinessStatsBloc>(),   // 📊 Business analytics
    BlocProvider<SubscriptionBloc>(),    // 💳 Subscriptions
    BlocProvider<CreatePostBloc>(),      // ✍️ Post creation
    BlocProvider<NewMessageBloc>(),      // 📨 New messages
    BlocProvider<NotificationBloc>(),    // 🔔 Notifications
    BlocProvider<AuthBloc>(),            // 🔐 Authentication
  ],
)

BLoC Patten Structure
lib/bloc/[feature_name]/
├── [feature]_event.dart    # 📥 User actions/inputs
├── [feature]_state.dart    # 📤 App states/outputs  
└── [feature]_bloc.dart     # 🧠 Business logic
-----------------------------------------------------------------------------
how to commit to new branch 'auth'

git checkout -b auth
# [make your changes]
git add .
git commit -m "Add new feature"
git push origin auth

-----------------------------------------------------------------------------

authentication structure
lib/bloc/auth_bloc/
├── auth_event.dart    # User actions (login, signup, etc.)
├── auth_state.dart    # App states (loading, authenticated, etc.)
└── auth_bloc.dart     # Business logic that connects events to states

lib/ui/auth/
├── auth_wrapper.dart  # Handles routing based on auth state
├── login_page.dart    # Login UI
└── signup_page.dart   # Signup UI

Example structure
// 1. User taps login button
onPressed: () => context.read<AuthBloc>().add(LoginRequested(...))

// 2. AuthBloc processes event
_onLoginRequested() {
  emit(loading());
  try {
    final user = await authRepository.login(request);
    emit(authenticated(user));
  } catch (e) {
    emit(error(e.message));
  }
}

// 3. UI responds to state change
BlocListener<AuthBloc, AuthState>(
  listener: (context, state) {
    if (state.status == AuthStatus.authenticated) {
      Navigator.pushReplacement(context, MyHomePage());
    }
  }
)

AuthWrapper handles routing based on auth state

main() → MyApp() → AuthWrapper()
                      ↓
                 BlocBuilder listens to AuthBloc
                      ↓
              Checks current auth status
                      ↓
    ┌─────────────────┼─────────────────┐
    ↓                 ↓                 ↓
Loading Screen    Welcome Page     Main App
(checking auth)   (not logged in)  (logged in)

-------------------------------------------------------------

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('user', 'business', 'admin');
CREATE TYPE post_type AS ENUM ('text', 'image', 'video', 'product');
CREATE TYPE notification_type AS ENUM ('like', 'comment', 'follow', 'message', 'system');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    bio TEXT,
    avatar_url TEXT,
    date_of_birth DATE,
    role user_role DEFAULT 'user',
    is_verified BOOLEAN DEFAULT FALSE,
    is_business BOOLEAN DEFAULT FALSE,
    business_name VARCHAR(100),
    business_description TEXT,
    location VARCHAR(100),
    website_url TEXT,
    social_links JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE public.categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon_url TEXT,
    color VARCHAR(7), -- Hex color code
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User categories (many-to-many relationship)
CREATE TABLE public.user_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, category_id)
);

-- Posts table
CREATE TABLE public.posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    content TEXT,
    post_type post_type DEFAULT 'text',
    media_urls TEXT[],
    category_id UUID REFERENCES public.categories(id),
    location VARCHAR(100),
    tags TEXT[],
    is_public BOOLEAN DEFAULT TRUE,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE public.products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    images TEXT[],
    category_id UUID REFERENCES public.categories(id),
    condition VARCHAR(20), -- new, used, refurbished
    availability VARCHAR(20) DEFAULT 'available', -- available, sold, reserved
    location VARCHAR(100),
    shipping_available BOOLEAN DEFAULT FALSE,
    shipping_cost DECIMAL(10,2),
    tags TEXT[],
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Likes table
CREATE TABLE public.likes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, post_id)
);

-- Comments table
CREATE TABLE public.comments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id), -- For nested comments
    content TEXT NOT NULL,
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Follows table
CREATE TABLE public.follows (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    follower_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    following_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(follower_id, following_id),
    CHECK (follower_id != following_id)
);

-- Chats table
CREATE TABLE public.chats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100), -- For group chats
    is_group BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat participants
CREATE TABLE public.chat_participants (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    is_admin BOOLEAN DEFAULT FALSE,
    UNIQUE(chat_id, user_id)
);

-- Messages table
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    content TEXT,
    message_type VARCHAR(20) DEFAULT 'text', -- text, image, video, file, system
    media_url TEXT,
    reply_to UUID REFERENCES public.messages(id),
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE public.subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    plan_name VARCHAR(50) NOT NULL,
    plan_type VARCHAR(20) NOT NULL, -- monthly, yearly
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    auto_renew BOOLEAN DEFAULT TRUE,
    payment_method JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);