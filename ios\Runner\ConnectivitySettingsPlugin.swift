import Flutter
import UIKit

public class ConnectivitySettingsPlugin: NSObject, FlutterPlugin {
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "connectivity_settings", binaryMessenger: registrar.messenger())
        let instance = ConnectivitySettingsPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "openNetworkSettings":
            openNetworkSettings(result: result)
        case "openSettings":
            openSettings(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func openNetworkSettings(result: @escaping FlutterResult) {
        // iOS doesn't allow direct access to WiFi settings
        // We can only open the main Settings app
        openSettings(result: result)
    }
    
    private func openSettings(result: @escaping FlutterResult) {
        DispatchQueue.main.async {
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                if UIApplication.shared.canOpenURL(settingsUrl) {
                    UIApplication.shared.open(settingsUrl) { success in
                        result(success)
                    }
                } else {
                    result(FlutterError(code: "UNAVAILABLE", message: "Could not open settings", details: nil))
                }
            } else {
                result(FlutterError(code: "UNAVAILABLE", message: "Settings URL not available", details: nil))
            }
        }
    }
}
