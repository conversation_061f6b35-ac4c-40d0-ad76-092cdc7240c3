# Stunning Call & Video Interfaces

This folder contains premium-quality call and video interfaces designed to match the standards of major apps like WhatsApp, Telegram, and FaceTime.

## 🎯 Features

### Voice Call Interface (`voice_call_interface.dart`)
- **Stunning Gradient Background**: Beautiful indigo-to-purple-to-black gradient
- **Pulse Animation**: Contact image pulses during incoming calls
- **Smooth Slide Animations**: Elegant entry animations with elastic curves
- **Real-time Call Timer**: Live duration display during active calls
- **Interactive Controls**: Mute, speaker, dialpad, and add call buttons
- **Professional UI**: Glassmorphism effects and smooth transitions
- **Incoming/Outgoing States**: Different layouts for incoming vs outgoing calls

### Video Call Interface (`video_call_interface.dart`)
- **Full-Screen Video Experience**: Immersive video call layout
- **Picture-in-Picture**: Self-video view with tap-to-switch functionality
- **Auto-Hide Controls**: Controls fade after 3 seconds, tap to show/hide
- **Camera Controls**: Toggle camera on/off, switch front/back camera
- **Gradient Overlays**: Beautiful overlays for better text readability
- **Responsive Design**: Adapts to different screen sizes
- **Professional Animations**: Smooth fade and slide transitions

### Call Demo Helper (`call_demo_helper.dart`)
- **Easy Integration**: Simple helper methods for triggering calls
- **Demo Widget**: Test interface with buttons for all call types
- **Extension Methods**: Convenient context extensions for quick access
- **Flexible API**: Support for custom contact names and images

## 🚀 Usage

### Basic Implementation

```dart
// Voice Call
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => VoiceCallInterface(
      contactName: 'John Doe',
      contactImage: 'https://example.com/avatar.jpg',
      isIncoming: false,
      onEndCall: () => print('Call ended'),
    ),
  ),
);

// Video Call
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => VideoCallInterface(
      contactName: 'Jane Smith',
      contactImage: 'https://example.com/avatar.jpg',
      isIncoming: true,
      onAccept: () => print('Call accepted'),
      onDecline: () => print('Call declined'),
    ),
  ),
);
```

### Using Helper Methods

```dart
// Start outgoing calls
CallDemoHelper.startVoiceCall(context, 
  contactName: 'Contact Name',
  contactImage: 'image_url'
);

CallDemoHelper.startVideoCall(context,
  contactName: 'Contact Name', 
  contactImage: 'image_url'
);

// Simulate incoming calls
CallDemoHelper.simulateIncomingVoiceCall(context);
CallDemoHelper.simulateIncomingVideoCall(context);
```

### Using Context Extensions

```dart
// Convenient extension methods
context.makeVoiceCall('John Doe', 'avatar_url');
context.makeVideoCall('Jane Smith', 'avatar_url');
context.simulateIncomingVoice();
context.simulateIncomingVideo();
```

## 🎨 Design Features

### Animations
- **Pulse Animation**: Contact image pulses during incoming calls
- **Slide Transitions**: Smooth slide-up animations for controls
- **Fade Effects**: Auto-hiding controls with fade animations
- **Elastic Curves**: Professional easing curves for natural motion

### Visual Effects
- **Gradient Backgrounds**: Multi-color gradients for depth
- **Glassmorphism**: Frosted glass effects on overlays
- **Box Shadows**: Subtle shadows for depth and elevation
- **Blur Effects**: Background blur for focus on content

### Color Scheme
- **Primary**: Indigo and purple gradients
- **Accent**: Teal for action buttons
- **Status Colors**: Green for accept, red for decline/end
- **Transparency**: Strategic use of alpha values for layering

## 🔧 Customization

### Themes
The interfaces automatically adapt to your app's theme:
- Light/dark mode support
- Dynamic color scheme integration
- Consistent with Material Design 3

### Callbacks
All interfaces support callback functions:
- `onAccept`: Called when incoming call is accepted
- `onDecline`: Called when incoming call is declined
- `onEndCall`: Called when active call is ended

### States
- **Incoming Call**: Shows accept/decline buttons with pulsing animation
- **Outgoing Call**: Shows connecting state then active call controls
- **Active Call**: Full control interface with timer and action buttons

## 📱 Integration with Chat

The interfaces are integrated into the chat AppBar:
- **Call Icon**: Triggers voice call interface
- **Video Icon**: Triggers video call interface
- **FAB Demo**: Floating action button for testing all call types

## 🎯 Best Practices

1. **Performance**: Animations are optimized for smooth 60fps performance
2. **Memory**: Controllers are properly disposed to prevent memory leaks
3. **Accessibility**: All buttons have proper semantic labels
4. **Responsive**: Layouts adapt to different screen sizes
5. **Professional**: Follows design patterns from major messaging apps

## 🔮 Future Enhancements

- Real WebRTC integration for actual video/audio calls
- Screen sharing capabilities
- Group call support
- Call recording functionality
- Advanced camera filters and effects
- Integration with push notifications for incoming calls

## 📋 Requirements

- Flutter 3.0+
- Material Design 3 support
- Network access for profile images
- Camera/microphone permissions (for real implementation)

This implementation provides a solid foundation for a production-ready calling system that can be easily extended with real communication protocols.
