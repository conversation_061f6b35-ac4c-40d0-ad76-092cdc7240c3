import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';

class SignupVerificationPage extends StatefulWidget {
  const SignupVerificationPage({super.key});

  @override
  State<SignupVerificationPage> createState() => _SignupVerificationPageState();
}

class _SignupVerificationPageState extends State<SignupVerificationPage> {
  final _codeController = TextEditingController();
  String? _codeError;
  bool _isResending = false;
  bool _isVerifying = false; // Race condition prevention
  int _resendCooldown = 0;
  Timer? _cooldownTimer;

  @override
  void dispose() {
    _codeController.dispose();
    _cooldownTimer?.cancel();
    super.dispose();
  }

  void _onVerify() {
    // Prevent multiple simultaneous verification attempts
    if (_isVerifying) return;

    final code = _codeController.text.trim();

    // Validate code format more strictly
    if (code.isEmpty) {
      setState(() {
        _codeError = 'Please enter the verification code';
      });
      return;
    }

    if (code.length != 6) {
      setState(() {
        _codeError = 'Please enter the 6-digit code';
      });
      return;
    }

    // Ensure code contains only numbers
    if (!RegExp(r'^\d{6}$').hasMatch(code)) {
      setState(() {
        _codeError = 'Code must contain only numbers';
      });
      return;
    }

    setState(() {
      _isVerifying = true;
      _codeError = null;
    });

    context.read<AuthBloc>().add(SignupVerificationSubmitted(code: code));
  }

  void _onResendCode() async {
    if (_isResending || _resendCooldown > 0) return;

    setState(() {
      _isResending = true;
    });

    final authState = context.read<AuthBloc>().state;
    if (authState.tempEmailOrPhone != null && authState.tempIsEmail != null) {
      try {
        // Trigger resend by submitting basic info again
        context.read<AuthBloc>().add(
          SignupBasicInfoSubmitted(
            name: authState.tempName ?? '',
            emailOrPhone: authState.tempEmailOrPhone!,
            dateOfBirth: authState.tempDateOfBirth ?? DateTime.now(),
            isEmail: authState.tempIsEmail!,
          ),
        );

        // Start cooldown timer
        _startResendCooldown();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Verification code sent to ${authState.tempEmailOrPhone}',
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Failed to resend code. Please try again.'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    }

    setState(() {
      _isResending = false;
    });
  }

  void _startResendCooldown() {
    setState(() {
      _resendCooldown =
          50; // 50 seconds cooldown (Supabase requires 8+ seconds)
    });

    _cooldownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _resendCooldown--;
      });

      if (_resendCooldown <= 0) {
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          // Reset verification state on any state change
          if (_isVerifying) {
            setState(() {
              _isVerifying = false;
            });
          }

          // Clear code error when state changes
          if (state.errorMessage == null && _codeError != null) {
            setState(() {
              _codeError = null;
            });
          }

          // Show error messages
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(seconds: 4),
              ),
            );
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),

                      // Header with progress
                      Column(
                        children: [
                          const AuthProgressIndicator(
                            currentStep: 3,
                            totalSteps: 6,
                          ),
                          const SizedBox(height: 20),
                          BlocBuilder<AuthBloc, AuthState>(
                            builder: (context, state) {
                              return AuthVerificationHeader(
                                identifier: state.tempEmailOrPhone ?? '',
                                isEmail: state.tempIsEmail ?? true,
                              );
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: 40),

                      // Verification info
                      BlocBuilder<AuthBloc, AuthState>(
                        builder: (context, state) {
                          final isEmail = state.tempIsEmail ?? true;
                          final contact = state.tempEmailOrPhone ?? '';

                          return Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: const Color(0xFF1DA1F2).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: const Color(0xFF1DA1F2).withOpacity(0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  isEmail ? Icons.email : Icons.phone,
                                  color: const Color(0xFF1DA1F2),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Verification code sent to:',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                      Text(
                                        contact,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF1DA1F2),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 32),

                      // Code input field
                      AuthCodeField(
                        controller: _codeController,
                        label: 'Verification code',
                        hintText: 'Enter 6-digit code',
                        errorText: _codeError,
                        autofocus: true,
                        onChanged: (value) {
                          if (_codeError != null) {
                            setState(() {
                              _codeError = null;
                            });
                          }
                        },
                      ),

                      const SizedBox(height: 24),

                      // Resend code link with cooldown
                      Center(
                        child: Column(
                          children: [
                            Text(
                              'Didn\'t receive the code?',
                              style: TextStyle(
                                fontSize: 15,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            if (_isResending)
                              const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Color(0xFF1DA1F2),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Sending...',
                                    style: TextStyle(
                                      fontSize: 15,
                                      color: Color(0xFF1DA1F2),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              )
                            else if (_resendCooldown > 0)
                              Text(
                                'Resend code in ${_resendCooldown}s',
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Colors.grey[500],
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            else
                              AuthTextButton(
                                text: 'Resend code',
                                onPressed: _onResendCode,
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                              ),
                          ],
                        ),
                      ),

                      const Spacer(),

                      // Verify button
                      BlocBuilder<AuthBloc, AuthState>(
                        builder: (context, state) {
                          return AuthPrimaryButton(
                            text: 'Verify',
                            isLoading: state.isLoading,
                            onPressed: _onVerify,
                          );
                        },
                      ),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
