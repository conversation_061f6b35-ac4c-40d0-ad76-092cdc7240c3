-- =====================================================
-- REAL-TIME CHAT SYSTEM
-- Professional messaging system for marketplace
-- =====================================================

-- =====================================================
-- 11. CHATS TABLE (Conversation Management)
-- =====================================================

CREATE TABLE public.chats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    
    -- Participants (for direct messages)
    participant_1_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    participant_2_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    
    -- Optional context
    product_id UUID REFERENCES public.products(id) ON DELETE SET NULL, -- Product inquiry
    order_id UUID REFERENCES public.orders(id) ON DELETE SET NULL, -- Order-related chat
    
    -- Chat metadata
    chat_type VARCHAR(20) DEFAULT 'direct' CHECK (chat_type IN ('direct', 'product_inquiry', 'order_support')),
    subject VARCHAR(200), -- Optional subject line
    
    -- Status and settings
    is_active BOOLEAN DEFAULT TRUE,
    is_archived_by_p1 BOOLEAN DEFAULT FALSE,
    is_archived_by_p2 BOOLEAN DEFAULT FALSE,
    is_blocked_by_p1 BOOLEAN DEFAULT FALSE,
    is_blocked_by_p2 BOOLEAN DEFAULT FALSE,
    
    -- Last activity tracking
    last_message_id UUID, -- Will be set by trigger
    last_message_at TIMESTAMPTZ DEFAULT NOW(),
    last_message_by UUID REFERENCES public.profiles(id),
    
    -- Read status
    last_read_by_p1 TIMESTAMPTZ DEFAULT NOW(),
    last_read_by_p2 TIMESTAMPTZ DEFAULT NOW(),
    unread_count_p1 INTEGER DEFAULT 0 CHECK (unread_count_p1 >= 0),
    unread_count_p2 INTEGER DEFAULT 0 CHECK (unread_count_p2 >= 0),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(participant_1_id, participant_2_id, product_id), -- One chat per product inquiry
    CHECK (participant_1_id != participant_2_id), -- Users cannot chat with themselves
    CHECK (participant_1_id < participant_2_id) -- Ensure consistent ordering
);

-- Indexes for performance
CREATE INDEX idx_chats_participant_1 ON public.chats(participant_1_id);
CREATE INDEX idx_chats_participant_2 ON public.chats(participant_2_id);
CREATE INDEX idx_chats_product_id ON public.chats(product_id);
CREATE INDEX idx_chats_order_id ON public.chats(order_id);
CREATE INDEX idx_chats_last_message_at ON public.chats(last_message_at DESC);
CREATE INDEX idx_chats_is_active ON public.chats(is_active);

-- Composite indexes for user's chat list
CREATE INDEX idx_chats_p1_active ON public.chats(participant_1_id, is_active, last_message_at DESC);
CREATE INDEX idx_chats_p2_active ON public.chats(participant_2_id, is_active, last_message_at DESC);

-- =====================================================
-- 12. MESSAGES TABLE (Chat Messages)
-- =====================================================

CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE NOT NULL,
    sender_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    
    -- Message content
    content TEXT CHECK (length(content) <= 4000), -- 4000 char limit
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN (
        'text', 'image', 'video', 'audio', 'file', 'location', 'product', 'order', 'system'
    )),
    
    -- Media and attachments
    media_url TEXT,
    media_metadata JSONB DEFAULT '{}', -- File size, dimensions, etc.
    thumbnail_url TEXT,
    
    -- Message references
    reply_to_id UUID REFERENCES public.messages(id) ON DELETE SET NULL, -- Reply to message
    forwarded_from_id UUID REFERENCES public.messages(id) ON DELETE SET NULL, -- Forwarded message
    
    -- Message status
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMPTZ,
    
    -- Read status
    is_read_by_recipient BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMPTZ,
    
    -- System message data
    system_data JSONB DEFAULT '{}', -- For system messages
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (
        (message_type = 'text' AND content IS NOT NULL) OR
        (message_type != 'text' AND (media_url IS NOT NULL OR system_data IS NOT NULL))
    ),
    CHECK (reply_to_id IS NULL OR reply_to_id != id) -- Messages cannot reply to themselves
);

-- Indexes for performance (critical for real-time chat)
CREATE INDEX idx_messages_chat_id ON public.messages(chat_id);
CREATE INDEX idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX idx_messages_reply_to_id ON public.messages(reply_to_id);

-- Composite indexes for chat message queries
CREATE INDEX idx_messages_chat_created ON public.messages(chat_id, created_at DESC) WHERE is_deleted = false;
CREATE INDEX idx_messages_chat_unread ON public.messages(chat_id, is_read_by_recipient, created_at DESC) WHERE is_deleted = false;

-- Partial index for unread messages
CREATE INDEX idx_messages_unread ON public.messages(chat_id, sender_id, created_at DESC) 
WHERE is_read_by_recipient = false AND is_deleted = false;

-- =====================================================
-- RLS POLICIES FOR CHAT SYSTEM
-- =====================================================

-- CHATS TABLE SECURITY
ALTER TABLE public.chats ENABLE ROW LEVEL SECURITY;

-- Users can only see chats they participate in
CREATE POLICY "chats_select_policy" ON public.chats
FOR SELECT USING (
    participant_1_id = auth.uid() OR 
    participant_2_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Users can create chats with others
CREATE POLICY "chats_insert_policy" ON public.chats
FOR INSERT WITH CHECK (
    (participant_1_id = auth.uid() OR participant_2_id = auth.uid()) AND
    auth.uid() IS NOT NULL
);

-- Users can update their own chat settings
CREATE POLICY "chats_update_policy" ON public.chats
FOR UPDATE USING (
    participant_1_id = auth.uid() OR 
    participant_2_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Only admins can delete chats
CREATE POLICY "chats_delete_policy" ON public.chats
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
    )
);

-- MESSAGES TABLE SECURITY
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Users can see messages in chats they participate in
CREATE POLICY "messages_select_policy" ON public.messages
FOR SELECT USING (
    is_deleted = false AND
    EXISTS (
        SELECT 1 FROM public.chats c 
        WHERE c.id = chat_id AND (
            c.participant_1_id = auth.uid() OR 
            c.participant_2_id = auth.uid()
        )
    ) OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Users can send messages in chats they participate in
CREATE POLICY "messages_insert_policy" ON public.messages
FOR INSERT WITH CHECK (
    auth.uid() = sender_id AND
    EXISTS (
        SELECT 1 FROM public.chats c 
        WHERE c.id = chat_id AND (
            c.participant_1_id = auth.uid() OR 
            c.participant_2_id = auth.uid()
        ) AND c.is_active = true
    )
);

-- Users can update their own messages (for editing)
CREATE POLICY "messages_update_policy" ON public.messages
FOR UPDATE USING (
    sender_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- Users can delete their own messages, moderators can delete any
CREATE POLICY "messages_delete_policy" ON public.messages
FOR DELETE USING (
    sender_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'super_admin', 'moderator')
    )
);

-- =====================================================
-- CHAT SYSTEM FUNCTIONS
-- =====================================================

-- Function to create or get existing chat
CREATE OR REPLACE FUNCTION public.get_or_create_chat(
    p_user1_id UUID,
    p_user2_id UUID,
    p_product_id UUID DEFAULT NULL,
    p_order_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    chat_id UUID;
    participant_1 UUID;
    participant_2 UUID;
BEGIN
    -- Ensure consistent ordering
    IF p_user1_id < p_user2_id THEN
        participant_1 := p_user1_id;
        participant_2 := p_user2_id;
    ELSE
        participant_1 := p_user2_id;
        participant_2 := p_user1_id;
    END IF;
    
    -- Try to find existing chat
    SELECT id INTO chat_id
    FROM public.chats
    WHERE 
        participant_1_id = participant_1 AND
        participant_2_id = participant_2 AND
        (p_product_id IS NULL OR product_id = p_product_id) AND
        (p_order_id IS NULL OR order_id = p_order_id);
    
    -- Create new chat if not found
    IF chat_id IS NULL THEN
        INSERT INTO public.chats (
            participant_1_id,
            participant_2_id,
            product_id,
            order_id,
            chat_type
        ) VALUES (
            participant_1,
            participant_2,
            p_product_id,
            p_order_id,
            CASE 
                WHEN p_product_id IS NOT NULL THEN 'product_inquiry'
                WHEN p_order_id IS NOT NULL THEN 'order_support'
                ELSE 'direct'
            END
        ) RETURNING id INTO chat_id;
    END IF;
    
    RETURN chat_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send a message
CREATE OR REPLACE FUNCTION public.send_message(
    p_chat_id UUID,
    p_sender_id UUID,
    p_content TEXT,
    p_message_type VARCHAR(20) DEFAULT 'text',
    p_media_url TEXT DEFAULT NULL,
    p_reply_to_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    message_id UUID;
    recipient_id UUID;
    chat_exists BOOLEAN;
BEGIN
    -- Verify chat exists and user is participant
    SELECT 
        true,
        CASE 
            WHEN participant_1_id = p_sender_id THEN participant_2_id
            ELSE participant_1_id
        END
    INTO chat_exists, recipient_id
    FROM public.chats
    WHERE 
        id = p_chat_id AND
        (participant_1_id = p_sender_id OR participant_2_id = p_sender_id) AND
        is_active = true;
    
    IF NOT chat_exists THEN
        RAISE EXCEPTION 'Chat not found or access denied';
    END IF;
    
    -- Insert message
    INSERT INTO public.messages (
        chat_id,
        sender_id,
        content,
        message_type,
        media_url,
        reply_to_id
    ) VALUES (
        p_chat_id,
        p_sender_id,
        p_content,
        p_message_type,
        p_media_url,
        p_reply_to_id
    ) RETURNING id INTO message_id;
    
    RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION public.mark_messages_read(
    p_chat_id UUID,
    p_user_id UUID
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
    other_participant_id UUID;
BEGIN
    -- Get the other participant
    SELECT
        CASE
            WHEN participant_1_id = p_user_id THEN participant_2_id
            ELSE participant_1_id
        END
    INTO other_participant_id
    FROM public.chats
    WHERE id = p_chat_id;

    -- Mark messages as read
    UPDATE public.messages
    SET
        is_read_by_recipient = true,
        read_at = NOW()
    WHERE
        chat_id = p_chat_id AND
        sender_id = other_participant_id AND
        is_read_by_recipient = false AND
        is_deleted = false;

    GET DIAGNOSTICS updated_count = ROW_COUNT;

    -- Update chat read status
    IF p_user_id = (SELECT participant_1_id FROM public.chats WHERE id = p_chat_id) THEN
        UPDATE public.chats
        SET
            last_read_by_p1 = NOW(),
            unread_count_p1 = 0
        WHERE id = p_chat_id;
    ELSE
        UPDATE public.chats
        SET
            last_read_by_p2 = NOW(),
            unread_count_p2 = 0
        WHERE id = p_chat_id;
    END IF;

    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's chat list
CREATE OR REPLACE FUNCTION public.get_user_chats(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    chat_id UUID,
    other_participant_id UUID,
    other_participant_name VARCHAR(100),
    other_participant_avatar TEXT,
    last_message TEXT,
    last_message_at TIMESTAMPTZ,
    last_message_by UUID,
    unread_count INTEGER,
    chat_type VARCHAR(20),
    product_name VARCHAR(200),
    is_online BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id,
        CASE
            WHEN c.participant_1_id = p_user_id THEN c.participant_2_id
            ELSE c.participant_1_id
        END as other_participant_id,
        CASE
            WHEN c.participant_1_id = p_user_id THEN p2.full_name
            ELSE p1.full_name
        END as other_participant_name,
        CASE
            WHEN c.participant_1_id = p_user_id THEN p2.avatar_url
            ELSE p1.avatar_url
        END as other_participant_avatar,
        COALESCE(lm.content, 'No messages yet') as last_message,
        c.last_message_at,
        c.last_message_by,
        CASE
            WHEN c.participant_1_id = p_user_id THEN c.unread_count_p1
            ELSE c.unread_count_p2
        END as unread_count,
        c.chat_type,
        prod.name as product_name,
        CASE
            WHEN c.participant_1_id = p_user_id THEN (p2.last_seen_at > NOW() - INTERVAL '5 minutes')
            ELSE (p1.last_seen_at > NOW() - INTERVAL '5 minutes')
        END as is_online
    FROM public.chats c
    JOIN public.profiles p1 ON c.participant_1_id = p1.id
    JOIN public.profiles p2 ON c.participant_2_id = p2.id
    LEFT JOIN public.messages lm ON c.last_message_id = lm.id
    LEFT JOIN public.products prod ON c.product_id = prod.id
    WHERE
        (c.participant_1_id = p_user_id OR c.participant_2_id = p_user_id) AND
        c.is_active = true AND
        NOT (
            (c.participant_1_id = p_user_id AND c.is_archived_by_p1) OR
            (c.participant_2_id = p_user_id AND c.is_archived_by_p2)
        )
    ORDER BY c.last_message_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get chat messages
CREATE OR REPLACE FUNCTION public.get_chat_messages(
    p_chat_id UUID,
    p_user_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    message_id UUID,
    sender_id UUID,
    sender_name VARCHAR(100),
    sender_avatar TEXT,
    content TEXT,
    message_type VARCHAR(20),
    media_url TEXT,
    reply_to_id UUID,
    reply_to_content TEXT,
    is_read BOOLEAN,
    created_at TIMESTAMPTZ,
    is_own_message BOOLEAN
) AS $$
BEGIN
    -- Verify user is participant
    IF NOT EXISTS (
        SELECT 1 FROM public.chats
        WHERE id = p_chat_id AND (participant_1_id = p_user_id OR participant_2_id = p_user_id)
    ) THEN
        RAISE EXCEPTION 'Access denied to chat';
    END IF;

    RETURN QUERY
    SELECT
        m.id,
        m.sender_id,
        p.full_name,
        p.avatar_url,
        m.content,
        m.message_type,
        m.media_url,
        m.reply_to_id,
        rm.content as reply_to_content,
        m.is_read_by_recipient,
        m.created_at,
        (m.sender_id = p_user_id) as is_own_message
    FROM public.messages m
    JOIN public.profiles p ON m.sender_id = p.id
    LEFT JOIN public.messages rm ON m.reply_to_id = rm.id
    WHERE
        m.chat_id = p_chat_id AND
        m.is_deleted = false
    ORDER BY m.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- CHAT SYSTEM TRIGGERS
-- =====================================================

-- Function to update chat on new message
CREATE OR REPLACE FUNCTION public.update_chat_on_message()
RETURNS TRIGGER AS $$
DECLARE
    sender_is_p1 BOOLEAN;
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Check if sender is participant 1
        SELECT (participant_1_id = NEW.sender_id) INTO sender_is_p1
        FROM public.chats WHERE id = NEW.chat_id;

        -- Update chat metadata
        UPDATE public.chats
        SET
            last_message_id = NEW.id,
            last_message_at = NEW.created_at,
            last_message_by = NEW.sender_id,
            updated_at = NEW.created_at,
            -- Update unread count for recipient
            unread_count_p1 = CASE
                WHEN sender_is_p1 THEN unread_count_p1
                ELSE unread_count_p1 + 1
            END,
            unread_count_p2 = CASE
                WHEN sender_is_p1 THEN unread_count_p2 + 1
                ELSE unread_count_p2
            END
        WHERE id = NEW.chat_id;

        RETURN NEW;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for chat updates
CREATE TRIGGER update_chat_on_message_trigger
    AFTER INSERT ON public.messages
    FOR EACH ROW EXECUTE FUNCTION public.update_chat_on_message();

-- Trigger to update message timestamps
CREATE TRIGGER update_messages_updated_at
    BEFORE UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_chats_updated_at
    BEFORE UPDATE ON public.chats
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- REAL-TIME SUBSCRIPTIONS HELPER
-- =====================================================

-- Function to get user's chat subscription channels
CREATE OR REPLACE FUNCTION public.get_user_chat_channels(p_user_id UUID)
RETURNS TEXT[] AS $$
DECLARE
    channels TEXT[];
BEGIN
    SELECT array_agg('chat:' || id::TEXT)
    INTO channels
    FROM public.chats
    WHERE participant_1_id = p_user_id OR participant_2_id = p_user_id;

    RETURN COALESCE(channels, ARRAY[]::TEXT[]);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions on chat tables
GRANT SELECT, INSERT, UPDATE ON public.chats TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.messages TO authenticated;
