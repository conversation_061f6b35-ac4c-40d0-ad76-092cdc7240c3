import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:google_fonts/google_fonts.dart';

class SalesChart extends StatefulWidget {
  final List<SalesData> data;
  final String title;
  final Color primaryColor;
  final double height;

  const SalesChart({
    super.key,
    required this.data,
    this.title = 'Sales Performance',
    this.primaryColor = Colors.blue,
    this.height = 300,
  });

  @override
  State<SalesChart> createState() => _SalesChartState();
}

class _SalesChartState extends State<SalesChart> {
  late TooltipBehavior _tooltip;

  @override
  void initState() {
    _tooltip = TooltipBehavior(enable: true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      height: widget.height,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: widget.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  LucideIcons.barChart3,
                  color: widget.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    Text(
                      'Sales by category',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      LucideIcons.trendingUp,
                      color: Colors.blue,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '+8.3%',
                      style: GoogleFonts.inter(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SfCartesianChart(
              primaryXAxis: const CategoryAxis(
                majorGridLines: MajorGridLines(width: 0),
                axisLine: AxisLine(width: 0),
                labelStyle: TextStyle(fontSize: 10),
              ),
              primaryYAxis: NumericAxis(
                majorGridLines: MajorGridLines(
                  width: 1,
                  color: Colors.grey.withOpacity(0.2),
                ),
                axisLine: const AxisLine(width: 0),
                labelFormat: '{value}',
                labelStyle: const TextStyle(fontSize: 10),
              ),
              tooltipBehavior: _tooltip,
              plotAreaBorderWidth: 0,
              series: <CartesianSeries<SalesData, String>>[
                ColumnSeries<SalesData, String>(
                  dataSource: widget.data,
                  xValueMapper: (SalesData sales, _) => sales.category,
                  yValueMapper: (SalesData sales, _) => sales.sales,
                  name: 'Sales',
                  color: widget.primaryColor,
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      widget.primaryColor,
                      widget.primaryColor.withOpacity(0.7),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                  dataLabelSettings: const DataLabelSettings(
                    isVisible: true,
                    textStyle: TextStyle(fontSize: 10, fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.3, end: 0),
          ),
        ],
      ),
    );
  }
}

class SalesData {
  final String category;
  final double sales;
  final Color? color;

  SalesData(this.category, this.sales, [this.color]);
}

// Pie Chart for Sales Distribution
class SalesPieChart extends StatefulWidget {
  final List<SalesData> data;
  final String title;
  final double height;

  const SalesPieChart({
    super.key,
    required this.data,
    this.title = 'Sales Distribution',
    this.height = 300,
  });

  @override
  State<SalesPieChart> createState() => _SalesPieChartState();
}

class _SalesPieChartState extends State<SalesPieChart> {
  late TooltipBehavior _tooltip;

  @override
  void initState() {
    _tooltip = TooltipBehavior(enable: true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      height: widget.height,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  LucideIcons.pieChart,
                  color: Colors.purple,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.title,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SfCircularChart(
              tooltipBehavior: _tooltip,
              legend: const Legend(
                isVisible: true,
                position: LegendPosition.bottom,
                textStyle: TextStyle(fontSize: 10),
              ),
              series: <PieSeries<SalesData, String>>[
                PieSeries<SalesData, String>(
                  dataSource: widget.data,
                  xValueMapper: (SalesData data, _) => data.category,
                  yValueMapper: (SalesData data, _) => data.sales,
                  name: 'Sales',
                  dataLabelSettings: const DataLabelSettings(
                    isVisible: true,
                    labelPosition: ChartDataLabelPosition.outside,
                    textStyle: TextStyle(fontSize: 10, fontWeight: FontWeight.w600),
                  ),
                  enableTooltip: true,
                  explode: true,
                  explodeIndex: 0,
                ),
              ],
            ).animate().fadeIn(duration: 800.ms).scale(begin: const Offset(0.8, 0.8)),
          ),
        ],
      ),
    );
  }
}

// Mock data for sales charts
class SalesChartData {
  static List<SalesData> get categorySales => [
        SalesData('Electronics', 45),
        SalesData('Clothing', 32),
        SalesData('Home', 28),
        SalesData('Books', 15),
        SalesData('Sports', 22),
      ];

  static List<SalesData> get salesDistribution => [
        SalesData('Online', 65),
        SalesData('In-Store', 35),
      ];
}
