import 'package:flutter/material.dart';
import 'package:business_app/bloc/connectivity_bloc/connectivity_bloc.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Professional offline banner widget (Instagram/Facebook style)
class OfflineBanner extends StatelessWidget {
  final ConnectivityState connectivityState;
  final VoidCallback? onRetry;
  final VoidCallback? onOpenSettings;

  const OfflineBanner({
    super.key,
    required this.connectivityState,
    this.onRetry,
    this.onOpenSettings,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: _getBannerHeight(),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: _getBannerColor(isDark),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: _buildBannerContent(context, theme, isDark),
      ),
    );
  }

  /// Get banner height based on state
  double _getBannerHeight() {
    if (connectivityState.isRetrying) return 60.0;
    if (connectivityState.hasLimitedConnection) return 80.0;
    return 70.0;
  }

  /// Get banner color based on state and theme
  Color _getBannerColor(bool isDark) {
    if (connectivityState.isRetrying) {
      return isDark ? Colors.orange[800]! : Colors.orange[100]!;
    }
    if (connectivityState.hasLimitedConnection) {
      return isDark ? Colors.amber[800]! : Colors.amber[100]!;
    }
    return isDark ? Colors.red[800]! : Colors.red[100]!;
  }

  /// Build banner content
  Widget _buildBannerContent(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    if (connectivityState.isRetrying) {
      return _buildRetryingContent(theme, isDark);
    }

    if (connectivityState.hasLimitedConnection) {
      return _buildLimitedConnectionContent(context, theme, isDark);
    }

    return _buildOfflineContent(context, theme, isDark);
  }

  /// Build retrying content
  Widget _buildRetryingContent(ThemeData theme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isDark ? Colors.orange[200]! : Colors.orange[700]!,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Checking connection...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? Colors.orange[200] : Colors.orange[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build limited connection content
  Widget _buildLimitedConnectionContent(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Icon(
                LucideIcons.wifiOff,
                size: 20,
                color: isDark ? Colors.amber[200] : Colors.amber[700],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Connected to ${connectivityState.connectionTypeString} but no internet access',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark ? Colors.amber[200] : Colors.amber[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildActionButton(
                context,
                'Retry',
                onRetry,
                isDark ? Colors.amber[200]! : Colors.amber[700]!,
              ),
              const SizedBox(width: 8),
              _buildActionButton(
                context,
                'Settings',
                onOpenSettings,
                isDark ? Colors.amber[200]! : Colors.amber[700]!,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build offline content
  Widget _buildOfflineContent(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          Icon(
            LucideIcons.wifiOff,
            size: 20,
            color: isDark ? Colors.red[200] : Colors.red[700],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'You are currently offline',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? Colors.red[200] : Colors.red[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildActionButton(
            context,
            'Retry',
            onRetry,
            isDark ? Colors.red[200]! : Colors.red[700]!,
          ),
          const SizedBox(width: 8),
          _buildActionButton(
            context,
            'Settings',
            onOpenSettings,
            isDark ? Colors.red[200]! : Colors.red[700]!,
          ),
        ],
      ),
    );
  }

  /// Build action button
  Widget _buildActionButton(
    BuildContext context,
    String text,
    VoidCallback? onPressed,
    Color color,
  ) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }
}
