import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/models/auth/user_role.dart';

/// A widget that conditionally shows content based on user role permissions
class RoleGuard extends StatelessWidget {
  final Widget child;
  final UserRole? requiredRole;
  final String? requiredPermission;
  final Widget? fallback;
  final bool showFallbackOnError;

  const RoleGuard({
    super.key,
    required this.child,
    this.requiredRole,
    this.requiredPermission,
    this.fallback,
    this.showFallbackOnError = true,
  }) : assert(
          requiredRole != null || requiredPermission != null,
          'Either requiredRole or requiredPermission must be provided',
        );

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // Check if user is authenticated
        if (state.status != AuthStatus.authenticated) {
          return _buildFallback();
        }

        final userRole = state.userRole ?? UserRole.user;

        // Check role-based access
        if (requiredRole != null) {
          if (!_hasRequiredRole(userRole, requiredRole!)) {
            return _buildFallback();
          }
        }

        // Check permission-based access
        if (requiredPermission != null) {
          if (!userRole.canAccess(requiredPermission!)) {
            return _buildFallback();
          }
        }

        // User has required access, show the child
        return child;
      },
    );
  }

  bool _hasRequiredRole(UserRole userRole, UserRole requiredRole) {
    // Check if user's role hierarchy level meets or exceeds required level
    return userRole.hierarchyLevel >= requiredRole.hierarchyLevel;
  }

  Widget _buildFallback() {
    if (fallback != null) {
      return fallback!;
    }

    if (showFallbackOnError) {
      return const SizedBox.shrink();
    }

    return Container();
  }
}

/// A widget that shows content only to admin users (Admin or Super Admin)
class AdminOnlyWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const AdminOnlyWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleGuard(
      requiredRole: UserRole.admin,
      fallback: fallback,
      child: child,
    );
  }
}

/// A widget that shows content only to moderators and above
class ModeratorOnlyWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const ModeratorOnlyWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleGuard(
      requiredRole: UserRole.moderator,
      fallback: fallback,
      child: child,
    );
  }
}

/// A widget that shows content only to super admins
class SuperAdminOnlyWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const SuperAdminOnlyWidget({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return RoleGuard(
      requiredRole: UserRole.superAdmin,
      fallback: fallback,
      child: child,
    );
  }
}

/// A widget that shows different content based on user role
class RoleBasedWidget extends StatelessWidget {
  final Widget? userWidget;
  final Widget? moderatorWidget;
  final Widget? adminWidget;
  final Widget? superAdminWidget;
  final Widget? fallback;

  const RoleBasedWidget({
    super.key,
    this.userWidget,
    this.moderatorWidget,
    this.adminWidget,
    this.superAdminWidget,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state.status != AuthStatus.authenticated) {
          return fallback ?? const SizedBox.shrink();
        }

        final userRole = state.userRole ?? UserRole.user;

        switch (userRole) {
          case UserRole.superAdmin:
            return superAdminWidget ?? adminWidget ?? moderatorWidget ?? userWidget ?? _buildFallback();
          case UserRole.admin:
            return adminWidget ?? moderatorWidget ?? userWidget ?? _buildFallback();
          case UserRole.moderator:
            return moderatorWidget ?? userWidget ?? _buildFallback();
          case UserRole.user:
            return userWidget ?? _buildFallback();
        }
      },
    );
  }

  Widget _buildFallback() {
    return fallback ?? const SizedBox.shrink();
  }
}

/// A mixin that provides role checking functionality to widgets
mixin RoleCheckMixin {
  bool hasRole(BuildContext context, UserRole requiredRole) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.hierarchyLevel >= requiredRole.hierarchyLevel;
  }

  bool hasPermission(BuildContext context, String permission) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.canAccess(permission);
  }

  UserRole? getCurrentUserRole(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return null;
    }

    return authState.userRole;
  }

  bool isAdmin(BuildContext context) {
    return hasRole(context, UserRole.admin);
  }

  bool isModerator(BuildContext context) {
    return hasRole(context, UserRole.moderator);
  }

  bool isSuperAdmin(BuildContext context) {
    return hasRole(context, UserRole.superAdmin);
  }
}

/// Extension to add role checking to BuildContext
extension RoleCheckExtension on BuildContext {
  bool hasRole(UserRole requiredRole) {
    final authState = read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.hierarchyLevel >= requiredRole.hierarchyLevel;
  }

  bool hasPermission(String permission) {
    final authState = read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.canAccess(permission);
  }

  UserRole? get currentUserRole {
    final authState = read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return null;
    }

    return authState.userRole;
  }

  bool get isAdmin => hasRole(UserRole.admin);
  bool get isModerator => hasRole(UserRole.moderator);
  bool get isSuperAdmin => hasRole(UserRole.superAdmin);
}
