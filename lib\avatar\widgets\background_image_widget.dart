import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/image_config.dart';
import '../services/image_picker_service.dart';
import '../services/supabase_storage_service.dart';

/// Professional background image widget with upload functionality
class BackgroundImageWidget extends StatefulWidget {
  final String? currentImageUrl;
  final Function(String?) onImageChanged;
  final Function(String)? onError;
  final double height;
  final bool isEditable;
  final String? userId;
  final Widget? overlayWidget;

  const BackgroundImageWidget({
    super.key,
    this.currentImageUrl,
    required this.onImageChanged,
    this.onError,
    this.height = 200,
    this.isEditable = true,
    this.userId,
    this.overlayWidget,
  });

  @override
  State<BackgroundImageWidget> createState() => _BackgroundImageWidgetState();
}

class _BackgroundImageWidgetState extends State<BackgroundImageWidget>
    with SingleTickerProviderStateMixin {
  File? _selectedImage;
  bool _isUploading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickAndUploadImage() async {
    if (!widget.isEditable || widget.userId == null) return;

    if (!mounted) return;

    final imageFile = await ImagePickerService.showImageSourceDialog(
      context: context,
      config: ImageConfig.background,
      title: 'Select Background Image',
    );

    if (imageFile != null && mounted) {
      setState(() {
        _selectedImage = imageFile;
        _isUploading = true;
      });

      try {
        final result = await SupabaseStorageService.uploadBackgroundImage(
          userId: widget.userId!,
          imageFile: imageFile,
          replaceExisting: true,
        );

        if (result.isSuccess && result.imageUrl != null) {
          widget.onImageChanged(result.imageUrl);
          
          // Show success feedback
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Background image updated successfully!'),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(milliseconds: 2000),
              ),
            );
          }
        } else {
          widget.onError?.call(result.error ?? 'Failed to upload image');
        }
      } catch (e) {
        widget.onError?.call('Upload failed: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isUploading = false;
          });
        }
      }
    }
  }

  ImageProvider? _getImageProvider() {
    if (_selectedImage != null) {
      return FileImage(_selectedImage!);
    } else if (widget.currentImageUrl != null && widget.currentImageUrl!.isNotEmpty) {
      // Use optimized URL if available
      final fileName = SupabaseStorageService.extractFileNameFromUrl(
        widget.currentImageUrl!,
        SupabaseStorageService.backgroundImagesBucket,
      );
      
      if (fileName != null) {
        final optimizedUrl = SupabaseStorageService.getOptimizedBackgroundImageUrl(fileName);
        return CachedNetworkImageProvider(optimizedUrl);
      } else {
        return CachedNetworkImageProvider(widget.currentImageUrl!);
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: GestureDetector(
        onTap: widget.isEditable ? _pickAndUploadImage : null,
        child: Container(
          height: widget.height,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Background image or placeholder
                _getImageProvider() != null
                    ? Image(
                        image: _getImageProvider()!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholder();
                        },
                      )
                    : _buildPlaceholder(),

                // Gradient overlay for better text readability
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.3),
                      ],
                    ),
                  ),
                ),

                // Loading overlay
                if (_isUploading)
                  Container(
                    color: Colors.black.withOpacity(0.5),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Uploading background image...',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Edit button
                if (widget.isEditable && !_isUploading)
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),

                // Overlay widget (e.g., profile info)
                if (widget.overlayWidget != null)
                  Positioned.fill(
                    child: widget.overlayWidget!,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 8),
          Text(
            widget.isEditable ? 'Tap to add background image' : 'No background image',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
