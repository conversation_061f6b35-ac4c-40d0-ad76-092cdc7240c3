import 'package:permission_handler/permission_handler.dart';
import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';

/// Service to handle camera and photo library permissions
class PermissionService {
  static const String _tag = 'PermissionService';

  /// Check if camera permission is granted
  static Future<bool> isCameraPermissionGranted() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  /// Check if photo library permission is granted
  static Future<bool> isPhotoLibraryPermissionGranted() async {
    final status = await Permission.photos.status;
    return status.isGranted;
  }

  /// Request camera permission
  static Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('$_tag: Error requesting camera permission: $e');
      return false;
    }
  }

  /// Request photo library permission
  static Future<bool> requestPhotoLibraryPermission() async {
    try {
      final status = await Permission.photos.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('$_tag: Error requesting photo library permission: $e');
      return false;
    }
  }

  /// Request both camera and photo library permissions
  static Future<Map<String, bool>> requestImagePermissions() async {
    try {
      final Map<Permission, PermissionStatus> statuses =
          await [Permission.camera, Permission.photos].request();

      return {
        'camera': statuses[Permission.camera]?.isGranted ?? false,
        'photos': statuses[Permission.photos]?.isGranted ?? false,
      };
    } catch (e) {
      debugPrint('$_tag: Error requesting image permissions: $e');
      return {'camera': false, 'photos': false};
    }
  }

  /// Check if permission is permanently denied
  static Future<bool> isCameraPermissionPermanentlyDenied() async {
    final status = await Permission.camera.status;
    return status.isPermanentlyDenied;
  }

  /// Check if photo library permission is permanently denied
  static Future<bool> isPhotoLibraryPermissionPermanentlyDenied() async {
    final status = await Permission.photos.status;
    return status.isPermanentlyDenied;
  }

  /// Show permission dialog and handle the result
  static Future<bool> handleCameraPermission(BuildContext context) async {
    // Check current status
    final status = await Permission.camera.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isPermanentlyDenied) {
      return await _showPermissionDeniedDialog(
        context,
        'Camera Permission Required',
        'Camera access is required to take photos. Please enable it in app settings.',
      );
    }

    // Request permission
    final result = await requestCameraPermission();
    if (!result && context.mounted) {
      _showPermissionRationalDialog(
        context,
        'Camera Permission',
        'Camera access is needed to take photos for your profile.',
      );
    }

    return result;
  }

  /// Show permission dialog and handle the result for photo library
  static Future<bool> handlePhotoLibraryPermission(BuildContext context) async {
    // Check current status
    final status = await Permission.photos.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isPermanentlyDenied) {
      return await _showPermissionDeniedDialog(
        context,
        'Photo Library Permission Required',
        'Photo library access is required to select images. Please enable it in app settings.',
      );
    }

    // Request permission
    final result = await requestPhotoLibraryPermission();
    if (!result && context.mounted) {
      _showPermissionRationalDialog(
        context,
        'Photo Library Permission',
        'Photo library access is needed to select images for your profile.',
      );
    }

    return result;
  }

  /// Show dialog when permission is permanently denied
  static Future<bool> _showPermissionDeniedDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                AppSettings.openAppSettings();
              },
              child: const Text(
                'Open Settings',
                style: TextStyle(
                  color: Color(0xFF1DA1F2),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// Show rationale dialog for permission
  static void _showPermissionRationalDialog(
    BuildContext context,
    String title,
    String message,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(
                  color: Color(0xFF1DA1F2),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Get permission status text for debugging
  static Future<String> getPermissionStatusText() async {
    final cameraStatus = await Permission.camera.status;
    final photosStatus = await Permission.photos.status;

    return 'Camera: ${cameraStatus.name}, Photos: ${photosStatus.name}';
  }
}
