import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:business_app/ui/drawer_items/my_shop/add_product_page.dart';
import 'package:business_app/bloc/product_bloc/product_bloc.dart';
import 'package:business_app/models/category/product_category.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

void main() {
  group('Image Upload Tests', () {
    testWidgets('AddProductPage should display image upload UI', (
      WidgetTester tester,
    ) async {
      // Create a mock ProductBloc
      final productBloc = ProductBloc();

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<ProductBloc>(
            create: (context) => productBloc,
            child: const AddProductPage(),
          ),
        ),
      );

      // Verify that the "Add Images" button is present
      expect(find.text('Add Images'), findsOneWidget);

      // Verify that the image section is present
      expect(find.text('Product Images'), findsOneWidget);

      // Verify that the placeholder text is shown when no images are selected
      expect(find.text('Tap to add product images'), findsOneWidget);
    });

    testWidgets('Image upload button should be present', (
      WidgetTester tester,
    ) async {
      final productBloc = ProductBloc();

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<ProductBloc>(
            create: (context) => productBloc,
            child: const AddProductPage(),
          ),
        ),
      );

      // Find the "Add Images" button
      final addImagesButton = find.text('Add Images');
      expect(addImagesButton, findsOneWidget);

      // Find the TextButton widget
      final textButtonFinder = find.byType(TextButton);
      expect(textButtonFinder, findsWidgets);
    });

    test('Image URL validation should work correctly', () {
      // Test network URL detection
      expect('https://example.com/image.jpg'.startsWith('http'), isTrue);
      expect('http://example.com/image.jpg'.startsWith('http'), isTrue);
      expect('assets/images/test.jpg'.startsWith('http'), isFalse);

      // Test Supabase Storage URL format
      const supabaseUrl =
          'https://supabase.co/storage/v1/object/public/product-images/test.jpg';
      expect(supabaseUrl.startsWith('https://'), isTrue);
    });

    testWidgets('Product card should handle both network and asset images', (
      WidgetTester tester,
    ) async {
      // This test would require creating a mock Product with network URLs
      // and verifying that the ProductCard displays them correctly

      // For now, we'll just verify the widget can be created
      expect(() => const AddProductPage(), returnsNormally);
    });
  });

  group('Image Display Tests', () {
    test('Network image detection should work', () {
      const networkUrl =
          'https://supabase.co/storage/v1/object/public/product-images/image.jpg';
      const assetPath = 'assets/images/product.jpg';

      expect(
        networkUrl.startsWith('http://') || networkUrl.startsWith('https://'),
        isTrue,
      );
      expect(
        assetPath.startsWith('http://') || assetPath.startsWith('https://'),
        isFalse,
      );
    });
  });
}
