import 'package:flutter/material.dart';

class ShopsPage extends StatelessWidget {
  final List<Channel> channels = [
    Channel(
      name: "Johannesburg's Bell Shits",
      followers: "114K",
      avatarUrl:
          "https://cdni.pornpics.com/1280/3/12/34248626/34248626_002_4670.jpg",
    ),
    Channel(
      name: "Look Good, Perfumes💓🥰",
      followers: "18K",
      avatarUrl:
          "https://cdni.pornpics.com/1280/7/56/15789577/15789577_010_4e24.jpg",
    ),
    Channel(
      name: "Cars for Hire",
      followers: "105K",
      avatarUrl:
          "https://cdni.pornpics.com/1280/7/751/78683887/78683887_057_d6a9.jpg",
    ),
    Channel(
      name: "Mibawa Television",
      followers: "145K",
      avatarUrl:
          "https://cdni.pornpics.com/1280/7/792/79500761/79500761_007_5c4e.jpg",
    ),
    Channel(
      name: "Mibawa Television",
      followers: "145K",
      avatarUrl:
          "https://cdni.pornpics.com/1280/7/792/79500761/79500761_007_5c4e.jpg",
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: channels.length + 1,
      separatorBuilder:
          (_, index) =>
              index < channels.length
                  ? Divider(indent: 70, endIndent: 20)
                  : SizedBox(),
      itemBuilder: (context, index) {
        if (index < channels.length) {
          return ChannelTile(channel: channels[index]);
        } else {
          // Last item: Explore more button
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 30),
            child: Center(
              child: OutlinedButton(
                onPressed: () {},
                style: OutlinedButton.styleFrom(
                  shape: StadiumBorder(),
                  side: BorderSide(color: Colors.green.shade600),
                  padding: EdgeInsets.symmetric(horizontal: 40, vertical: 14),
                ),
                child: Text("Explore more", style: TextStyle(fontSize: 16)),
              ),
            ),
          );
        }
      },
    );
  }
}

class ChannelTile extends StatefulWidget {
  final Channel channel;

  const ChannelTile({Key? key, required this.channel}) : super(key: key);

  @override
  State<ChannelTile> createState() => _ChannelTileState();
}

class _ChannelTileState extends State<ChannelTile> {
  bool isFollowing = false;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: NetworkImage(widget.channel.avatarUrl),
        radius: 25,
      ),
      title: Text(
        widget.channel.name,
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      subtitle: Text("${widget.channel.followers} followers"),
      trailing: AnimatedSwitcher(
        duration: Duration(milliseconds: 400),
        transitionBuilder:
            (child, animation) =>
                ScaleTransition(scale: animation, child: child),
        child:
            isFollowing
                ? ElevatedButton.icon(
                  key: ValueKey("Following"),
                  onPressed: () => setState(() => isFollowing = false),
                  icon: Icon(Icons.check, size: 18),
                  label: Text("Following"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade100,
                    foregroundColor: Colors.green.shade800,
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    shape: StadiumBorder(),
                    elevation: 0,
                  ),
                )
                : ElevatedButton(
                  key: ValueKey("Follow"),
                  onPressed: () => setState(() => isFollowing = true),
                  child: Text("Follow"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade100,
                    foregroundColor: Colors.green.shade800,
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    shape: StadiumBorder(),
                    elevation: 0,
                  ),
                ),
      ),
    );
  }
}

class Channel {
  final String name;
  final String followers;
  final String avatarUrl;

  Channel({
    required this.name,
    required this.followers,
    required this.avatarUrl,
  });
}
