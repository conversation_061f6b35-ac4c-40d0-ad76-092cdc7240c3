// ignore_for_file: deprecated_member_use, library_private_types_in_public_api

import 'package:business_app/ui/chat_interface/new_chat_ui.dart';
import 'package:business_app/ui/customer_profiles/card/about_customer.dart';
import 'package:business_app/ui/customer_profiles/components/followers/followings.dart';
import 'package:business_app/ui/customer_profiles/components/pages/products_ui.dart';

import 'package:business_app/ui/home_page.dart';
import 'package:business_app/ui/user_profiles/components/pages/user_posts_page.dart';
import 'package:business_app/ui/user_profiles/components/pages/user_shops_page.dart';
import 'package:flutter/material.dart';

class CustomerProfilePage extends StatefulWidget {
  const CustomerProfilePage({super.key});

  @override
  _CustomerProfilePageState createState() => _CustomerProfilePageState();
}

// Custom Toast Method with Circular Edges
void showCustomToast(BuildContext context, String message) {
  final overlay = Overlay.of(context);
  late OverlayEntry overlayEntry;

  overlayEntry = OverlayEntry(
    builder:
        (context) => Positioned(
          bottom: 100.0,
          left: 20.0,
          right: 20.0,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
              decoration: BoxDecoration(
                color: Colors.blue.shade600,
                borderRadius: BorderRadius.circular(
                  5.0,
                ), // Highly circular edges
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10.0,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                message,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.0,
                  // fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
  );

  overlay.insert(overlayEntry);

  // Remove the toast after 800ms as requested
  Future.delayed(Duration(milliseconds: 2000), () {
    overlayEntry.remove();
  });
}

class _CustomerProfilePageState extends State<CustomerProfilePage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _profilePhotoAnimation;
  final double expandedHeight = 300;
  bool isFollowing = false;
  bool isFavorite = false;
  bool isNotificationsOff = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    _profilePhotoAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        body: NestedScrollView(
          physics: BouncingScrollPhysics(),
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverAppBar(
                //expandedHeight: expandedHeight,
                expandedHeight: 240,
                pinned: true,
                floating: false,
                stretch: true,
                //backgroundColor: Colors.blue,
                /*backgroundColor:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.withOpacity(0.6)
                        : Colors.white.withOpacity(0.6),*/
                //Color(0xFF1DA1F2),
                /*leading: IconButton(
                  icon: Icon(
                    Icons.arrow_back, // color: Colors.white
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => MyHomePage()),
                    );
                  },
                ),*/
                leading: Row(
                  children: [
                    SizedBox(width: 16),
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            Colors.black.withOpacity(0.5),
                            Colors.black.withOpacity(0.1),
                          ],
                          stops: [0.4, 1.0],
                          center: Alignment.center,
                          radius: 1.0,
                        ),
                      ),
                      child: IconButton(
                        icon: Icon(Icons.arrow_back, color: Colors.white),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MyHomePage(),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
                actions: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        isNotificationsOff = !isNotificationsOff;
                      });
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            Colors.black.withOpacity(0.5),
                            Colors.black.withOpacity(0.1),
                          ],
                          stops: [0.4, 1.0],
                          center: Alignment.center,
                          radius: 1.0,
                        ),
                      ),
                      child: Icon(
                        isNotificationsOff
                            ? Icons.notifications_off
                            : Icons.notifications,
                        color: Colors.white,
                        // size: 20,
                      ),
                    ),
                  ),
                  //without gradient
                  // IconButton(
                  //   icon: Icon(
                  //     Icons.search, //color: Colors.white
                  //   ),
                  //   onPressed: () {},
                  // ),
                  SizedBox(width: 8),
                  //without gradient
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        isFavorite = !isFavorite;
                      });
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            Colors.black.withOpacity(0.5),
                            Colors.black.withOpacity(0.1),
                          ],
                          stops: [0.4, 1.0],
                          center: Alignment.center,
                          radius: 1.0,
                        ),
                      ),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: Colors.red,
                        // size: 20,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  //without gradient
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.black.withOpacity(0.5),
                          Colors.black.withOpacity(0.1),
                        ],
                        stops: [0.4, 1.0],
                        center: Alignment.center,
                        radius: 1.0,
                      ),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.more_vert, color: Colors.white),
                      onPressed: () {
                        // Show the MoreAbout bottom sheet
                        showModalBottomSheet(
                          context: context,
                          backgroundColor: Colors.transparent,
                          builder: (BuildContext context) => MoreAbout(),
                        );
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  //without gradient
                  // IconButton(
                  //   icon: Icon(
                  //     Icons.more_vert, //color: Colors.white
                  //   ),
                  //   onPressed: () {
                  //     // Show the MoreAbout bottom sheet
                  //     showModalBottomSheet(
                  //       context: context,
                  //       backgroundColor: Colors.transparent,
                  //       builder: (BuildContext context) => MoreAbout(),
                  //     );
                  //   },
                  // ),
                ],
                flexibleSpace: LayoutBuilder(
                  builder: (context, constraints) {
                    final percent =
                        ((constraints.maxHeight - kToolbarHeight) /
                            (expandedHeight - kToolbarHeight));
                    return FlexibleSpaceBar(
                      stretchModes: [
                        StretchMode.zoomBackground,
                        StretchMode.fadeTitle,
                      ],
                      background: Stack(
                        fit: StackFit.expand,
                        children: [
                          Image.network(
                            'https://cdni.pornpics.com/1280/3/12/34248626/34248626_002_4670.jpg',
                            fit: BoxFit.cover,
                          ),
                          Positioned(
                            left: 16,
                            bottom: 16,
                            child: Opacity(
                              opacity: percent.clamp(0.0, 1.0),
                              child: ScaleTransition(
                                scale: _profilePhotoAnimation,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        GestureDetector(
                                          onTap: () {},
                                          child: Stack(
                                            children: [
                                              const CircleAvatar(
                                                radius: 40,
                                                backgroundColor: Colors.amber,
                                                child: CircleAvatar(
                                                  radius: 38,
                                                  backgroundImage: NetworkImage(
                                                    'https://cdni.pornpics.com/1280/7/725/40669812/40669812_034_2bed.jpg',
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                bottom: 0,
                                                right: 12,
                                                child: Container(
                                                  width: 12,
                                                  height: 12,
                                                  decoration: BoxDecoration(
                                                    color: Colors.green,
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color: Colors.white,
                                                      width: 2,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        // CircleAvatar(
                                        //   radius: 40,
                                        //   backgroundImage: NetworkImage(
                                        //     'https://cdni.pornpics.com/1280/7/725/40669812/40669812_034_2bed.jpg',
                                        //   ),
                                        //   backgroundColor: Colors.blue,
                                        // ),
                                        SizedBox(width: 8),
                                        Column(
                                          children: [
                                            Text(
                                              "31.8M",
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                            // SizedBox(width: 4),
                                            Text(
                                              "Likes",
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.normal,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    //call and message buttons
                                    SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Row(
                                        children: [
                                          SizedBox(width: 10),
                                          OutlinedButton(
                                            onPressed: () {},
                                            style: OutlinedButton.styleFrom(
                                              side: BorderSide(
                                                color: Colors.grey,
                                              ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                              backgroundColor: Colors
                                                  .transparent
                                                  .withOpacity(0.3),
                                              minimumSize: Size(
                                                120,
                                                40,
                                              ), // Width and height
                                            ),

                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.call,
                                                  size: 20,
                                                  color: Colors.blue,
                                                ),
                                                SizedBox(width: 4),
                                                Text(
                                                  'Call',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(width: 24),
                                          OutlinedButton(
                                            onPressed: () {
                                              messageBottomSheet(context);
                                            },

                                            style: OutlinedButton.styleFrom(
                                              side: BorderSide(
                                                color: Colors.grey,
                                              ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                              backgroundColor: Colors
                                                  .transparent
                                                  .withOpacity(0.3),
                                              minimumSize: Size(
                                                120,
                                                40,
                                              ), // Width and height
                                            ),
                                            child: Text(
                                              'Message',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              SliverToBoxAdapter(child: _buildProfileDetails()),
              SliverPersistentHeader(
                pinned: true,
                delegate: _SliverTabBarDelegate(
                  TabBar(
                    //labelColor: Colors.blue,
                    labelColor: Color(0xFF1DA1F2),
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: Color(0xFF1DA1F2),
                    indicatorWeight: 5,
                    labelStyle: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontWeight: FontWeight.normal,
                      fontSize: 16,
                    ),
                    tabs: const [
                      Tab(text: 'Products'),
                      Tab(text: 'Shops'),
                      Tab(text: 'Posts'),
                    ],
                  ),
                ),
              ),
            ];
          },
          body: TabBarView(
            physics: BouncingScrollPhysics(),
            children: [
              // _buildTabContent('Products'),
              // _buildTabContent('Groups'),
              // _buildTabContent('Posts'),
              //ProductsPage(),
              ProductsUi(),
              UserShopsPage(),
              UserPostsPage(),

              //ShopsPage(),
              //PostsPage(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileDetails() {
    return FadeTransition(
      opacity: _profilePhotoAnimation,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              'Christina Lungu',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                          SizedBox(width: 4),
                          Icon(Icons.verified, color: Colors.blue, size: 20),
                        ],
                      ),
                      Text(
                        '@chris_lungu',
                        style: TextStyle(color: Colors.grey, fontSize: 16),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 10),
                /* OutlinedButton(
                  onPressed: () {},
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    backgroundColor: Colors.white.withOpacity(0.1),
                  ),
                  child: Text(
                    'Follow',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),*/
                AnimatedSwitcher(
                  duration: Duration(milliseconds: 400),
                  transitionBuilder:
                      (child, animation) =>
                          ScaleTransition(scale: animation, child: child),
                  child:
                      isFollowing
                          ? ElevatedButton.icon(
                            key: ValueKey("Following"),
                            onPressed: () {
                              setState(() => isFollowing = false);
                              showCustomToast(
                                context,
                                "Okay, you've unfollowed Christina Lungu",
                              );
                            },
                            icon: Icon(Icons.check, size: 18),
                            label: Text(
                              "Following",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green.shade100,
                              foregroundColor: Colors.green.shade800,
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              shape: StadiumBorder(),
                              elevation: 0,
                            ),
                          )
                          : ElevatedButton(
                            key: ValueKey("Follow"),
                            onPressed: () {
                              setState(() => isFollowing = true);
                              showCustomToast(
                                context,
                                "Okay, you're following Christina Lungu",
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green.shade100,
                              foregroundColor: Colors.green.shade800,
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              shape: StadiumBorder(),
                              elevation: 0,
                            ),
                            child: const Text(
                              "Follow",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                ),
              ],
            ),

            SizedBox(height: 12),
            Text(
              'Beauty, cosmetics, & personal care, Education, Event Planner\nMother of 3 ❤️',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 6),

            // Following and Customers
            Wrap(
              spacing: 8,
              runSpacing: 4,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => Followings()),
                      );
                    },
                    child: Row(
                      children: [
                        Text(
                          '36',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Following',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                        SizedBox(width: 16),
                        Text(
                          '860K',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Customers',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            //status on/off
            const SizedBox(height: 8),
            Divider(),
            Row(
              children: [
                Icon(Icons.call, size: 18),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '+265 9852 00 100',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.location_on, size: 18, color: Colors.blue),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    'Area 25',
                    style: TextStyle(
                      fontSize: 16,

                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            // Bio
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, size: 18),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    'Open now',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            Divider(),
            /* Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              elevation: 4,
              //margin: EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.location_on,
                                size: 18,
                                color: Colors.blue,
                              ),
                              SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  'Open now',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              isFavorite = !isFavorite;
                            });
                          },
                          child: Row(
                            children: [
                              Icon(
                                isFavorite ? Icons.favorite : Icons.favorite_border,
                                size: 18,
                                color: Colors.red,
                              ),
                              SizedBox(width: 4),
                              Text(
                                isFavorite ? 'Liked business' : 'Like business',
                                style: TextStyle(
                                  fontSize: 16,
                                  //color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    //SizedBox(height: 8),
                  ],
                ),
              ),
            ),*/
          ],
        ),
      ),
    );
  }
}

/// For pinning the TabBar
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _SliverTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      //color: Colors.transparent,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}

//message bottom sheet
// Reusable function to show the custom bottom sheet
void messageBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    //backgroundColor: Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
    ),
    builder: (BuildContext context) {
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),
            ),
            const SizedBox(height: 20),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => ChatInterfaceUI()),
                );
              },
              child: const Text(
                'Direct Message',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 20),
            const Text('Email', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 20),
            Row(
              children: [
                Text('Visit', style: TextStyle(fontSize: 16)),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    "www.chris-fashions.com",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.blue,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      );
    },
  );
}

// appbar more options bottom sheet
// Stateful class for the bottom sheet
class MoreAbout extends StatefulWidget {
  const MoreAbout({super.key});

  @override
  _MoreAboutState createState() => _MoreAboutState();
}

class _MoreAboutState extends State<MoreAbout> {
  bool _isHidden = false; // Example state for the switch

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: BouncingScrollPhysics(),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          // color: Colors.black.withOpacity(0.9),
          // color: Theme.of(context).colorScheme.surface.withOpacity(0.94),
          color: Colors.black.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top handle bar
            Container(
              margin: EdgeInsets.symmetric(vertical: 10),
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[700],
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            // Bottom sheet options
            _buildOption('Block', Colors.red, () {
              Navigator.pop(context);
              // Add block action
            }),
            _buildOption('Report Business', Colors.red, () {
              Navigator.pop(context);
              // Add report action
            }),
            // _buildOption('Add', Colors.white, () {
            //   Navigator.pop(context);
            //   // Add add action
            // }),
            _buildOption('Unfollow', Colors.white, () {
              Navigator.pop(context);
              // Add add action
            }),
            _buildOption(
              'Hide this Content',
              Colors.white,
              () {},
              trailing: Switch(
                value: _isHidden,
                onChanged: (value) {
                  setState(() {
                    _isHidden = value;
                  });
                  // Add hide content action
                },
                activeColor: Colors.white,
                inactiveThumbColor: Colors.grey,
                inactiveTrackColor: Colors.grey[800],
              ),
            ),
            _buildOption('About', Colors.white, () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AboutCustomer()),
              );
            }),
            //original code
            /* _buildOption(
              'Send Profile to...',
              Colors.white,
              () {
                Navigator.pop(context);
                // Add send profile action
              },
              trailing: Icon(Icons.send, color: Colors.blue),
            ),
            */
            _buildOption(
              'Send Profile to...',
              Colors.white,
              () {
                Navigator.pop(context);
                // Add send profile action
              },
              trailing: Icon(Icons.send, color: Colors.blue),
            ),
            _buildOption(
              'Copy Business URL',
              //trailing: Icon(Icons.send, color: Colors.blue),
              Colors.white,
              () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    content: Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.link, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'Link copied',
                            style: TextStyle(color: Colors.white, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                    duration: Duration(seconds: 3),
                  ),
                );
                // Add share URL action
              },
            ),
            /* original code
            _buildOption('Copy Business URL', Colors.white, () {
              Navigator.pop(context);
              // Add share URL action
            }),
            */
            // Done button
            // Padding(
            //   padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            //   child: TextButton(
            //     onPressed: () {
            //       Navigator.pop(context);
            //     },
            //     child: Text(
            //       'Done',
            //       style: TextStyle(
            //         color: Colors.white,
            //         fontSize: 16,
            //         fontWeight: FontWeight.bold,
            //       ),
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  // Helper method to build each option
  Widget _buildOption(
    String text,
    Color color,
    VoidCallback onTap, {
    Widget? trailing,
  }) {
    return ListTile(
      title: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: trailing,
      onTap: onTap,
    );
  }
}
