import 'package:equatable/equatable.dart';

enum BusinessStatsStatus { initial, loading, loaded, error, exporting }

class StatCard extends Equatable {
  final String id;
  final String title;
  final String value;
  final String subtitle;
  final double percentage;
  final bool isPositive;
  final String color;

  const StatCard({
    required this.id,
    required this.title,
    required this.value,
    required this.subtitle,
    this.percentage = 0.0,
    this.isPositive = true,
    required this.color,
  });

  StatCard copyWith({
    String? id,
    String? title,
    String? value,
    String? subtitle,
    double? percentage,
    bool? isPositive,
    String? color,
  }) {
    return StatCard(
      id: id ?? this.id,
      title: title ?? this.title,
      value: value ?? this.value,
      subtitle: subtitle ?? this.subtitle,
      percentage: percentage ?? this.percentage,
      isPositive: isPositive ?? this.isPositive,
      color: color ?? this.color,
    );
  }

  @override
  List<Object> get props => [id, title, value, subtitle, percentage, isPositive, color];
}

class ChartData extends Equatable {
  final String label;
  final double value;
  final DateTime date;

  const ChartData({
    required this.label,
    required this.value,
    required this.date,
  });

  @override
  List<Object> get props => [label, value, date];
}

class BusinessInsight extends Equatable {
  final String id;
  final String title;
  final String description;
  final String type; // 'suggestion', 'warning', 'info'
  final String? actionText;

  const BusinessInsight({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.actionText,
  });

  @override
  List<Object?> get props => [id, title, description, type, actionText];
}

class BusinessStatsState extends Equatable {
  final BusinessStatsStatus status;
  final String selectedTimeRange;
  final String selectedStatsType;
  final List<StatCard> statCards;
  final List<ChartData> salesData;
  final List<ChartData> customerData;
  final List<ChartData> productData;
  final List<BusinessInsight> insights;
  final Set<String> expandedCards;
  final String? errorMessage;

  const BusinessStatsState({
    this.status = BusinessStatsStatus.initial,
    this.selectedTimeRange = 'month',
    this.selectedStatsType = 'overview',
    this.statCards = const [],
    this.salesData = const [],
    this.customerData = const [],
    this.productData = const [],
    this.insights = const [],
    this.expandedCards = const {},
    this.errorMessage,
  });

  BusinessStatsState copyWith({
    BusinessStatsStatus? status,
    String? selectedTimeRange,
    String? selectedStatsType,
    List<StatCard>? statCards,
    List<ChartData>? salesData,
    List<ChartData>? customerData,
    List<ChartData>? productData,
    List<BusinessInsight>? insights,
    Set<String>? expandedCards,
    String? errorMessage,
  }) {
    return BusinessStatsState(
      status: status ?? this.status,
      selectedTimeRange: selectedTimeRange ?? this.selectedTimeRange,
      selectedStatsType: selectedStatsType ?? this.selectedStatsType,
      statCards: statCards ?? this.statCards,
      salesData: salesData ?? this.salesData,
      customerData: customerData ?? this.customerData,
      productData: productData ?? this.productData,
      insights: insights ?? this.insights,
      expandedCards: expandedCards ?? this.expandedCards,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        selectedTimeRange,
        selectedStatsType,
        statCards,
        salesData,
        customerData,
        productData,
        insights,
        expandedCards,
        errorMessage,
      ];
}
