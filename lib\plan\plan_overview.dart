import 'package:business_app/const/assets.dart';
import 'package:flutter/material.dart';

class PlanOverviewPage extends StatelessWidget {
  const PlanOverviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.black,
      appBar: AppBar(
        // backgroundColor: Colors.black,
        // foreground,
        title: const Text(
          "Plan Overview",
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        elevation: 0,
        leading: const BackButton(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Top Plan Card
            Card(
              // color: const Color(0xFF121212),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //const Icon(Icons.music_note, , size: 30),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipOval(
                          child: Image.asset(
                            Assets.trademateLogo,
                            width: 24.0,
                            height: 24.0,
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text("Premium", style: TextStyle(fontSize: 16)),
                      ],
                    ),

                    const SizedBox(height: 16),
                    Row(
                      children: const [
                        Icon(Icons.person_outline),
                        SizedBox(width: 8),
                        Text("Offer • ", style: TextStyle()),
                        Text("Active", style: TextStyle(color: Colors.green)),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(Icons.workspace_premium_outlined),
                        SizedBox(width: 8),
                        Text("Monthly", style: TextStyle()),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: const [
                        Icon(Icons.access_time),
                        SizedBox(width: 8),
                        Text("Offer expires 10/11/25", style: TextStyle()),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Plan Includes Card
            Card(
              // color: const Color(0xFF121212),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 20,
                  horizontal: 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Plan includes",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: 16),
                    ..._planFeatures.map(
                      (feature) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.check_circle_outline,
                              color: Colors.greenAccent,
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                feature,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// List of features included in the plan
final List<String> _planFeatures = [
  "Ad-free music listening",
  "Download music for offline playback",
  "High-quality audio streaming",
  "Unlimited skips",
  "Listen on multiple devices",
  "No interruptions while playing",
];
