import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/theme_bloc/theme_bloc.dart';
import 'package:business_app/bloc/theme_bloc/theme_state.dart';
import 'package:business_app/ui/splash/splash_screen.dart';
import 'package:business_app/widgets/connectivity/connectivity_wrapper.dart';
import 'package:flutter/material.dart';

// The main rout of my app Trade-Mate

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // Create themes once as static final to avoid recreation
  static final ThemeData _lightTheme = _buildLightTheme();
  static final ThemeData _darkTheme = _buildDarkTheme();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      // Optimized buildWhen - rebuild only when theme actually changes
      buildWhen:
          (previous, current) =>
              previous.mode != current.mode ||
              previous.themeData != current.themeData,
      builder: (context, state) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: _lightTheme,
          darkTheme: _darkTheme,
          themeMode: _getThemeMode(state.mode),
          // Reduced animation duration for faster response
          themeAnimationDuration: const Duration(milliseconds: 100),
          themeAnimationCurve: Curves.fastOutSlowIn,
          home: const ConnectivityWrapper(child: SplashScreen()),
        );
      },
    );
  }

  ThemeMode _getThemeMode(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.system:
        return ThemeMode.system;
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
    }
  }

  static ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: const Color(0xFF1DA1F2),
      scaffoldBackgroundColor: Colors.white,
      colorScheme: const ColorScheme.light(
        primary: Color(0xFF1DA1F2),
        secondary: Colors.blueAccent,
        surface: Colors.white,
      ),
      textTheme: ThemeData.light().textTheme.apply(
        fontFamily: 'SF-Pro-Display',
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        selectedItemColor: Colors.black,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
    );
  }

  static ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: const Color(0xFF1DA1F2),
      scaffoldBackgroundColor: const Color(0xFF121212),
      colorScheme: const ColorScheme.dark(
        primary: Color(0xFF1DA1F2),
        secondary: Colors.blueAccent,
        surface: Color(0xFF1E1E1E),
      ),
      textTheme: ThemeData.dark().textTheme.apply(fontFamily: 'SF-Pro-Display'),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.grey,
        backgroundColor: Color(0xFF1E1E1E),
        elevation: 8,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1E1E1E),
        foregroundColor: Colors.white,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
    );
  }
}

//bloc ends
