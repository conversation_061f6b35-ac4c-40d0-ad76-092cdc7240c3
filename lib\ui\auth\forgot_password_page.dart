import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/ui/auth/components/auth_header.dart';
import 'package:business_app/ui/auth/components/auth_button.dart';
import 'package:business_app/ui/auth/components/auth_text_field.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _identifierController = TextEditingController();
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _showCodeField = false;
  bool _showPasswordField = false;
  String? _identifierError;
  String? _codeError;
  String? _passwordError;

  // Resend functionality
  bool _isResending = false;
  bool _isVerifying = false; // Prevent multiple verification attempts
  String? _resolvedEmail; // Store the resolved email for resend

  @override
  void dispose() {
    _identifierController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _onSendCode() {
    final identifier = _identifierController.text.trim();

    if (identifier.isEmpty) {
      setState(() {
        _identifierError = 'Please enter your email, phone, or username';
      });
      return;
    }

    // Enhanced validation for different identifier types
    if (!_isValidIdentifier(identifier)) {
      setState(() {
        if (identifier.contains('@')) {
          _identifierError = 'Please enter a valid email address';
        } else if (RegExp(r'^\+?[0-9]').hasMatch(identifier)) {
          _identifierError = _getPhoneValidationError(identifier);
        } else {
          _identifierError =
              'Username must be 3-30 characters (letters, numbers, underscore)';
        }
      });
      return;
    }

    context.read<AuthBloc>().add(
      ForgotPasswordRequested(emailOrPhone: identifier),
    );
  }

  void _onVerifyCode() {
    // Prevent multiple simultaneous verification attempts
    if (_isVerifying) return;

    final code = _codeController.text.trim();

    // Validate code format more strictly
    if (code.isEmpty) {
      setState(() {
        _codeError = 'Please enter the verification code';
      });
      return;
    }

    if (code.length != 6) {
      setState(() {
        _codeError = 'Please enter the 6-digit code';
      });
      return;
    }

    // Ensure code contains only numbers
    if (!RegExp(r'^\d{6}$').hasMatch(code)) {
      setState(() {
        _codeError = 'Code must contain only numbers';
      });
      return;
    }

    setState(() {
      _isVerifying = true;
    });

    context.read<AuthBloc>().add(PasswordResetCodeSubmitted(code: code));
  }

  void _onResetPassword() {
    final password = _passwordController.text;

    if (password.length < 8) {
      setState(() {
        _passwordError = 'Password must be at least 8 characters';
      });
      return;
    }

    context.read<AuthBloc>().add(NewPasswordSubmitted(newPassword: password));
  }

  void _onResendCode() async {
    if (_isResending || _resolvedEmail == null) return;

    setState(() {
      _isResending = true;
    });

    context.read<AuthBloc>().add(
      PasswordResetOTPResendRequested(email: _resolvedEmail!),
    );

    // Reset the resending state after a short delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    });
  }

  bool _isValidIdentifier(String identifier) {
    if (identifier.trim().isEmpty) return false;

    // Email validation
    if (identifier.contains('@')) {
      return RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      ).hasMatch(identifier);
    }

    // Phone validation (enhanced for international numbers)
    if (RegExp(r'^\+?[0-9]').hasMatch(identifier)) {
      return _isValidPhoneNumber(identifier);
    }

    // Username validation
    return RegExp(r'^[a-zA-Z0-9_]{3,30}$').hasMatch(identifier);
  }

  bool _isValidPhoneNumber(String phoneNumber) {
    // Handle empty or null input
    if (phoneNumber.trim().isEmpty) return false;

    try {
      // Use phone_numbers_parser for professional validation
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      // Check if the phone number is valid
      final isValid = parsedNumber.isValid();

      // For forgot password, we accept both mobile and fixed line numbers
      return isValid;
    } catch (e) {
      // If parsing fails, try fallback validation for local numbers
      final digitsOnly = phoneNumber.replaceAll(RegExp(r'\D'), '');

      // Basic fallback: must have at least 10 digits and not exceed 15
      if (digitsOnly.length >= 10 && digitsOnly.length <= 15) {
        return true;
      }

      return false;
    }
  }

  String _getPhoneValidationError(String phoneNumber) {
    try {
      final parsedNumber = PhoneNumber.parse(phoneNumber);

      if (!parsedNumber.isValid()) {
        // Try to provide more specific error messages
        if (phoneNumber.length < 8) {
          return 'Phone number is too short';
        } else if (phoneNumber.length > 15) {
          return 'Phone number is too long';
        } else {
          return 'Please enter a valid phone number (e.g., +265 123 456 789)';
        }
      }

      return 'Invalid phone number format';
    } catch (e) {
      // Handle parsing errors
      if (!phoneNumber.startsWith('+')) {
        return 'Phone number must include country code (e.g., +265)';
      }
      return 'Please enter a valid phone number with country code';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          // Reset verification state on any state change (except when loading)
          if (state.status != AuthStatus.loading && _isVerifying) {
            setState(() {
              _isVerifying = false;
            });
          }

          // Handle success messages first
          if (state.successMessage != null) {
            if (state.successMessage!.contains('Reset code sent')) {
              setState(() {
                _showCodeField = true;
                _showPasswordField = false; // Ensure password field is hidden
                _identifierError = null;
                _codeError = null;
                _passwordError = null;
                // Store the resolved email for resend functionality
                _resolvedEmail = state.resolvedEmail;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('6-digit reset code sent to your email'),
                  backgroundColor: Colors.blue,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            } else if (state.successMessage!.contains(
              'Reset code sent again',
            )) {
              // Handle resend success
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Reset code sent again to your email'),
                  backgroundColor: Colors.blue,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            } else if (state.successMessage!.contains('Password reset')) {
              // Navigate back to login
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text(
                    'Password reset successfully! You can now sign in.',
                  ),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            }
          }
          // Handle OTP verification success (when resetCode is set but no success message)
          else if (state.resetCode != null &&
              state.status != AuthStatus.error &&
              state.status != AuthStatus.loading &&
              state.successMessage == null) {
            // ONLY show password field when:
            // 1. OTP was successfully verified (resetCode is set)
            // 2. No error occurred
            // 3. Not currently loading
            // 4. No success message (to avoid conflicts with success message handling)
            setState(() {
              _showPasswordField = true;
              _codeError = null;
              _isVerifying = false; // Reset verification state
            });

            // Show success message for OTP verification
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  'Code verified! Now enter your new password.',
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(seconds: 2),
              ),
            );
          }
          // Handle errors
          else if (state.status == AuthStatus.error) {
            // ALWAYS clear password field on any error
            setState(() {
              _showPasswordField = false;
              _passwordError = null;
              _isVerifying = false; // Reset verification state on error
            });

            // Handle specific error messages
            String errorMessage = state.errorMessage ?? 'An error occurred';
            Color backgroundColor = Colors.red;

            if (errorMessage.contains('OTP expired')) {
              errorMessage = 'OTP expired';
              backgroundColor = Colors.orange;
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: backgroundColor,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),

                        // Header
                        const AuthForgotPasswordHeader(),

                        const SizedBox(height: 40),

                        // Identifier field (supports email, phone, username)
                        AuthTextField(
                          controller: _identifierController,
                          hintText: 'Email, phone, or username',
                          keyboardType: TextInputType.text,
                          errorText: _identifierError,
                          enabled: !_showCodeField,
                          onChanged: (value) {
                            if (_identifierError != null) {
                              setState(() {
                                _identifierError = null;
                              });
                            }
                          },
                        ),

                        const SizedBox(height: 20),

                        // OTP Code field (animated)
                        if (_showCodeField) ...[
                          AuthCodeField(
                            controller: _codeController,
                            hintText: 'Enter 6-digit reset code from email',
                            errorText: _codeError,
                            autofocus: true,
                            onChanged: (value) {
                              if (_codeError != null) {
                                setState(() {
                                  _codeError = null;
                                });
                              }
                            },
                          ),
                          const SizedBox(height: 8),

                          // OTP timing guidance
                          Text(
                            'Code expires in 60 seconds. Enter it quickly!',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),

                          // Resend code link
                          Center(
                            child: Column(
                              children: [
                                Text(
                                  'Didn\'t receive the code?',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextButton(
                                  onPressed:
                                      _isResending ? null : _onResendCode,
                                  child: Text(
                                    _isResending ? 'Sending...' : 'Resend Code',
                                    style: TextStyle(
                                      color:
                                          _isResending
                                              ? Colors.grey[500]
                                              : const Color(0xFF1DA1F2),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                        ],

                        // Password field (animated)
                        if (_showPasswordField) ...[
                          AuthPasswordField(
                            controller: _passwordController,
                            hintText: 'New password',
                            errorText: _passwordError,
                            autofocus: true,
                            onChanged: (value) {
                              if (_passwordError != null) {
                                setState(() {
                                  _passwordError = null;
                                });
                              }
                            },
                          ),
                          const SizedBox(height: 20),
                        ],

                        // Action button
                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            String buttonText = 'Search';
                            VoidCallback? onPressed = _onSendCode;
                            bool isLoading = state.isLoading || _isVerifying;

                            if (_showPasswordField) {
                              buttonText = 'Reset password';
                              onPressed = _onResetPassword;
                            } else if (_showCodeField) {
                              buttonText =
                                  _isVerifying ? 'Verifying...' : 'Verify';
                              onPressed = _isVerifying ? null : _onVerifyCode;
                            }

                            return AuthPrimaryButton(
                              text: buttonText,
                              isLoading: isLoading,
                              onPressed: onPressed,
                            );
                          },
                        ),

                        const Spacer(),

                        // Back to login link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Remember your password? ',
                              style: TextStyle(
                                fontSize: 15,
                                color: Colors.grey[600],
                              ),
                            ),
                            AuthTextButton(
                              text: 'Sign in',
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
