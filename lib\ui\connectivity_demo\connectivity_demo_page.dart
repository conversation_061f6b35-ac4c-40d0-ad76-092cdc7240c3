import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/connectivity_bloc/connectivity_bloc.dart';
import 'package:business_app/widgets/connectivity/connectivity_indicator.dart';
import 'package:business_app/widgets/connectivity/connectivity_wrapper.dart';
import 'package:business_app/utils/connectivity_utils.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Demo page showcasing all connectivity features
/// This demonstrates how big apps handle offline scenarios
class ConnectivityDemoPage extends StatefulWidget {
  const ConnectivityDemoPage({super.key});

  @override
  State<ConnectivityDemoPage> createState() => _ConnectivityDemoPageState();
}

class _ConnectivityDemoPageState extends State<ConnectivityDemoPage>
    with ConnectivityAware {
  bool _showMessages = true;
  String _lastAction = 'None';

  @override
  void onConnectivityChanged(ConnectivityState state) {
    setState(() {
      if (state.isConnected) {
        _lastAction = 'Connection restored - ${state.connectionTypeString}';
      } else if (state.hasLimitedConnection) {
        _lastAction = 'Limited connection - ${state.connectionTypeString}';
      } else {
        _lastAction = 'Connection lost';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      showBanner: true,
      showSnackBar: _showMessages,
      onConnectivityRestored: () {
        setState(() => _lastAction = 'Callback: Connection restored');
      },
      onConnectivityLost: () {
        setState(() => _lastAction = 'Callback: Connection lost');
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Connectivity Demo'),
          actions: const [
            Padding(
              padding: EdgeInsets.only(right: 16.0),
              child: DetailedConnectivityIndicator(),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusSection(),
              const SizedBox(height: 24),
              _buildIndicatorsSection(),
              const SizedBox(height: 24),
              _buildActionsSection(),
              const SizedBox(height: 24),
              _buildSettingsSection(),
              const SizedBox(height: 24),
              _buildTestSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Status',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 12),
                _buildStatusRow('Connection', state.connectionTypeString),
                _buildStatusRow('Internet Access', state.isConnected ? 'Yes' : 'No'),
                _buildStatusRow('Status', _getStatusText(state)),
                _buildStatusRow('Last Checked', _formatTime(state.lastChecked)),
                _buildStatusRow('Last Action', _lastAction),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildIndicatorsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Connectivity Indicators',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildIndicatorDemo('Compact Indicator', const CompactConnectivityIndicator()),
            const SizedBox(height: 12),
            _buildIndicatorDemo('Detailed Indicator', const DetailedConnectivityIndicator()),
            const SizedBox(height: 12),
            _buildIndicatorDemo('Dot Indicator', const ConnectivityDotIndicator()),
            const SizedBox(height: 12),
            _buildIndicatorDemo('Status Bar', const StatusBarConnectivityIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildIndicatorDemo(String label, Widget indicator) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        indicator,
      ],
    );
  }

  Widget _buildActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _checkConnectivity(),
                  icon: const Icon(LucideIcons.refreshCw, size: 16),
                  label: const Text('Check Connection'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _retryConnection(),
                  icon: const Icon(LucideIcons.rotateCcw, size: 16),
                  label: const Text('Retry'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _openWiFiSettings(),
                  icon: const Icon(LucideIcons.wifi, size: 16),
                  label: const Text('WiFi Settings'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _openMobileDataSettings(),
                  icon: const Icon(LucideIcons.smartphone, size: 16),
                  label: const Text('Mobile Data'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Demo Settings',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Show Offline Messages'),
              subtitle: const Text('Enable/disable SnackBar notifications'),
              value: _showMessages,
              onChanged: (value) => setState(() => _showMessages = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Network Operations',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _testNetworkOperation(),
              icon: const Icon(LucideIcons.globe, size: 16),
              label: const Text('Test API Call (Online Only)'),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: () => _testWithRetry(),
              icon: const Icon(LucideIcons.repeat, size: 16),
              label: const Text('Test with Auto-Retry'),
            ),
          ],
        ),
      ),
    );
  }

  String _getStatusText(ConnectivityState state) {
    if (state.isRetrying) return 'Checking...';
    if (state.isConnected) return 'Online';
    if (state.hasLimitedConnection) return 'Limited';
    if (state.hasError) return 'Error';
    return 'Offline';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:${time.second.toString().padLeft(2, '0')}';
  }

  void _checkConnectivity() {
    context.read<ConnectivityBloc>().add(const ConnectivityCheckRequested());
    setState(() => _lastAction = 'Manual check requested');
  }

  void _retryConnection() {
    context.read<ConnectivityBloc>().add(const ConnectivityRetryRequested());
    setState(() => _lastAction = 'Retry requested');
  }

  void _openWiFiSettings() {
    context.read<ConnectivityBloc>().add(
      const ConnectivityOpenSettingsRequested(
        settingsType: ConnectivitySettingsType.wifi,
      ),
    );
    setState(() => _lastAction = 'WiFi settings opened');
  }

  void _openMobileDataSettings() {
    context.read<ConnectivityBloc>().add(
      const ConnectivityOpenSettingsRequested(
        settingsType: ConnectivitySettingsType.mobileData,
      ),
    );
    setState(() => _lastAction = 'Mobile data settings opened');
  }

  Future<void> _testNetworkOperation() async {
    final result = await executeIfOnline(
      () async {
        // Simulate API call
        await Future.delayed(const Duration(seconds: 1));
        return 'API call successful!';
      },
      onOffline: () {
        setState(() => _lastAction = 'API call blocked - offline');
      },
    );

    if (result != null) {
      setState(() => _lastAction = result);
    }
  }

  Future<void> _testWithRetry() async {
    setState(() => _lastAction = 'Testing with retry...');
    
    final result = await ConnectivityUtils.executeWithConnectivityCheck(
      context,
      () async {
        // Simulate API call that might fail
        await Future.delayed(const Duration(seconds: 1));
        return 'Retry test successful!';
      },
      maxRetries: 3,
      showMessages: true,
    );

    setState(() => _lastAction = result ?? 'Retry test failed');
  }
}
