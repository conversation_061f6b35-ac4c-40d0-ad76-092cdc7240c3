import 'package:equatable/equatable.dart';

enum NewMessageStatus { initial, loading, loaded, error, searching }

class Contact extends Equatable {
  final String id;
  final String name;
  final String phoneNumber;
  final String? profileImage;
  final bool isOnline;
  final DateTime? lastSeen;
  final bool isSelected;

  const Contact({
    required this.id,
    required this.name,
    required this.phoneNumber,
    this.profileImage,
    this.isOnline = false,
    this.lastSeen,
    this.isSelected = false,
  });

  Contact copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? profileImage,
    bool? isOnline,
    DateTime? lastSeen,
    bool? isSelected,
  }) {
    return Contact(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImage: profileImage ?? this.profileImage,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        phoneNumber,
        profileImage,
        isOnline,
        lastSeen,
        isSelected,
      ];
}

class NewMessageState extends Equatable {
  final NewMessageStatus status;
  final List<Contact> contacts;
  final List<Contact> suggestedContacts;
  final List<Contact> recentContacts;
  final List<Contact> filteredContacts;
  final String searchQuery;
  final int currentPageIndex;
  final Set<String> selectedContactIds;
  final String? errorMessage;
  final bool isSearching;

  const NewMessageState({
    this.status = NewMessageStatus.initial,
    this.contacts = const [],
    this.suggestedContacts = const [],
    this.recentContacts = const [],
    this.filteredContacts = const [],
    this.searchQuery = '',
    this.currentPageIndex = 0,
    this.selectedContactIds = const {},
    this.errorMessage,
    this.isSearching = false,
  });

  NewMessageState copyWith({
    NewMessageStatus? status,
    List<Contact>? contacts,
    List<Contact>? suggestedContacts,
    List<Contact>? recentContacts,
    List<Contact>? filteredContacts,
    String? searchQuery,
    int? currentPageIndex,
    Set<String>? selectedContactIds,
    String? errorMessage,
    bool? isSearching,
  }) {
    return NewMessageState(
      status: status ?? this.status,
      contacts: contacts ?? this.contacts,
      suggestedContacts: suggestedContacts ?? this.suggestedContacts,
      recentContacts: recentContacts ?? this.recentContacts,
      filteredContacts: filteredContacts ?? this.filteredContacts,
      searchQuery: searchQuery ?? this.searchQuery,
      currentPageIndex: currentPageIndex ?? this.currentPageIndex,
      selectedContactIds: selectedContactIds ?? this.selectedContactIds,
      errorMessage: errorMessage ?? this.errorMessage,
      isSearching: isSearching ?? this.isSearching,
    );
  }

  @override
  List<Object?> get props => [
        status,
        contacts,
        suggestedContacts,
        recentContacts,
        filteredContacts,
        searchQuery,
        currentPageIndex,
        selectedContactIds,
        errorMessage,
        isSearching,
      ];
}
