import 'package:equatable/equatable.dart';

/// Result model for image upload operations
class ImageUploadResult extends Equatable {
  final bool isSuccess;
  final String? imageUrl;
  final String? fileName;
  final String? error;
  final int? fileSizeBytes;

  const ImageUploadResult({
    required this.isSuccess,
    this.imageUrl,
    this.fileName,
    this.error,
    this.fileSizeBytes,
  });

  /// Success result
  factory ImageUploadResult.success({
    required String imageUrl,
    required String fileName,
    int? fileSizeBytes,
  }) {
    return ImageUploadResult(
      isSuccess: true,
      imageUrl: imageUrl,
      fileName: fileName,
      fileSizeBytes: fileSizeBytes,
    );
  }

  /// Error result
  factory ImageUploadResult.error(String error) {
    return ImageUploadResult(
      isSuccess: false,
      error: error,
    );
  }

  @override
  List<Object?> get props => [
        isSuccess,
        imageUrl,
        fileName,
        error,
        fileSizeBytes,
      ];

  @override
  String toString() {
    if (isSuccess) {
      return 'ImageUploadResult.success(url: $imageUrl, fileName: $fileName, size: $fileSizeBytes bytes)';
    } else {
      return 'ImageUploadResult.error($error)';
    }
  }
}
