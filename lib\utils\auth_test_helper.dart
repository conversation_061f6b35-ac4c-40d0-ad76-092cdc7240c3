import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../services/supabase_service.dart';
import '../services/session_service.dart';
import '../services/error_handler_service.dart';

/// Test helper utility for verifying authentication flows
/// Only available in debug mode for testing purposes
class AuthTestHelper {
  static const String _logTag = '🧪 AuthTestHelper';

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  /// Test user existence checking functionality
  static Future<TestResult> testUserExistenceCheck({
    required String email,
    required String phone,
  }) async {
    if (!kDebugMode) {
      return TestResult.error('Test helper only available in debug mode');
    }

    _logger.i('$_logTag Testing user existence checking...');

    try {
      // Test 1: Check non-existent email
      _logger.i('$_logTag Step 1: Testing non-existent email check...');
      final nonExistentEmail =
          'nonexistent_${DateTime.now().millisecondsSinceEpoch}@test.com';
      final emailExists = await AuthService.checkUserExistsByEmail(
        nonExistentEmail,
      );

      if (emailExists) {
        return TestResult.error(
          'Non-existent email check failed: should return false',
        );
      }
      _logger.i('$_logTag ✅ Non-existent email check passed');

      // Test 2: Check non-existent phone
      _logger.i('$_logTag Step 2: Testing non-existent phone check...');
      final nonExistentPhone = '+265${DateTime.now().millisecondsSinceEpoch}';
      final phoneExists = await AuthService.checkUserExistsByPhone(
        nonExistentPhone,
      );

      if (phoneExists) {
        return TestResult.error(
          'Non-existent phone check failed: should return false',
        );
      }
      _logger.i('$_logTag ✅ Non-existent phone check passed');

      // Test 3: Check existing email (if provided)
      if (email.isNotEmpty) {
        _logger.i('$_logTag Step 3: Testing existing email check...');
        final existingEmailCheck = await AuthService.checkUserExistsByEmail(
          email,
        );
        _logger.i('$_logTag Email "$email" exists: $existingEmailCheck');
      }

      // Test 4: Check existing phone (if provided)
      if (phone.isNotEmpty) {
        _logger.i('$_logTag Step 4: Testing existing phone check...');
        final existingPhoneCheck = await AuthService.checkUserExistsByPhone(
          phone,
        );
        _logger.i('$_logTag Phone "$phone" exists: $existingPhoneCheck');
      }

      return TestResult.success(
        'User existence checking tests completed successfully',
      );
    } catch (e) {
      return TestResult.error('User existence check test failed: $e');
    }
  }

  /// Test email authentication flow
  static Future<TestResult> testEmailAuth({
    required String email,
    required String password,
    required String fullName,
  }) async {
    if (!kDebugMode) {
      return TestResult.error('Test helper only available in debug mode');
    }

    _logger.i('$_logTag Testing email authentication flow...');

    try {
      // Test 1: Email signup
      _logger.i('$_logTag Step 1: Testing email signup...');
      final signupResult = await AuthService.signUpWithEmail(
        email: email,
        password: password,
        fullName: fullName,
      );

      if (!signupResult.isSuccess) {
        return TestResult.error('Email signup failed: ${signupResult.error}');
      }

      _logger.i('$_logTag ✅ Email signup successful');

      // Test 2: Email signin
      _logger.i('$_logTag Step 2: Testing email signin...');
      final signinResult = await AuthService.signInWithEmail(
        email: email,
        password: password,
      );

      if (!signinResult.isSuccess) {
        return TestResult.error('Email signin failed: ${signinResult.error}');
      }

      _logger.i('$_logTag ✅ Email signin successful');

      return TestResult.success(
        'Email authentication flow completed successfully',
      );
    } catch (e) {
      final error = ErrorHandlerService.handleAuthError(
        e,
        context: 'Email Auth Test',
      );
      return TestResult.error('Email auth test failed: ${error.message}');
    }
  }

  /// Test phone authentication flow
  static Future<TestResult> testPhoneAuth({
    required String phone,
    required String password,
    required String fullName,
  }) async {
    if (!kDebugMode) {
      return TestResult.error('Test helper only available in debug mode');
    }

    _logger.i('$_logTag Testing phone authentication flow...');

    try {
      // Test 1: Phone signup (OTP sending)
      _logger.i('$_logTag Step 1: Testing phone OTP sending...');
      final otpResult = await AuthService.sendPhoneOTP(phone: phone);

      if (!otpResult.isSuccess) {
        return TestResult.error('Phone OTP sending failed: ${otpResult.error}');
      }

      _logger.i('$_logTag ✅ Phone OTP sent successfully');

      // Note: Actual OTP verification requires manual input
      _logger.w(
        '$_logTag ⚠️ Manual OTP verification required for complete test',
      );

      return TestResult.success(
        'Phone authentication flow initiated successfully',
      );
    } catch (e) {
      final error = ErrorHandlerService.handleAuthError(
        e,
        context: 'Phone Auth Test',
      );
      return TestResult.error('Phone auth test failed: ${error.message}');
    }
  }

  /// Test session management
  static Future<TestResult> testSessionManagement() async {
    if (!kDebugMode) {
      return TestResult.error('Test helper only available in debug mode');
    }

    _logger.i('$_logTag Testing session management...');

    try {
      // Test 1: Check current session
      _logger.i('$_logTag Step 1: Checking current session...');
      final isValid = SessionService.isSessionValid;
      _logger.i('$_logTag Session valid: $isValid');

      // Test 2: Session refresh
      _logger.i('$_logTag Step 2: Testing session refresh...');
      final refreshResult = await SessionService.refreshSessionIfNeeded();

      if (!refreshResult.isSuccess) {
        return TestResult.warning(
          'Session refresh failed: ${refreshResult.error}',
        );
      }

      _logger.i('$_logTag ✅ Session management working correctly');

      return TestResult.success(
        'Session management test completed successfully',
      );
    } catch (e) {
      final error = ErrorHandlerService.handleAppError(
        e,
        context: 'Session Test',
      );
      return TestResult.error('Session test failed: ${error.userMessage}');
    }
  }

  /// Test error handling
  static Future<TestResult> testErrorHandling() async {
    if (!kDebugMode) {
      return TestResult.error('Test helper only available in debug mode');
    }

    _logger.i('$_logTag Testing error handling...');

    try {
      // Test 1: Invalid email signin
      _logger.i('$_logTag Step 1: Testing invalid credentials error...');
      final invalidResult = await AuthService.signInWithEmail(
        email: '<EMAIL>',
        password: 'wrongpassword',
      );

      if (invalidResult.isSuccess) {
        return TestResult.warning(
          'Expected error for invalid credentials, but got success',
        );
      }

      _logger.i('$_logTag ✅ Invalid credentials error handled correctly');

      // Test 2: Network error simulation
      _logger.i('$_logTag Step 2: Testing error message formatting...');
      final errorMessage = ErrorHandlerService.getDisplayMessage(
        invalidResult.error,
      );

      if (errorMessage.isEmpty) {
        return TestResult.error('Error message is empty');
      }

      _logger.i('$_logTag ✅ Error message formatted correctly: $errorMessage');

      return TestResult.success('Error handling test completed successfully');
    } catch (e) {
      final error = ErrorHandlerService.handleAppError(
        e,
        context: 'Error Handling Test',
      );
      return TestResult.error(
        'Error handling test failed: ${error.userMessage}',
      );
    }
  }

  /// Test password reset flow
  static Future<TestResult> testPasswordReset({required String email}) async {
    if (!kDebugMode) {
      return TestResult.error('Test helper only available in debug mode');
    }

    _logger.i('$_logTag Testing password reset flow...');

    try {
      // Test password reset email sending
      _logger.i('$_logTag Step 1: Testing password reset email...');
      final resetResult = await AuthService.sendPasswordResetEmail(
        email: email,
      );

      if (!resetResult.isSuccess) {
        return TestResult.error('Password reset failed: ${resetResult.error}');
      }

      _logger.i('$_logTag ✅ Password reset email sent successfully');

      return TestResult.success('Password reset test completed successfully');
    } catch (e) {
      final error = ErrorHandlerService.handleAuthError(
        e,
        context: 'Password Reset Test',
      );
      return TestResult.error('Password reset test failed: ${error.message}');
    }
  }

  /// Run comprehensive authentication test suite
  static Future<List<TestResult>> runTestSuite({
    String testEmail = '<EMAIL>',
    String testPassword = 'TestPassword123!',
    String testPhone = '+1234567890',
    String testName = 'Test User',
  }) async {
    if (!kDebugMode) {
      return [TestResult.error('Test suite only available in debug mode')];
    }

    _logger.i('$_logTag 🚀 Running comprehensive authentication test suite...');

    final results = <TestResult>[];

    // Test 1: Session Management
    results.add(await testSessionManagement());

    // Test 2: Error Handling
    results.add(await testErrorHandling());

    // Test 3: Password Reset
    results.add(await testPasswordReset(email: testEmail));

    // Test 4: Email Authentication (if not already signed in)
    if (!AuthService.isAuthenticated) {
      results.add(
        await testEmailAuth(
          email: testEmail,
          password: testPassword,
          fullName: testName,
        ),
      );
    }

    // Test 5: Phone Authentication
    results.add(
      await testPhoneAuth(
        phone: testPhone,
        password: testPassword,
        fullName: testName,
      ),
    );

    // Print summary
    final passed = results.where((r) => r.status == TestStatus.success).length;
    final warnings =
        results.where((r) => r.status == TestStatus.warning).length;
    final failed = results.where((r) => r.status == TestStatus.error).length;

    _logger.i('$_logTag 📊 Test Suite Summary:');
    _logger.i('$_logTag ✅ Passed: $passed');
    _logger.w('$_logTag ⚠️ Warnings: $warnings');
    _logger.e('$_logTag ❌ Failed: $failed');

    return results;
  }

  /// Print detailed test results
  static void printResults(List<TestResult> results) {
    if (!kDebugMode) return;

    _logger.i('$_logTag 📋 Detailed Test Results:');
    _logger.i('$_logTag ${'=' * 50}');

    for (int i = 0; i < results.length; i++) {
      final result = results[i];
      final icon =
          result.status == TestStatus.success
              ? '✅'
              : result.status == TestStatus.warning
              ? '⚠️'
              : '❌';

      switch (result.status) {
        case TestStatus.success:
          _logger.i('$_logTag $icon Test ${i + 1}: ${result.message}');
          break;
        case TestStatus.warning:
          _logger.w('$_logTag $icon Test ${i + 1}: ${result.message}');
          break;
        case TestStatus.error:
          _logger.e('$_logTag $icon Test ${i + 1}: ${result.message}');
          break;
      }
    }

    _logger.i('$_logTag ${'=' * 50}');
  }
}

/// Test result class
class TestResult {
  final TestStatus status;
  final String message;
  final DateTime timestamp;

  TestResult._(this.status, this.message) : timestamp = DateTime.now();

  factory TestResult.success(String message) {
    return TestResult._(TestStatus.success, message);
  }

  factory TestResult.warning(String message) {
    return TestResult._(TestStatus.warning, message);
  }

  factory TestResult.error(String message) {
    return TestResult._(TestStatus.error, message);
  }

  @override
  String toString() {
    return 'TestResult(status: $status, message: $message)';
  }
}

/// Test status enum
enum TestStatus { success, warning, error }
