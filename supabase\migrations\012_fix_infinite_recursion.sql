-- =====================================================
-- FIX INFINITE RECURSION IN RLS POLICIES
-- Resolves circular dependency between profiles and follows tables
-- =====================================================

-- The issue: profiles_select_policy references follows table,
-- and follows_select_policy references profiles table,
-- creating infinite recursion when PostgreSQL evaluates the policies.

-- SOLUTION: Simplify the profiles_select_policy to avoid circular reference
-- and use security definer functions for complex checks

-- =====================================================
-- 1. DROP EXISTING PROBLEMATIC POLICIES
-- =====================================================

-- Drop the problematic profiles select policy
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;

-- =====================================================
-- 2. CREATE SIMPLIFIED PROFILES SELECT POLICY
-- =====================================================

-- Create a simplified policy that doesn't reference other tables with RLS
CREATE POLICY "profiles_select_policy" ON public.profiles
FOR SELECT USING (
    -- Own profile (always allowed)
    auth.uid() = id OR
    -- Public profiles (non-private) - this is safe as it doesn't reference other RLS tables
    is_private = false
    -- Note: We removed the follows check to break the circular dependency
    -- Following relationships will be handled at the application level
);

-- =====================================================
-- 3. CREATE SECURITY DEFINER FUNCTIONS FOR COMPLEX CHECKS
-- =====================================================

-- Function to check if user can view a profile (including follows check)
-- This function runs with elevated privileges and bypasses RLS
CREATE OR REPLACE FUNCTION public.can_view_profile(target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    target_is_private BOOLEAN;
    is_following BOOLEAN;
BEGIN
    -- If no user is authenticated, only public profiles are viewable
    IF auth.uid() IS NULL THEN
        SELECT is_private INTO target_is_private
        FROM public.profiles
        WHERE id = target_user_id;
        
        RETURN NOT COALESCE(target_is_private, true);
    END IF;
    
    -- If viewing own profile, always allowed
    IF auth.uid() = target_user_id THEN
        RETURN true;
    END IF;
    
    -- Check if target profile is private
    SELECT is_private INTO target_is_private
    FROM public.profiles
    WHERE id = target_user_id;
    
    -- If profile is public, allow access
    IF NOT COALESCE(target_is_private, false) THEN
        RETURN true;
    END IF;
    
    -- If profile is private, check if user is following
    SELECT EXISTS(
        SELECT 1 FROM public.follows 
        WHERE follower_id = auth.uid() AND following_id = target_user_id
    ) INTO is_following;
    
    RETURN is_following;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. FIX OTHER POTENTIAL CIRCULAR DEPENDENCIES
-- =====================================================

-- Update follows_select_policy to use security definer function instead of direct profile check
DROP POLICY IF EXISTS "follows_select_policy" ON public.follows;

CREATE POLICY "follows_select_policy" ON public.follows
FOR SELECT USING (
    -- Can see follows where they are the follower
    follower_id = auth.uid() OR
    -- Can see follows where they are being followed
    following_id = auth.uid() OR
    -- Admin access (using security definer function to avoid recursion)
    public.has_any_role(ARRAY['admin', 'super_admin', 'moderator'])
);

-- Update follows_delete_policy similarly
DROP POLICY IF EXISTS "follows_delete_policy" ON public.follows;

CREATE POLICY "follows_delete_policy" ON public.follows
FOR DELETE USING (
    -- Can only delete follows where they are the follower
    follower_id = auth.uid() OR
    -- Admin access (using security definer function to avoid recursion)
    public.has_any_role(ARRAY['admin', 'super_admin', 'moderator'])
);

-- =====================================================
-- 5. GRANT PERMISSIONS
-- =====================================================

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.can_view_profile(UUID) TO authenticated, anon;

-- =====================================================
-- 6. UPDATE APPLICATION USAGE
-- =====================================================

-- Note for developers:
-- Instead of relying on RLS to filter profiles based on follows,
-- use the can_view_profile() function in your application queries:
--
-- Example:
-- SELECT * FROM profiles
-- WHERE public.can_view_profile(id) = true;
--
-- This approach:
-- 1. Eliminates infinite recursion
-- 2. Provides better performance
-- 3. Gives more control over access logic
-- 4. Is easier to debug and maintain

-- =====================================================
-- 7. VERIFICATION QUERY
-- =====================================================

-- Test query to verify the fix works:
-- SELECT id, username, is_private FROM public.profiles LIMIT 5;
