import 'package:equatable/equatable.dart';

class ProductCategory extends Equatable {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String iconPath;
  final bool isSelected;

  const ProductCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.iconPath,
    this.isSelected = false,
  });

  // Factory constructor for creating ProductCategory from JSON
  factory ProductCategory.fromJson(Map<String, dynamic> json) {
    return ProductCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['image_url'] as String,
      iconPath: json['icon_path'] as String,
      isSelected: json['is_selected'] as bool? ?? false,
    );
  }

  // Convert ProductCategory to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'icon_path': iconPath,
      'is_selected': isSelected,
    };
  }

  // Copy with method for immutable updates
  ProductCategory copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? iconPath,
    bool? isSelected,
  }) {
    return ProductCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      iconPath: iconPath ?? this.iconPath,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    imageUrl,
    iconPath,
    isSelected,
  ];

  @override
  String toString() {
    return 'ProductCategory(id: $id, name: $name, isSelected: $isSelected)';
  }
}

// Predefined categories for the app
class CategoryData {
  static List<ProductCategory> getDefaultCategories() {
    return [
      const ProductCategory(
        id: 'electronics_technology',
        name: 'Electronics & Technology',
        description: 'Phones, laptops, gadgets, tech accessories, software',
        imageUrl:
            'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400',
        iconPath: 'assets/icons/electronics_technology.png',
      ),
      const ProductCategory(
        id: 'vehicles_automotive',
        name: 'Vehicles & Automotive',
        description: 'Cars, motorcycles, bicycles, parts, automotive services',
        imageUrl:
            'https://images.unsplash.com/photo-1494976688153-ca3ce29d8df4?w=400',
        iconPath: 'assets/icons/vehicles_automotive.png',
      ),
      const ProductCategory(
        id: 'home_garden',
        name: 'Home & Garden',
        description: 'Furniture, appliances, tools, decor, home improvement',
        imageUrl:
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400',
        iconPath: 'assets/icons/home_garden.png',
      ),
      const ProductCategory(
        id: 'fashion_clothing',
        name: 'Fashion & Clothing',
        description: 'Men\'s, women\'s, kids\' clothing, shoes, accessories',
        imageUrl:
            'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',
        iconPath: 'assets/icons/fashion_clothing.png',
      ),
      const ProductCategory(
        id: 'health_beauty',
        name: 'Health & Beauty',
        description: 'Cosmetics, skincare, wellness products, medical supplies',
        imageUrl:
            'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400',
        iconPath: 'assets/icons/health_beauty.png',
      ),
      const ProductCategory(
        id: 'food_beverages',
        name: 'Food & Beverages',
        description: 'Groceries, snacks, drinks, specialty foods, catering',
        imageUrl:
            'https://images.unsplash.com/photo-**********-92c53300491e?w=400',
        iconPath: 'assets/icons/food_beverages.png',
      ),
      const ProductCategory(
        id: 'sports_fitness',
        name: 'Sports & Fitness',
        description:
            'Exercise equipment, sportswear, outdoor gear, fitness services',
        imageUrl:
            'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        iconPath: 'assets/icons/sports_fitness.png',
      ),
      const ProductCategory(
        id: 'business_professional',
        name: 'Business & Professional',
        description: 'Office supplies, equipment, consulting, legal services',
        imageUrl:
            'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400',
        iconPath: 'assets/icons/business_professional.png',
      ),
      const ProductCategory(
        id: 'creative_design',
        name: 'Creative & Design',
        description:
            'Art supplies, graphic design, photography, marketing services',
        imageUrl:
            'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400',
        iconPath: 'assets/icons/creative_design.png',
      ),
      const ProductCategory(
        id: 'maintenance_repair',
        name: 'Maintenance & Repair',
        description:
            'Technical services, home repairs, equipment maintenance, installation',
        imageUrl:
            'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400',
        iconPath: 'assets/icons/maintenance_repair.png',
      ),
      const ProductCategory(
        id: 'others',
        name: 'Others',
        description:
            'Education, training, books, pets, miscellaneous items and unique products',
        imageUrl:
            'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',
        iconPath: 'assets/icons/others.png',
      ),
    ];
  }
}
