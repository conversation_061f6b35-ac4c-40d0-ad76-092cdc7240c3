# Business App Database Architecture - Essential Tables

## Overview
This document outlines the essential database tables needed for a commercial-scale **Trading and Marketplace** application with social media features, business analytics, e-commerce capabilities, and role-based access control.

## Total Commercial Scale Estimate: 55-65 Tables
For a production-ready business app similar to Facebook Marketplace, Instagram Shopping, or Amazon Business.

---

## Phase 1: Core Foundation (8-10 Tables)

### 1. User Management & Authentication
```sql
-- Essential for any app
CREATE TABLE profiles (
    id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    avatar_url TEXT,
    bio TEXT,
    role VARCHAR(20) DEFAULT 'user', -- user, business, admin
    is_verified BOOLEAN DEFAULT FALSE,
    is_business BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Business/Shop Profiles ⭐ CRITICAL
```sql
-- Critical for marketplace functionality
CREATE TABLE shops (
    id UUID PRIMARY KEY,
    owner_id UUID REFERENCES profiles(id),
    shop_name VARCHAR(100) NOT NULL,
    shop_description TEXT,
    shop_logo_url TEXT,
    shop_banner_url TEXT,
    business_type VARCHAR(50), -- individual, company, etc.
    location VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    website_url TEXT,
    social_links JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    total_sales INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Product Categories
```sql
-- Foundation for product organization
CREATE TABLE categories (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_url TEXT,
    parent_id UUID REFERENCES categories(id), -- For subcategories
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Products
```sql
-- Core marketplace functionality
CREATE TABLE products (
    id UUID PRIMARY KEY,
    shop_id UUID REFERENCES shops(id),
    category_id UUID REFERENCES categories(id),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    images TEXT[], -- Array of image URLs
    stock_quantity INTEGER DEFAULT 0,
    condition VARCHAR(20) DEFAULT 'new', -- new, used, refurbished
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, sold
    is_featured BOOLEAN DEFAULT FALSE,
    tags TEXT[],
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Posts (Social Media)
```sql
-- Social features foundation
CREATE TABLE posts (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES profiles(id),
    product_id UUID REFERENCES products(id), -- Optional link to product
    content TEXT,
    media_urls TEXT[],
    post_type VARCHAR(20) DEFAULT 'text', -- text, image, video, product
    is_public BOOLEAN DEFAULT TRUE,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

---

## Phase 2: Essential Interactions (5-6 Tables)

### 6. Follows
```sql
-- Social networking foundation
CREATE TABLE follows (
    id UUID PRIMARY KEY,
    follower_id UUID REFERENCES profiles(id),
    following_id UUID REFERENCES profiles(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(follower_id, following_id)
);
```

### 7. Likes
```sql
-- Engagement tracking
CREATE TABLE likes (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES profiles(id),
    post_id UUID REFERENCES posts(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, post_id)
);
```

### 8. Comments
```sql
-- User engagement
CREATE TABLE comments (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES profiles(id),
    post_id UUID REFERENCES posts(id),
    parent_id UUID REFERENCES comments(id), -- For replies
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

---

## Phase 3: Commerce Essentials (4-5 Tables)

### 9. Orders
```sql
-- E-commerce foundation
CREATE TABLE orders (
    id UUID PRIMARY KEY,
    buyer_id UUID REFERENCES profiles(id),
    shop_id UUID REFERENCES shops(id),
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'pending', -- pending, confirmed, shipped, delivered, cancelled
    shipping_address JSONB,
    payment_method VARCHAR(50),
    payment_status VARCHAR(20) DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 10. Order Items
```sql
-- Order details
CREATE TABLE order_items (
    id UUID PRIMARY KEY,
    order_id UUID REFERENCES orders(id),
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

---

## Phase 4: Communication (2-3 Tables)

### 11. Chats
```sql
-- Messaging foundation
CREATE TABLE chats (
    id UUID PRIMARY KEY,
    buyer_id UUID REFERENCES profiles(id),
    seller_id UUID REFERENCES profiles(id),
    product_id UUID REFERENCES products(id), -- Optional
    last_message_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 12. Messages
```sql
-- Chat messages
CREATE TABLE messages (
    id UUID PRIMARY KEY,
    chat_id UUID REFERENCES chats(id),
    sender_id UUID REFERENCES profiles(id),
    content TEXT,
    message_type VARCHAR(20) DEFAULT 'text',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

---

## Phase 5: Reviews & Trust (2 Tables)

### 13. Reviews
```sql
-- Trust and reputation system
CREATE TABLE reviews (
    id UUID PRIMARY KEY,
    reviewer_id UUID REFERENCES profiles(id),
    shop_id UUID REFERENCES shops(id),
    product_id UUID REFERENCES products(id), -- Optional
    order_id UUID REFERENCES orders(id),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT FALSE, -- Verified purchase
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 14. Notifications
```sql
-- User engagement
CREATE TABLE notifications (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES profiles(id),
    type VARCHAR(50) NOT NULL, -- order, message, follow, like, etc.
    title VARCHAR(200) NOT NULL,
    message TEXT,
    data JSONB DEFAULT '{}', -- Additional data
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

---

## Implementation Timeline

### Week 1-2: Core Foundation
- ✅ Tables 1-5 (profiles, shops, categories, products, posts)
- Focus: User registration, business profiles, product listing

### Week 3: Social Interactions  
- ✅ Tables 6-8 (follows, likes, comments)
- Focus: Social media features, user engagement

### Week 4: Basic Commerce
- ✅ Tables 9-10 (orders, order_items)
- Focus: Transaction processing, order management

### Week 5: Communication
- ✅ Tables 11-12 (chats, messages)
- Focus: Buyer-seller communication

### Week 6: Trust & Engagement
- ✅ Tables 13-14 (reviews, notifications)
- Focus: Reputation system, user notifications

---

## Business Logic Coverage

### ✅ User Management
- User profiles and authentication
- Role-based access (user, business, admin)
- Business verification system

### ✅ Shop Management  
- Multi-vendor marketplace support
- Business profiles with ratings
- Shop verification and status

### ✅ Product Catalog
- Categorized product organization
- Inventory management
- Product status and conditions

### ✅ Social Features
- Post creation and sharing
- Like, comment, and follow system
- Social engagement tracking

### ✅ E-commerce
- Order processing and management
- Payment status tracking
- Transaction history

### ✅ Communication
- Direct messaging between users
- Product-specific conversations
- Message status tracking

### ✅ Trust System
- Review and rating system
- Verified purchase reviews
- Shop reputation management

---

## Key Design Principles

### 🔧 Scalability Foundation
- **Proper relationships** between users, shops, and products
- **Flexible JSON fields** for future features
- **Status fields** for workflow management
- **Timestamp tracking** for analytics
- **Unique constraints** for data integrity

### 🚀 Commercial Readiness
- **Multi-vendor support** through shops table
- **Review system** for building trust
- **Order management** for transactions
- **Notification system** for engagement
- **Social features** for user retention

### 📊 Analytics Ready
- Timestamp fields for trend analysis
- Counter fields for performance metrics
- Status tracking for conversion funnels
- User behavior tracking capabilities

---

## Future Expansion Tables (Phase 6+)

### Advanced E-commerce (15-20 tables)
- shopping_carts, cart_items
- payment_methods, transactions
- shipping_addresses, shipping_methods
- discounts, wishlists, product_variants
- inventory_tracking, refunds, disputes

### Business Analytics (8-12 tables)
- business_metrics, revenue_tracking
- user_analytics, product_analytics
- search_analytics, conversion_tracking
- reports, audit_logs

### Advanced Features (12-15 tables)
- locations, tags, bookmarks
- moderation_actions, feature_flags
- api_keys, sessions, password_resets
- push_tokens, promotional_campaigns

---

## MVP Features Covered by Essential Tables

### ✅ Core Functionality
- User registration and authentication
- Business profile creation and management
- Product listing and categorization
- Social media posting and interactions
- Basic e-commerce transactions
- Buyer-seller communication
- Review and rating system
- Notification system

### ✅ Business Requirements
- Multi-vendor marketplace support
- Role-based access control
- Trust and reputation system
- Order processing and tracking
- Social engagement features
- Search and discovery capabilities

---

## Technical Notes

### Database Platform
- **Primary**: Supabase/PostgreSQL
- **UUID**: Used for all primary keys for scalability
- **JSONB**: Used for flexible data storage
- **Timestamps**: All tables include created_at/updated_at
- **Constraints**: Proper foreign keys and unique constraints

### Performance Considerations
- **Indexing**: Consider indexes on frequently queried fields
- **Partitioning**: For large tables like messages, posts
- **Caching**: Redis for frequently accessed data
- **CDN**: For media file storage and delivery

### Security Features
- **Row Level Security** (RLS) in Supabase
- **Role-based permissions** in application layer
- **Input validation** and sanitization
- **Rate limiting** for API endpoints

---

## Current App Integration

### Existing BLoC Structure
```dart
MultiBlocProvider(
  providers: [
    BlocProvider<ThemeBloc>(),           // 🎨 Theme management
    BlocProvider<NavigationBloc>(),      // 🧭 Navigation & tabs
    BlocProvider<ProfileBloc>(),         // 👤 User profiles
    BlocProvider<PostsBloc>(),           // 📝 Posts & content
    BlocProvider<SearchBloc>(),          // 🔍 Search functionality
    BlocProvider<ChatBloc>(),            // 💬 Chat/messaging
    BlocProvider<BusinessStatsBloc>(),   // 📊 Business analytics
    BlocProvider<SubscriptionBloc>(),    // 💳 Subscriptions
    BlocProvider<CreatePostBloc>(),      // ✍️ Post creation
    BlocProvider<NewMessageBloc>(),      // 📨 New messages
    BlocProvider<NotificationBloc>(),    // 🔔 Notifications
    BlocProvider<AuthBloc>(),            // 🔐 Authentication
    BlocProvider<CategorySelectionBloc>(), // 📂 Categories
    BlocProvider<ProductBloc>(),         // 🛍️ Products
  ],
)
```

### Database Integration Priority
1. **profiles** → AuthBloc, ProfileBloc
2. **shops** → BusinessStatsBloc, ProductBloc
3. **categories** → CategorySelectionBloc, ProductBloc
4. **products** → ProductBloc, SearchBloc
5. **posts** → PostsBloc, CreatePostBloc
6. **chats/messages** → ChatBloc, NewMessageBloc
7. **notifications** → NotificationBloc

---

## Next Steps

### Phase 1 Implementation (Week 1-2)
1. Set up Supabase project
2. Create profiles table with RLS
3. Implement shops table with business logic
4. Set up categories with default data
5. Create products table with proper relationships
6. Implement posts table for social features

### Phase 2 Implementation (Week 3)
1. Add social interaction tables (follows, likes, comments)
2. Integrate with existing PostsBloc
3. Implement engagement tracking
4. Add social features to UI

### Phase 3 Implementation (Week 4)
1. Create order management tables
2. Implement basic e-commerce flow
3. Add payment status tracking
4. Create order history features

---

*Last Updated: 2025-07-17*
*Total Essential Tables: 14*
*Commercial Scale Estimate: 55-65 tables*
*Current App: Trading and Marketplace with Social Features*
