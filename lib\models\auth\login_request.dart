import 'package:equatable/equatable.dart';

class LoginRequest extends Equatable {
  final String identifier; // Can be email, phone, or username
  final String password;
  final bool rememberMe;

  const LoginRequest({
    required this.identifier,
    required this.password,
    this.rememberMe = false,
  });

  // Factory constructor for creating LoginRequest from JSON
  factory LoginRequest.fromJson(Map<String, dynamic> json) {
    return LoginRequest(
      identifier: json['identifier'] as String,
      password: json['password'] as String,
      rememberMe: json['remember_me'] as bool? ?? false,
    );
  }

  // Convert LoginRequest to JSON
  Map<String, dynamic> toJson() {
    return {
      'identifier': identifier,
      'password': password,
      'remember_me': rememberMe,
    };
  }

  // Create a copy with updated fields
  LoginRequest copyWith({
    String? identifier,
    String? password,
    bool? rememberMe,
  }) {
    return LoginRequest(
      identifier: identifier ?? this.identifier,
      password: password ?? this.password,
      rememberMe: rememberMe ?? this.rememberMe,
    );
  }

  // Validation methods
  bool get isValidEmail {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(identifier);
  }

  bool get isValidPhone {
    // Basic phone validation - can be enhanced based on requirements
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(identifier.replaceAll(RegExp(r'[\s\-\(\)]'), ''));
  }

  bool get isValidUsername {
    // Username should be 3-30 characters, alphanumeric and underscores
    return RegExp(r'^[a-zA-Z0-9_]{3,30}$').hasMatch(identifier);
  }

  bool get isValidIdentifier {
    return isValidEmail || isValidPhone || isValidUsername;
  }

  bool get isValidPassword {
    return password.length >= 8;
  }

  bool get isValid {
    return isValidIdentifier && isValidPassword;
  }

  // Get identifier type
  String get identifierType {
    if (isValidEmail) return 'email';
    if (isValidPhone) return 'phone';
    if (isValidUsername) return 'username';
    return 'unknown';
  }

  @override
  List<Object?> get props => [identifier, password, rememberMe];

  @override
  String toString() {
    return 'LoginRequest(identifier: $identifier, rememberMe: $rememberMe)';
  }
}

class LoginResponse extends Equatable {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final Map<String, dynamic> user;

  const LoginResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.user,
  });

  // Factory constructor for creating LoginResponse from JSON
  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
      tokenType: json['token_type'] as String? ?? 'Bearer',
      expiresIn: json['expires_in'] as int,
      user: json['user'] as Map<String, dynamic>,
    );
  }

  // Convert LoginResponse to JSON
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
      'user': user,
    };
  }

  // Get expiration date
  DateTime get expirationDate {
    return DateTime.now().add(Duration(seconds: expiresIn));
  }

  // Check if token is expired
  bool get isExpired {
    return DateTime.now().isAfter(expirationDate);
  }

  @override
  List<Object?> get props => [accessToken, refreshToken, tokenType, expiresIn, user];

  @override
  String toString() {
    return 'LoginResponse(tokenType: $tokenType, expiresIn: $expiresIn)';
  }
}
