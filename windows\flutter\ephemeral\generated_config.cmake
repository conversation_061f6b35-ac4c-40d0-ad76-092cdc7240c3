# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "G:\\business_app" PROJECT_DIR)

set(FLUTTER_VERSION "8.0.21+100" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 8 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 21 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 100 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter"
  "PROJECT_DIR=G:\\business_app"
  "FLUTTER_ROOT=C:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=G:\\business_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=G:\\business_app"
  "FLUTTER_TARGET=G:\\business_app\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=G:\\business_app\\.dart_tool\\package_config.json"
)
