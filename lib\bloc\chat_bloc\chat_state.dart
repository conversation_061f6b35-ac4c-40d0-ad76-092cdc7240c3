import 'package:equatable/equatable.dart';

enum ChatStatus { initial, loading, loaded, error, sending }

class Message extends Equatable {
  final String id;
  final String senderId;
  final String senderName;
  final String content;
  final DateTime timestamp;
  final bool isRead;
  final String? attachmentUrl;
  final String? attachmentType;
  final bool isSentByMe;

  const Message({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.content,
    required this.timestamp,
    this.isRead = false,
    this.attachmentUrl,
    this.attachmentType,
    this.isSentByMe = false,
  });

  Message copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? content,
    DateTime? timestamp,
    bool? isRead,
    String? attachmentUrl,
    String? attachmentType,
    bool? isSentByMe,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      attachmentType: attachmentType ?? this.attachmentType,
      isSentByMe: isSentByMe ?? this.isSentByMe,
    );
  }

  @override
  List<Object?> get props => [
        id,
        senderId,
        senderName,
        content,
        timestamp,
        isRead,
        attachmentUrl,
        attachmentType,
        isSentByMe,
      ];
}

class Conversation extends Equatable {
  final String id;
  final String participantId;
  final String participantName;
  final String participantProfileImage;
  final Message? lastMessage;
  final int unreadCount;
  final bool isOnline;
  final DateTime lastSeen;

  const Conversation({
    required this.id,
    required this.participantId,
    required this.participantName,
    required this.participantProfileImage,
    this.lastMessage,
    this.unreadCount = 0,
    this.isOnline = false,
    required this.lastSeen,
  });

  Conversation copyWith({
    String? id,
    String? participantId,
    String? participantName,
    String? participantProfileImage,
    Message? lastMessage,
    int? unreadCount,
    bool? isOnline,
    DateTime? lastSeen,
  }) {
    return Conversation(
      id: id ?? this.id,
      participantId: participantId ?? this.participantId,
      participantName: participantName ?? this.participantName,
      participantProfileImage: participantProfileImage ?? this.participantProfileImage,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  @override
  List<Object?> get props => [
        id,
        participantId,
        participantName,
        participantProfileImage,
        lastMessage,
        unreadCount,
        isOnline,
        lastSeen,
      ];
}

class ChatState extends Equatable {
  final ChatStatus status;
  final List<Conversation> conversations;
  final List<Message> messages;
  final String? currentConversationId;
  final bool showAttachmentOptions;
  final String? selectedAttachmentPath;
  final String? selectedAttachmentType;
  final Map<String, bool> typingUsers;
  final String? errorMessage;

  const ChatState({
    this.status = ChatStatus.initial,
    this.conversations = const [],
    this.messages = const [],
    this.currentConversationId,
    this.showAttachmentOptions = false,
    this.selectedAttachmentPath,
    this.selectedAttachmentType,
    this.typingUsers = const {},
    this.errorMessage,
  });

  ChatState copyWith({
    ChatStatus? status,
    List<Conversation>? conversations,
    List<Message>? messages,
    String? currentConversationId,
    bool? showAttachmentOptions,
    String? selectedAttachmentPath,
    String? selectedAttachmentType,
    Map<String, bool>? typingUsers,
    String? errorMessage,
  }) {
    return ChatState(
      status: status ?? this.status,
      conversations: conversations ?? this.conversations,
      messages: messages ?? this.messages,
      currentConversationId: currentConversationId ?? this.currentConversationId,
      showAttachmentOptions: showAttachmentOptions ?? this.showAttachmentOptions,
      selectedAttachmentPath: selectedAttachmentPath ?? this.selectedAttachmentPath,
      selectedAttachmentType: selectedAttachmentType ?? this.selectedAttachmentType,
      typingUsers: typingUsers ?? this.typingUsers,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        conversations,
        messages,
        currentConversationId,
        showAttachmentOptions,
        selectedAttachmentPath,
        selectedAttachmentType,
        typingUsers,
        errorMessage,
      ];
}
