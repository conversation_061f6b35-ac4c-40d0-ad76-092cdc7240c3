import 'package:flutter/material.dart';
import '../base/base_skeleton.dart';
import '../base/skeleton_config.dart';

/// Skeleton screen for the profile page that matches the actual layout
class ProfileSkeleton extends StatefulWidget {
  const ProfileSkeleton({super.key});

  @override
  State<ProfileSkeleton> createState() => _ProfileSkeletonState();
}

class _ProfileSkeletonState extends State<ProfileSkeleton>
    with TickerProviderStateMixin {
  late AnimationController _staggerController;
  late List<Animation<double>> _staggerAnimations;

  final double profilePicRadius = 60.0;
  final double coverImageHeight = 200.0;
  final double profilePicOverlap = 30.0;

  @override
  void initState() {
    super.initState();
    _setupStaggerAnimation();
  }

  void _setupStaggerAnimation() {
    _staggerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Create staggered animations for different sections
    _staggerAnimations = List.generate(8, (index) {
      final start = index * 0.1;
      final end = start + 0.3;
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _staggerController,
          curve: Interval(start, end.clamp(0.0, 1.0), curve: Curves.easeOut),
        ),
      );
    });

    _staggerController.forward();
  }

  @override
  void dispose() {
    _staggerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        physics: const BouncingScrollPhysics(),
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          // App bar with skeleton actions
          SliverAppBar(
            pinned: true,
            actions: [
              _buildSkeletonAction(0),
              const SizedBox(width: 8),
              _buildSkeletonAction(1),
              const SizedBox(width: 8),
              _buildSkeletonAction(2),
              const SizedBox(width: 8),
            ],
            expandedHeight: coverImageHeight + profilePicRadius - profilePicOverlap,
            flexibleSpace: FlexibleSpaceBar(
              background: _buildHeaderSection(),
            ),
          ),

          // Profile info section
          SliverToBoxAdapter(
            child: _buildProfileInfoSection(),
          ),

          // Tab bar skeleton
          SliverPersistentHeader(
            pinned: true,
            delegate: _SkeletonTabBarDelegate(),
          ),
        ],
        body: _buildTabContent(),
      ),
    );
  }

  Widget _buildSkeletonAction(int index) {
    return AnimatedBuilder(
      animation: _staggerAnimations[index],
      builder: (context, child) {
        return Opacity(
          opacity: _staggerAnimations[index].value,
          child: const SkeletonCircle(size: 40),
        );
      },
    );
  }

  Widget _buildHeaderSection() {
    return Stack(
      children: [
        // Cover image skeleton
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          height: coverImageHeight,
          child: AnimatedBuilder(
            animation: _staggerAnimations[0],
            builder: (context, child) {
              return Opacity(
                opacity: _staggerAnimations[0].value,
                child: const SkeletonContainer(
                  width: double.infinity,
                  height: double.infinity,
                  borderRadius: BorderRadius.zero,
                ),
              );
            },
          ),
        ),

        // Profile avatar skeleton
        Positioned(
          left: 20,
          bottom: 0,
          child: AnimatedBuilder(
            animation: _staggerAnimations[1],
            builder: (context, child) {
              return Opacity(
                opacity: _staggerAnimations[1].value,
                child: Stack(
                  children: [
                    // Main avatar
                    SkeletonCircle(size: (profilePicRadius * 2) + 4),
                    
                    // Online status indicator
                    Positioned(
                      bottom: 10,
                      right: 12,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: SkeletonConfig.getBaseColor(context),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfileInfoSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and verified icon
                    AnimatedBuilder(
                      animation: _staggerAnimations[2],
                      builder: (context, child) {
                        return Opacity(
                          opacity: _staggerAnimations[2].value,
                          child: Row(
                            children: [
                              const SkeletonText(width: 150, height: 20),
                              const SizedBox(width: 8),
                              SkeletonContainer(
                                width: 20,
                                height: 20,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Username
                    AnimatedBuilder(
                      animation: _staggerAnimations[3],
                      builder: (context, child) {
                        return Opacity(
                          opacity: _staggerAnimations[3].value,
                          child: const SkeletonText(width: 120, height: 16),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Bio
                    AnimatedBuilder(
                      animation: _staggerAnimations[4],
                      builder: (context, child) {
                        return Opacity(
                          opacity: _staggerAnimations[4].value,
                          child: const Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SkeletonText(width: double.infinity, height: 16),
                              SizedBox(height: 4),
                              SkeletonText(width: 200, height: 16),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Following and Customers stats
          AnimatedBuilder(
            animation: _staggerAnimations[5],
            builder: (context, child) {
              return Opacity(
                opacity: _staggerAnimations[5].value,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      const SkeletonText(width: 30, height: 16),
                      const SizedBox(width: 4),
                      const SkeletonText(width: 70, height: 16),
                      const SizedBox(width: 16),
                      const SkeletonText(width: 40, height: 16),
                      const SizedBox(width: 4),
                      const SkeletonText(width: 80, height: 16),
                    ],
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 16),

          // Edit profile button
          AnimatedBuilder(
            animation: _staggerAnimations[6],
            builder: (context, child) {
              return Opacity(
                opacity: _staggerAnimations[6].value,
                child: SkeletonContainer(
                  width: double.infinity,
                  height: 48,
                  borderRadius: BorderRadius.circular(SkeletonConfig.buttonBorderRadius),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return AnimatedBuilder(
      animation: _staggerAnimations[7],
      builder: (context, child) {
        return Opacity(
          opacity: _staggerAnimations[7].value,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: 6,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                child: const SkeletonCard(
                  width: double.infinity,
                  height: 120,
                ),
              );
            },
          ),
        );
      },
    );
  }
}

/// Custom delegate for skeleton tab bar
class _SkeletonTabBarDelegate extends SliverPersistentHeaderDelegate {
  @override
  double get minExtent => 48.0;

  @override
  double get maxExtent => 48.0;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 48,
              alignment: Alignment.center,
              child: const SkeletonText(width: 80, height: 16),
            ),
          ),
          Expanded(
            child: Container(
              height: 48,
              alignment: Alignment.center,
              child: const SkeletonText(width: 60, height: 16),
            ),
          ),
          Expanded(
            child: Container(
              height: 48,
              alignment: Alignment.center,
              child: const SkeletonText(width: 50, height: 16),
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) =>
      false;
}
