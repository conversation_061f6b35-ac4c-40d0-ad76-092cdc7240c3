import 'package:equatable/equatable.dart';

class NavigationState extends Equatable {
  final int selectedPageIndex;
  final bool isBottomBarVisible;
  final bool isDrawerOpen;
  final int selectedTabIndex;

  const NavigationState({
    this.selectedPageIndex = 0,
    this.isBottomBarVisible = true,
    this.isDrawerOpen = false,
    this.selectedTabIndex = 0,
  });

  NavigationState copyWith({
    int? selectedPageIndex,
    bool? isBottomBarVisible,
    bool? isDrawerOpen,
    int? selectedTabIndex,
  }) {
    return NavigationState(
      selectedPageIndex: selectedPageIndex ?? this.selectedPageIndex,
      isBottomBarVisible: isBottomBarVisible ?? this.isBottomBarVisible,
      isDrawerOpen: isDrawerOpen ?? this.isDrawerOpen,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
    );
  }

  @override
  List<Object> get props => [
        selectedPageIndex,
        isBottomBarVisible,
        isDrawerOpen,
        selectedTabIndex,
      ];
}
