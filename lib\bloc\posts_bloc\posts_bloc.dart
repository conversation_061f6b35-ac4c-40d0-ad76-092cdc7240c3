import 'package:flutter_bloc/flutter_bloc.dart';
import 'posts_event.dart';
import 'posts_state.dart';

class PostsBloc extends Bloc<PostsEvent, PostsState> {
  PostsBloc() : super(const PostsState()) {
    on<LoadPostsEvent>(_onLoadPosts);
    on<RefreshPostsEvent>(_onRefreshPosts);
    on<LoadMorePostsEvent>(_onLoadMorePosts);
    on<LikePostEvent>(_onLikePost);
    on<UnlikePostEvent>(_onUnlikePost);
    on<SharePostEvent>(_onSharePost);
    on<CommentOnPostEvent>(_onCommentOnPost);
    on<DeletePostEvent>(_onDeletePost);
    on<ReportPostEvent>(_onReportPost);
    on<ChangePostTabEvent>(_onChangePostTab);
    on<ToggleLikeEvent>(_onToggleLike);
    on<ToggleBookmarkEvent>(_onToggleBookmark);
  }

  Future<void> _onLoadPosts(
    LoadPostsEvent event,
    Emitter<PostsState> emit,
  ) async {
    emit(state.copyWith(status: PostsStatus.loading));

    try {
      await Future.delayed(const Duration(milliseconds: 800));

      // Mock posts data - replace with actual API call
      final posts = _generateMockPosts();

      emit(
        state.copyWith(
          status: PostsStatus.loaded,
          posts: posts,
          hasReachedMax: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: PostsStatus.error,
          errorMessage: 'Failed to load posts: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onRefreshPosts(
    RefreshPostsEvent event,
    Emitter<PostsState> emit,
  ) async {
    emit(state.copyWith(status: PostsStatus.refreshing));

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final posts = _generateMockPosts();

      emit(
        state.copyWith(
          status: PostsStatus.loaded,
          posts: posts,
          hasReachedMax: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: PostsStatus.error,
          errorMessage: 'Failed to refresh posts: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadMorePosts(
    LoadMorePostsEvent event,
    Emitter<PostsState> emit,
  ) async {
    if (state.hasReachedMax) return;

    emit(state.copyWith(status: PostsStatus.loadingMore));

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final morePosts = _generateMockPosts(startIndex: state.posts.length);

      emit(
        state.copyWith(
          status: PostsStatus.loaded,
          posts: [...state.posts, ...morePosts],
          hasReachedMax: morePosts.length < 10,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: PostsStatus.error,
          errorMessage: 'Failed to load more posts: ${e.toString()}',
        ),
      );
    }
  }

  void _onLikePost(LikePostEvent event, Emitter<PostsState> emit) {
    final updatedPosts =
        state.posts.map((post) {
          if (post.id == event.postId) {
            return post.copyWith(
              isLiked: true,
              likesCount: post.likesCount + 1,
            );
          }
          return post;
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }

  void _onUnlikePost(UnlikePostEvent event, Emitter<PostsState> emit) {
    final updatedPosts =
        state.posts.map((post) {
          if (post.id == event.postId) {
            return post.copyWith(
              isLiked: false,
              likesCount: post.likesCount - 1,
            );
          }
          return post;
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }

  void _onSharePost(SharePostEvent event, Emitter<PostsState> emit) {
    final updatedPosts =
        state.posts.map((post) {
          if (post.id == event.postId) {
            return post.copyWith(sharesCount: post.sharesCount + 1);
          }
          return post;
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }

  void _onCommentOnPost(CommentOnPostEvent event, Emitter<PostsState> emit) {
    final updatedPosts =
        state.posts.map((post) {
          if (post.id == event.postId) {
            return post.copyWith(commentsCount: post.commentsCount + 1);
          }
          return post;
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }

  void _onDeletePost(DeletePostEvent event, Emitter<PostsState> emit) {
    final updatedPosts =
        state.posts.where((post) => post.id != event.postId).toList();
    emit(state.copyWith(posts: updatedPosts));
  }

  void _onReportPost(ReportPostEvent event, Emitter<PostsState> emit) {
    // Handle post reporting logic here
    // For now, just remove the post from the list
    final updatedPosts =
        state.posts.where((post) => post.id != event.postId).toList();
    emit(state.copyWith(posts: updatedPosts));
  }

  void _onChangePostTab(ChangePostTabEvent event, Emitter<PostsState> emit) {
    emit(state.copyWith(selectedTabIndex: event.tabIndex));
  }

  void _onToggleLike(ToggleLikeEvent event, Emitter<PostsState> emit) {
    final updatedPosts =
        state.posts.map((post) {
          if (post.id == event.postId) {
            if (post.isLiked) {
              return post.copyWith(
                isLiked: false,
                likesCount: post.likesCount - 1,
              );
            } else {
              return post.copyWith(
                isLiked: true,
                likesCount: post.likesCount + 1,
              );
            }
          }
          return post;
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }

  void _onToggleBookmark(ToggleBookmarkEvent event, Emitter<PostsState> emit) {
    final updatedPosts =
        state.posts.map((post) {
          if (post.id == event.postId) {
            return post.copyWith(isBookmarked: !post.isBookmarked);
          }
          return post;
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }

  List<Post> _generateMockPosts({int startIndex = 0}) {
    return List.generate(10, (index) {
      final postIndex = startIndex + index;
      return Post(
        id: 'post_$postIndex',
        userId: 'user_$postIndex',
        authorName: 'User ${postIndex + 1}',
        authorUsername: '@user${postIndex + 1}',
        authorProfileImage: 'assets/images/profile${(postIndex % 3) + 1}.jpg',
        postContent:
            'This is a sample post content for post ${postIndex + 1}. It contains some interesting information about business and entrepreneurship.',
        mediaUrls: postIndex % 3 == 0 ? ['assets/images/kitchen_util.jpg'] : [],
        createdAt: DateTime.now().subtract(Duration(hours: postIndex)),
        likesCount: (postIndex * 12) % 100,
        commentsCount: (postIndex * 5) % 20,
        sharesCount: (postIndex * 2) % 10,
        isLiked: postIndex % 4 == 0,
        isBookmarked: postIndex % 5 == 0,
      );
    });
  }
}
