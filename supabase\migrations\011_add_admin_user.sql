-- =====================================================
-- ADD ADMIN USER MIGRATION
-- Assigns admin role to specific email address
-- =====================================================

-- Function to safely add admin user (idempotent)
CREATE OR REPLACE FUNCTION public.add_initial_admin()
RETURNS VOID AS $$
DECLARE
    admin_email TEXT := '<EMAIL>';
    user_exists BOOLEAN := FALSE;
    current_role VARCHAR(20);
BEGIN
    -- Check if user exists
    SELECT EXISTS(
        SELECT 1 FROM public.profiles 
        WHERE email = admin_email
    ) INTO user_exists;
    
    IF user_exists THEN
        -- Get current role
        SELECT role INTO current_role
        FROM public.profiles
        WHERE email = admin_email;
        
        -- Only update if not already an admin
        IF current_role NOT IN ('admin', 'super_admin', 'moderator') THEN
            UPDATE public.profiles 
            SET 
                role = 'super_admin',
                updated_at = NOW()
            WHERE email = admin_email;
            
            RAISE NOTICE 'Successfully assigned super_admin role to %', admin_email;
        ELSE
            RAISE NOTICE 'User % already has admin role: %', admin_email, current_role;
        END IF;
    ELSE
        RAISE NOTICE 'User with email % not found. Please create an account first.', admin_email;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Execute the function to add admin user
SELECT public.add_initial_admin();

-- Drop the temporary function
DROP FUNCTION public.add_initial_admin();

-- =====================================================
-- MANUAL ADMIN ASSIGNMENT (Alternative Method)
-- =====================================================

-- If the function approach doesn't work, you can use this direct update:
-- UPDATE public.profiles 
-- SET role = 'super_admin', updated_at = NOW() 
-- WHERE email = '<EMAIL>';

-- =====================================================
-- VERIFICATION QUERY
-- =====================================================

-- Query to verify admin assignment
-- SELECT id, username, full_name, email, role, created_at 
-- FROM public.profiles 
-- WHERE email = '<EMAIL>';
