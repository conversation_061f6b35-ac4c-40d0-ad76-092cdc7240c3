import 'package:flutter_bloc/flutter_bloc.dart';
import 'create_post_event.dart';
import 'create_post_state.dart';

class CreatePostBloc extends Bloc<CreatePostEvent, CreatePostState> {
  CreatePostBloc() : super(const CreatePostState()) {
    on<UpdatePostContentEvent>(_onUpdatePostContent);
    on<AddImageEvent>(_onAddImage);
    on<RemoveImageEvent>(_onRemoveImage);
    on<AddVideoEvent>(_onAddVideo);
    on<RemoveVideoEvent>(_onRemoveVideo);
    on<ToggleLocationEvent>(_onToggleLocation);
    on<UpdateLocationEvent>(_onUpdateLocation);
    on<AddTagEvent>(_onAddTag);
    on<RemoveTagEvent>(_onRemoveTag);
    on<TogglePrivacyEvent>(_onTogglePrivacy);
    on<SubmitPostEvent>(_onSubmitPost);
    on<SaveDraftEvent>(_onSaveDraft);
    on<LoadDraftEvent>(_onLoadDraft);
    on<ClearPostEvent>(_onClearPost);
    on<ValidatePostEvent>(_onValidatePost);
    on<SchedulePostEvent>(_onSchedulePost);
  }

  void _onUpdatePostContent(
    UpdatePostContentEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(state.copyWith(
      content: event.content,
      status: CreatePostStatus.editing,
    ));
    
    // Auto-validate on content change
    add(ValidatePostEvent());
  }

  void _onAddImage(
    AddImageEvent event,
    Emitter<CreatePostState> emit,
  ) {
    if (state.images.length >= 4) return; // Max 4 images
    
    final updatedImages = [...state.images, event.imagePath];
    emit(state.copyWith(
      images: updatedImages,
      status: CreatePostStatus.editing,
    ));
  }

  void _onRemoveImage(
    RemoveImageEvent event,
    Emitter<CreatePostState> emit,
  ) {
    if (event.imageIndex < 0 || event.imageIndex >= state.images.length) return;
    
    final updatedImages = List<String>.from(state.images);
    updatedImages.removeAt(event.imageIndex);
    
    emit(state.copyWith(
      images: updatedImages,
      status: CreatePostStatus.editing,
    ));
  }

  void _onAddVideo(
    AddVideoEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(state.copyWith(
      videoPath: event.videoPath,
      images: [], // Clear images when video is added
      status: CreatePostStatus.editing,
    ));
  }

  void _onRemoveVideo(
    RemoveVideoEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(state.copyWith(
      videoPath: null,
      status: CreatePostStatus.editing,
    ));
  }

  void _onToggleLocation(
    ToggleLocationEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(state.copyWith(
      includeLocation: event.includeLocation,
      location: event.includeLocation ? state.location : null,
      status: CreatePostStatus.editing,
    ));
  }

  void _onUpdateLocation(
    UpdateLocationEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(state.copyWith(
      location: event.location,
      includeLocation: true,
      status: CreatePostStatus.editing,
    ));
  }

  void _onAddTag(
    AddTagEvent event,
    Emitter<CreatePostState> emit,
  ) {
    if (state.tags.contains(event.tag) || state.tags.length >= 10) return;
    
    final updatedTags = [...state.tags, event.tag];
    emit(state.copyWith(
      tags: updatedTags,
      status: CreatePostStatus.editing,
    ));
  }

  void _onRemoveTag(
    RemoveTagEvent event,
    Emitter<CreatePostState> emit,
  ) {
    final updatedTags = state.tags.where((tag) => tag != event.tag).toList();
    emit(state.copyWith(
      tags: updatedTags,
      status: CreatePostStatus.editing,
    ));
  }

  void _onTogglePrivacy(
    TogglePrivacyEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(state.copyWith(
      privacy: event.privacy,
      status: CreatePostStatus.editing,
    ));
  }

  Future<void> _onSubmitPost(
    SubmitPostEvent event,
    Emitter<CreatePostState> emit,
  ) async {
    if (!state.canSubmit) return;

    emit(state.copyWith(status: CreatePostStatus.submitting));
    
    try {
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock post submission
      emit(state.copyWith(status: CreatePostStatus.submitted));
      
      // Clear the form after successful submission
      add(ClearPostEvent());
    } catch (e) {
      emit(state.copyWith(
        status: CreatePostStatus.error,
        errorMessage: 'Failed to submit post: ${e.toString()}',
      ));
    }
  }

  Future<void> _onSaveDraft(
    SaveDraftEvent event,
    Emitter<CreatePostState> emit,
  ) async {
    if (!state.hasContent) return;

    emit(state.copyWith(status: CreatePostStatus.savingDraft));
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final draft = PostDraft(
        id: 'draft_${DateTime.now().millisecondsSinceEpoch}',
        content: state.content,
        images: state.images,
        videoPath: state.videoPath,
        location: state.location,
        tags: state.tags,
        privacy: state.privacy,
        createdAt: DateTime.now(),
        scheduledTime: state.scheduledTime,
      );
      
      emit(state.copyWith(
        status: CreatePostStatus.draftSaved,
        currentDraft: draft,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: CreatePostStatus.error,
        errorMessage: 'Failed to save draft: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadDraft(
    LoadDraftEvent event,
    Emitter<CreatePostState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      // Mock draft loading - replace with actual draft retrieval
      final mockDraft = PostDraft(
        id: event.draftId,
        content: 'This is a saved draft content...',
        images: ['assets/images/kitchen_util.jpg'],
        location: 'Sample Location',
        tags: ['business', 'draft'],
        privacy: 'public',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      );
      
      emit(state.copyWith(
        content: mockDraft.content,
        images: mockDraft.images,
        videoPath: mockDraft.videoPath,
        location: mockDraft.location,
        includeLocation: mockDraft.location != null,
        tags: mockDraft.tags,
        privacy: mockDraft.privacy,
        scheduledTime: mockDraft.scheduledTime,
        currentDraft: mockDraft,
        status: CreatePostStatus.editing,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: CreatePostStatus.error,
        errorMessage: 'Failed to load draft: ${e.toString()}',
      ));
    }
  }

  void _onClearPost(
    ClearPostEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(const CreatePostState());
  }

  void _onValidatePost(
    ValidatePostEvent event,
    Emitter<CreatePostState> emit,
  ) {
    final errors = <String>[];
    
    if (state.content.trim().isEmpty) {
      errors.add('Post content cannot be empty');
    }
    
    if (state.isOverLimit) {
      errors.add('Post content exceeds character limit');
    }
    
    if (state.images.isEmpty && state.videoPath == null && state.content.trim().length < 10) {
      errors.add('Post should have more content or include media');
    }
    
    emit(state.copyWith(
      validationErrors: errors,
      status: errors.isEmpty ? CreatePostStatus.editing : CreatePostStatus.validating,
    ));
  }

  void _onSchedulePost(
    SchedulePostEvent event,
    Emitter<CreatePostState> emit,
  ) {
    emit(state.copyWith(
      scheduledTime: event.scheduledTime,
      status: CreatePostStatus.editing,
    ));
  }
}
