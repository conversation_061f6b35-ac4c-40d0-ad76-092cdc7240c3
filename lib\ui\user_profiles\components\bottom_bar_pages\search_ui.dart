import 'package:business_app/const/assets.dart';
import 'package:business_app/bloc/search_bloc/search_bloc.dart';
import 'package:business_app/bloc/search_bloc/search_event.dart';
import 'package:business_app/bloc/search_bloc/search_state.dart';
import 'package:business_app/ui/business_stats/business_stats.dart';
import 'package:business_app/ui/home_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';

class SearchUi extends StatelessWidget {
  final void Function(bool isVisible)? onScrollBottomBarVisibility;

  const SearchUi({Key? key, this.onScrollBottomBarVisibility})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is UserScrollNotification &&
            onScrollBottomBarVisibility != null) {
          if (notification.direction == ScrollDirection.reverse) {
            onScrollBottomBarVisibility!(false);
          } else if (notification.direction == ScrollDirection.forward) {
            onScrollBottomBarVisibility!(true);
          }
        }
        return false;
      },
      child: Scaffold(
        //backgroundColor: Colors.black,
        appBar: AppBar(
          // backgroundColor: Colors.black,
          elevation: 0,
          title: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: 18,
                backgroundImage: NetworkImage(
                  'https://cdni.pornpics.com/1280/1/364/86796990/86796990_002_3ad9.jpg',
                ),
              ),
              const SizedBox(width: 12),

              // Search bar (as a tap area)
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (_) => const SearchPage()),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[900],
                      borderRadius: BorderRadius.circular(26),
                    ),
                    child: Row(
                      children: const [
                        Icon(Icons.search, color: Colors.white54, size: 20),
                        SizedBox(width: 10),
                        Text(
                          "Search",
                          style: TextStyle(color: Colors.white54, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        body: GridView.builder(
          physics: BouncingScrollPhysics(),
          padding: const EdgeInsets.all(2),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: screenWidth < 600 ? 3 : 5,
            crossAxisSpacing: 2,
            mainAxisSpacing: 2,
            childAspectRatio: 1,
          ),
          itemCount: 60,
          itemBuilder:
              (context, index) => Container(
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  image: DecorationImage(
                    image: NetworkImage(
                      'https://picsum.photos/300?image=$index',
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
        ),
        // Custom FAB for Search UI

        /*
        floatingActionButton: SearchUiCustomFAB(),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        */

        //~floating action button for all pages
        floatingActionButton: SearchCustomExpandableFab(),
        floatingActionButtonLocation: ExpandableFab.location,
      ),
    );
  }
}

// Custom FAB for Search UI
class SearchUiCustomFAB extends StatelessWidget {
  const SearchUiCustomFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        // Navigate to full search page
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SearchPage()),
        );
      },
      backgroundColor: Colors.blue,
      child: const Icon(Icons.search, color: Colors.white),
    );
  }
}

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load search history when page opens
    context.read<SearchBloc>().add(LoadSearchHistoryEvent());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: TextField(
          controller: _searchController,
          autofocus: true,
          style: const TextStyle(color: Colors.white),
          onChanged: (query) {
            if (query.isNotEmpty) {
              context.read<SearchBloc>().add(SearchQueryChangedEvent(query));
            }
          },
          onSubmitted: (query) {
            if (query.isNotEmpty) {
              context.read<SearchBloc>().add(SearchSubmittedEvent(query));
            }
          },
          decoration: InputDecoration(
            hintText: 'Search',
            hintStyle: const TextStyle(color: Colors.white54),
            filled: true,
            fillColor: Colors.grey[900],
            prefixIcon: const Icon(Icons.search, color: Colors.white54),
            contentPadding: const EdgeInsets.symmetric(vertical: 10.0),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(26),
              borderSide: BorderSide.none,
            ),
          ),
        ),
      ),
      body: BlocBuilder<SearchBloc, SearchState>(
        builder: (context, state) {
          if (state.status == SearchStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.query.isEmpty) {
            // Show search history
            return ListView.builder(
              padding: const EdgeInsets.all(10),
              itemCount: state.searchHistory.length,
              itemBuilder: (_, index) {
                final historyItem = state.searchHistory[index];
                return Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.history, color: Colors.white54),
                      title: Text(
                        historyItem,
                        style: const TextStyle(color: Colors.white),
                      ),
                      trailing: IconButton(
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white54,
                          size: 18,
                        ),
                        onPressed: () {
                          context.read<SearchBloc>().add(
                            RemoveFromSearchHistoryEvent(historyItem),
                          );
                        },
                      ),
                      onTap: () {
                        _searchController.text = historyItem;
                        context.read<SearchBloc>().add(
                          SearchSubmittedEvent(historyItem),
                        );
                      },
                    ),
                    const Divider(color: Colors.white12),
                  ],
                );
              },
            );
          } else {
            // Show search results
            return ListView.builder(
              padding: const EdgeInsets.all(10),
              itemCount: state.results.length,
              itemBuilder: (_, index) {
                final result = state.results[index];
                return Column(
                  children: [
                    ListTile(
                      leading: CircleAvatar(
                        backgroundImage: NetworkImage(result.imageUrl),
                      ),
                      title: Text(
                        result.title,
                        style: const TextStyle(color: Colors.white),
                      ),
                      subtitle: Text(
                        result.subtitle,
                        style: const TextStyle(color: Colors.white54),
                      ),
                    ),
                    const Divider(color: Colors.white12),
                  ],
                );
              },
            );
          }
        },
      ),
      // Custom FAB for Search page
      floatingActionButton: SearchCustomFAB(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }
}

// Custom FAB for Search Page
class SearchCustomFAB extends StatelessWidget {
  const SearchCustomFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        // Add search-specific actions
        _showSearchOptions(context);
      },
      backgroundColor: Colors.blue,
      child: const Icon(Icons.filter_list, color: Colors.white),
    );
  }

  void _showSearchOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Search Filters',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                _buildFilterOption(context, 'People', Icons.person),
                _buildFilterOption(context, 'Posts', Icons.article),
                _buildFilterOption(context, 'Products', Icons.shopping_bag),
                _buildFilterOption(context, 'Businesses', Icons.business),
                const SizedBox(height: 10),
              ],
            ),
          ),
    );
  }

  Widget _buildFilterOption(BuildContext context, String title, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      onTap: () {
        Navigator.pop(context);
        // Add filter logic here
        context.read<SearchBloc>().add(
          ToggleSearchFilterEvent(title.toLowerCase()),
        );
      },
    );
  }
}

// search ui ends------------------------------------------------

//create post page------------------------------------------------

class NewCreatePostPage extends StatelessWidget {
  const NewCreatePostPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isSmallScreen = mediaQuery.size.width < 400;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () {
            // Replace with your navigation logic
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => MyHomePage(),
                //MainUserProfilePage(),
              ),
            );
          }, //=> Navigator.pop(context), //need a change here
        ),
        actions: [
          TextButton(
            onPressed: () {},
            child: const Text(
              "Post",
              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 22,
                  backgroundImage: AssetImage(Assets.newProfile),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: TextField(
                    maxLines: null,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                    decoration: const InputDecoration(
                      hintText: "What is happening?!",
                      hintStyle: TextStyle(color: Colors.grey),
                      border: InputBorder.none,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.grey.shade800),
                  ),
                  child: Row(
                    children: const [
                      Icon(Icons.public, size: 16, color: Colors.blue),
                      SizedBox(width: 4),
                      Text(
                        "Everyone",
                        style: TextStyle(color: Colors.blue, fontSize: 13),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(color: Colors.grey),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.image, color: Colors.blue),
                    ),
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.gif_box, color: Colors.blue),
                    ),
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.poll, color: Colors.blue),
                    ),
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(
                        Icons.emoji_emotions,
                        color: Colors.blue,
                      ),
                    ),
                    if (!isSmallScreen)
                      IconButton(
                        onPressed: () {},
                        icon: const Icon(Icons.location_on, color: Colors.blue),
                      ),
                  ],
                ),
                const Padding(
                  padding: EdgeInsets.only(right: 12),
                  child: Icon(
                    Icons.circle_outlined,
                    color: Colors.grey,
                    size: 18,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

//create post page ends here--------------------------------------
// new post ui #1

class SocialPostScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF6B48FF), Color(0xFF2C1A7A)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Profile Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.green,
                      child: CircleAvatar(
                        radius: 18,
                        backgroundImage: NetworkImage(
                          'https://via.placeholder.com/150',
                        ), // Replace with actual image URL
                      ),
                    ),
                    SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Khaled',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '@eng_khaled',
                          style: TextStyle(color: Colors.white70, fontSize: 14),
                        ),
                      ],
                    ),
                    Spacer(),
                    Icon(Icons.send, color: Colors.white),
                  ],
                ),
              ),
              // Main Content
              Expanded(
                child: Center(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Background Image or Placeholder
                      Container(
                        width: MediaQuery.of(context).size.width * 0.9,
                        height: MediaQuery.of(context).size.height * 0.6,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: NetworkImage(
                              'https://cdni.pornpics.com/1280/1/364/86796990/86796990_002_3ad9.jpg',
                            ), // Replace with actual image URL
                            fit: BoxFit.cover,
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      // 3D Elements (Simulated with Positioned Widgets)
                      Positioned(
                        top: 20,
                        left: 20,
                        child: Container(
                          width: 50,
                          height: 50,
                          color: Colors.pink,
                          child: Icon(Icons.category, color: Colors.white),
                        ),
                      ),
                      Positioned(
                        top: 80,
                        right: 20,
                        child: Container(
                          width: 60,
                          height: 60,
                          color: Colors.blue,
                          child: Icon(Icons.smartphone, color: Colors.white),
                        ),
                      ),
                      Positioned(
                        bottom: 20,
                        left: 20,
                        child: Container(
                          width: 70,
                          height: 70,
                          color: Colors.grey,
                          child: Icon(Icons.house, color: Colors.white),
                        ),
                      ),
                      Positioned(
                        bottom: 80,
                        right: 20,
                        child: Container(
                          width: 50,
                          height: 50,
                          color: Colors.green,
                          child: Icon(Icons.language, color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Post Text and Actions
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hi guys, take a look at the latest posts\nMode, I tried to discover the tools\nfor 3D modeling, what do you\nthink?',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.favorite, color: Colors.white70, size: 16),
                        SizedBox(width: 4),
                        Text(
                          '44 Minute',
                          style: TextStyle(color: Colors.white70),
                        ),
                        Spacer(),
                        Icon(Icons.comment, color: Colors.white70, size: 16),
                        Icon(Icons.share, color: Colors.white70, size: 16),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

//for main FAB ---------------------------------------------------------------------------------------------------------
class SearchCustomExpandableFab extends StatelessWidget {
  const SearchCustomExpandableFab({super.key});

  @override
  Widget build(BuildContext context) {
    final GlobalKey<ExpandableFabState> fabKey =
        GlobalKey<ExpandableFabState>();

    return ExpandableFab(
      key: fabKey,
      type: ExpandableFabType.up,
      distance: 60,
      childrenAnimation: ExpandableFabAnimation.none,
      // fanAngle: 40,
      openButtonBuilder: RotateFloatingActionButtonBuilder(
        child: const Icon(Icons.add, size: 40),
        fabSize: ExpandableFabSize.regular,
        foregroundColor: Colors.amber,
        backgroundColor: Colors.blue,
        shape: const CircleBorder(),
        angle: 3.14 * 2,
        elevation: 2,
      ),
      closeButtonBuilder: FloatingActionButtonBuilder(
        size: 24,
        builder: (
          BuildContext context,
          void Function()? onPressed,
          Animation<double> progress,
        ) {
          return IconButton(
            onPressed: onPressed,
            icon: const Icon(
              Icons.close_outlined,
              size: 40,
              color: Colors.amber,
            ),
          );
        },
      ),
      overlayStyle: ExpandableFabOverlayStyle(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withOpacity(0.9)
                : Colors.white.withOpacity(0.9),
      ),

      children: [
        _buildActionRow(
          label: 'Go Live',
          icon: Icons.video_call_outlined,
          onPressed: () {},
        ),
        _buildActionRow(
          label: 'Categories',
          icon: Icons.sort,
          onPressed: () {},
        ),
        _buildActionRow(
          label: 'Business Stats',
          icon: Icons.bar_chart,
          onPressed: () {
            Navigator.of(
              context,
            ).push(MaterialPageRoute(builder: (context) => BusinessStats()));
          },
        ),

        /*FloatingActionButton.small(
          heroTag: null,
          tooltip: 'Add',
          onPressed: () => _handleAction('Add'),
          child: const Icon(Icons.add),
        ),*/
      ],
    );
  }

  Widget _buildActionRow({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
            //color: Colors.deepPurple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(32),
          ),
          child: Text(label, style: const TextStyle(fontSize: 16)),
        ),
        FloatingActionButton.small(
          heroTag: null,
          tooltip: label,
          onPressed: onPressed,
          backgroundColor: Colors.white,
          foregroundColor: Colors.blue, //Colors.deepPurple,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(32),
          ),
          // elevation: 2,
          highlightElevation: 4,
          focusElevation: 4,
          hoverElevation: 4,

          // splashColor: Colors.deepPurple.withOpacity(0.2),
          // highlightColor: Colors.deepPurple.withOpacity(0.2),
          child: Icon(icon),
        ),
      ],
    );
  }
}
