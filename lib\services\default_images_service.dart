import 'dart:math';

/// Service for providing default product images based on categories
class DefaultImagesService {
  // High-quality Unsplash images for different product categories
  static const Map<String, List<String>> _categoryImages = {
    'electronics_technology': [
      'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1542393545-10f5cde2c810?w=400&h=400&fit=crop',
    ],
    'vehicles_automotive': [
      'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=400&h=400&fit=crop', // Red sports car
      'https://images.unsplash.com/photo-1494976688153-ca3ce29d8df4?w=400&h=400&fit=crop', // Blue car
      'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=400&fit=crop', // White car
      'https://images.unsplash.com/photo-1549399542-7e3f8b79c341?w=400&h=400&fit=crop', // Motorcycle
      'https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=400&h=400&fit=crop', // Car interior
    ],
    'fashion_clothing': [
      'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1490481651871-ab68de25d43d?w=400&h=400&fit=crop',
    ],
    'home_garden': [
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1493663284031-b7e3aaa4cab7?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-**********-a586c61ea9bc?w=400&h=400&fit=crop',
    ],
    'health_beauty': [
      'https://images.unsplash.com/photo-**********-195a672e8a03?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1505944270255-72b8c68c6a70?w=400&h=400&fit=crop',
    ],
    'sports_fitness': [
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-**********-7cc5ac882d5f?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
    ],
    'books_media': [
      'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1512820790803-83ca734da794?w=400&h=400&fit=crop',
    ],
    'food_beverages': [
      'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=400&fit=crop',
    ],
    'toys_games': [
      'https://images.unsplash.com/photo-1558060370-d644479cb6f7?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=400&fit=crop',
    ],
    'business_professional': [
      'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=400&fit=crop', // Business meeting
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop', // Office setup
      'https://images.unsplash.com/photo-1497032628192-86f99bcd76bc?w=400&h=400&fit=crop', // Business handshake
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=400&fit=crop', // Laptop work
      'https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=400&fit=crop', // Team meeting
    ],
    'creative_design': [
      'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=400&fit=crop', // Art supplies/palette
      'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=400&fit=crop', // Paint brushes
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop', // Design workspace
      'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=400&fit=crop', // Creative tools
      'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&h=400&fit=crop', // Art materials
    ],
    'maintenance_repair': [
      'https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=400&h=400&fit=crop', // Tools and repair
      'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400&h=400&fit=crop', // Toolbox
      'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=400&fit=crop', // Repair work
      'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=400&h=400&fit=crop', // Construction tools
      'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=400&fit=crop', // Home repair
    ],
    'education_training': [
      'https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?w=400&h=400&fit=crop', // Books and learning
      'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop', // Stack of books
      'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400&h=400&fit=crop', // Open book
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop', // Study setup
      'https://images.unsplash.com/photo-1512820790803-83ca734da794?w=400&h=400&fit=crop', // Educational materials
    ],
    'other': [
      'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop', // Shopping bags/general commerce
      'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop', // General products
      'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop', // Retail store
      'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=400&fit=crop', // Technology
      'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=400&fit=crop', // Food items
    ],
  };

  // Fallback images for when category is not found
  static const List<String> _fallbackImages = [
    'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
  ];

  /// Get a random default image for a specific category
  static String getDefaultImageForCategory(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) {
      return _getRandomFallbackImage();
    }

    // Convert category name to key format
    final categoryKey = _getCategoryKey(categoryName);
    final categoryImages = _categoryImages[categoryKey];

    if (categoryImages == null || categoryImages.isEmpty) {
      return _getRandomFallbackImage();
    }

    // Return a random image from the category
    final random = Random();
    return categoryImages[random.nextInt(categoryImages.length)];
  }

  /// Get multiple default images for a category (for products with multiple images)
  static List<String> getDefaultImagesForCategory(
    String? categoryName, {
    int count = 1,
  }) {
    if (count <= 0) return [];

    final images = <String>[];
    final categoryKey = _getCategoryKey(categoryName ?? '');
    final categoryImages = _categoryImages[categoryKey] ?? _fallbackImages;

    final random = Random();
    for (int i = 0; i < count; i++) {
      images.add(categoryImages[random.nextInt(categoryImages.length)]);
    }

    return images;
  }

  /// Get a random fallback image
  static String _getRandomFallbackImage() {
    final random = Random();
    return _fallbackImages[random.nextInt(_fallbackImages.length)];
  }

  /// Convert category name to key format
  static String _getCategoryKey(String categoryName) {
    return categoryName
        .toLowerCase()
        .replaceAll(' ', '_')
        .replaceAll('&', '')
        .replaceAll('-', '_')
        .trim();
  }

  /// Get all available category keys
  static List<String> getAvailableCategories() {
    return _categoryImages.keys.toList();
  }

  /// Check if a category has default images
  static bool hasImagesForCategory(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) return false;
    final categoryKey = _getCategoryKey(categoryName);
    return _categoryImages.containsKey(categoryKey);
  }

  /// Get a placeholder image URL for when no image is available
  static String getPlaceholderImage() {
    return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&q=80';
  }

  /// Generate product images based on product name and category
  static List<String> generateProductImages({
    required String productName,
    String? categoryName,
    int imageCount = 1,
  }) {
    // If no images needed, return empty list
    if (imageCount <= 0) return [];

    // Get category-specific images
    if (categoryName != null && categoryName.isNotEmpty) {
      return getDefaultImagesForCategory(categoryName, count: imageCount);
    }

    // Fallback to general images
    return getDefaultImagesForCategory('other', count: imageCount);
  }

  /// Get preview images for category selection in UI
  static List<String> getCategoryPreviewImages(String categoryName) {
    return getDefaultImagesForCategory(categoryName, count: 3);
  }

  /// Get a single hero image for a category
  static String getCategoryHeroImage(String categoryName) {
    return getDefaultImageForCategory(categoryName);
  }

  /// Validate if an image URL is accessible (basic check)
  static bool isValidImageUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Get optimized image URL with specific dimensions
  static String getOptimizedImageUrl(
    String baseUrl, {
    int? width,
    int? height,
    int quality = 80,
  }) {
    if (!baseUrl.contains('unsplash.com')) {
      return baseUrl; // Return as-is for non-Unsplash URLs
    }

    final uri = Uri.parse(baseUrl);
    final queryParams = Map<String, String>.from(uri.queryParameters);

    if (width != null) queryParams['w'] = width.toString();
    if (height != null) queryParams['h'] = height.toString();
    queryParams['q'] = quality.toString();
    queryParams['fit'] = 'crop';

    return uri.replace(queryParameters: queryParams).toString();
  }
}
