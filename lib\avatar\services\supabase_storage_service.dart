import 'dart:io';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/auth/auth_result.dart';
import '../../services/supabase_service.dart';
import '../models/image_config.dart';
import '../models/image_upload_result.dart';

/// Enhanced Supabase Storage Service following best practices
class SupabaseStorageService extends BaseSupabaseService {
  static final Logger _logger = Logger();
  static const String _logTag = '[SupabaseStorageService]';

  // Storage bucket names - matching your actual Supabase buckets
  static const String _profileImagesBucket = 'profile-images';
  static const String _backgroundImagesBucket =
      'background-images'; // Fixed typo: was 'backgroung images'
  static const String _productImagesBucket = 'product-images';
  static const String _shopLogoBucket = 'shop-logo';
  static const String _shopBannerBucket = 'shop-banner';

  // Public getters for bucket names (needed for delete operations)
  static String get profileImagesBucket => _profileImagesBucket;
  static String get backgroundImagesBucket => _backgroundImagesBucket;
  static String get productImagesBucket => _productImagesBucket;
  static String get shopLogoBucket => _shopLogoBucket;
  static String get shopBannerBucket => _shopBannerBucket;

  /// Upload profile image with optimized settings
  static Future<ImageUploadResult> uploadProfileImage({
    required String userId,
    required File imageFile,
    bool replaceExisting = true,
  }) async {
    return _uploadImageWithConfig(
      bucket: _profileImagesBucket,
      userId: userId,
      imageFile: imageFile,
      config: ImageConfig.profile,
      prefix: 'profile',
      replaceExisting: replaceExisting,
    );
  }

  /// Upload background image with optimized settings
  static Future<ImageUploadResult> uploadBackgroundImage({
    required String userId,
    required File imageFile,
    bool replaceExisting = true,
  }) async {
    return _uploadImageWithConfig(
      bucket: _backgroundImagesBucket,
      userId: userId,
      imageFile: imageFile,
      config: ImageConfig.background,
      prefix: 'background',
      replaceExisting: replaceExisting,
    );
  }

  /// Upload product image with optimized settings
  static Future<ImageUploadResult> uploadProductImage({
    required String userId,
    required File imageFile,
    String? productId,
    bool replaceExisting = true,
  }) async {
    return _uploadImageWithConfig(
      bucket: _productImagesBucket,
      userId: userId,
      imageFile: imageFile,
      config: ImageConfig.product,
      prefix: productId != null ? 'product_$productId' : 'product',
      replaceExisting: replaceExisting,
    );
  }

  /// Upload shop logo with optimized settings
  static Future<ImageUploadResult> uploadShopLogo({
    required String userId,
    required File imageFile,
    String? shopId,
    bool replaceExisting = true,
  }) async {
    return _uploadImageWithConfig(
      bucket: _shopLogoBucket,
      userId: userId,
      imageFile: imageFile,
      config: ImageConfig.shopLogo,
      prefix: shopId != null ? 'shop_logo_$shopId' : 'shop_logo',
      replaceExisting: replaceExisting,
    );
  }

  /// Upload shop banner with optimized settings
  static Future<ImageUploadResult> uploadShopBanner({
    required String userId,
    required File imageFile,
    String? shopId,
    bool replaceExisting = true,
  }) async {
    return _uploadImageWithConfig(
      bucket: _shopBannerBucket,
      userId: userId,
      imageFile: imageFile,
      config: ImageConfig.shopBanner,
      prefix: shopId != null ? 'shop_banner_$shopId' : 'shop_banner',
      replaceExisting: replaceExisting,
    );
  }

  /// Generic image upload with configuration
  static Future<ImageUploadResult> _uploadImageWithConfig({
    required String bucket,
    required String userId,
    required File imageFile,
    required ImageConfig config,
    required String prefix,
    bool replaceExisting = true,
  }) async {
    try {
      _logger.i('$_logTag 📤 Uploading image to bucket: $bucket');

      // Validate file exists
      if (!await imageFile.exists()) {
        const error = 'Image file does not exist';
        _logger.e('$_logTag ❌ $error');
        return ImageUploadResult.error(error);
      }

      // Check file size
      final fileSize = await imageFile.length();
      if (fileSize > config.maxFileSizeBytes) {
        final maxSizeMB = (config.maxFileSizeBytes / (1024 * 1024))
            .toStringAsFixed(1);
        final error = 'Image file must be less than ${maxSizeMB}MB';
        _logger.e('$_logTag ❌ $error');
        return ImageUploadResult.error(error);
      }

      // Validate file extension
      final fileExtension = path.extension(imageFile.path).toLowerCase();
      if (!config.allowedExtensions.contains(fileExtension)) {
        final allowedFormats = config.allowedExtensions.join(', ');
        final error = 'Invalid image format. Allowed formats: $allowedFormats';
        _logger.e('$_logTag ❌ $error');
        return ImageUploadResult.error(error);
      }

      // Generate unique filename with user folder structure
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '$userId/${prefix}_$timestamp$fileExtension';

      // Read file bytes
      final Uint8List fileBytes = await imageFile.readAsBytes();

      // Upload to Supabase Storage with proper options
      await BaseSupabaseService.client.storage
          .from(bucket)
          .uploadBinary(
            fileName,
            fileBytes,
            fileOptions: FileOptions(
              cacheControl: '3600', // 1 hour cache
              upsert: replaceExisting,
              contentType: _getContentType(fileExtension),
            ),
          );

      // Get public URL
      final publicUrl = BaseSupabaseService.client.storage
          .from(bucket)
          .getPublicUrl(fileName);

      _logger.i('$_logTag ✅ Image uploaded successfully: $publicUrl');
      return ImageUploadResult.success(
        imageUrl: publicUrl,
        fileName: fileName,
        fileSizeBytes: fileSize,
      );
    } on StorageException catch (e) {
      final error = 'Storage error: ${e.message}';
      _logger.e('$_logTag ❌ $error');
      return ImageUploadResult.error(error);
    } catch (e) {
      final error = 'Failed to upload image: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return ImageUploadResult.error(error);
    }
  }

  /// Get optimized image URL with transformations
  static String getOptimizedImageUrl(
    String bucket,
    String fileName, {
    int? width,
    int? height,
    int quality = 80,
    String resize = 'cover',
  }) {
    try {
      return BaseSupabaseService.client.storage
          .from(bucket)
          .getPublicUrl(
            fileName,
            transform: TransformOptions(
              width: width,
              height: height,
              quality: quality,
              resize:
                  resize == 'cover'
                      ? ResizeMode.cover
                      : resize == 'contain'
                      ? ResizeMode.contain
                      : ResizeMode.fill,
            ),
          );
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to get optimized URL: $e');
      // Return original URL as fallback
      return BaseSupabaseService.client.storage
          .from(bucket)
          .getPublicUrl(fileName);
    }
  }

  /// Get optimized profile image URL
  static String getOptimizedProfileImageUrl(String fileName) {
    return getOptimizedImageUrl(
      _profileImagesBucket,
      fileName,
      width: ImageConfig.profile.transformWidth,
      height: ImageConfig.profile.transformHeight,
      quality: ImageConfig.profile.transformQuality ?? 80,
    );
  }

  /// Get optimized background image URL
  static String getOptimizedBackgroundImageUrl(String fileName) {
    return getOptimizedImageUrl(
      _backgroundImagesBucket,
      fileName,
      width: ImageConfig.background.transformWidth,
      height: ImageConfig.background.transformHeight,
      quality: ImageConfig.background.transformQuality ?? 80,
    );
  }

  /// Delete image from storage
  static Future<AuthResult<bool>> deleteImage({
    required String bucket,
    required String fileName,
  }) async {
    try {
      _logger.i('$_logTag 🗑️ Deleting image: $fileName from bucket: $bucket');

      await BaseSupabaseService.client.storage.from(bucket).remove([fileName]);

      _logger.i('$_logTag ✅ Image deleted successfully');
      return AuthResult.success(true);
    } on StorageException catch (e) {
      final error = 'Storage error: ${e.message}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    } catch (e) {
      final error = 'Failed to delete image: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Extract filename from URL for deletion
  static String? extractFileNameFromUrl(String url, String bucket) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      // Find bucket in path and get everything after it
      final bucketIndex = pathSegments.indexOf(bucket);
      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        // Join all segments after bucket to handle folder structure
        return pathSegments.sublist(bucketIndex + 1).join('/');
      }

      return null;
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to extract filename from URL: $e');
      return null;
    }
  }

  /// Get content type based on file extension
  static String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}
