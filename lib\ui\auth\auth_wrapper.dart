import 'package:business_app/ui/auth/welcome_page.dart';
import 'package:business_app/ui/home_page.dart';
import 'package:business_app/ui/auth/signup_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:logger/logger.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      buildWhen: (previous, current) {
        // Rebuild when authentication status or signup step changes
        return previous.status != current.status ||
            previous.signupStep != current.signupStep;
      },
      builder: (context, state) {
        _logger.i('🔄 AuthWrapper: Current state - ${state.status}');

        switch (state.status) {
          case AuthStatus.initial:
          case AuthStatus.loading:
            _logger.i('⏳ AuthWrapper: Showing loading state');
            // Show minimal loading during the brief auth check
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );

          case AuthStatus.unauthenticated:
          case AuthStatus.error:
            _logger.i('🚫 AuthWrapper: User unauthenticated/error state');

            // Check if user needs onboarding (Google users or regular signup)
            if (state.signupStep != null) {
              _logger.i(
                '📝 AuthWrapper: Routing user to signup step: ${state.signupStep}',
              );
              // User needs to complete onboarding
              return const SignupPage();
            }
            _logger.i('🏠 AuthWrapper: Routing to welcome page');
            // Regular unauthenticated users go to welcome page
            return const WelcomePageAlternative();

          case AuthStatus.authenticated:
            _logger.i('✅ AuthWrapper: User authenticated, routing to home');
            // Go directly to home page - no delays
            return const MyHomePage();
        }
      },
    );
  }
}
