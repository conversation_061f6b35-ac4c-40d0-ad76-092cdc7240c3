import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/models/auth/user_role.dart';

/// Utility class for security-related operations
class SecurityUtils {
  /// Private constructor to prevent instantiation
  SecurityUtils._();

  /// Checks if the current user has the required role
  static bool hasRole(BuildContext context, UserRole requiredRole) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.hierarchyLevel >= requiredRole.hierarchyLevel;
  }

  /// Checks if the current user has a specific permission
  static bool hasPermission(BuildContext context, String permission) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return false;
    }

    final userRole = authState.userRole ?? UserRole.user;
    return userRole.canAccess(permission);
  }

  /// Gets the current user's role
  static UserRole? getCurrentUserRole(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    if (authState.status != AuthStatus.authenticated) {
      return null;
    }

    return authState.userRole;
  }

  /// Checks if the current user is an admin (Admin or Super Admin)
  static bool isAdmin(BuildContext context) {
    return hasRole(context, UserRole.admin);
  }

  /// Checks if the current user is a moderator or above
  static bool isModerator(BuildContext context) {
    return hasRole(context, UserRole.moderator);
  }

  /// Checks if the current user is a super admin
  static bool isSuperAdmin(BuildContext context) {
    return hasRole(context, UserRole.superAdmin);
  }

  /// Checks if the current user is authenticated
  static bool isAuthenticated(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    return authState.status == AuthStatus.authenticated;
  }

  /// Shows an access denied dialog
  static void showAccessDeniedDialog(
    BuildContext context, {
    String? title,
    String? message,
    UserRole? requiredRole,
    String? requiredPermission,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.security,
              color: Colors.red.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              title ?? 'Access Denied',
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message ?? 'You don\'t have permission to perform this action.',
              style: const TextStyle(fontSize: 16),
            ),
            if (requiredRole != null) ...[
              const SizedBox(height: 12),
              Text(
                'Required role: ${requiredRole.displayName}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
            if (requiredPermission != null) ...[
              const SizedBox(height: 12),
              Text(
                'Required permission: $requiredPermission',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Shows an access denied snackbar
  static void showAccessDeniedSnackBar(
    BuildContext context, {
    String? message,
    UserRole? requiredRole,
    String? requiredPermission,
  }) {
    String snackBarMessage = message ?? 'Access denied';
    
    if (requiredRole != null) {
      snackBarMessage += ' - Required role: ${requiredRole.displayName}';
    } else if (requiredPermission != null) {
      snackBarMessage += ' - Required permission: $requiredPermission';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.security,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                snackBarMessage,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        duration: const Duration(milliseconds: 800),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// Executes an action only if the user has the required role
  static void executeWithRoleCheck(
    BuildContext context,
    UserRole requiredRole,
    VoidCallback action, {
    VoidCallback? onAccessDenied,
    bool showAccessDeniedDialog = true,
  }) {
    if (hasRole(context, requiredRole)) {
      action();
    } else {
      if (onAccessDenied != null) {
        onAccessDenied();
      } else if (showAccessDeniedDialog) {
        SecurityUtils.showAccessDeniedDialog(
          context,
          requiredRole: requiredRole,
        );
      }
    }
  }

  /// Executes an action only if the user has the required permission
  static void executeWithPermissionCheck(
    BuildContext context,
    String permission,
    VoidCallback action, {
    VoidCallback? onAccessDenied,
    bool showAccessDeniedDialog = true,
  }) {
    if (hasPermission(context, permission)) {
      action();
    } else {
      if (onAccessDenied != null) {
        onAccessDenied();
      } else if (showAccessDeniedDialog) {
        SecurityUtils.showAccessDeniedDialog(
          context,
          requiredPermission: permission,
        );
      }
    }
  }

  /// Gets a user-friendly role display name with color
  static Widget getRoleChip(UserRole role) {
    Color chipColor;
    IconData iconData;

    switch (role) {
      case UserRole.user:
        chipColor = Colors.grey;
        iconData = Icons.person;
        break;
      case UserRole.moderator:
        chipColor = Colors.amber;
        iconData = Icons.shield;
        break;
      case UserRole.admin:
        chipColor = Colors.blue;
        iconData = Icons.admin_panel_settings;
        break;
      case UserRole.superAdmin:
        chipColor = Colors.red;
        iconData = Icons.security;
        break;
    }

    return Chip(
      avatar: Icon(
        iconData,
        color: Colors.white,
        size: 16,
      ),
      label: Text(
        role.displayName,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
      backgroundColor: chipColor,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    );
  }

  /// Validates if a role change is allowed
  static bool canChangeRole(
    BuildContext context,
    UserRole fromRole,
    UserRole toRole,
  ) {
    final currentUserRole = getCurrentUserRole(context);
    if (currentUserRole == null) return false;

    // Only super admins can assign roles
    if (!currentUserRole.isSuperAdmin) return false;

    // Can't demote yourself
    final authState = context.read<AuthBloc>().state;
    if (authState.userId == 'current_user_id') {
      // This would need to be implemented with actual user ID comparison
      return false;
    }

    // Can't promote someone to a role higher than your own
    if (toRole.hierarchyLevel > currentUserRole.hierarchyLevel) {
      return false;
    }

    return true;
  }

  /// Gets security audit log entry
  static Map<String, dynamic> createAuditLogEntry(
    String action,
    BuildContext context, {
    Map<String, dynamic>? additionalData,
  }) {
    final authState = context.read<AuthBloc>().state;
    final userRole = authState.userRole ?? UserRole.user;

    return {
      'timestamp': DateTime.now().toIso8601String(),
      'userId': authState.userId,
      'userName': authState.userName,
      'userRole': userRole.value,
      'action': action,
      'additionalData': additionalData ?? {},
    };
  }
}
