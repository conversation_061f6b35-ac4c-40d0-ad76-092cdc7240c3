# Shop Image Upload Upgrade Guide

This guide shows how to upgrade from the basic `ShopImageUploadWidget` to the new comprehensive `ShopLogoFormField` and `ShopBannerFormField` widgets.

## Why Upgrade?

### Current ShopImageUploadWidget Limitations:
- ❌ Only returns file paths, no actual upload
- ❌ No device permission handling
- ❌ No database integration
- ❌ Basic error handling
- ❌ Manual upload implementation required

### New Form Fields Benefits:
- ✅ **Complete Upload Flow**: Automatic upload to Supabase storage
- ✅ **Device Permissions**: Automatic camera/gallery permission handling
- ✅ **Database Integration**: Automatic shop record updates
- ✅ **Error Handling**: Comprehensive error management with user feedback
- ✅ **Loading States**: Visual feedback during operations
- ✅ **Image Processing**: Automatic resizing and compression
- ✅ **Form Integration**: Seamless integration with existing forms

## Quick Migration

### Step 1: Update Imports
The new widgets are already available through the existing import:

```dart
import 'widgets/shop_form_field.dart'; // Already imported in shop_creation_page.dart
```

This import now includes:
- `ShopFormField` (existing)
- `ShopLogoFormField` (new)
- `ShopBannerFormField` (new)

### Step 2: Replace ShopImageUploadWidget

#### Before (Current Implementation):
```dart
// Shop Logo Upload
ShopImageUploadWidget(
  imageUrl: state.shopLogoUrl,
  label: 'Shop Logo',
  hint: 'Upload your shop logo\n(Optional)',
  icon: Icons.image,
  height: 150,
  onImageSelected: (imagePath) => context
      .read<ShopCreationBloc>()
      .add(UploadShopLogoEvent(imagePath)),
  onRemoveImage: () => context.read<ShopCreationBloc>().add(
    const UpdateShopLogoEvent(null),
  ),
  isLoading: state.isUploading,
  uploadProgress: state.uploadProgress,
),

// Shop Banner Upload
ShopImageUploadWidget(
  imageUrl: state.shopBannerUrl,
  label: 'Shop Banner',
  hint: 'Upload your shop banner\n(Optional)',
  icon: Icons.panorama,
  height: 120,
  onImageSelected: (imagePath) => context
      .read<ShopCreationBloc>()
      .add(UploadShopBannerEvent(imagePath)),
  onRemoveImage: () => context.read<ShopCreationBloc>().add(
    const UpdateShopBannerEvent(null),
  ),
  isLoading: state.isUploading,
  uploadProgress: state.uploadProgress,
),
```

#### After (New Implementation):
```dart
// Shop Logo Upload
ShopLogoFormField(
  label: 'Shop Logo',
  currentLogoUrl: state.shopLogoUrl,
  onLogoChanged: (logoUrl) => context
      .read<ShopCreationBloc>()
      .add(UpdateShopLogoEvent(logoUrl)),
  onError: (error) => _showError(context, error),
  size: 120,
  isEditable: true,
),

// Shop Banner Upload
ShopBannerFormField(
  label: 'Shop Banner',
  currentBannerUrl: state.shopBannerUrl,
  onBannerChanged: (bannerUrl) => context
      .read<ShopCreationBloc>()
      .add(UpdateShopBannerEvent(bannerUrl)),
  onError: (error) => _showError(context, error),
  height: 200,
  isEditable: true,
  overlayWidget: state.shopName.isNotEmpty
      ? Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                state.shopName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 3,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
            ],
          ),
        )
      : null,
),
```

### Step 3: Update BLoC Events (Optional)

If you want to simplify your BLoC, you can remove the upload events since the widgets handle uploads automatically:

#### Before:
```dart
// BLoC needs to handle file upload
class UploadShopLogoEvent extends ShopCreationEvent {
  final String imagePath;
  const UploadShopLogoEvent(this.imagePath);
}

class UploadShopBannerEvent extends ShopCreationEvent {
  final String imagePath;
  const UploadShopBannerEvent(this.imagePath);
}
```

#### After:
```dart
// BLoC only needs to store URLs
class UpdateShopLogoEvent extends ShopCreationEvent {
  final String? logoUrl;
  const UpdateShopLogoEvent(this.logoUrl);
}

class UpdateShopBannerEvent extends ShopCreationEvent {
  final String? bannerUrl;
  const UpdateShopBannerEvent(this.bannerUrl);
}
```

### Step 4: Add Error Handling Helper

Add this helper method to show errors:

```dart
void _showError(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Row(
        children: [
          const Icon(Icons.error, color: Colors.white),
          const SizedBox(width: 8),
          Expanded(child: Text(message)),
        ],
      ),
      backgroundColor: Colors.red,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
    ),
  );
}
```

## Complete Example Integration

Here's how the shop creation page would look with the new widgets:

```dart
class _ShopCreationPageState extends State<ShopCreationPage> {
  // ... existing code ...

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ShopCreationBloc, ShopCreationState>(
      builder: (context, state) {
        return Scaffold(
          // ... existing scaffold code ...
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // ... existing welcome text ...

                  // Shop Logo Upload - NEW
                  ShopLogoFormField(
                    label: 'Shop Logo',
                    currentLogoUrl: state.shopLogoUrl,
                    onLogoChanged: (logoUrl) => context
                        .read<ShopCreationBloc>()
                        .add(UpdateShopLogoEvent(logoUrl)),
                    onError: (error) => _showError(context, error),
                    size: 120,
                    isEditable: true,
                  ),
                  const SizedBox(height: 24),

                  // Shop Banner Upload - NEW
                  ShopBannerFormField(
                    label: 'Shop Banner',
                    currentBannerUrl: state.shopBannerUrl,
                    onBannerChanged: (bannerUrl) => context
                        .read<ShopCreationBloc>()
                        .add(UpdateShopBannerEvent(bannerUrl)),
                    onError: (error) => _showError(context, error),
                    height: 200,
                    isEditable: true,
                    overlayWidget: state.shopName.isNotEmpty
                        ? Container(
                            padding: const EdgeInsets.all(16),
                            child: Text(
                              state.shopName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 32),

                  // Shop Name Field
                  ShopFormField(
                    label: 'Shop Name',
                    hint: 'Enter your shop name',
                    value: state.shopName,
                    onChanged: (value) => context
                        .read<ShopCreationBloc>()
                        .add(UpdateShopNameEvent(value)),
                    errorText: state.shopNameError,
                    prefixIcon: Icons.store,
                    isRequired: true,
                    maxLength: 100,
                  ),
                  const SizedBox(height: 24),

                  // Shop Description Field
                  ShopFormField(
                    label: 'Shop Description',
                    hint: 'Describe what your shop offers...',
                    value: state.shopDescription,
                    onChanged: (value) => context
                        .read<ShopCreationBloc>()
                        .add(UpdateShopDescriptionEvent(value)),
                    errorText: state.shopDescriptionError,
                    prefixIcon: Icons.description,
                    maxLines: 4,
                    isRequired: true,
                  ),
                  const SizedBox(height: 40),

                  // Create Shop Button
                  GradientButton(
                    text: 'Create My Shop',
                    icon: Icons.rocket_launch,
                    onPressed: state.isFormValid && !state.isLoading
                        ? () => context
                            .read<ShopCreationBloc>()
                            .add(const CreateShopEvent())
                        : null,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
```

## Benefits After Migration

1. **Simplified Code**: No need to handle file uploads manually
2. **Better UX**: Automatic loading states and error handling
3. **Device Integration**: Proper permission handling
4. **Database Sync**: Automatic shop record updates
5. **Image Optimization**: Automatic resizing and compression
6. **Error Recovery**: User-friendly error messages and retry options

## Testing

After migration, test:
1. **Camera Access**: Take photos with camera
2. **Gallery Access**: Select images from gallery
3. **Permissions**: Test permission denied scenarios
4. **Upload Flow**: Verify images upload to Supabase
5. **Database Updates**: Confirm shop records are updated
6. **Error Handling**: Test network failures and invalid files

## Rollback Plan

If you need to rollback, simply:
1. Replace the new form fields with the original `ShopImageUploadWidget`
2. Restore the original BLoC upload events
3. The old widgets are still available and functional
