-- =====================================================
-- SOCIAL INTERACTION TRIGGERS
-- Automated counter updates and notifications
-- =====================================================

-- =====================================================
-- TRIGGERS FOR COUNTER UPDATES
-- =====================================================

-- Trigger for follows counter updates
CREATE TRIGGER follows_counter_trigger
    AFTER INSERT OR DELETE ON public.follows
    FOR EACH ROW EXECUTE FUNCTION public.update_profile_counters();

-- Trigger for post likes counter updates
CREATE TRIGGER post_likes_counter_trigger
    AFTER INSERT OR DELETE ON public.likes
    FOR EACH ROW EXECUTE FUNCTION public.update_post_counters();

-- Trigger for post comments counter updates
CREATE TRIGGER post_comments_counter_trigger
    AFTER INSERT OR DELETE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION public.update_post_counters();

-- Trigger for comment likes counter updates
CREATE OR REPLACE FUNCTION public.update_comment_counters()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle comment likes
    IF TG_TABLE_NAME = 'comment_likes' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE public.comments 
            SET likes_count = likes_count + 1 
            WHERE id = NEW.comment_id;
            RETURN NEW;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE public.comments 
            SET likes_count = GREATEST(likes_count - 1, 0) 
            WHERE id = OLD.comment_id;
            RETURN OLD;
        END IF;
    END IF;
    
    -- Handle comment replies
    IF TG_TABLE_NAME = 'comments' THEN
        IF TG_OP = 'INSERT' AND NEW.parent_id IS NOT NULL THEN
            UPDATE public.comments 
            SET replies_count = replies_count + 1 
            WHERE id = NEW.parent_id;
            RETURN NEW;
        ELSIF TG_OP = 'DELETE' AND OLD.parent_id IS NOT NULL THEN
            UPDATE public.comments 
            SET replies_count = GREATEST(replies_count - 1, 0) 
            WHERE id = OLD.parent_id;
            RETURN OLD;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for comment counters
CREATE TRIGGER comment_likes_counter_trigger
    AFTER INSERT OR DELETE ON public.comment_likes
    FOR EACH ROW EXECUTE FUNCTION public.update_comment_counters();

CREATE TRIGGER comment_replies_counter_trigger
    AFTER INSERT OR DELETE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION public.update_comment_counters();

-- Trigger for comment updated_at
CREATE TRIGGER update_comments_updated_at
    BEFORE UPDATE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- NOTIFICATION TRIGGERS
-- =====================================================

-- Trigger for follow notifications
CREATE TRIGGER follow_notification_trigger
    AFTER INSERT ON public.follows
    FOR EACH ROW EXECUTE FUNCTION public.handle_follow_notification();

-- Function to handle like notifications
CREATE OR REPLACE FUNCTION public.handle_like_notification()
RETURNS TRIGGER AS $$
DECLARE
    post_author_id UUID;
    liker_name TEXT;
    post_content TEXT;
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Get post author and liker info
        SELECT p.user_id, pr.full_name, LEFT(p.content, 50)
        INTO post_author_id, liker_name, post_content
        FROM public.posts p
        JOIN public.profiles pr ON pr.id = NEW.user_id
        WHERE p.id = NEW.post_id;
        
        -- Don't notify if user likes their own post
        IF post_author_id != NEW.user_id THEN
            -- Create notification for post author
            PERFORM public.create_notification(
                post_author_id,
                'like',
                'Post Liked',
                liker_name || ' liked your post',
                jsonb_build_object(
                    'liker_id', NEW.user_id,
                    'liker_name', liker_name,
                    'post_id', NEW.post_id,
                    'post_content', post_content
                )
            );
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger for like notifications
CREATE TRIGGER like_notification_trigger
    AFTER INSERT ON public.likes
    FOR EACH ROW EXECUTE FUNCTION public.handle_like_notification();

-- Function to handle comment notifications
CREATE OR REPLACE FUNCTION public.handle_comment_notification()
RETURNS TRIGGER AS $$
DECLARE
    post_author_id UUID;
    commenter_name TEXT;
    post_content TEXT;
    parent_comment_author_id UUID;
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Get post author and commenter info
        SELECT p.user_id, pr.full_name, LEFT(p.content, 50)
        INTO post_author_id, commenter_name, post_content
        FROM public.posts p
        JOIN public.profiles pr ON pr.id = NEW.user_id
        WHERE p.id = NEW.post_id;
        
        -- Notify post author (if not commenting on own post)
        IF post_author_id != NEW.user_id THEN
            PERFORM public.create_notification(
                post_author_id,
                'comment',
                'New Comment',
                commenter_name || ' commented on your post',
                jsonb_build_object(
                    'commenter_id', NEW.user_id,
                    'commenter_name', commenter_name,
                    'post_id', NEW.post_id,
                    'comment_id', NEW.id,
                    'post_content', post_content
                )
            );
        END IF;
        
        -- If it's a reply, notify the parent comment author
        IF NEW.parent_id IS NOT NULL THEN
            SELECT c.user_id INTO parent_comment_author_id
            FROM public.comments c
            WHERE c.id = NEW.parent_id;
            
            -- Notify parent comment author (if not replying to own comment)
            IF parent_comment_author_id != NEW.user_id AND parent_comment_author_id != post_author_id THEN
                PERFORM public.create_notification(
                    parent_comment_author_id,
                    'reply',
                    'Comment Reply',
                    commenter_name || ' replied to your comment',
                    jsonb_build_object(
                        'commenter_id', NEW.user_id,
                        'commenter_name', commenter_name,
                        'post_id', NEW.post_id,
                        'comment_id', NEW.id,
                        'parent_comment_id', NEW.parent_id
                    )
                );
            END IF;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger for comment notifications
CREATE TRIGGER comment_notification_trigger
    AFTER INSERT ON public.comments
    FOR EACH ROW EXECUTE FUNCTION public.handle_comment_notification();

-- =====================================================
-- SOCIAL ANALYTICS FUNCTIONS
-- =====================================================

-- Function to get user engagement stats
CREATE OR REPLACE FUNCTION public.get_user_engagement_stats(user_uuid UUID)
RETURNS TABLE (
    total_posts INTEGER,
    total_likes_received INTEGER,
    total_comments_received INTEGER,
    total_likes_given INTEGER,
    total_comments_given INTEGER,
    followers_count INTEGER,
    following_count INTEGER,
    engagement_rate DECIMAL(5,2)
) AS $$
DECLARE
    user_posts_count INTEGER;
    user_likes_received INTEGER;
    user_comments_received INTEGER;
BEGIN
    -- Get basic counts
    SELECT 
        COUNT(p.id)::INTEGER,
        COALESCE(SUM(p.likes_count), 0)::INTEGER,
        COALESCE(SUM(p.comments_count), 0)::INTEGER
    INTO user_posts_count, user_likes_received, user_comments_received
    FROM public.posts p
    WHERE p.user_id = user_uuid AND p.status = 'published';
    
    RETURN QUERY
    SELECT 
        user_posts_count,
        user_likes_received,
        user_comments_received,
        (SELECT COUNT(*)::INTEGER FROM public.likes WHERE user_id = user_uuid),
        (SELECT COUNT(*)::INTEGER FROM public.comments WHERE user_id = user_uuid AND is_deleted = false),
        (SELECT followers_count FROM public.profiles WHERE id = user_uuid),
        (SELECT following_count FROM public.profiles WHERE id = user_uuid),
        CASE 
            WHEN user_posts_count > 0 THEN 
                ROUND(((user_likes_received + user_comments_received)::DECIMAL / user_posts_count), 2)
            ELSE 0.00
        END as engagement_rate;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trending posts
CREATE OR REPLACE FUNCTION public.get_trending_posts(
    time_period INTERVAL DEFAULT '24 hours',
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    username VARCHAR(50),
    full_name VARCHAR(100),
    avatar_url TEXT,
    content TEXT,
    media_urls TEXT[],
    post_type VARCHAR(20),
    likes_count INTEGER,
    comments_count INTEGER,
    created_at TIMESTAMPTZ,
    engagement_score DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.user_id,
        pr.username,
        pr.full_name,
        pr.avatar_url,
        p.content,
        p.media_urls,
        p.post_type,
        p.likes_count,
        p.comments_count,
        p.created_at,
        -- Engagement score calculation (likes * 1 + comments * 2, with time decay)
        ROUND(
            (p.likes_count * 1.0 + p.comments_count * 2.0) * 
            EXP(-EXTRACT(EPOCH FROM (NOW() - p.created_at)) / 86400.0), -- Time decay factor
            2
        ) as engagement_score
    FROM public.posts p
    JOIN public.profiles pr ON p.user_id = pr.id
    WHERE 
        p.status = 'published' AND
        p.is_public = true AND
        p.created_at >= NOW() - time_period
    ORDER BY engagement_score DESC, p.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get post comments with replies
CREATE OR REPLACE FUNCTION public.get_post_comments(
    post_uuid UUID,
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    username VARCHAR(50),
    full_name VARCHAR(100),
    avatar_url TEXT,
    content TEXT,
    parent_id UUID,
    likes_count INTEGER,
    replies_count INTEGER,
    created_at TIMESTAMPTZ,
    is_liked BOOLEAN,
    level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE comment_tree AS (
        -- Base case: top-level comments
        SELECT 
            c.id,
            c.user_id,
            pr.username,
            pr.full_name,
            pr.avatar_url,
            c.content,
            c.parent_id,
            c.likes_count,
            c.replies_count,
            c.created_at,
            EXISTS(SELECT 1 FROM public.comment_likes cl WHERE cl.comment_id = c.id AND cl.user_id = auth.uid()) as is_liked,
            0 as level
        FROM public.comments c
        JOIN public.profiles pr ON c.user_id = pr.id
        WHERE c.post_id = post_uuid AND c.parent_id IS NULL AND c.is_deleted = false
        
        UNION ALL
        
        -- Recursive case: replies
        SELECT 
            c.id,
            c.user_id,
            pr.username,
            pr.full_name,
            pr.avatar_url,
            c.content,
            c.parent_id,
            c.likes_count,
            c.replies_count,
            c.created_at,
            EXISTS(SELECT 1 FROM public.comment_likes cl WHERE cl.comment_id = c.id AND cl.user_id = auth.uid()) as is_liked,
            ct.level + 1
        FROM public.comments c
        JOIN public.profiles pr ON c.user_id = pr.id
        JOIN comment_tree ct ON c.parent_id = ct.id
        WHERE c.is_deleted = false AND ct.level < 3 -- Limit nesting to 3 levels
    )
    SELECT * FROM comment_tree
    ORDER BY level, created_at ASC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
