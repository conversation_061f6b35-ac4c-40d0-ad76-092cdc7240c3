import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class VoiceCallInterface extends StatefulWidget {
  final String contactName;
  final String contactImage;
  final bool isIncoming;
  final VoidCallback? onAccept;
  final VoidCallback? onDecline;
  final VoidCallback? onEndCall;

  const VoiceCallInterface({
    super.key,
    required this.contactName,
    required this.contactImage,
    this.isIncoming = false,
    this.onAccept,
    this.onDecline,
    this.onEndCall,
  });

  @override
  State<VoiceCallInterface> createState() => _VoiceCallInterfaceState();
}

class _VoiceCallInterfaceState extends State<VoiceCallInterface>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isCallActive = false;
  String _callDuration = "00:00";
  
  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));
    
    _slideController.forward();
    
    if (!widget.isIncoming) {
      _isCallActive = true;
      _startCallTimer();
    }
  }
  
  void _startCallTimer() {
    // Simulate call timer
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _isCallActive) {
        setState(() {
          final seconds = DateTime.now().second;
          final minutes = DateTime.now().minute;
          _callDuration = "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
        });
        _startCallTimer();
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.indigo.shade900,
              Colors.purple.shade900,
              Colors.black87,
            ],
          ),
        ),
        child: SafeArea(
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              children: [
                _buildHeader(),
                Expanded(child: _buildCallContent()),
                _buildCallControls(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: Colors.white,
              size: 28,
            ),
          ),
          const Spacer(),
          Text(
            widget.isIncoming ? 'Incoming Call' : 'Voice Call',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () {},
            icon: const Icon(
              Icons.more_vert,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCallContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Profile Image with Pulse Animation
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: widget.isIncoming ? _pulseAnimation.value : 1.0,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.3),
                      blurRadius: 30,
                      spreadRadius: widget.isIncoming ? 10 : 5,
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 100,
                  backgroundImage: NetworkImage(widget.contactImage),
                  backgroundColor: Colors.grey.shade300,
                ),
              ),
            );
          },
        ),
        
        const SizedBox(height: 40),
        
        // Contact Name
        Text(
          widget.contactName,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 32,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Call Status
        Text(
          widget.isIncoming 
              ? 'Incoming voice call...' 
              : _isCallActive 
                  ? _callDuration
                  : 'Calling...',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 18,
            fontWeight: FontWeight.w400,
          ),
        ),
        
        if (!widget.isIncoming && !_isCallActive) ...[
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              'Connecting...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCallControls() {
    return Container(
      padding: const EdgeInsets.all(30),
      child: Column(
        children: [
          if (!widget.isIncoming) ...[
            // Call Action Buttons (Mute, Speaker, etc.)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  icon: _isMuted ? Icons.mic_off : Icons.mic,
                  isActive: _isMuted,
                  onTap: () => setState(() => _isMuted = !_isMuted),
                ),
                _buildActionButton(
                  icon: Icons.dialpad,
                  onTap: () {},
                ),
                _buildActionButton(
                  icon: _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                  isActive: _isSpeakerOn,
                  onTap: () => setState(() => _isSpeakerOn = !_isSpeakerOn),
                ),
                _buildActionButton(
                  icon: Icons.add_call,
                  onTap: () {},
                ),
              ],
            ),
            const SizedBox(height: 40),
          ],
          
          // Main Call Buttons
          Row(
            mainAxisAlignment: widget.isIncoming 
                ? MainAxisAlignment.spaceEvenly 
                : MainAxisAlignment.center,
            children: [
              if (widget.isIncoming) ...[
                // Accept Button
                _buildMainCallButton(
                  icon: Icons.call,
                  color: Colors.green,
                  onTap: () {
                    setState(() => _isCallActive = true);
                    _startCallTimer();
                    widget.onAccept?.call();
                  },
                ),
                
                // Decline Button
                _buildMainCallButton(
                  icon: Icons.call_end,
                  color: Colors.red,
                  onTap: () {
                    widget.onDecline?.call();
                    Navigator.pop(context);
                  },
                ),
              ] else ...[
                // End Call Button
                _buildMainCallButton(
                  icon: Icons.call_end,
                  color: Colors.red,
                  onTap: () {
                    widget.onEndCall?.call();
                    Navigator.pop(context);
                  },
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isActive 
              ? Colors.white.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.1),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildMainCallButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color,
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.4),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }
}
