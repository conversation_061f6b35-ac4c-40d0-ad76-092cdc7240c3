import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // Register the connectivity settings plugin
    let controller = window?.rootViewController as! FlutterViewController
    ConnectivitySettingsPlugin.register(with: registrar(forPlugin: "ConnectivitySettingsPlugin")!)

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
