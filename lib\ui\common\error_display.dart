/*import 'package:business_app/models/auth/auth_result.dart';
import 'package:flutter/material.dart';
import '../../services/error_handler_service.dart';

/// Professional error display component for consistent error handling across the app
class ErrorDisplay extends StatelessWidget {
  final dynamic error;
  final VoidCallback? onRetry;
  final bool showIcon;
  final bool showRetry;
  final EdgeInsets padding;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? iconColor;
  final Color? textColor;
  final double borderRadius;
  final double iconSize;

  const ErrorDisplay({
    super.key,
    required this.error,
    this.onRetry,
    this.showIcon = true,
    this.showRetry = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    this.textStyle,
    this.backgroundColor,
    this.iconColor,
    this.textColor,
    this.borderRadius = 8.0,
    this.iconSize = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorMessage = ErrorHandlerService.getDisplayMessage(error);
    final errorType = _getErrorType(error);

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? _getBackgroundColor(theme, errorType),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Row(
        children: [
          if (showIcon) ...[
            Icon(
              _getErrorIcon(errorType),
              color: iconColor ?? _getIconColor(theme, errorType),
              size: iconSize,
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Text(
              errorMessage,
              style:
                  textStyle ??
                  TextStyle(
                    color: textColor ?? _getTextColor(theme, errorType),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          if (showRetry && onRetry != null) ...[
            const SizedBox(width: 8),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                minimumSize: const Size(60, 36),
                backgroundColor: Colors.transparent,
                foregroundColor: _getRetryColor(theme, errorType),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              child: const Text(
                'Retry',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Get error type from error object
  ErrorDisplayType _getErrorType(dynamic error) {
    if (error is AuthError) {
      return ErrorDisplayType.auth;
    } else if (error is NetworkError) {
      return ErrorDisplayType.network;
    } else if (error is AppError) {
      switch (error.severity) {
        case ErrorSeverity.critical:
        case ErrorSeverity.high:
          return ErrorDisplayType.critical;
        case ErrorSeverity.medium:
          return ErrorDisplayType.warning;
        case ErrorSeverity.low:
        // return ErrorDisplayType.info;
      }
    } else if (error is String && error.toLowerCase().contains('network')) {
      return ErrorDisplayType.network;
    } else if (error is String && error.toLowerCase().contains('permission')) {
      return ErrorDisplayType.permission;
    }

    return ErrorDisplayType.general;
  }

  /// Get appropriate icon for error type
  IconData _getErrorIcon(ErrorDisplayType type) {
    switch (type) {
      case ErrorDisplayType.auth:
        return Icons.lock_outline;
      case ErrorDisplayType.network:
        return Icons.wifi_off_outlined;
      case ErrorDisplayType.permission:
        return Icons.no_accounts_outlined;
      case ErrorDisplayType.critical:
        return Icons.error_outline;
      case ErrorDisplayType.warning:
        return Icons.warning_amber_outlined;
      //case ErrorDisplayType.info:
      // return Icons.info_outline;
      case ErrorDisplayType.general:
        return Icons.error_outline;
    }
  }

  /// Get background color based on error type
  Color _getBackgroundColor(ThemeData theme, ErrorDisplayType type) {
    final isDark = theme.brightness == Brightness.dark;

    switch (type) {
      case ErrorDisplayType.auth:
        return isDark ? const Color(0xFF362A33) : const Color(0xFFFBEDED);
      case ErrorDisplayType.network:
        return isDark ? const Color(0xFF2A3136) : const Color(0xFFEDF4FB);
      case ErrorDisplayType.permission:
        return isDark ? const Color(0xFF363333) : const Color(0xFFFFF8E1);
      case ErrorDisplayType.critical:
        return isDark ? const Color(0xFF3B2A2A) : const Color(0xFFFDEDED);
      case ErrorDisplayType.warning:
        return isDark ? const Color(0xFF3B3A2A) : const Color(0xFFFFF8E1);
      //case ErrorDisplayType.info:
      // return isDark ? const Color(0xFF2A3136) : const Color(0xFFE8F4FD);
      case ErrorDisplayType.general:
        return isDark ? const Color(0xFF333333) : const Color(0xFFF5F5F5);
    }
  }

  /// Get icon color based on error type
  Color _getIconColor(ThemeData theme, ErrorDisplayType type) {
    switch (type) {
      case ErrorDisplayType.auth:
        return const Color(0xFFE53935);
      case ErrorDisplayType.network:
        return const Color(0xFF2196F3);
      case ErrorDisplayType.permission:
        return const Color(0xFFFFA000);
      case ErrorDisplayType.critical:
        return const Color(0xFFE53935);
      case ErrorDisplayType.warning:
        return const Color(0xFFFFA000);
      //case ErrorDisplayType.info:
      //return const Color(0xFF2196F3);
      case ErrorDisplayType.general:
        return const Color(0xFF757575);
    }
  }

  /// Get text color based on error type
  Color _getTextColor(ThemeData theme, ErrorDisplayType type) {
    final isDark = theme.brightness == Brightness.dark;

    switch (type) {
      case ErrorDisplayType.auth:
        return isDark ? const Color(0xFFE57373) : const Color(0xFFB71C1C);
      case ErrorDisplayType.network:
        return isDark ? const Color(0xFF64B5F6) : const Color(0xFF0D47A1);
      case ErrorDisplayType.permission:
        return isDark ? const Color(0xFFFFD54F) : const Color(0xFFE65100);
      case ErrorDisplayType.critical:
        return isDark ? const Color(0xFFE57373) : const Color(0xFFB71C1C);
      case ErrorDisplayType.warning:
        return isDark ? const Color(0xFFFFD54F) : const Color(0xFFE65100);
      //case ErrorDisplayType.info:
      //  return isDark ? const Color(0xFF64B5F6) : const Color(0xFF0D47A1);
      case ErrorDisplayType.general:
        return isDark ? Colors.white70 : Colors.black87;
    }
  }

  /// Get retry button color based on error type
  Color _getRetryColor(ThemeData theme, ErrorDisplayType type) {
    switch (type) {
      case ErrorDisplayType.auth:
        return const Color(0xFFE53935);
      case ErrorDisplayType.network:
        return const Color(0xFF2196F3);
      case ErrorDisplayType.permission:
        return const Color(0xFFFFA000);
      case ErrorDisplayType.critical:
        return const Color(0xFFE53935);
      case ErrorDisplayType.warning:
        return const Color(0xFFFFA000);
      //case ErrorDisplayType.info:
      // return const Color(0xFF2196F3);
      case ErrorDisplayType.general:
        return theme.colorScheme.primary;
    }
  }
}

/// Snackbar version of error display
class ErrorSnackBar extends SnackBar {
  ErrorSnackBar({
    super.key,
    required dynamic error,
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) : super(
         content: ErrorDisplay(
           error: error,
           onRetry: onRetry,
           padding: EdgeInsets.zero,
           backgroundColor: Colors.transparent,
         ),
         behavior: SnackBarBehavior.floating,
         backgroundColor: Colors.transparent,
         elevation: 0,
         duration: duration,
         padding: const EdgeInsets.all(16),
       );
}

/// Error display types for styling
enum ErrorDisplayType {
  auth,
  network,
  permission,
  critical,
  warning,
  // info,
  general,
}

/// Extension methods for showing error snackbars
extension ErrorSnackBarExtension on BuildContext {
  void showErrorSnackBar(
    dynamic error, {
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(this).showSnackBar(
      ErrorSnackBar(error: error, onRetry: onRetry, duration: duration),
    );
  }
}
*/
