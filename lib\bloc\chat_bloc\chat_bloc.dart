import 'package:flutter_bloc/flutter_bloc.dart';
import 'chat_event.dart';
import 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  ChatBloc() : super(const ChatState()) {
    on<LoadConversationsEvent>(_onLoadConversations);
    on<LoadMessagesEvent>(_onLoadMessages);
    on<SendMessageEvent>(_onSendMessage);
    on<StartNewConversationEvent>(_onStartNewConversation);
    on<MarkMessageAsReadEvent>(_onMarkMessageAsRead);
    on<DeleteMessageEvent>(_onDeleteMessage);
    on<DeleteConversationEvent>(_onDeleteConversation);
    on<ToggleTypingEvent>(_onToggleTyping);
    on<ShowAttachmentOptionsEvent>(_onShowAttachmentOptions);
    on<HideAttachmentOptionsEvent>(_onHideAttachmentOptions);
    on<SelectAttachmentEvent>(_onSelectAttachment);
  }

  Future<void> _onLoadConversations(
    LoadConversationsEvent event,
    Emitter<ChatState> emit,
  ) async {
    emit(state.copyWith(status: ChatStatus.loading));
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final conversations = _generateMockConversations();
      
      emit(state.copyWith(
        status: ChatStatus.loaded,
        conversations: conversations,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ChatStatus.error,
        errorMessage: 'Failed to load conversations: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadMessages(
    LoadMessagesEvent event,
    Emitter<ChatState> emit,
  ) async {
    emit(state.copyWith(
      status: ChatStatus.loading,
      currentConversationId: event.conversationId,
    ));
    
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      final messages = _generateMockMessages(event.conversationId);
      
      emit(state.copyWith(
        status: ChatStatus.loaded,
        messages: messages,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ChatStatus.error,
        errorMessage: 'Failed to load messages: ${e.toString()}',
      ));
    }
  }

  Future<void> _onSendMessage(
    SendMessageEvent event,
    Emitter<ChatState> emit,
  ) async {
    if (event.message.trim().isEmpty && event.attachmentPath == null) return;

    emit(state.copyWith(status: ChatStatus.sending));
    
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      
      final newMessage = Message(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
        senderId: 'current_user',
        senderName: 'You',
        content: event.message,
        timestamp: DateTime.now(),
        isRead: true,
        attachmentUrl: event.attachmentPath,
        attachmentType: event.attachmentType,
        isSentByMe: true,
      );

      final updatedMessages = [...state.messages, newMessage];
      
      // Update conversation with new last message
      final updatedConversations = state.conversations.map((conv) {
        if (conv.id == event.conversationId) {
          return conv.copyWith(lastMessage: newMessage);
        }
        return conv;
      }).toList();

      emit(state.copyWith(
        status: ChatStatus.loaded,
        messages: updatedMessages,
        conversations: updatedConversations,
        selectedAttachmentPath: null,
        selectedAttachmentType: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ChatStatus.error,
        errorMessage: 'Failed to send message: ${e.toString()}',
      ));
    }
  }

  Future<void> _onStartNewConversation(
    StartNewConversationEvent event,
    Emitter<ChatState> emit,
  ) async {
    try {
      final newConversation = Conversation(
        id: 'conv_${DateTime.now().millisecondsSinceEpoch}',
        participantId: event.userId,
        participantName: event.userName,
        participantProfileImage: 'assets/images/profile1.jpg',
        lastSeen: DateTime.now(),
        isOnline: true,
      );

      final updatedConversations = [newConversation, ...state.conversations];
      
      emit(state.copyWith(
        conversations: updatedConversations,
        currentConversationId: newConversation.id,
        messages: [],
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ChatStatus.error,
        errorMessage: 'Failed to start conversation: ${e.toString()}',
      ));
    }
  }

  void _onMarkMessageAsRead(
    MarkMessageAsReadEvent event,
    Emitter<ChatState> emit,
  ) {
    final updatedMessages = state.messages.map((message) {
      if (message.id == event.messageId) {
        return message.copyWith(isRead: true);
      }
      return message;
    }).toList();

    emit(state.copyWith(messages: updatedMessages));
  }

  void _onDeleteMessage(
    DeleteMessageEvent event,
    Emitter<ChatState> emit,
  ) {
    final updatedMessages = state.messages
        .where((message) => message.id != event.messageId)
        .toList();

    emit(state.copyWith(messages: updatedMessages));
  }

  void _onDeleteConversation(
    DeleteConversationEvent event,
    Emitter<ChatState> emit,
  ) {
    final updatedConversations = state.conversations
        .where((conv) => conv.id != event.conversationId)
        .toList();

    emit(state.copyWith(conversations: updatedConversations));
  }

  void _onToggleTyping(
    ToggleTypingEvent event,
    Emitter<ChatState> emit,
  ) {
    final updatedTypingUsers = Map<String, bool>.from(state.typingUsers);
    updatedTypingUsers[event.conversationId] = event.isTyping;

    emit(state.copyWith(typingUsers: updatedTypingUsers));
  }

  void _onShowAttachmentOptions(
    ShowAttachmentOptionsEvent event,
    Emitter<ChatState> emit,
  ) {
    emit(state.copyWith(showAttachmentOptions: true));
  }

  void _onHideAttachmentOptions(
    HideAttachmentOptionsEvent event,
    Emitter<ChatState> emit,
  ) {
    emit(state.copyWith(showAttachmentOptions: false));
  }

  void _onSelectAttachment(
    SelectAttachmentEvent event,
    Emitter<ChatState> emit,
  ) {
    emit(state.copyWith(
      selectedAttachmentPath: event.attachmentPath,
      selectedAttachmentType: event.attachmentType,
      showAttachmentOptions: false,
    ));
  }

  List<Conversation> _generateMockConversations() {
    return List.generate(10, (index) {
      return Conversation(
        id: 'conv_$index',
        participantId: 'user_$index',
        participantName: 'User ${index + 1}',
        participantProfileImage: 'assets/images/profile${(index % 3) + 1}.jpg',
        lastMessage: Message(
          id: 'msg_last_$index',
          senderId: 'user_$index',
          senderName: 'User ${index + 1}',
          content: 'Last message from conversation $index',
          timestamp: DateTime.now().subtract(Duration(minutes: index * 10)),
          isSentByMe: false,
        ),
        unreadCount: index % 3,
        isOnline: index % 2 == 0,
        lastSeen: DateTime.now().subtract(Duration(minutes: index * 5)),
      );
    });
  }

  List<Message> _generateMockMessages(String conversationId) {
    return List.generate(20, (index) {
      final isSentByMe = index % 3 == 0;
      return Message(
        id: 'msg_${conversationId}_$index',
        senderId: isSentByMe ? 'current_user' : 'other_user',
        senderName: isSentByMe ? 'You' : 'Other User',
        content: 'Message $index in conversation $conversationId',
        timestamp: DateTime.now().subtract(Duration(minutes: index * 2)),
        isRead: true,
        isSentByMe: isSentByMe,
      );
    }).reversed.toList();
  }
}
