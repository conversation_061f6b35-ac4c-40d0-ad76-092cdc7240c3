import 'package:equatable/equatable.dart';
import '../../services/shop_service.dart';

enum ShopCreationStatus {
  initial,
  loading,
  uploading,
  validating,
  success,
  error,
  checkingExistingShop,
  existingShopFound,
}

class ShopCreationState extends Equatable {
  final ShopCreationStatus status;
  final String shopName;
  final String shopDescription;
  final String? shopLogoUrl;
  final String? shopBannerUrl;
  final Map<String, String> validationErrors;
  final String? errorMessage;
  final String? successMessage;
  final bool isFormValid;
  final Shop? createdShop;
  final Shop? existingShop;
  final double uploadProgress;

  const ShopCreationState({
    this.status = ShopCreationStatus.initial,
    this.shopName = '',
    this.shopDescription = '',
    this.shopLogoUrl,
    this.shopBannerUrl,
    this.validationErrors = const {},
    this.errorMessage,
    this.successMessage,
    this.isFormValid = false,
    this.createdShop,
    this.existingShop,
    this.uploadProgress = 0.0,
  });

  ShopCreationState copyWith({
    ShopCreationStatus? status,
    String? shopName,
    String? shopDescription,
    String? shopLogoUrl,
    String? shopBannerUrl,
    Map<String, String>? validationErrors,
    String? errorMessage,
    String? successMessage,
    bool? isFormValid,
    Shop? createdShop,
    Shop? existingShop,
    double? uploadProgress,
  }) {
    return ShopCreationState(
      status: status ?? this.status,
      shopName: shopName ?? this.shopName,
      shopDescription: shopDescription ?? this.shopDescription,
      shopLogoUrl: shopLogoUrl ?? this.shopLogoUrl,
      shopBannerUrl: shopBannerUrl ?? this.shopBannerUrl,
      validationErrors: validationErrors ?? this.validationErrors,
      errorMessage: errorMessage,
      successMessage: successMessage,
      isFormValid: isFormValid ?? this.isFormValid,
      createdShop: createdShop ?? this.createdShop,
      existingShop: existingShop ?? this.existingShop,
      uploadProgress: uploadProgress ?? this.uploadProgress,
    );
  }

  // Helper methods for validation
  bool get hasShopNameError => validationErrors.containsKey('shopName');
  bool get hasShopDescriptionError => validationErrors.containsKey('shopDescription');
  
  String? get shopNameError => validationErrors['shopName'];
  String? get shopDescriptionError => validationErrors['shopDescription'];

  bool get isLoading => status == ShopCreationStatus.loading;
  bool get isUploading => status == ShopCreationStatus.uploading;
  bool get isValidating => status == ShopCreationStatus.validating;
  bool get isCheckingExistingShop => status == ShopCreationStatus.checkingExistingShop;
  bool get hasExistingShop => status == ShopCreationStatus.existingShopFound && existingShop != null;

  @override
  List<Object?> get props => [
        status,
        shopName,
        shopDescription,
        shopLogoUrl,
        shopBannerUrl,
        validationErrors,
        errorMessage,
        successMessage,
        isFormValid,
        createdShop,
        existingShop,
        uploadProgress,
      ];
}
