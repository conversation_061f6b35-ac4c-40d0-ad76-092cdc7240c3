# Dynamic Drawer Profile Implementation Guide

## Overview

The `CustomAdvancedDrawer` has been updated to display dynamic user profile information fetched from the Supabase database. This includes the user's profile picture, name, username, followers count (displayed as "Customers"), and following count.

## Features

### 1. Dynamic Profile Data
- **Profile Picture**: Searches database for `avatar_url`, displays grey avatar with person icon if not found
- **Full Name**: User's real name from the `full_name` field
- **Username**: Displayed with @ symbol from the `username` field
- **Followers Count**: Displayed as "Customers" with smart formatting (K, M)
- **Following Count**: Displayed with smart formatting (K, M)

### 2. Loading States
- Shows a loading indicator while fetching user data
- Graceful fallback to default values if data is unavailable

### 3. Pull-to-Refresh
- Users can pull down on the drawer content to refresh profile data
- Automatically updates the display with latest information

### 4. Error Handling
- Network image loading with fallback to default asset
- Handles cases where user profile is not found

## Implementation Details

### Database Schema
The drawer fetches data from the `profiles` table with these key fields:
```sql
- id (UUID) - User ID from auth.users
- username (VARCHAR) - Unique username
- full_name (VARCHAR) - User's display name
- avatar_url (TEXT) - Profile picture URL
- followers_count (INTEGER) - Number of followers
- following_count (INTEGER) - Number of users following
```

### Key Components

#### 1. CustomAdvancedDrawer
- Converted from StatelessWidget to StatefulWidget
- Automatically loads user profile on initialization
- Provides refresh functionality

#### 2. DrawerProfileWidget (Helper Component)
- Reusable widget for displaying profile information
- Handles loading states and error cases
- Customizable tap behavior

#### 3. DrawerStatsWidget (Helper Component)
- Displays follower/following counts with smart formatting
- Formats large numbers (1K, 1.5M, etc.)

### Usage Example

```dart
// Basic usage - the drawer automatically loads user data
CustomAdvancedDrawer(
  controller: _advancedDrawerController,
  child: YourMainContent(),
)

// The drawer will automatically:
// 1. Load current user profile on initialization
// 2. Display profile picture, name, username
// 3. Show formatted follower/following counts
// 4. Handle loading and error states
```

### Data Flow

1. **Initialization**: `_loadCurrentUserProfile()` called in `initState()`
2. **Data Fetching**: Uses `AuthService.getCurrentUserProfile()` to get user data
3. **State Management**: Updates local state with fetched user data
4. **UI Update**: Rebuilds drawer with dynamic content
5. **Refresh**: Pull-to-refresh triggers data reload

### Smart Number Formatting

The drawer includes intelligent number formatting for follower/following counts:
- 0-999: Display as-is (e.g., "42")
- 1,000-999,999: Display with K suffix (e.g., "1.2K")
- 1,000,000+: Display with M suffix (e.g., "1.5M")

### Error Handling

1. **Network Images**: Automatic fallback to grey avatar with person icon if profile image fails to load
2. **Missing Data**: Displays "Loading..." or default values for missing fields
3. **API Errors**: Gracefully handles database connection issues
4. **Empty URLs**: Shows grey avatar if `avatar_url` is null or empty in database

### Customization Options

#### Profile Image Fallback
```dart
// Grey avatar fallback (current implementation)
Widget _buildGreyAvatar() {
  return Container(
    width: 60,
    height: 60,
    decoration: const BoxDecoration(
      color: Colors.grey,
      shape: BoxShape.circle,
    ),
    child: const Icon(Icons.person, color: Colors.white, size: 30),
  );
}

// Database image with fallback
Widget _buildProfileImage() {
  if (currentUser?.profileImageUrl != null &&
      currentUser!.profileImageUrl!.isNotEmpty) {
    return Image.network(
      currentUser!.profileImageUrl!,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return _buildGreyAvatar();
      },
    );
  } else {
    return _buildGreyAvatar();
  }
}
```

#### Custom Tap Behavior
```dart
DrawerProfileWidget(
  user: currentUser,
  onTap: () {
    // Custom navigation or action
    Navigator.pushNamed(context, '/custom-profile');
  },
)
```

### Integration with Authentication

The drawer automatically integrates with your existing authentication system:
- Uses `AuthService.getCurrentUserProfile()` for data fetching
- Respects user authentication state
- Updates when user logs in/out

### Performance Considerations

1. **Caching**: User data is cached in widget state to avoid repeated API calls
2. **Lazy Loading**: Profile data only loads when drawer is initialized
3. **Efficient Updates**: Only rebuilds affected parts when data changes

### Testing

Use the provided example page to test the functionality:
```dart
// Navigate to the example page
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => DrawerProfileExamplePage(),
  ),
);
```

## Best Practices

1. **Always handle loading states** to provide good user experience
2. **Implement proper error handling** for network requests
3. **Use pull-to-refresh** to allow users to update their data
4. **Test with different user profiles** to ensure robustness
5. **Consider offline scenarios** and provide appropriate fallbacks

## Troubleshooting

### Common Issues

1. **Profile not loading**: Check user authentication status
2. **Images not displaying**: Verify image URLs and network connectivity
3. **Counts not updating**: Ensure database triggers are working correctly
4. **Loading state stuck**: Check for proper error handling in async operations

### Debug Tips

1. Add logging to track data fetching process
2. Use Flutter Inspector to verify widget state
3. Check Supabase dashboard for database connectivity
4. Test with different user accounts and data states
