# Google Sign-In Setup Guide for Business App

This guide provides step-by-step instructions to configure Google Sign-In with Supabase for your Flutter business app.

## Prerequisites

- Google Cloud Console account
- Supabase project
- Flutter development environment

## Step 1: Google Cloud Console Setup

### 1.1 Create/Access Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API (if not already enabled)

### 1.2 Configure OAuth Consent Screen
1. Go to **APIs & Services** > **OAuth consent screen**
2. Choose **External** user type
3. Fill in the required information:
   - App name: "Business App" (or your preferred name)
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes: `email`, `profile`, `openid`
5. Save and continue

### 1.3 Create OAuth 2.0 Credentials

#### For iOS:
1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth client ID**
3. Application type: **iOS**
4. Name: "Business App iOS"
5. Bundle ID: `com.example.business_app` (or your actual bundle ID)

#### For Android:
1. Application type: **Android**
2. Name: "Business App Android"
3. Package name: `com.example.business_app` (or your actual package name)
4. SHA-1 certificate fingerprint: Get this by running:
   ```bash
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```

#### For Web:
1. Application type: **Web application**
2. Name: "Business App Web"
3. Authorized JavaScript origins: `http://localhost:3000`, `https://yourdomain.com`
4. Authorized redirect URIs: `https://your-project-ref.supabase.co/auth/v1/callback`

### 1.4 Download Configuration Files
- For iOS: Download the `GoogleService-Info.plist`
- For Android: Download the `google-services.json`
- Note down the **Client IDs** for each platform

## Step 2: Supabase Configuration

### 2.1 Enable Google Provider
1. Go to your Supabase Dashboard
2. Navigate to **Authentication** > **Providers**
3. Enable **Google** provider
4. Add your Google OAuth credentials:
   - Client ID (Web)
   - Client Secret (Web)

### 2.2 Configure Redirect URLs
Add your app's redirect URLs in Supabase:
- For development: `http://localhost:3000`
- For production: Your actual domain

## Step 3: Flutter App Configuration

### 3.1 Update Client IDs
Replace the placeholder values in `lib/services/google_signin_service.dart`:

```dart
static const String _iosClientId = 'YOUR_ACTUAL_IOS_CLIENT_ID';
static const String _androidClientId = 'YOUR_ACTUAL_ANDROID_CLIENT_ID';
static const String _webClientId = 'YOUR_ACTUAL_WEB_CLIENT_ID';
```

### 3.2 iOS Configuration
1. Replace `GoogleService-Info.plist` in `ios/Runner/` with your downloaded file
2. Update `ios/Runner/Info.plist` with your actual values:
   - Replace `YOUR_IOS_CLIENT_ID` with your iOS client ID
   - Replace `YOUR_WEB_CLIENT_ID` with your web client ID
   - Replace `YOUR_REVERSED_CLIENT_ID` with the REVERSED_CLIENT_ID from GoogleService-Info.plist

### 3.3 Android Configuration
1. Replace `android/app/google-services.json` with your downloaded file
2. The build.gradle files are already configured

## Step 4: Testing

### 4.1 Run the App
```bash
flutter clean
flutter pub get
flutter run
```

### 4.2 Test Google Sign-In
1. Navigate to the login page
2. Tap the "Continue with Google" button
3. Complete the Google authentication flow
4. Verify successful login and user data

## Troubleshooting

### Common Issues:

1. **"Sign in failed" error**
   - Check that client IDs are correctly configured
   - Verify SHA-1 fingerprint for Android
   - Ensure bundle ID matches for iOS

2. **"Network error"**
   - Check internet connection
   - Verify Supabase configuration

3. **"Invalid client" error**
   - Double-check client IDs in all configuration files
   - Ensure OAuth consent screen is properly configured

### Debug Steps:
1. Check console logs for detailed error messages
2. Verify all configuration files have correct values
3. Test on both iOS and Android platforms
4. Ensure Supabase project is properly configured

## Security Notes

- Never commit actual client IDs to version control
- Use environment variables for production
- Regularly rotate client secrets
- Monitor authentication logs in Supabase

## Support

For additional help:
- [Google Sign-In Documentation](https://developers.google.com/identity/sign-in/android)
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Flutter Google Sign-In Package](https://pub.dev/packages/google_sign_in)
