import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'search_event.dart';
import 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  static const String _searchHistoryKey = 'search_history';

  SearchBloc() : super(const SearchState()) {
    on<SearchQueryChangedEvent>(_onSearchQueryChanged);
    on<SearchSubmittedEvent>(_onSearchSubmitted);
    on<ClearSearchEvent>(_onClearSearch);
    on<LoadSearchHistoryEvent>(_onLoadSearchHistory);
    on<AddToSearchHistoryEvent>(_onAddToSearchHistory);
    on<RemoveFromSearchHistoryEvent>(_onRemoveFromSearchHistory);
    on<ClearSearchHistoryEvent>(_onClearSearchHistory);
    on<ToggleSearchFilterEvent>(_onToggleSearchFilter);
    on<LoadTrendingSearchesEvent>(_onLoadTrendingSearches);
    on<SelectSearchSuggestionEvent>(_onSelectSearchSuggestion);

    // Load initial data
    add(LoadSearchHistoryEvent());
    add(LoadTrendingSearchesEvent());
  }

  void _onSearchQueryChanged(
    SearchQueryChangedEvent event,
    Emitter<SearchState> emit,
  ) {
    emit(state.copyWith(query: event.query));
    
    if (event.query.isNotEmpty) {
      // Generate suggestions based on query
      final suggestions = _generateSuggestions(event.query);
      emit(state.copyWith(suggestions: suggestions));
    } else {
      emit(state.copyWith(suggestions: []));
    }
  }

  Future<void> _onSearchSubmitted(
    SearchSubmittedEvent event,
    Emitter<SearchState> emit,
  ) async {
    if (event.query.trim().isEmpty) return;

    emit(state.copyWith(status: SearchStatus.loading));
    
    try {
      // Add to search history
      add(AddToSearchHistoryEvent(event.query));
      
      // Simulate search API call
      await Future.delayed(const Duration(milliseconds: 800));
      
      final results = _generateMockSearchResults(event.query);
      
      emit(state.copyWith(
        status: SearchStatus.loaded,
        results: results,
        query: event.query,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SearchStatus.error,
        errorMessage: 'Search failed: ${e.toString()}',
      ));
    }
  }

  void _onClearSearch(
    ClearSearchEvent event,
    Emitter<SearchState> emit,
  ) {
    emit(state.copyWith(
      query: '',
      results: [],
      suggestions: [],
      status: SearchStatus.initial,
    ));
  }

  Future<void> _onLoadSearchHistory(
    LoadSearchHistoryEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_searchHistoryKey) ?? [];
      emit(state.copyWith(searchHistory: history));
    } catch (e) {
      // Handle error silently for search history
    }
  }

  Future<void> _onAddToSearchHistory(
    AddToSearchHistoryEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentHistory = List<String>.from(state.searchHistory);
      
      // Remove if already exists to avoid duplicates
      currentHistory.remove(event.query);
      
      // Add to beginning
      currentHistory.insert(0, event.query);
      
      // Keep only last 20 searches
      if (currentHistory.length > 20) {
        currentHistory.removeRange(20, currentHistory.length);
      }
      
      await prefs.setStringList(_searchHistoryKey, currentHistory);
      emit(state.copyWith(searchHistory: currentHistory));
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _onRemoveFromSearchHistory(
    RemoveFromSearchHistoryEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentHistory = List<String>.from(state.searchHistory);
      currentHistory.remove(event.query);
      
      await prefs.setStringList(_searchHistoryKey, currentHistory);
      emit(state.copyWith(searchHistory: currentHistory));
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _onClearSearchHistory(
    ClearSearchHistoryEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_searchHistoryKey);
      emit(state.copyWith(searchHistory: []));
    } catch (e) {
      // Handle error silently
    }
  }

  void _onToggleSearchFilter(
    ToggleSearchFilterEvent event,
    Emitter<SearchState> emit,
  ) {
    final currentFilters = List<String>.from(state.activeFilters);
    
    if (currentFilters.contains(event.filter)) {
      currentFilters.remove(event.filter);
    } else {
      currentFilters.add(event.filter);
    }
    
    emit(state.copyWith(activeFilters: currentFilters));
  }

  Future<void> _onLoadTrendingSearches(
    LoadTrendingSearchesEvent event,
    Emitter<SearchState> emit,
  ) async {
    // Mock trending searches
    const trendingSearches = [
      'Electronics',
      'Fashion',
      'Home & Garden',
      'Sports',
      'Books',
      'Automotive',
      'Health & Beauty',
      'Toys & Games',
    ];
    
    emit(state.copyWith(trendingSearches: trendingSearches));
  }

  void _onSelectSearchSuggestion(
    SelectSearchSuggestionEvent event,
    Emitter<SearchState> emit,
  ) {
    add(SearchSubmittedEvent(event.suggestion));
  }

  List<String> _generateSuggestions(String query) {
    // Mock suggestions based on query
    final allSuggestions = [
      'Electronics store',
      'Electronic devices',
      'Fashion boutique',
      'Fashion accessories',
      'Home decor',
      'Home appliances',
      'Sports equipment',
      'Sports wear',
      'Book store',
      'Bookmarks',
    ];
    
    return allSuggestions
        .where((suggestion) => 
            suggestion.toLowerCase().contains(query.toLowerCase()))
        .take(5)
        .toList();
  }

  List<SearchResult> _generateMockSearchResults(String query) {
    // Mock search results
    return List.generate(10, (index) {
      return SearchResult(
        id: 'result_$index',
        title: '$query Result ${index + 1}',
        subtitle: 'This is a search result for $query',
        imageUrl: 'assets/images/profile${(index % 3) + 1}.jpg',
        type: ['user', 'post', 'product', 'business'][index % 4],
      );
    });
  }
}
