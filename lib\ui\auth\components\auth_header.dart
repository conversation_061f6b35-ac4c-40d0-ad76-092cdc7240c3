import 'package:flutter/material.dart';
import 'package:business_app/const/assets.dart';

class AuthHeader extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final bool showLogo;
  final bool showBackButton;
  final bool showCloseButton;
  final VoidCallback? onBackPressed;
  final VoidCallback? onClosePressed;
  final double logoSize;
  final TextAlign textAlign;

  const AuthHeader({
    super.key,
    this.title,
    this.subtitle,
    this.showLogo = true,
    this.showBackButton = false,
    this.showCloseButton = false,
    this.onBackPressed,
    this.onClosePressed,
    this.logoSize = 40,
    this.textAlign = TextAlign.center,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment:
          textAlign == TextAlign.center
              ? CrossAxisAlignment.center
              : CrossAxisAlignment.start,
      children: [
        // Navigation buttons row
        if (showBackButton || showCloseButton)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (showBackButton)
                IconButton(
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: theme.iconTheme.color,
                  ),
                )
              else
                const SizedBox(width: 48),

              if (showCloseButton)
                IconButton(
                  onPressed:
                      onClosePressed ?? () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: theme.iconTheme.color,
                  ),
                )
              else
                const SizedBox(width: 48),
            ],
          ),

        const SizedBox(height: 20),

        // Logo
        if (showLogo)
          Center(
            child: ClipOval(
              child: Image.asset(
                Assets.trademateLogo,
                width: logoSize,
                height: logoSize,
                fit: BoxFit.cover,
              ),
            ),
          ),

        if (showLogo && (title != null || subtitle != null))
          const SizedBox(height: 32),

        // Title
        if (title != null)
          Text(
            title!,
            textAlign: textAlign,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: theme.textTheme.headlineLarge?.color,
              height: 1.2,
            ),
          ),

        if (title != null && subtitle != null) const SizedBox(height: 12),

        // Subtitle
        if (subtitle != null)
          Text(
            subtitle!,
            textAlign: textAlign,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
      ],
    );
  }
}

class AuthWelcomeHeader extends StatelessWidget {
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthWelcomeHeader({
    super.key,
    this.showBackButton = false,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      //title: 'See what\'s happening\nin the world right now',
      title: 'Your ultimate trading & marketplace center.',
      showLogo: true,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      logoSize: 50,
    );
  }
}

class AuthLoginHeader extends StatelessWidget {
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthLoginHeader({
    super.key,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      title: 'Sign in to Trademate',
      showLogo: false,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      textAlign: TextAlign.start,
    );
  }
}

class AuthSignupHeader extends StatelessWidget {
  final String step;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthSignupHeader({
    super.key,
    required this.step,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      title: 'Create your account',
      subtitle: step,
      showLogo: false,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      textAlign: TextAlign.start,
    );
  }
}

class AuthForgotPasswordHeader extends StatelessWidget {
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthForgotPasswordHeader({
    super.key,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      title: 'Find your Trademate account',
      subtitle: 'Enter your email, phone, or username.',
      showLogo: false,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      textAlign: TextAlign.start,
    );
  }
}

class AuthVerificationHeader extends StatelessWidget {
  final String identifier;
  final bool isEmail;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthVerificationHeader({
    super.key,
    required this.identifier,
    required this.isEmail,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      title: 'We sent you a code',
      subtitle:
          'Enter it below to verify your ${isEmail ? 'email' : 'phone number'}.',
      showLogo: false,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      textAlign: TextAlign.start,
    );
  }
}

class AuthPasswordHeader extends StatelessWidget {
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthPasswordHeader({
    super.key,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      title: 'You\'ll need a password',
      subtitle: 'Make sure it\'s 8 characters or more.',
      showLogo: false,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      textAlign: TextAlign.start,
    );
  }
}

class AuthUsernameHeader extends StatelessWidget {
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthUsernameHeader({
    super.key,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      title: 'What should we call you?',
      subtitle: 'Your @username is unique. You can always change it later.',
      showLogo: false,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      textAlign: TextAlign.start,
    );
  }
}

class AuthProfileHeader extends StatelessWidget {
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const AuthProfileHeader({
    super.key,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AuthHeader(
      title: 'Pick a profile picture',
      subtitle: 'Have a favorite selfie? Upload it now.',
      showLogo: false,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      textAlign: TextAlign.start,
    );
  }
}

class AuthProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final Color? activeColor;
  final Color? inactiveColor;

  const AuthProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    Theme.of(context);
    final activeCol = activeColor ?? const Color(0xFF1DA1F2);
    final inactiveCol = inactiveColor ?? Colors.grey[300]!;

    return Row(
      children: List.generate(totalSteps, (index) {
        final isActive = index < currentStep;
        return Expanded(
          child: Container(
            height: 4,
            margin: EdgeInsets.only(right: index < totalSteps - 1 ? 8 : 0),
            decoration: BoxDecoration(
              color: isActive ? activeCol : inactiveCol,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      }),
    );
  }
}
