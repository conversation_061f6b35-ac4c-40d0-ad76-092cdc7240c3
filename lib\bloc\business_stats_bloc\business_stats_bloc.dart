import 'package:flutter_bloc/flutter_bloc.dart';
import 'business_stats_event.dart';
import 'business_stats_state.dart';

class BusinessStatsBloc extends Bloc<BusinessStatsEvent, BusinessStatsState> {
  BusinessStatsBloc() : super(const BusinessStatsState()) {
    on<LoadBusinessStatsEvent>(_onLoadBusinessStats);
    on<RefreshBusinessStatsEvent>(_onRefreshBusinessStats);
    on<ChangeStatsTimeRangeEvent>(_onChangeStatsTimeRange);
    on<ChangeStatsTypeEvent>(_onChangeStatsType);
    on<LoadSalesDataEvent>(_onLoadSalesData);
    on<LoadCustomerDataEvent>(_onLoadCustomerData);
    on<LoadProductDataEvent>(_onLoadProductData);
    on<ExportStatsEvent>(_onExportStats);
    on<ToggleStatsCardEvent>(_onToggleStatsCard);
  }

  Future<void> _onLoadBusinessStats(
    LoadBusinessStatsEvent event,
    Emitter<BusinessStatsState> emit,
  ) async {
    emit(state.copyWith(status: BusinessStatsStatus.loading));
    
    try {
      await Future.delayed(const Duration(milliseconds: 800));
      
      final statCards = _generateMockStatCards();
      final salesData = _generateMockSalesData();
      final customerData = _generateMockCustomerData();
      final productData = _generateMockProductData();
      final insights = _generateMockInsights();
      
      emit(state.copyWith(
        status: BusinessStatsStatus.loaded,
        statCards: statCards,
        salesData: salesData,
        customerData: customerData,
        productData: productData,
        insights: insights,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: BusinessStatsStatus.error,
        errorMessage: 'Failed to load business stats: ${e.toString()}',
      ));
    }
  }

  Future<void> _onRefreshBusinessStats(
    RefreshBusinessStatsEvent event,
    Emitter<BusinessStatsState> emit,
  ) async {
    emit(state.copyWith(status: BusinessStatsStatus.loading));
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Refresh all data
      add(LoadBusinessStatsEvent());
    } catch (e) {
      emit(state.copyWith(
        status: BusinessStatsStatus.error,
        errorMessage: 'Failed to refresh stats: ${e.toString()}',
      ));
    }
  }

  Future<void> _onChangeStatsTimeRange(
    ChangeStatsTimeRangeEvent event,
    Emitter<BusinessStatsState> emit,
  ) async {
    emit(state.copyWith(
      selectedTimeRange: event.timeRange,
      status: BusinessStatsStatus.loading,
    ));
    
    // Reload data for new time range
    add(LoadSalesDataEvent(event.timeRange));
    add(LoadCustomerDataEvent(event.timeRange));
    add(LoadProductDataEvent(event.timeRange));
  }

  void _onChangeStatsType(
    ChangeStatsTypeEvent event,
    Emitter<BusinessStatsState> emit,
  ) {
    emit(state.copyWith(selectedStatsType: event.statsType));
  }

  Future<void> _onLoadSalesData(
    LoadSalesDataEvent event,
    Emitter<BusinessStatsState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      final salesData = _generateMockSalesData(timeRange: event.timeRange);
      
      emit(state.copyWith(
        salesData: salesData,
        status: BusinessStatsStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: BusinessStatsStatus.error,
        errorMessage: 'Failed to load sales data: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadCustomerData(
    LoadCustomerDataEvent event,
    Emitter<BusinessStatsState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      final customerData = _generateMockCustomerData(timeRange: event.timeRange);
      
      emit(state.copyWith(
        customerData: customerData,
        status: BusinessStatsStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: BusinessStatsStatus.error,
        errorMessage: 'Failed to load customer data: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoadProductData(
    LoadProductDataEvent event,
    Emitter<BusinessStatsState> emit,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      final productData = _generateMockProductData(timeRange: event.timeRange);
      
      emit(state.copyWith(
        productData: productData,
        status: BusinessStatsStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: BusinessStatsStatus.error,
        errorMessage: 'Failed to load product data: ${e.toString()}',
      ));
    }
  }

  Future<void> _onExportStats(
    ExportStatsEvent event,
    Emitter<BusinessStatsState> emit,
  ) async {
    emit(state.copyWith(status: BusinessStatsStatus.exporting));
    
    try {
      await Future.delayed(const Duration(seconds: 2));
      
      // Simulate export process
      emit(state.copyWith(status: BusinessStatsStatus.loaded));
    } catch (e) {
      emit(state.copyWith(
        status: BusinessStatsStatus.error,
        errorMessage: 'Failed to export stats: ${e.toString()}',
      ));
    }
  }

  void _onToggleStatsCard(
    ToggleStatsCardEvent event,
    Emitter<BusinessStatsState> emit,
  ) {
    final expandedCards = Set<String>.from(state.expandedCards);
    
    if (expandedCards.contains(event.cardId)) {
      expandedCards.remove(event.cardId);
    } else {
      expandedCards.add(event.cardId);
    }
    
    emit(state.copyWith(expandedCards: expandedCards));
  }

  List<StatCard> _generateMockStatCards() {
    return [
      const StatCard(
        id: 'views',
        title: 'Views',
        value: '28.6K',
        subtitle: '+12% from last month',
        percentage: 12.0,
        isPositive: true,
        color: 'blue',
      ),
      const StatCard(
        id: 'profits',
        title: 'Profits',
        value: '\$12.5K',
        subtitle: '+8% from last month',
        percentage: 8.0,
        isPositive: true,
        color: 'green',
      ),
      const StatCard(
        id: 'losses',
        title: 'Losses',
        value: '\$1.3K',
        subtitle: '-5% from last month',
        percentage: 5.0,
        isPositive: false,
        color: 'red',
      ),
      const StatCard(
        id: 'orders',
        title: 'Orders',
        value: '1.1K',
        subtitle: '+15% from last month',
        percentage: 15.0,
        isPositive: true,
        color: 'purple',
      ),
    ];
  }

  List<ChartData> _generateMockSalesData({String timeRange = 'month'}) {
    return List.generate(30, (index) {
      return ChartData(
        label: 'Day ${index + 1}',
        value: (index * 100 + 500).toDouble(),
        date: DateTime.now().subtract(Duration(days: 29 - index)),
      );
    });
  }

  List<ChartData> _generateMockCustomerData({String timeRange = 'month'}) {
    return List.generate(30, (index) {
      return ChartData(
        label: 'Day ${index + 1}',
        value: (index * 5 + 20).toDouble(),
        date: DateTime.now().subtract(Duration(days: 29 - index)),
      );
    });
  }

  List<ChartData> _generateMockProductData({String timeRange = 'month'}) {
    return List.generate(30, (index) {
      return ChartData(
        label: 'Day ${index + 1}',
        value: (index * 2 + 10).toDouble(),
        date: DateTime.now().subtract(Duration(days: 29 - index)),
      );
    });
  }

  List<BusinessInsight> _generateMockInsights() {
    return [
      const BusinessInsight(
        id: 'insight_1',
        title: 'Increase Stock of Trending Items',
        description: 'Items in electronics category are seeing high demand.',
        type: 'suggestion',
        actionText: 'View Details',
      ),
      const BusinessInsight(
        id: 'insight_2',
        title: 'Improve Response Time',
        description: 'Customers are more likely to purchase from responsive businesses.',
        type: 'warning',
        actionText: 'Improve Now',
      ),
      const BusinessInsight(
        id: 'insight_3',
        title: 'Offer Limited-Time Discounts',
        description: 'Boost sales with time-sensitive offers to create urgency.',
        type: 'suggestion',
        actionText: 'Create Offer',
      ),
    ];
  }
}
