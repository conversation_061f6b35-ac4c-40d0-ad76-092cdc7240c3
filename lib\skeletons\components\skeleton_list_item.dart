import 'package:flutter/material.dart';
import '../base/base_skeleton.dart';

/// Skeleton for list items (products, shops, posts)
class SkeletonListItem extends StatelessWidget {
  final bool showAvatar;
  final bool showTrailing;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final bool isLoading;

  const SkeletonListItem({
    super.key,
    this.showAvatar = true,
    this.showTrailing = false,
    this.height,
    this.margin,
    this.isLoading = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isLoading) {
      return Container(
        height: height ?? 80,
        margin:
            margin ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                if (showAvatar) ...[
                  const CircleAvatar(radius: 24),
                  const SizedBox(width: 12),
                ],
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Sample Title'),
                      SizedBox(height: 8),
                      Text('Sample subtitle'),
                    ],
                  ),
                ),
                if (showTrailing) ...[
                  const SizedBox(width: 12),
                  const Icon(Icons.more_vert),
                ],
              ],
            ),
          ),
        ),
      );
    }

    return Container(
      height: height ?? 80,
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              if (showAvatar) ...[
                const SkeletonCircle(size: 48),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SkeletonText(width: double.infinity, height: 16),
                    const SizedBox(height: 8),
                    SkeletonText(
                      width: MediaQuery.of(context).size.width * 0.6,
                      height: 14,
                    ),
                  ],
                ),
              ),
              if (showTrailing) ...[
                const SizedBox(width: 12),
                const SkeletonContainer(width: 24, height: 24),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Skeleton for grid items (products)
class SkeletonGridItem extends StatelessWidget {
  final double? aspectRatio;
  final EdgeInsetsGeometry? margin;
  final bool isLoading;

  const SkeletonGridItem({
    super.key,
    this.aspectRatio,
    this.margin,
    this.isLoading = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      child: Card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image placeholder
            AspectRatio(
              aspectRatio: aspectRatio ?? 1.0,
              child: const SkeletonContainer(
                width: double.infinity,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SkeletonText(width: double.infinity, height: 16),
                  const SizedBox(height: 8),
                  SkeletonText(
                    width: MediaQuery.of(context).size.width * 0.3,
                    height: 14,
                  ),
                  const SizedBox(height: 8),
                  const SkeletonText(width: 80, height: 18),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Skeleton for post items
class SkeletonPostItem extends StatelessWidget {
  final bool showImage;
  final EdgeInsetsGeometry? margin;
  final bool isLoading;

  const SkeletonPostItem({
    super.key,
    this.showImage = true,
    this.margin,
    this.isLoading = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with avatar and name
              Row(
                children: [
                  const SkeletonCircle(size: 40),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SkeletonText(width: 120, height: 16),
                        const SizedBox(height: 4),
                        SkeletonText(
                          width: MediaQuery.of(context).size.width * 0.3,
                          height: 12,
                        ),
                      ],
                    ),
                  ),
                  const SkeletonContainer(width: 24, height: 24),
                ],
              ),

              const SizedBox(height: 12),

              // Post content
              const SkeletonText(width: double.infinity, height: 16),
              const SizedBox(height: 8),
              SkeletonText(
                width: MediaQuery.of(context).size.width * 0.8,
                height: 16,
              ),
              const SizedBox(height: 8),
              SkeletonText(
                width: MediaQuery.of(context).size.width * 0.6,
                height: 16,
              ),

              if (showImage) ...[
                const SizedBox(height: 12),
                const SkeletonContainer(width: double.infinity, height: 200),
              ],

              const SizedBox(height: 12),

              // Action buttons
              Row(
                children: [
                  const SkeletonContainer(width: 60, height: 32),
                  const SizedBox(width: 16),
                  const SkeletonContainer(width: 60, height: 32),
                  const SizedBox(width: 16),
                  const SkeletonContainer(width: 60, height: 32),
                  const Spacer(),
                  const SkeletonContainer(width: 32, height: 32),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Skeleton for shop items
class SkeletonShopItem extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final bool isLoading;

  const SkeletonShopItem({super.key, this.margin, this.isLoading = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: SizedBox(
        height: 140,
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const SkeletonCircle(size: 60),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SkeletonText(
                            width: double.infinity,
                            height: 18,
                          ),
                          const SizedBox(height: 8),
                          SkeletonText(
                            width: MediaQuery.of(context).size.width * 0.5,
                            height: 14,
                          ),
                          const SizedBox(height: 8),
                          const SkeletonText(width: 100, height: 14),
                        ],
                      ),
                    ),
                    const SkeletonContainer(width: 80, height: 32),
                  ],
                ),

                const SizedBox(height: 16),

                // Tags or categories
                Row(
                  children: [
                    const SkeletonContainer(width: 60, height: 24),
                    const SizedBox(width: 8),
                    const SkeletonContainer(width: 80, height: 24),
                    const SizedBox(width: 8),
                    const SkeletonContainer(width: 70, height: 24),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
