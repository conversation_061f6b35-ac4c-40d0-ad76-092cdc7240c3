import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

// Initial app start event
class AuthCheckRequested extends AuthEvent {}

// Login events
class LoginRequested extends AuthEvent {
  final String identifier; // Can be email, phone, or username
  final String password;

  const LoginRequested({required this.identifier, required this.password});

  @override
  List<Object?> get props => [identifier, password];
}

// Email OTP Login (magic link) - separate from password login
class EmailOtpLoginRequested extends AuthEvent {
  final String email;
  final String? emailRedirectTo;

  const EmailOtpLoginRequested({required this.email, this.emailRedirectTo});

  @override
  List<Object?> get props => [email, emailRedirectTo];
}

// OTP Login events
class LoginOtpRequested extends AuthEvent {
  final String emailOrPhone;
  final bool isEmail;

  const LoginOtpRequested({required this.emailOrPhone, required this.isEmail});

  @override
  List<Object?> get props => [emailOrPhone, isEmail];
}

class LoginOtpVerificationSubmitted extends AuthEvent {
  final String code;

  const LoginOtpVerificationSubmitted({required this.code});

  @override
  List<Object?> get props => [code];
}

// Email verification events
class EmailVerificationRequested extends AuthEvent {
  final String email;

  const EmailVerificationRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class EmailVerificationResendRequested extends AuthEvent {
  final String email;

  const EmailVerificationResendRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

// Signup events
class SignupStarted extends AuthEvent {}

class SignupBasicInfoSubmitted extends AuthEvent {
  final String name;
  final String emailOrPhone;
  final DateTime dateOfBirth;
  final bool isEmail; // true if email, false if phone

  const SignupBasicInfoSubmitted({
    required this.name,
    required this.emailOrPhone,
    required this.dateOfBirth,
    required this.isEmail,
  });

  @override
  List<Object?> get props => [name, emailOrPhone, dateOfBirth, isEmail];
}

class SignupVerificationCodeSent extends AuthEvent {
  final String verificationCode;

  const SignupVerificationCodeSent({required this.verificationCode});

  @override
  List<Object?> get props => [verificationCode];
}

class SignupVerificationSubmitted extends AuthEvent {
  final String code;

  const SignupVerificationSubmitted({required this.code});

  @override
  List<Object?> get props => [code];
}

class SignupPasswordCreated extends AuthEvent {
  final String password;

  const SignupPasswordCreated({required this.password});

  @override
  List<Object?> get props => [password];
}

class SignupBirthdaySelected extends AuthEvent {
  final DateTime dateOfBirth;

  const SignupBirthdaySelected({required this.dateOfBirth});

  @override
  List<Object?> get props => [dateOfBirth];
}

class SignupUsernameSelected extends AuthEvent {
  final String username;

  const SignupUsernameSelected({required this.username});

  @override
  List<Object?> get props => [username];
}

class SignupProfileSetup extends AuthEvent {
  final String? profileImagePath;
  final String? bio;

  const SignupProfileSetup({this.profileImagePath, this.bio});

  @override
  List<Object?> get props => [profileImagePath, bio];
}

class SignupCategorySelection extends AuthEvent {
  final List<String> selectedCategories;

  const SignupCategorySelection({required this.selectedCategories});

  @override
  List<Object?> get props => [selectedCategories];
}

class SignupCompleted extends AuthEvent {}

// Password reset events
class ForgotPasswordRequested extends AuthEvent {
  final String emailOrPhone;

  const ForgotPasswordRequested({required this.emailOrPhone});

  @override
  List<Object?> get props => [emailOrPhone];
}

class PasswordResetCodeSubmitted extends AuthEvent {
  final String code;

  const PasswordResetCodeSubmitted({required this.code});

  @override
  List<Object?> get props => [code];
}

class PasswordResetOTPResendRequested extends AuthEvent {
  final String email;

  const PasswordResetOTPResendRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class NewPasswordSubmitted extends AuthEvent {
  final String newPassword;

  const NewPasswordSubmitted({required this.newPassword});

  @override
  List<Object?> get props => [newPassword];
}

// New device verification events
class NewDeviceVerificationRequested extends AuthEvent {
  final String email;

  const NewDeviceVerificationRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class NewDeviceOTPSubmitted extends AuthEvent {
  final String code;

  const NewDeviceOTPSubmitted({required this.code});

  @override
  List<Object?> get props => [code];
}

// Email change verification events
class EmailChangeRequested extends AuthEvent {
  final String newEmail;

  const EmailChangeRequested({required this.newEmail});

  @override
  List<Object?> get props => [newEmail];
}

class EmailChangeOTPSubmitted extends AuthEvent {
  final String code;

  const EmailChangeOTPSubmitted({required this.code});

  @override
  List<Object?> get props => [code];
}

// Security operation events
class SecurityOperationRequested extends AuthEvent {
  final String operation;
  final String email;

  const SecurityOperationRequested({
    required this.operation,
    required this.email,
  });

  @override
  List<Object?> get props => [operation, email];
}

class SecurityOTPSubmitted extends AuthEvent {
  final String code;
  final String operation;

  const SecurityOTPSubmitted({required this.code, required this.operation});

  @override
  List<Object?> get props => [code, operation];
}

// Social login events
class GoogleSignInRequested extends AuthEvent {
  const GoogleSignInRequested();

  @override
  List<Object?> get props => [];
}

class GoogleSignupRequested extends AuthEvent {}

class GoogleUserNeedsOnboarding extends AuthEvent {
  final String userId;
  final String userName;
  final String? userEmail;
  final String? userProfileImage;
  final String? accessToken;

  const GoogleUserNeedsOnboarding({
    required this.userId,
    required this.userName,
    this.userEmail,
    this.userProfileImage,
    this.accessToken,
  });

  @override
  List<Object?> get props => [
    userId,
    userName,
    userEmail,
    userProfileImage,
    accessToken,
  ];
}

class GoogleSignInCompleted extends AuthEvent {
  final String userId;
  final String userName;
  final String? userEmail;
  final String? userProfileImage;
  final String? accessToken;

  const GoogleSignInCompleted({
    required this.userId,
    required this.userName,
    this.userEmail,
    this.userProfileImage,
    this.accessToken,
  });

  @override
  List<Object?> get props => [
    userId,
    userName,
    userEmail,
    userProfileImage,
    accessToken,
  ];
}

class GoogleOnboardingContinueRequested extends AuthEvent {}

class AppleSignInRequested extends AuthEvent {}

// Logout event
class LogoutRequested extends AuthEvent {}

// Navigation events
class AuthNavigateToLogin extends AuthEvent {}

class AuthNavigateToSignup extends AuthEvent {}

class AuthNavigateToForgotPassword extends AuthEvent {}

class AuthNavigateBack extends AuthEvent {}

// Clear error event
class AuthClearError extends AuthEvent {}
