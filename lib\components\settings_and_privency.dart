import 'package:business_app/settings/theme_settings.dart';
import 'package:business_app/version_info/package_info.dart';
import 'package:flutter/material.dart';
import 'package:rive_animated_icon/rive_animated_icon.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:async';

// Import your components and dialogs
import 'package:business_app/components/settings_card.dart';

// Import admin pages
import 'package:business_app/admin/super_admin_page.dart';
import 'package:business_app/admin/admin_page.dart';
import 'package:business_app/admin/moderator_page.dart';

// Import auth bloc and models
import 'package:business_app/bloc/auth_bloc/auth_bloc.dart';
import 'package:business_app/bloc/auth_bloc/auth_state.dart';
import 'package:business_app/bloc/auth_bloc/auth_event.dart';
import 'package:business_app/models/auth/user_role.dart';
//import 'package:business_app/widgets/theme_selection_dialog.dart';
// You can create these pages later as needed
// import 'package:business_app/screens/language_settings_page.dart';
// import 'package:business_app/screens/privacy_checkup_page.dart';
// import 'package:business_app/screens/about_trademate_page.dart';
// etc.

class SettingsAndPrivacy extends StatefulWidget {
  const SettingsAndPrivacy({super.key});

  @override
  State<SettingsAndPrivacy> createState() => _SettingsAndPrivacyState();
}

class _SettingsAndPrivacyState extends State<SettingsAndPrivacy> {
  PackageInfo? _packageInfo;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    final info = await PackageInfo.fromPlatform();
    setState(() {
      _packageInfo = info;
    });
  }

  @override
  Widget build(BuildContext context) {
    //for pure dark & white👇
    return Scaffold(
      //backgroundColor: const Color(0xFF18191A),
      //for pure dark & white👇
      //backgroundColor: isDark ? Colors.black : Colors.white,
      appBar: AppBar(
        //backgroundColor: const Color(0xFF242526),
        centerTitle: true,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Center(
                child: Text(
                  "Settings & Privacy",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    // color: Colors.white,
                  ),
                ),
              ),
            ),
            RiveAnimatedIcon(
              riveIcon: RiveIcon.search,
              width: 35,
              height: 35,
              strokeWidth: 2,
              color: Color(0xFF1DA1F2),
              //color: Colors.white,
              loopAnimation: false,
              onTap: () {
                // Handle search action
              },
            ),
          ],
        ),
      ),
      body: ListView(
        physics: const BouncingScrollPhysics(),
        children: [
          const SettingsCard(), // Custom card widget for profile or info
          const SizedBox(height: 10),

          const Divider(
            // color: Colors.white54,
            thickness: 0.3,
            indent: 20.0,
            endIndent: 20.0,
          ),

          _buildSectionHeader('ACCOUNT'),

          _buildSettingItem(
            icon: Icons.key,
            title: 'Password and security',
            subtitle: 'Change password, 2FA, and alerts',
            onTap: () {
              // Navigate to password and security page
            },
          ),
          _buildSettingItem(
            icon: Icons.privacy_tip_outlined,
            title: 'Privacy Checkup',
            subtitle: 'Review key privacy settings',
            onTap: () {
              // Navigator.push(context, MaterialPageRoute(builder: (_) => PrivacyCheckupPage()));
            },
          ),
          _buildSettingItem(
            icon: Icons.lock_outline,
            title: 'Privacy Shortcuts',
            subtitle: 'Manage your privacy',
            onTap: () {
              // Navigate to privacy shortcuts page
            },
          ),

          const Divider(
            //color: Colors.white54,
            thickness: 0.3,
            indent: 20.0,
            endIndent: 20.0,
          ),

          _buildSectionHeader('BUSINESS SETTINGS'),

          _buildSettingItem(
            icon: Icons.local_shipping,
            title: 'Shipping Settings',
            subtitle: 'Configure shipping rates and options',
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const ShippingSettingsPage()),
              );
            },
          ),

          const Divider(
            //color: Colors.white54,
            thickness: 0.3,
            indent: 20.0,
            endIndent: 20.0,
          ),

          // Admin Panel Section - Only visible to admin users
          BlocBuilder<AuthBloc, AuthState>(
            builder: (context, authState) {
              final userRole = authState.userRole ?? UserRole.user;

              // Only show admin section if user has admin privileges
              if (!userRole.permissions.canAccessAdminPanel) {
                return const SizedBox.shrink();
              }

              return Column(
                children: [
                  _buildSectionHeader('ADMINISTRATION'),

                  // Super Admin Panel
                  if (userRole.isSuperAdmin)
                    _buildSettingItem(
                      icon: Icons.security,
                      title: 'Super Admin Panel',
                      subtitle: 'Full system access and management',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => const SuperAdminPage(),
                          ),
                        );
                      },
                    ),

                  // Admin Panel
                  if (userRole.isAdmin)
                    _buildSettingItem(
                      icon: Icons.admin_panel_settings,
                      title: 'Admin Panel',
                      subtitle: 'User management and content moderation',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const AdminPage()),
                        );
                      },
                    ),

                  // Moderator Panel
                  if (userRole.isModerator)
                    _buildSettingItem(
                      icon: Icons.shield,
                      title: 'Moderator Panel',
                      subtitle: 'Content review and user reports',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => const ModeratorPage(),
                          ),
                        );
                      },
                    ),

                  const Divider(thickness: 0.3, indent: 20.0, endIndent: 20.0),
                ],
              );
            },
          ),

          _buildSectionHeader('PREFERENCES'),

          _buildSettingItem(
            icon: Icons.color_lens,
            title: 'Appearance',
            subtitle: 'Manage App theme',
            onTap: () {
              // Show theme selection dialog
              // showDialog(
              //   context: context,
              //   builder: (_) => const ThemeSelectionDialog(),
              // );
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => ThemeSettings()),
              );
            },
          ),
          _buildSettingItem(
            icon: Icons.language,
            title: 'Language and Region',
            subtitle: 'Change language preferences',
            onTap: () {
              // Navigator.push(context, MaterialPageRoute(builder: (_) => LanguageSettingsPage()));
            },
          ),

          _buildSettingItem(
            icon: Icons.notifications,
            title: 'Notification Settings',
            subtitle: 'Manage push notifications',
            onTap: () {
              // Navigate to notification settings
            },
          ),

          const Divider(
            // color: Colors.white54,
            thickness: 0.3,
            indent: 20.0,
            endIndent: 20.0,
          ),

          _buildSectionHeader('YOUR INFORMATION'),

          _buildSettingItem(
            icon: Icons.download,
            title: 'Download Your Information',
            subtitle: 'Get a copy of your Trademate data',
            onTap: () {
              // Navigate to download information page
            },
          ),
          _buildSettingItem(
            icon: Icons.delete_outline,
            title: 'Account Ownership and Control',
            subtitle: 'Memorialization settings, account deletion',
            onTap: () {
              // Navigate to account control page
            },
          ),

          const Divider(
            //color: Colors.white54,
            thickness: 0.3,
            indent: 20.0,
            endIndent: 20.0,
          ),

          _buildSectionHeader('ABOUT'),

          _buildSettingItem(
            icon: Icons.info_outline,
            title: 'About Trademate',
            subtitle: "Version",
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => AppInfo()),
              );
            },
          ),

          // Simple Package Info Section
          _buildPackageInfoSection(),

          _buildSectionHeader('LEGAL'),

          _buildSettingItem(
            icon: Icons.gavel_outlined,
            title: 'Terms & Policies',
            subtitle: 'Review legal info and terms',
            onTap: () {
              // Navigate to terms and policies
            },
          ),
          _buildSectionHeader('ACCOUNTS'),

          _buildSettingItem(
            icon: Icons.person_outline,
            title: 'Logout',
            subtitle: 'logout from your account',
            onTap: () => _showLogoutConfirmation(context),
          ),

          const SizedBox(height: 30),

          const Center(
            child: Text(
              'Trademate © 2025',
              style: TextStyle(/*color: Colors.white54,*/ fontSize: 12),
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// Section headers (like "ACCOUNT", "PREFERENCES")
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          //color: Colors.white70,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Simple Package Info Section
  Widget _buildPackageInfoSection() {
    if (_packageInfo == null) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'App Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          _buildInfoRow('App Name', _packageInfo!.appName),
          _buildInfoRow('Package Name', _packageInfo!.packageName),
          _buildInfoRow('Version', _packageInfo!.version),
          _buildInfoRow('Build Number', _packageInfo!.buildNumber),
        ],
      ),
    );
  }

  /// Helper method to build info rows
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(
                  context,
                ).textTheme.bodyMedium?.color?.withOpacity(0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  /// A single setting item with icon, title, subtitle, and navigation support
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon /*color: Colors.white70*/),
      title: Text(
        title,
        style: const TextStyle(
          /*  color: Colors.white*/
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle:
          subtitle != null
              ? Text(
                subtitle,
                style: const TextStyle(/*color: Colors.white60*/ fontSize: 12),
              )
              : null,
      trailing: const Icon(
        Icons.arrow_forward_ios,
        //color: Colors.white38,
        size: 16,
      ),
      onTap: onTap, // This makes the item navigable
    );
  }

  /// Professional logout confirmation dialog with enhanced UX
  Future<void> _showLogoutConfirmation(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // Prevent accidental dismissal
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.logout, color: Colors.red[600], size: 24),
              ),
              const SizedBox(width: 12),
              const Text(
                'Logout',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Are you sure you want to logout from your account?',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'You\'ll need to sign in again to access your account.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // Cancel button
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Cancel',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
            // Logout button
            ElevatedButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: const Text(
                'Logout',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        );
      },
    );

    // Handle logout confirmation
    if (result == true && context.mounted) {
      _performLogout(context);
    }
  }

  /// Perform logout with loading state and error handling
  Future<void> _performLogout(BuildContext context) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext loadingContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(strokeWidth: 3),
              const SizedBox(height: 16),
              const Text(
                'Signing out...',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        );
      },
    );

    // Trigger logout event
    context.read<AuthBloc>().add(LogoutRequested());

    // Listen for auth state changes to handle completion
    final authBloc = context.read<AuthBloc>();
    final subscription = authBloc.stream.listen((authState) {
      if (context.mounted) {
        // Close loading dialog
        Navigator.of(context).pop();

        if (authState.status == AuthStatus.unauthenticated) {
          // Logout successful - show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Successfully logged out',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green[600],
              duration: const Duration(milliseconds: 800),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
          // Navigation will be handled automatically by AuthWrapper
        } else if (authState.status == AuthStatus.error) {
          // Logout failed - show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      authState.errorMessage ??
                          'Logout failed. Please try again.',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red[600],
              duration: const Duration(milliseconds: 800),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      }
    });

    // Cancel subscription after a reasonable timeout
    Future.delayed(const Duration(seconds: 5), () {
      subscription.cancel();
    });
  }
}

// Shipping Settings Page
class ShippingSettingsPage extends StatefulWidget {
  const ShippingSettingsPage({super.key});

  @override
  ShippingSettingsPageState createState() => ShippingSettingsPageState();
}

class ShippingSettingsPageState extends State<ShippingSettingsPage> {
  // Business owner shipping settings (these would be saved to backend)
  bool enableShipping = true;
  double shippingCost = 10.0;
  double freeShippingThreshold = 100.0;
  bool enableFreeShipping = true;

  final TextEditingController _shippingCostController = TextEditingController();
  final TextEditingController _freeShippingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _shippingCostController.text = shippingCost.toStringAsFixed(2);
    _freeShippingController.text = freeShippingThreshold.toStringAsFixed(0);
  }

  @override
  void dispose() {
    _shippingCostController.dispose();
    _freeShippingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shipping Settings'),
        centerTitle: true,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: const Text(
              'Save',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Header Card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.local_shipping, color: Colors.blue, size: 24),
                    const SizedBox(width: 12),
                    const Text(
                      'Shipping Configuration',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Configure how customers can purchase from your business. Enable shipping for online orders or disable for direct sales only.',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Enable Shipping Toggle
          Card(
            child: SwitchListTile(
              title: const Text(
                'Enable Shipping',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              subtitle: Text(
                enableShipping
                    ? 'Customers can buy online with shipping'
                    : 'Direct sales only - no shipping options',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              value: enableShipping,
              onChanged: (value) {
                setState(() {
                  enableShipping = value;
                });
              },
              secondary: Icon(
                enableShipping ? Icons.local_shipping : Icons.store,
                color: enableShipping ? Colors.blue : Colors.grey,
              ),
            ),
          ),

          if (enableShipping) ...[
            const SizedBox(height: 16),

            // Shipping Cost
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Shipping Cost',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _shippingCostController,
                      keyboardType: TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      decoration: InputDecoration(
                        prefixText: '\$',
                        hintText: '10.00',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      onChanged: (value) {
                        final cost = double.tryParse(value);
                        if (cost != null) {
                          setState(() {
                            shippingCost = cost;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Standard shipping fee for all orders',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Free Shipping Toggle
            Card(
              child: SwitchListTile(
                title: const Text(
                  'Free Shipping Threshold',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                subtitle: Text(
                  enableFreeShipping
                      ? 'Offer free shipping on large orders'
                      : 'Always charge shipping fee',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                value: enableFreeShipping,
                onChanged: (value) {
                  setState(() {
                    enableFreeShipping = value;
                  });
                },
                secondary: Icon(
                  enableFreeShipping ? Icons.card_giftcard : Icons.money_off,
                  color: enableFreeShipping ? Colors.green : Colors.grey,
                ),
              ),
            ),

            if (enableFreeShipping) ...[
              const SizedBox(height: 16),

              // Free Shipping Threshold
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Free Shipping Amount',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _freeShippingController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          prefixText: '\$',
                          hintText: '100',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        onChanged: (value) {
                          final threshold = double.tryParse(value);
                          if (threshold != null) {
                            setState(() {
                              freeShippingThreshold = threshold;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Orders above this amount get free shipping',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],

          const SizedBox(height: 24),

          // Preview Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.preview, color: Colors.grey[700], size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Customer Experience Preview',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                if (enableShipping) ...[
                  _buildPreviewItem(
                    'Buy Online Option',
                    'Available with shipping costs',
                    Icons.local_shipping,
                    Colors.blue,
                  ),
                  const SizedBox(height: 8),
                  _buildPreviewItem(
                    'Shipping Cost',
                    '\$${shippingCost.toStringAsFixed(2)}',
                    Icons.attach_money,
                    Colors.orange,
                  ),
                  if (enableFreeShipping) ...[
                    const SizedBox(height: 8),
                    _buildPreviewItem(
                      'Free Shipping',
                      'On orders over \$${freeShippingThreshold.toStringAsFixed(0)}',
                      Icons.card_giftcard,
                      Colors.green,
                    ),
                  ],
                ] else ...[
                  _buildPreviewItem(
                    'Direct Sales Only',
                    'No shipping options available',
                    Icons.store,
                    Colors.grey,
                  ),
                ],
                const SizedBox(height: 8),
                _buildPreviewItem(
                  'Buy Direct Option',
                  'Always available - no shipping',
                  Icons.store,
                  Colors.green,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _saveSettings() {
    // Here you would save to backend/database
    // For now, just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Shipping settings saved successfully!'),
        backgroundColor: Colors.green,
        duration: const Duration(milliseconds: 800),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );

    // Navigate back
    Navigator.pop(context);
  }
}
