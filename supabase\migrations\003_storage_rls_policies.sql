-- =====================================================
-- STORAGE BUCKET RLS POLICIES
-- Required for image upload functionality
-- =====================================================

-- Enable RLS on storage.objects table (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PRODUCT IMAGES BUCKET POLICIES
-- =====================================================

-- Policy: Allow authenticated users to upload product images to their own folder
CREATE POLICY "Users can upload product images to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'product-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to product images (for viewing products)
CREATE POLICY "Public can view product images" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'product-images');

-- Policy: Allow users to manage their own product images
CREATE POLICY "Users can manage own product images" 
ON storage.objects 
FOR ALL 
TO authenticated 
USING (
  bucket_id = 'product-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- =====================================================
-- PROFILE IMAGES BUCKET POLICIES
-- =====================================================

-- Policy: Allow authenticated users to upload profile images to their own folder
CREATE POLICY "Users can upload profile images to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own profile images
CREATE POLICY "Users can view own profile images" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to profile images (for viewing other users' profiles)
CREATE POLICY "Public can view profile images" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'profile-images');

-- Policy: Allow users to update their own profile images
CREATE POLICY "Users can update own profile images" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own profile images
CREATE POLICY "Users can delete own profile images" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'profile-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- =====================================================
-- BACKGROUND IMAGES BUCKET POLICIES
-- =====================================================

-- Policy: Allow authenticated users to upload background images to their own folder
CREATE POLICY "Users can upload background images to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own background images
CREATE POLICY "Users can view own background images" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to background images
CREATE POLICY "Public can view background images" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'background-images');

-- Policy: Allow users to update their own background images
CREATE POLICY "Users can update own background images" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own background images
CREATE POLICY "Users can delete own background images" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'background-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- =====================================================
-- SHOP LOGO BUCKET POLICIES
-- =====================================================

-- Policy: Allow authenticated users to upload shop logos to their own folder
CREATE POLICY "Users can upload shop logos to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own shop logos
CREATE POLICY "Users can view own shop logos" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to shop logos (for viewing shops)
CREATE POLICY "Public can view shop logos" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'shop-logo');

-- Policy: Allow users to update their own shop logos
CREATE POLICY "Users can update own shop logos" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own shop logos
CREATE POLICY "Users can delete own shop logos" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'shop-logo' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- =====================================================
-- SHOP BANNER BUCKET POLICIES
-- =====================================================

-- Policy: Allow authenticated users to upload shop banners to their own folder
CREATE POLICY "Users can upload shop banners to own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to view their own shop banners
CREATE POLICY "Users can view own shop banners" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow public access to shop banners (for viewing shops)
CREATE POLICY "Public can view shop banners" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'shop-banner');

-- Policy: Allow users to update their own shop banners
CREATE POLICY "Users can update own shop banners" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy: Allow users to delete their own shop banners
CREATE POLICY "Users can delete own shop banners" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'shop-banner' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
