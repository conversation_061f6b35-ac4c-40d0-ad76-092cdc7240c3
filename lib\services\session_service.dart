import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import '../supabase/config.dart';
import '../models/auth/auth_result.dart';
import '../models/auth/user_model.dart';

/// Professional session management service for Supabase authentication
/// Handles automatic token refresh, session persistence, and authentication state monitoring
class SessionService {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  static const String _logTag = '🔐 SessionService';
  static const String _sessionKey = 'supabase_session';
  static const String _userProfileKey = 'user_profile';
  static const String _lastRefreshKey = 'last_refresh';

  static Timer? _refreshTimer;
  static StreamSubscription<AuthState>? _authSubscription;
  static final StreamController<SessionState> _sessionController =
      StreamController<SessionState>.broadcast();

  /// Initialize session management
  static Future<void> initialize() async {
    _logger.i('$_logTag Initializing session management...');

    try {
      // Start listening to auth state changes
      _startAuthStateListener();

      // Check for existing session
      await _restoreSession();

      // Start automatic token refresh
      _startTokenRefreshTimer();

      _logger.i('$_logTag ✅ Session management initialized');
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to initialize session management: $e');
    }
  }

  /// Dispose session management resources
  static Future<void> dispose() async {
    _logger.i('$_logTag Disposing session management...');

    _refreshTimer?.cancel();
    _authSubscription?.cancel();
    await _sessionController.close();

    _logger.i('$_logTag ✅ Session management disposed');
  }

  /// Get session state stream
  static Stream<SessionState> get sessionStateStream =>
      _sessionController.stream;

  /// Get current session state
  static SessionState get currentSessionState {
    final user = SupabaseConfig.currentUser;
    final session = SupabaseConfig.currentSession;

    if (user != null && session != null) {
      return SessionState.authenticated(user, session);
    } else {
      return SessionState.unauthenticated();
    }
  }

  /// Check if session is valid and not expired
  static bool get isSessionValid {
    final session = SupabaseConfig.currentSession;
    if (session == null) return false;

    final expiresAt = session.expiresAt;
    if (expiresAt == null) return true; // No expiration

    // Check if session expires within next 5 minutes
    final now = DateTime.now().millisecondsSinceEpoch / 1000;
    final bufferTime = 5 * 60; // 5 minutes buffer

    return expiresAt > (now + bufferTime);
  }

  /// Refresh session if needed
  static Future<AuthResult<Session>> refreshSessionIfNeeded() async {
    _logger.d('$_logTag Checking if session refresh is needed...');

    if (isSessionValid) {
      _logger.d('$_logTag Session is still valid, no refresh needed');
      return AuthResult.success(SupabaseConfig.currentSession!);
    }

    return await refreshSession();
  }

  /// Force refresh session
  static Future<AuthResult<Session>> refreshSession() async {
    _logger.i('$_logTag Refreshing session...');

    try {
      final response = await SupabaseConfig.client.auth.refreshSession();

      if (response.session != null) {
        _logger.i('$_logTag ✅ Session refreshed successfully');

        // Save session to persistent storage
        await _saveSession(response.session!);

        // Update last refresh time
        await _saveLastRefreshTime();

        // Emit session state change
        _sessionController.add(
          SessionState.authenticated(response.user!, response.session!),
        );

        return AuthResult.success(response.session!);
      } else {
        const error = 'Failed to refresh session: No session data received';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }
    } catch (e) {
      final error = 'Session refresh failed: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');

      // If refresh fails, clear session and emit unauthenticated state
      await clearSession();

      return AuthResult.error(error);
    }
  }

  /// Save session to persistent storage
  static Future<void> saveSession(
    Session session, [
    UserModel? userProfile,
  ]) async {
    _logger.d('$_logTag Saving session to persistent storage...');

    try {
      await _saveSession(session);

      if (userProfile != null) {
        await _saveUserProfile(userProfile);
      }

      await _saveLastRefreshTime();

      _logger.i('$_logTag ✅ Session saved successfully');
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to save session: $e');
    }
  }

  /// Clear session from persistent storage
  static Future<void> clearSession() async {
    _logger.i('$_logTag Clearing session from persistent storage...');

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_sessionKey);
      await prefs.remove(_userProfileKey);
      await prefs.remove(_lastRefreshKey);

      // Emit unauthenticated state
      _sessionController.add(SessionState.unauthenticated());

      _logger.i('$_logTag ✅ Session cleared successfully');
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to clear session: $e');
    }
  }

  /// Get saved user profile
  static Future<UserModel?> getSavedUserProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_userProfileKey);

      if (profileJson != null) {
        final profileMap = Map<String, dynamic>.from(
          Uri.splitQueryString(profileJson),
        );
        return UserModel.fromJson(profileMap);
      }
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to get saved user profile: $e');
    }

    return null;
  }

  // ==================== PRIVATE METHODS ====================

  /// Start listening to auth state changes
  static void _startAuthStateListener() {
    _authSubscription = SupabaseConfig.client.auth.onAuthStateChange.listen(
      (data) async {
        final event = data.event;
        final session = data.session;

        _logger.d('$_logTag Auth state changed: $event');

        switch (event) {
          case AuthChangeEvent.signedIn:
            if (session != null) {
              _sessionController.add(
                SessionState.authenticated(session.user, session),
              );
              saveSession(session);
            }
            break;

          case AuthChangeEvent.signedOut:
            _sessionController.add(SessionState.unauthenticated());
            clearSession();
            break;

          case AuthChangeEvent.tokenRefreshed:
            if (session != null) {
              _sessionController.add(
                SessionState.authenticated(session.user, session),
              );
              saveSession(session);
            }
            break;

          case AuthChangeEvent.passwordRecovery:
            // Handle password recovery - user is temporarily authenticated
            // Don't emit session state change to avoid interfering with password reset flow
            _logger.i(
              '$_logTag Password recovery event - user temporarily authenticated',
            );
            if (session != null) {
              // Save the temporary session but don't emit authenticated state
              // This prevents AuthBloc from triggering AuthCheckRequested during password reset
              await saveSession(session);
            }
            break;

          default:
            break;
        }
      },
      onError: (error) {
        _logger.e('$_logTag ❌ Auth state listener error: $error');
        _sessionController.add(SessionState.error(error.toString()));
      },
    );
  }

  /// Restore session from persistent storage
  static Future<void> _restoreSession() async {
    _logger.d('$_logTag Attempting to restore session from storage...');

    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionData = prefs.getString(_sessionKey);

      if (sessionData != null) {
        // Session data exists, but Supabase handles restoration automatically
        _logger.d('$_logTag Session data found in storage');

        // Check if current session is valid
        final currentSession = SupabaseConfig.currentSession;
        if (currentSession != null) {
          _logger.i('$_logTag ✅ Session restored successfully');
          _sessionController.add(
            SessionState.authenticated(currentSession.user, currentSession),
          );
        } else {
          _logger.w('$_logTag Session data exists but no active session');
          await clearSession();
        }
      } else {
        _logger.d('$_logTag No session data found in storage');
      }
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to restore session: $e');
      await clearSession();
    }
  }

  /// Start automatic token refresh timer
  static void _startTokenRefreshTimer() {
    // Refresh every 30 minutes
    _refreshTimer = Timer.periodic(const Duration(minutes: 30), (timer) {
      refreshSessionIfNeeded();
    });

    _logger.i('$_logTag ✅ Token refresh timer started (30 minute intervals)');
  }

  /// Save session to SharedPreferences
  static Future<void> _saveSession(Session session) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_sessionKey, jsonEncode(session.toJson()));
  }

  /// Save user profile to SharedPreferences
  static Future<void> _saveUserProfile(UserModel userProfile) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userProfileKey, userProfile.toJson().toString());
  }

  /// Save last refresh time
  static Future<void> _saveLastRefreshTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastRefreshKey, DateTime.now().millisecondsSinceEpoch);
  }
}

/// Session state for the application
class SessionState {
  final SessionStatus status;
  final User? user;
  final Session? session;
  final String? error;

  const SessionState._({
    required this.status,
    this.user,
    this.session,
    this.error,
  });

  factory SessionState.authenticated(User user, Session session) {
    return SessionState._(
      status: SessionStatus.authenticated,
      user: user,
      session: session,
    );
  }

  factory SessionState.unauthenticated() {
    return const SessionState._(status: SessionStatus.unauthenticated);
  }

  factory SessionState.loading() {
    return const SessionState._(status: SessionStatus.loading);
  }

  factory SessionState.error(String error) {
    return SessionState._(status: SessionStatus.error, error: error);
  }

  bool get isAuthenticated => status == SessionStatus.authenticated;
  bool get isLoading => status == SessionStatus.loading;
  bool get hasError => status == SessionStatus.error;
}

enum SessionStatus { authenticated, unauthenticated, loading, error }
