import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/shop_service.dart';
import '../../services/storage_service.dart';
import '../../services/supabase_service.dart';
import 'shop_creation_event.dart';
import 'shop_creation_state.dart';

class ShopCreationBloc extends Bloc<ShopCreationEvent, ShopCreationState> {
  ShopCreationBloc() : super(const ShopCreationState()) {
    on<UpdateShopNameEvent>(_onUpdateShopName);
    on<UpdateShopDescriptionEvent>(_onUpdateShopDescription);
    on<UpdateShopLogoEvent>(_onUpdateShopLogo);
    on<UpdateShopBannerEvent>(_onUpdateShopBanner);
    on<UploadShopLogoEvent>(_onUploadShopLogo);
    on<UploadShopBannerEvent>(_onUploadShopBanner);
    on<ValidateFormEvent>(_onValidateForm);
    on<ClearValidationErrorsEvent>(_onClearValidationErrors);
    on<CreateShopEvent>(_onCreateShop);
    on<ResetFormEvent>(_onResetForm);
    on<CheckExistingShopEvent>(_onCheckExistingShop);
  }

  void _onUpdateShopName(
    UpdateShopNameEvent event,
    Emitter<ShopCreationState> emit,
  ) {
    emit(
      state.copyWith(
        shopName: event.shopName,
        validationErrors: Map.from(state.validationErrors)..remove('shopName'),
      ),
    );
    add(const ValidateFormEvent());
  }

  void _onUpdateShopDescription(
    UpdateShopDescriptionEvent event,
    Emitter<ShopCreationState> emit,
  ) {
    emit(
      state.copyWith(
        shopDescription: event.shopDescription,
        validationErrors: Map.from(state.validationErrors)
          ..remove('shopDescription'),
      ),
    );
    add(const ValidateFormEvent());
  }

  void _onUpdateShopLogo(
    UpdateShopLogoEvent event,
    Emitter<ShopCreationState> emit,
  ) {
    emit(state.copyWith(shopLogoUrl: event.logoUrl));
  }

  void _onUpdateShopBanner(
    UpdateShopBannerEvent event,
    Emitter<ShopCreationState> emit,
  ) {
    emit(state.copyWith(shopBannerUrl: event.bannerUrl));
  }

  Future<void> _onUploadShopLogo(
    UploadShopLogoEvent event,
    Emitter<ShopCreationState> emit,
  ) async {
    try {
      emit(
        state.copyWith(
          status: ShopCreationStatus.uploading,
          uploadProgress: 0.0,
        ),
      );

      final file = File(event.imagePath);

      // Get current user ID
      final userId = BaseSupabaseService.client.auth.currentUser?.id;
      if (userId == null) {
        emit(
          state.copyWith(
            status: ShopCreationStatus.error,
            errorMessage: 'User not authenticated',
          ),
        );
        return;
      }

      final result = await StorageService.uploadShopLogo(
        userId: userId,
        imageFile: file,
      );

      if (result.isSuccess && result.data != null) {
        emit(
          state.copyWith(
            status: ShopCreationStatus.initial,
            shopLogoUrl: result.data,
            uploadProgress: 1.0,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: ShopCreationStatus.error,
            errorMessage: result.error ?? 'Failed to upload logo',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ShopCreationStatus.error,
          errorMessage: 'Failed to upload logo: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUploadShopBanner(
    UploadShopBannerEvent event,
    Emitter<ShopCreationState> emit,
  ) async {
    try {
      emit(
        state.copyWith(
          status: ShopCreationStatus.uploading,
          uploadProgress: 0.0,
        ),
      );

      final file = File(event.imagePath);

      // Get current user ID
      final userId = BaseSupabaseService.client.auth.currentUser?.id;
      if (userId == null) {
        emit(
          state.copyWith(
            status: ShopCreationStatus.error,
            errorMessage: 'User not authenticated',
          ),
        );
        return;
      }

      final result = await StorageService.uploadShopBanner(
        userId: userId,
        imageFile: file,
      );

      if (result.isSuccess && result.data != null) {
        emit(
          state.copyWith(
            status: ShopCreationStatus.initial,
            shopBannerUrl: result.data,
            uploadProgress: 1.0,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: ShopCreationStatus.error,
            errorMessage: result.error ?? 'Failed to upload banner',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ShopCreationStatus.error,
          errorMessage: 'Failed to upload banner: ${e.toString()}',
        ),
      );
    }
  }

  void _onValidateForm(
    ValidateFormEvent event,
    Emitter<ShopCreationState> emit,
  ) {
    final errors = <String, String>{};

    // Validate shop name
    if (state.shopName.trim().isEmpty) {
      errors['shopName'] = 'Shop name is required';
    } else if (state.shopName.trim().length < 3) {
      errors['shopName'] = 'Shop name must be at least 3 characters';
    } else if (state.shopName.trim().length > 100) {
      errors['shopName'] = 'Shop name must be less than 100 characters';
    }

    // Validate shop description
    if (state.shopDescription.trim().isEmpty) {
      errors['shopDescription'] = 'Shop description is required';
    } else if (state.shopDescription.trim().length < 10) {
      errors['shopDescription'] =
          'Shop description must be at least 10 characters';
    }

    final isValid = errors.isEmpty;

    emit(state.copyWith(validationErrors: errors, isFormValid: isValid));
  }

  void _onClearValidationErrors(
    ClearValidationErrorsEvent event,
    Emitter<ShopCreationState> emit,
  ) {
    emit(
      state.copyWith(
        validationErrors: {},
        errorMessage: null,
        successMessage: null,
      ),
    );
  }

  Future<void> _onCreateShop(
    CreateShopEvent event,
    Emitter<ShopCreationState> emit,
  ) async {
    if (!state.isFormValid) {
      add(const ValidateFormEvent());
      return;
    }

    try {
      emit(state.copyWith(status: ShopCreationStatus.loading));

      final shop = await ShopService.createShop(
        shopName: state.shopName.trim(),
        shopDescription: state.shopDescription.trim(),
      );

      // Update shop with logo and banner if provided
      if (state.shopLogoUrl != null || state.shopBannerUrl != null) {
        final updatedShop = await ShopService.updateShop(
          shopId: shop.id,
          shopLogoUrl: state.shopLogoUrl,
          shopBannerUrl: state.shopBannerUrl,
        );

        emit(
          state.copyWith(
            status: ShopCreationStatus.success,
            createdShop: updatedShop,
            successMessage: 'Shop created successfully!',
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: ShopCreationStatus.success,
            createdShop: shop,
            successMessage: 'Shop created successfully!',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ShopCreationStatus.error,
          errorMessage: 'Failed to create shop: ${e.toString()}',
        ),
      );
    }
  }

  void _onResetForm(ResetFormEvent event, Emitter<ShopCreationState> emit) {
    emit(const ShopCreationState());
  }

  Future<void> _onCheckExistingShop(
    CheckExistingShopEvent event,
    Emitter<ShopCreationState> emit,
  ) async {
    try {
      emit(state.copyWith(status: ShopCreationStatus.checkingExistingShop));

      final existingShop = await ShopService.getUserShop();

      if (existingShop != null) {
        emit(
          state.copyWith(
            status: ShopCreationStatus.existingShopFound,
            existingShop: existingShop,
          ),
        );
      } else {
        emit(state.copyWith(status: ShopCreationStatus.initial));
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ShopCreationStatus.error,
          errorMessage: 'Failed to check existing shop: ${e.toString()}',
        ),
      );
    }
  }
}
