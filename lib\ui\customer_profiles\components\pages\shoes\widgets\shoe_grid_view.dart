import 'package:flutter/material.dart';
import 'package:business_app/ui/customer_profiles/components/pages/shoes/models/shoe_model.dart';
import 'package:business_app/ui/customer_profiles/components/pages/shoes/widgets/shoe_card.dart';

class ShoeGridView extends StatefulWidget {
  final List<ShoeModel> shoes;
  final Function(ShoeModel) onShoeSelected;

  const ShoeGridView({
    Key? key,
    required this.shoes,
    required this.onShoeSelected,
  }) : super(key: key);

  @override
  State<ShoeGridView> createState() => _ShoeGridViewState();
}

class _ShoeGridViewState extends State<ShoeGridView> {
  final Set<String> _favoriteShoes = {};

  void _toggleFavorite(String id) {
    setState(() {
      if (_favoriteShoes.contains(id)) {
        _favoriteShoes.remove(id);
      } else {
        _favoriteShoes.add(id);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _favoriteShoes.contains(id)
              ? 'Added to favorites'
              : 'Removed from favorites',
        ),
        duration: const Duration(milliseconds: 800),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _addToCart(ShoeModel shoe) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${shoe.name} added to cart'),
        duration: const Duration(milliseconds: 800),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.shoes.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No shoes found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Try adjusting your filters',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: GridView.builder(
          itemCount: widget.shoes.length,
          physics: const BouncingScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _getCrossAxisCount(context),
            childAspectRatio: 0.68, // 👈 Matches ShoeCard height
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
          ),
          itemBuilder: (context, index) {
            final shoe = widget.shoes[index];
            return ShoeCard(
              shoe: shoe,
              isFavorite: _favoriteShoes.contains(shoe.id),
              onTap: () => widget.onShoeSelected(shoe),
              onAddToCart: () => _addToCart(shoe),
              onToggleFavorite: () => _toggleFavorite(shoe.id),
            );
          },
        ),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 600) return 3;
    return 2;
  }
}
